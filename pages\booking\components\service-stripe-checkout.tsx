import { InitiateServicePayment } from "@/src/services/booking.service";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe, Stripe } from "@stripe/stripe-js";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import StripeCheckoutForm from "./service-stripe-checkout-form";
import CircularLoader from "@/components/ui/circular-loader";

type Props = {
	bookingId: string;
	clientEmail: string | undefined;
	serviceId: string | undefined;
	serviceOptions: Array<string> | null | undefined;
	onSuccessFullPayment: VoidFunction;
};

const StripeCheckout = (props: Props) => {
	const [stripe, setStripe] = useState<Stripe | null>(null);
	useEffect(() => {
		loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY).then((val) =>
			setStripe(val),
		);
	}, []);
	const { data: clientSecret } = useQuery({
		queryKey: ["service-payment"],
		queryFn: async () =>
			// @ts-expect-error type error
			(await InitiateServicePayment(props)).data.clientSecret,
		// when there is both serviceId and clientEmail, we can initiate the payment
		enabled: !!props.serviceId && !!props.clientEmail,
	});

	return (
		<section className="w-full h-full ">
			<div className="w-full h-full flex items-center justify-center">
				{clientSecret ? (
					<Elements stripe={stripe} options={{ clientSecret }}>
						<StripeCheckoutForm
							clientSecret={clientSecret}
							onSuccessFullPayment={props.onSuccessFullPayment}
							bookingId={props.bookingId}
						/>
					</Elements>
				) : (
					<CircularLoader />
				)}
			</div>
		</section>
	);
};

export default StripeCheckout;
