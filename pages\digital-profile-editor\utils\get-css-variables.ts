import { entries, inlineSwitch } from "@/lib/helpers"
import { ITheme } from "../types"

type Config = {
  theme?: ITheme
}

declare module 'react' {
  export interface CSSProperties extends Partial<ReturnType<typeof getCSSVariables> & ButtonVariables> { }
}

type Props = 'color' | 'fontSize' | 'boxShadow' | 'borderColor' | 'borderWidth' | 'fontWeight' | 'borderRadius' | 'background' | 'padding' | 'hoverEffect' | 'gradient'

type ButtonVariables = {
  [index: `--profile-button-${number}-${Props}`]: string
}

function getButtonVariables(buttonStyles: ITheme['buttonStyles']) {
  let vars = {} as ButtonVariables;
  buttonStyles?.forEach(({ style }, index) => {
    const map = {
      color: 'color',
      fontSize: 'fontSize',
      boxShadow: 'boxShadow',
      borderColor: 'borderColor',
      borderWidth: 'borderWidth',
      fontWeight: 'fontWeight',
      borderRadius: 'borderRadius',
      padding: 'padding',
    } as const;
    entries(map).forEach(([k, v]) => {
      vars = {
        ...vars,
        [`--profile-button-${index}-background`]: style.gradient || style.background,
        [`--profile-button-${index}-${k}`]: style[v]
      }
    })
  })
  return vars
}

export function getCSSVariables({ theme }: Config) {
  const { colors, backgroundValue, backgroundType, buttonStyles } = theme || {}
  const cssVariables = {
    '--profile-body-text': colors?.text || '',
    '--profile-primary': colors?.primary || '',
    '--profile-secondary': colors?.secondary || '',
    '--profile-accent': colors?.accent || '',
    '--profile-background': inlineSwitch(
      backgroundType || 'solid',
      ['solid', colors?.background || ''],
      { default: backgroundValue || '' }
    ),
    '--profile-font-header': theme?.fonts?.heading || '',
    '--profile-font-body': theme?.fonts?.body || '',
    ...getButtonVariables(buttonStyles || [])
  } as const

  return cssVariables
}
