import { AxiosResponse } from "axios";
import { BusinessLinkI } from "../interfaces/digital-profile";
import { Service } from "../interfaces/services";
import { api } from "./api.service";
import { Category } from "../interfaces/category";

export type CreateServicePayload = {
  name: string; // Name of the service
  description: string; // Description of the service
  messageAfterScheduling: string; // Message to show after scheduling
  duration: number; // Duration in minutes
  blockExtraTimeBefore: number;
  blockExtraTimeAfter: number;
  price: number; // Price of the service
  category: string; // Reference to the category of the service
  color?: string; // Optional color representation for the service
  picture: File | null;
  access: "public" | "private"; // Access control for the service
  isGroupEvent: boolean; // If the service allows group bookings
  serviceOptions?: Array<string> | null;
};

export function convertObjectToFormData<T = CreateServicePayload>(
  obj: T,
): FormData {
  // filter out null undefined values
  const keysOfObj = Object.entries(obj as any).filter(([_, v]) => {
    return ![undefined, null].includes(v as any);
  });
  const typedFormData = new FormData();
  for (const [k, v] of keysOfObj) {
    if (Array.isArray(v)) typedFormData.set(k, JSON.stringify(v));
    else if (v instanceof Date) typedFormData.set(k, v.toISOString());
    else if (["string", "boolean", "number"].includes(typeof v))
      typedFormData.set(k, String(v));
    else if (v instanceof Blob) typedFormData.set(k, v);
    else if (typeof v === "object") typedFormData.set(k, JSON.stringify(v));
  }
  return typedFormData;
}

export const CreateService = async (
  payload: CreateServicePayload,
): Promise<
  AxiosResponse<{
    service: Service;
    directBookingLink: string;
  }>
> => {
  try {
    const payloadFormData = convertObjectToFormData(payload);
    //Object.keys(payload).forEach(k => console.log(k, payloadFormData.get(k)))
    //throw new Error('testing testing')
    const response = await api.post(`/create-service`, payloadFormData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};
export const VerifyStripeAccount = async () => {
  try {
    const response = await api.get<{ success: true, status: true }>(`/check-stripe`);
    return response;
  } catch (error) {
    throw error;
  }
};
export const GetBusinessLink = async (): Promise<AxiosResponse> => {
  try {
    const response = await api.get(`/business-link`);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};
export const GetBusinessLinkById = async (
  Id: string,
): Promise<AxiosResponse> => {
  try {
    const response = await api.get(`/business-link/${Id}`);
    return response;
  } catch (error) {
    console.error("get-link:", error);
    throw error;
  }
};

export const GetBusinessLinkByUsername = async (
  username: string,
): Promise<
  AxiosResponse<{
    businessLink: BusinessLinkI;
  }>
> => {
  try {
    const response = await api.get(`/links/${username}`);
    return response;
  } catch (error) {
    console.error("get-link-username:", error);
    throw error;
  }
};

export const AddBusinessLink = async (
  payload: unknown,
): Promise<AxiosResponse> => {
  try {
    const response = await api.post(`/business-link`, payload);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};
export const UpdateBusinessLink = async (
  payload: unknown,
): Promise<AxiosResponse> => {
  try {
    const response = await api.put(`/business-link`, payload);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

export const UpdateService = async ({
  urlName,
  payload,
}: {
  urlName: string;
  payload: object;
}): Promise<AxiosResponse> => {
  try {
    const payloadFormData = convertObjectToFormData(payload)
    // throw new Error('not working')
    const response = await api.put(`/service/${urlName}`, payloadFormData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

export interface TCategory {
  _id: string;
  name: string;
}

export const GetCategories = async (): Promise<
  AxiosResponse<{
    categories: TCategory[];
  }>
> => {
  try {
    const response = await api.get(`/category`);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

export const GetCategory = async (): Promise<
  AxiosResponse<{
    categories: Category[];
  }>
> => {
  try {
    const response = await api.get(`/category`);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

export const CreateCategory = async (payload: {
  name: string;
}): Promise<
  AxiosResponse<{
    category: Category;
  }>
> => {
  try {
    const response = await api.post(`/category`, payload);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

export const DeleteCategory = async (id: string) => {
  try {
    const response = await api.delete(`/category/delete/${id}`,);
    return response;
  } catch (error) {
    console.error("delete:", error);
    throw error;
  }
};

export const UpdateCategory = async (payload: { data: Pick<TCategory, 'name'>, id: string }) => {
  try {
    const response = await api.put(`/category/update/${payload.id}`, payload.data);
    return response;
  } catch (error) {
    console.error("delete:", error);
    throw error;
  }
};

export type TService = {
  _id: string;
  userId: ObjectId<{}>;
  name: string;
  description?: string;
  messageAfterScheduling?: string;
  duration: number;
  blockExtraTimeBefore?: number;
  blockExtraTimeAfter?: number;
  price: number;
  category: ObjectId<{
    name: string
  }>;
  color?: string;
  picture?: string;
  access: "public" | "private";
  isGroupEvent?: boolean;
  directBookingLink?: string;
  urlName: string;
  createdAt: string;
  updatedAt: string;
  assignedStaff: string[];
  serviceOptions?: Array<{
    name: string;
    description?: string;
    price: number;
    _id: string;
  }>;
};

export const GetServices = async (): Promise<
  AxiosResponse<{ success: true; Services: TService[] }>
> => {
  try {
    const response = await api.get(`/service`);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

export const GetServiceByUrlName = async (
  urlName: string,
): Promise<
  AxiosResponse<{
    success: boolean;
    Service: TService;
  }>
> => {
  try {
    const response = await api.get(`/service/${urlName}`);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

export const GetServiceById = async (
  serviceId: string,
): Promise<
  AxiosResponse<{
    success: boolean;
    Service: TService;
  }>
> => {
  try {
    const response = await api.get(`/service/by-id/${serviceId}`);
    return response;
  } catch (error) {
    console.error("Get Service by id:", error);
    throw error;
  }
};

export const DeleteService = async (urlName: string): Promise<AxiosResponse> => {
  try {
    const response = await api.delete(`/delete-service/${urlName}`);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

export type ServiceAddon = {
  _id: string
  name: string;
  description: string
  price: number;
  isActive: boolean
}

export const AddonsApi = {
  GetAll: async () => {
    try {
      const response = await api.get<{ serviceOptions: Array<ServiceAddon> }>(`/service-options`);
      return response;
    } catch (error) {
      console.error("get add-ons", error);
      throw error;
    }
  },
  Delete: async (id: string) => {
    try {
      const response = await api.delete(`/service-options/${id}`);
      return response;
    } catch (error) {
      console.error("delete add-on", error);
      throw error;
    }
  },
  Update: async (payload: { data: Omit<ServiceAddon, "_id">, id: string }) => {
    try {
      const response = await api.put(`/service-options/${payload.id}`, payload.data);
      return response;
    } catch (error) {
      console.error("update add-on", error);
      throw error;
    }
  },
  Create: async (payload: Omit<ServiceAddon, '_id'>) => {
    try {
      const response = await api.post(`/service-options`, payload);
      return response;
    } catch (error) {
      console.error("create add-on", error);
      throw error;
    }
  },

}
