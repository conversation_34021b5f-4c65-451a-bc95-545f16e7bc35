import React from "react";


export const LocationIcon: React.FC<React.SVGProps<SVGSVGElement>> = ({ width = 24, height = 24, ...rest }) => (
  <svg width={width} height={height} viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" {...rest} >
    <path fillRule="evenodd" clipRule="evenodd" d="M4.6875 10.5C4.6875 6.46142 7.96142 3.1875 12 3.1875C16.0386 3.1875 19.3125 6.46142 19.3125 10.5C19.3125 13.2965 18.1521 14.9427 16.7817 16.2224C16.2039 16.7619 15.634 17.197 15.0281 17.6597L15.0279 17.6598C14.8488 17.7965 14.6665 17.9357 14.4799 18.0807C14.1019 18.3744 13.6803 18.7139 13.308 19.0817C12.944 19.4412 12.5056 19.9438 12.2128 20.5976C12.1622 20.7108 12.1009 20.7724 12.0701 20.795C12.0573 20.8043 12.0508 20.8066 12.0486 20.8073L12.0485 20.8073C12.047 20.8079 12.0335 20.8125 12 20.8125C11.9665 20.8125 11.953 20.8079 11.9515 20.8073L11.9514 20.8073C11.9492 20.8066 11.9427 20.8043 11.9299 20.795C11.8991 20.7724 11.8378 20.7108 11.7872 20.5976C11.4944 19.9438 11.056 19.4412 10.692 19.0817C10.3197 18.7139 9.89805 18.3744 9.52009 18.0807C9.33352 17.9357 9.15123 17.7965 8.97209 17.6598L8.97195 17.6597C8.36599 17.197 7.7961 16.7619 7.21827 16.2224C5.84787 14.9427 4.6875 13.2965 4.6875 10.5ZM21.75 10.5C21.75 15.5986 18.6507 17.9626 16.4564 19.6364C15.5035 20.3632 14.7213 20.9599 14.4375 21.5938C14.0317 22.5 13.1825 23.25 12 23.25C10.8175 23.25 9.96828 22.5 9.5625 21.5938C9.27867 20.9599 8.49646 20.3632 7.54364 19.6364C5.34926 17.9626 2.25 15.5986 2.25 10.5C2.25 5.11522 6.61522 0.75 12 0.75C17.3848 0.75 21.75 5.11522 21.75 10.5ZM14.25 10.5C14.25 11.7426 13.2426 12.75 12 12.75C10.7574 12.75 9.75 11.7426 9.75 10.5C9.75 9.25736 10.7574 8.25 12 8.25C13.2426 8.25 14.25 9.25736 14.25 10.5ZM16.5 10.5C16.5 12.9853 14.4853 15 12 15C9.51472 15 7.5 12.9853 7.5 10.5C7.5 8.01472 9.51472 6 12 6C14.4853 6 16.5 8.01472 16.5 10.5Z" fill="inherit" />
  </svg>

);
// CheckboxIcon SVG Component (for the select all leads checkbox)
export const CheckboxIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="24" height="24" rx="8" fill="#E91E63" />
    <path fillRule="evenodd" clipRule="evenodd" d="M18.8601 6.28821C19.2532 6.62517 19.2988 7.21702 18.9618 7.61013L11.4618 16.3601C11.2919 16.5584 11.0469 16.6768 10.786 16.6868C10.525 16.6968 10.2717 16.5976 10.0871 16.4129L5.71209 12.0379C5.34597 11.6718 5.34597 11.0782 5.71209 10.7121C6.0782 10.346 6.6718 10.346 7.03791 10.7121L10.697 14.3712L17.5382 6.3899C17.8752 5.99678 18.467 5.95126 18.8601 6.28821Z" fill="white" />
  </svg>
);

export const ThunderboltIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
	<svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg" {...props}>
		<g clipPath="url(#clip0_31_471)">
			<path d="M12.7832 6.04943L6.10548 13.1199C5.83869 13.4024 5.46731 13.5625 5.07876 13.5625C4.09871 13.5625 3.41662 12.5887 3.75154 11.6676L4.8125 8.75H2.3424C1.53198 8.75 0.875 8.09302 0.875 7.2826C0.875 7.09591 0.910622 6.91095 0.979954 6.73762L3.01255 1.65613C3.30695 0.920118 4.0198 0.4375 4.8125 0.4375H8.3125C9.15925 0.4375 9.74535 1.28324 9.44803 2.07608L8.75 3.9375H11.8728C12.5644 3.9375 13.125 4.49811 13.125 5.18967C13.125 5.50938 13.0027 5.81699 12.7832 6.04943Z" fill="inherit" />
		</g>
		<defs>
			<clipPath id="clip0_31_471">
				<rect width="14" height="14" fill="white" />
			</clipPath>
		</defs>
	</svg>
);

export const CheckCircleIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
	<svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg" {...props}>
		<rect width="14" height="14" rx="7" fill="inherit" />
		<path fillRule="evenodd" clipRule="evenodd" d="M11.0017 3.6681C11.2311 3.86466 11.2576 4.20991 11.0611 4.43922L6.68605 9.54339C6.58692 9.65905 6.44404 9.72812 6.29182 9.73396C6.1396 9.73981 5.99185 9.6819 5.88413 9.57419L3.33205 7.02211C3.11848 6.80854 3.11848 6.46228 3.33205 6.24871C3.54562 6.03514 3.89188 6.03514 4.10545 6.24871L6.23994 8.3832L10.2306 3.72742C10.4272 3.4981 10.7724 3.47155 11.0017 3.6681Z" fill="white" />
	</svg>
);

export const MailIcon: React.FC = () => (
	<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
		<path fillRule="evenodd" clipRule="evenodd" d="M21 9.74727V17.25C21 18.4926 19.9926 19.5 18.75 19.5H5.25C4.00736 19.5 3 18.4926 3 17.25V9.74727C3 9.71728 3.0006 9.68738 3.00178 9.65758L9.525 14.55C10.9917 15.65 13.0083 15.65 14.475 14.55L20.9982 9.65758C20.9994 9.68738 21 9.71728 21 9.74727ZM19.7906 7.7508L13.1093 3.96472C12.4212 3.57479 11.5788 3.57479 10.8907 3.96472L4.20941 7.7508L10.875 12.75C11.5417 13.25 12.4583 13.25 13.125 12.75L19.7906 7.7508ZM0.75 9.74727C0.75 8.12663 1.62145 6.63117 3.03144 5.83217L9.78144 2.00717C11.1577 1.2273 12.8423 1.22731 14.2186 2.00717L20.9686 5.83217C22.3786 6.63117 23.25 8.12663 23.25 9.74727V17.25C23.25 19.7353 21.2353 21.75 18.75 21.75H5.25C2.76472 21.75 0.75 19.7353 0.75 17.25V9.74727Z" fill="black" />
	</svg>

)

export const PhoneIcon: React.FC = () => (
	<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
		<path fillRule="evenodd" clipRule="evenodd" d="M21.1865 14.7493C20.5737 13.9322 19.748 13.2994 18.7996 12.9201L18.7514 12.9008C17.353 12.3414 15.7642 12.5516 14.5593 13.4552L14.0199 13.8598C13.7213 14.0837 13.3035 14.054 13.0396 13.7901L10.2095 10.96C9.94559 10.6961 9.9159 10.2783 10.1398 9.97967L10.5444 9.44031C11.448 8.23542 11.6582 6.64657 11.0988 5.24818L11.0795 5.19997C10.7002 4.25164 10.0674 3.4259 9.25027 2.81308L8.96444 2.59871C8.047 1.91063 6.85037 1.71644 5.76242 2.07909L5.4784 2.17376C3.91793 2.69392 2.69343 3.91842 2.17328 5.47889C1.73327 6.79891 1.67884 8.21718 2.01631 9.56706C2.65965 12.1404 3.99034 14.4908 5.86599 16.3665L7.63312 18.1336C9.50877 20.0093 11.8592 21.34 14.4325 21.9833C15.7824 22.3208 17.2007 22.2663 18.5207 21.8263C20.0812 21.3062 21.3057 20.0817 21.8258 18.5212L21.9205 18.2372C22.2832 17.1492 22.089 15.9526 21.4009 15.0352L21.1865 14.7493ZM7.73827 4.82908L7.45244 4.61471C7.19654 4.42278 6.86277 4.36862 6.55932 4.46977L6.27529 4.56445C5.46731 4.83377 4.83329 5.4678 4.56396 6.27578C4.27552 7.14109 4.23982 8.0709 4.46107 8.95587C4.99365 11.0862 6.09524 13.0319 7.6479 14.5846L9.41503 16.3517C10.9677 17.9044 12.9134 19.006 15.0437 19.5385C15.9287 19.7598 16.8585 19.7241 17.7238 19.4356C18.5318 19.1663 19.1658 18.5323 19.4352 17.7243L19.5298 17.4403C19.631 17.1368 19.5768 16.8031 19.3849 16.5472L19.1705 16.2613C18.835 15.814 18.3829 15.4675 17.8637 15.2598L17.8155 15.2406C17.2337 15.0078 16.5726 15.0953 16.0713 15.4712L15.5319 15.8758C14.2301 16.8521 12.4084 16.7227 11.2577 15.572L8.4276 12.7419C7.27691 11.5912 7.14745 9.76952 8.12384 8.46767L8.52836 7.92831C8.90435 7.42699 8.99178 6.76592 8.75905 6.18409L8.73976 6.13587C8.53208 5.61667 8.18562 5.16459 7.73827 4.82908Z" fill="black" />
	</svg>
)

export const FilterIcon: React.FC = () => (
	<svg width="18" height="14" viewBox="0 0 18 14" fill="none" xmlns="http://www.w3.org/2000/svg">
		<path fillRule="evenodd" clipRule="evenodd" d="M0.25 1.0625C0.25 0.544733 0.669733 0.125 1.1875 0.125H16.8125C17.3303 0.125 17.75 0.544733 17.75 1.0625C17.75 1.58027 17.3303 2 16.8125 2H1.1875C0.669733 2 0.25 1.58027 0.25 1.0625ZM2.75 7C2.75 6.48223 3.16973 6.0625 3.6875 6.0625H14.3125C14.8303 6.0625 15.25 6.48223 15.25 7C15.25 7.51777 14.8303 7.9375 14.3125 7.9375H3.6875C3.16973 7.9375 2.75 7.51777 2.75 7ZM7.4375 12C6.91973 12 6.5 12.4197 6.5 12.9375C6.5 13.4553 6.91973 13.875 7.4375 13.875H10.5625C11.0803 13.875 11.5 13.4553 11.5 12.9375C11.5 12.4197 11.0803 12 10.5625 12H7.4375Z" fill="black" />
	</svg>

)

export const CreditIcon: React.FC<React.SVGProps<SVGSVGElement>> = ({ width = 24, height = 25, ...props }) => (
	<svg width={width} height={height} viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
		<rect y="0.5" width="24" height="24" rx="12" fill="#E91E63" />
		<path d="M11.6869 17.1055C10.7989 17.1055 10.0309 16.9215 9.38285 16.5535C8.73485 16.1775 8.23485 15.6495 7.88285 14.9695C7.53085 14.2815 7.35485 13.4695 7.35485 12.5335C7.35485 11.6055 7.53885 10.7975 7.90685 10.1095C8.27485 9.42149 8.79085 8.88949 9.45485 8.51349C10.1189 8.12949 10.8909 7.93749 11.7709 7.93749C12.5149 7.93749 13.1749 8.07349 13.7509 8.34549C14.3349 8.60949 14.8109 8.98549 15.1789 9.47349C15.5469 9.96149 15.7749 10.5375 15.8629 11.2015H13.8109C13.6829 10.7535 13.4349 10.4095 13.0669 10.1695C12.7069 9.92949 12.2589 9.80949 11.7229 9.80949C11.2429 9.80949 10.8269 9.91749 10.4749 10.1335C10.1309 10.3495 9.86685 10.6615 9.68285 11.0695C9.49885 11.4695 9.40685 11.9535 9.40685 12.5215C9.40685 13.0735 9.49885 13.5535 9.68285 13.9615C9.87485 14.3695 10.1429 14.6855 10.4869 14.9095C10.8389 15.1255 11.2509 15.2335 11.7229 15.2335C12.2669 15.2335 12.7269 15.1095 13.1029 14.8615C13.4869 14.6055 13.7389 14.2575 13.8589 13.8175H15.8749C15.7709 14.4735 15.5269 15.0495 15.1429 15.5455C14.7669 16.0415 14.2829 16.4255 13.6909 16.6975C13.0989 16.9695 12.4309 17.1055 11.6869 17.1055Z" fill="white" />
	</svg>
)