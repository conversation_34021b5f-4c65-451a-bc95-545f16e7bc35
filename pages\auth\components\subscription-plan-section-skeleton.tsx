const SubscriptionPlanSkeleton = () => {
  return (
    <div className="w-full max-w-2xl m-auto p-6">
      <div className="bg-white overflow-hidden animate-pulse">
        {/* Header Skeleton */}
        <div className="p-6 border-b border-gray-200">
          <div className="h-8 bg-gray-200 rounded-md w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded-md w-2/3"></div>
        </div>

        {/* Pricing Section Skeleton */}
        <div className="p-6 space-y-6">
          <div className="space-y-4">
            <div className="h-6 bg-gray-200 rounded-md w-1/4"></div>

            {/* Price Skeleton */}
            <div className="flex items-baseline justify-between">
              <div className="h-4 bg-gray-200 rounded-md w-16"></div>
              <div className="flex items-baseline gap-1">
                <div className="h-8 bg-gray-200 rounded-md w-24"></div>
                <div className="h-4 bg-gray-200 rounded-md w-16"></div>
              </div>
            </div>

            {/* Discount Skeleton */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center justify-between">
              <div className="h-4 bg-green-200 rounded-md w-32"></div>
              <div className="h-4 bg-green-200 rounded-md w-16"></div>
            </div>

            {/* Total Skeleton */}
            <div className="pt-4 border-t border-gray-200">
              <div className="flex items-baseline justify-between">
                <div className="h-4 bg-gray-200 rounded-md w-16"></div>
                <div className="flex items-baseline gap-1">
                  <div className="h-8 bg-gray-200 rounded-md w-24"></div>
                  <div className="h-4 bg-gray-200 rounded-md w-16"></div>
                </div>
              </div>
            </div>
          </div>

          {/* Features Skeleton */}
          <div className="space-y-4">
            <div className="h-6 bg-gray-200 rounded-md w-1/3"></div>
            <div className="space-y-3">
              {[1, 2, 3, 4].map((_, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="h-5 w-5 rounded-full bg-gray-200"></div>
                  <div className="h-4 bg-gray-200 rounded-md w-3/4"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionPlanSkeleton;
