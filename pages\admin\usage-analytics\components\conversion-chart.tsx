import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON>oot<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"
import { TrendingUp } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis } from "recharts"

// we will use a multiple bar chart for this
// name: Conversion Analytics
const chartData = [
  { month: "January", conversions: 45, rate: 2.3 },
  { month: "February", conversions: 52, rate: 2.7 },
  { month: "March", conversions: 48, rate: 2.5 },
  { month: "April", conversions: 70, rate: 3.1 },
  { month: "May", conversions: 65, rate: 2.9 },
  { month: "June", conversions: 75, rate: 3.3 },
];

const chartConfig = {
  conversions: {
    label: "Conversions",
    color: "blue",
  },
  rate: {
    label: "Rate",
    color: "yellow",
  },
} satisfies ChartConfig

/**
 * @dev use useQuery to fetch conversion analytics
 */
function ConversionAnalytics() {
  return (
    <Card >
      <CardHeader>
        <CardTitle>Conversion Analytics</CardTitle>
        <CardDescription>January - June 2024</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <BarChart accessibilityLayer data={chartData}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <ChartTooltip 
              cursor={false}
              content={<ChartTooltipContent indicator="dashed" />}
            />
            <Bar dataKey="conversions" className="fill-chart-1" radius={4}  />
            <Bar dataKey="rate" className="fill-chart-2" radius={4} />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          Showing total visitors for the last 6 months
        </div>
      </CardFooter>
    </Card>
  )
}

export default ConversionAnalytics