import { cn } from "@/lib/utils";
import Popover from "./popover";
import { motion } from "framer-motion";
import { InformationSquareIcon } from "hugeicons-react";

type MoreInfoProps = {
  content: string;
  className?: string;
};

const MoreInfoImpl = (props: MoreInfoProps) => {
  return (
    <Popover transformOrigin="top">
      <Popover.Trigger>
        <motion.div whileHover={{ scale: 1.05 }} className="cursor-pointer">
          <InformationSquareIcon className="text-paragraph/40 w-4 h-4" />
        </motion.div>
      </Popover.Trigger>
      <Popover.Content className={cn("-right-[530%] -translate-x-1/2 min-h-fit min-w-[0px] w-[180px] !max-w-[180px] px-3 py-2 rounded-xl text-sm", props.className)}>
        {props.content}
      </Popover.Content>
    </Popover>
  );
};

type MoreInfoStaticProps = MoreInfoProps

const MoreInfoStatic = (props: MoreInfoStaticProps) => {
  return (
    <div className={cn("flex items-start gap-x-1", props.className)}>
      <InformationSquareIcon className="text-paragraph/40 w-4 h-4 mt-1" />
      <span className="text-paragraph/70" >{props.content}</span>
    </div>
  );
};

const MoreInfo = Object.assign(MoreInfoImpl, {
  Static: MoreInfoStatic,
});

export default MoreInfo;
