export interface BusinessWebsiteI {
  image: string;
  businessName: string;
  businessUrl: string;
}

export interface BusinessLinkSettingsI {
  buttonColor: string;
  description?: string;
  textColor: string;
  backgroundColor: string;
  backgroundImageUrl?: string | null; // Optional, since it can be null
}

export interface BusinessLinkI {
  _id: string;
  userId?: {
    _id: string;
    photo: string;
    username: string;
  };
  profileUrl?: string;
  businessWebsites?: BusinessWebsiteI[]; // An array of business websites
  settings?: BusinessLinkSettingsI;
  createdAt: Date;
  updatedAt: Date;
}
