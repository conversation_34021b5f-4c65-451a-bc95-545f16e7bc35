import { createContext, useContext } from "react";

type AnalyticsInterval = "today" | "weekly" | "monthly" | "yearly";

type AnalyticsIntervalContextType = {
  interval: AnalyticsInterval;
};

const AnalyticsIntervalContext =
  createContext<AnalyticsIntervalContextType | null>(null);


export const intervalReadableFormatMap = {
  today: "Today",
  weekly: "this week",
  monthly: "this month",
  yearly: "this year",
} satisfies Record<AnalyticsInterval, string>;


export const useAnalyticsInterval = () => {
  const context = useContext(AnalyticsIntervalContext);
  if (!context) {
    throw new Error(
      "useAnalyticsInterval must be used within an AnalyticsIntervalProvider"
    );
  }
  return context;
};

export const AnalyticsIntervalProvider = AnalyticsIntervalContext.Provider;
