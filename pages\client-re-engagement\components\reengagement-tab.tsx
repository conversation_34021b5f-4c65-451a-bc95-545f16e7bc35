import { Card } from "@/components/ui/card";
import CircularLoader from "@/components/ui/circular-loader";
import Tabs from "@/components/ui/tabs";
import {
  capitalizeFirstWord,
  entries,
  inlineSwitch,
  noop,
  omit,
  pick,
} from "@/lib/helpers";
import {
  ClientToReEngage,
  GetClientsToReEngageResponse,
  GetReEngagementStrategies,
  GetReEngagementStrategiesResponse,
} from "@/src/services/analytics.service";
import { useMutation, UseQueryResult } from "@tanstack/react-query";
import React, { useState } from "react";
import ClientListTable from "./reengagement-list-table";
import Modal from "@/components/ui/modal";
import { Typography } from "@/components/typography";
import { Button } from "@/components/buttons";
import { X } from "lucide-react";
import { useModalsBuilder } from "@/lib/modals-builder";
import { formatDate } from "date-fns";
import {
  Calendar,
  ChevronRight,
  Envelope,
  FileArrowDown,
  Gift,
} from "@gravity-ui/icons";
import { cn } from "@/lib/utils";
import PendingOverlay from "@/components/ui/pending-overlay";
import BasicTooltip from "@/components/ui/basic-tooltip";
import { motion } from "framer-motion";
import { useClientSelection } from "./client-selection-context";

type Props = {
  tabId: "all" | "inactive" | "at-risk";
  clientsToReEngage: ClientToReEngage[];
  dataQuery: UseQueryResult<
    Omit<GetClientsToReEngageResponse, "message">,
    Error
  >;
};

const ReEngagementTab: React.FC<Props> = ({
  tabId = "all",
  dataQuery,
  clientsToReEngage,
}) => {
  const getStrategiesMutation = useMutation({
    mutationFn: GetReEngagementStrategies,
  });
  const { modals, modalFunctions } = useModalsBuilder({
    toEngage: {
      open: false,
      data: null as GetReEngagementStrategiesResponse | null,
    },
  });
  const { selectedClients } = useClientSelection();
  return (
    <Tabs.Content
      className="mt-2 focus:outline-none active:outline-none w-full"
      value={tabId}
    >
      <Typography className="text-paragraph/60 text-xs mb-6 px-1.5">
        {inlineSwitch(
          tabId,
          [
            "inactive",
            <>Clients who haven't interacted with us in the past 30 days.</>,
          ],
          [
            "at-risk",
            <>These clients have exceeded their usual engagement interval.</>,
          ],
          {
            default: (
              <>Clients who haven't interacted with us in the past 30 days.</>
            ),
          }
        )}
      </Typography>
      {selectedClients.length > 0 && (
        <div className="w-full md:max-w-[560px] flex items-center mb-3 gap-x-3">
          <BasicTooltip
            trigger={
              <motion.div
                whileTap={{ scale: 0.9 }}
                whileHover={{ scale: 1.05 }}
              >
                <FileArrowDown
                  width={20}
                  height={20}
                  className="text-paragraph/80 cursor-pointer"
                />
              </motion.div>
            }
            content="Download CSV"
          />
          <BasicTooltip
            trigger={
              <motion.div
                whileTap={{ scale: 0.9 }}
                whileHover={{ scale: 1.05 }}
              >
                <Envelope
                  width={20}
                  height={20}
                  className="text-paragraph/80 cursor-pointer"
                />
              </motion.div>
            }
            content="Send as Email"
          />
          <Typography className="text-paragraph/60 text-xs px-1.5 ml-auto">
            {selectedClients.length} selected
          </Typography>
        </div>
      )}
      {dataQuery.isFetching ? (
        <div className="w-full py-6 flex items-center justify-center">
          <CircularLoader />
        </div>
      ) : clientsToReEngage.length > 0 ? (
        <Card className="rounded-lg !px-0 overflow-x-auto no-scrollbar md:max-w-fit ">
          <ClientListTable
            clients={clientsToReEngage}
            onClickClient={async (client) => {
              const data = await getStrategiesMutation.mutateAsync(
                client.client._id
              );
              modalFunctions.openModal("toEngage", pick(data, "data"));
            }}
          />
        </Card>
      ) : (
        <div className="w-full py-6 flex items-center justify-center">
          <Typography variant={"p"} className="font-bold text-paragraph">
            No clients to re-engage
          </Typography>
        </div>
      )}
      <PendingOverlay isPending={getStrategiesMutation.isPending} />
      {modals.toEngage.data && (
        <Modal
          open={modals.toEngage.open}
          onClose={() => modalFunctions.closeModal("toEngage")}
        >
          <Modal.Body
            className={`min-w-80 !max-h-[500px] w-fit flex flex-col gap-y-7 items-center justify-center rounded-[32px] bg-white overflow-y-auto no-scrollbar `}
          >
            <div className="w-full flex justify-between items-center ">
              <Typography variant={"h4"} className="font-bold ">
                Client Details
              </Typography>
              <Button
                variant="icon"
                onTap={() => modalFunctions.closeModal("toEngage")}
                className="p-1 !rounded-full "
              >
                <X className="w-5 h-5" />
              </Button>
            </div>
            <div className="w-full flex flex-col gap-y-2 overflow-y-auto no-scrollbar relative">
              <div className="w-full flex items-center gap-x-2">
                <p className="bg-subtle-gray text-gray-500 rounded-full min-w-[40px] min-h-[40px] font-bold flex items-center justify-center">
                  {modals.toEngage.data?.client.name.slice(0, 1)}
                </p>
                <div>
                  <Typography className="text-paragraph font-bold">
                    {modals.toEngage.data?.client.name}
                  </Typography>
                  <div className="w-full flex items-center gap-x-2 text-sm">
                    <div
                      className={cn(
                        "min-w-1.5 min-h-1.5 rounded-full bg-yellow-400",
                        {
                          "bg-red-600": tabId === "at-risk",
                        }
                      )}
                    />
                    <Typography className="text-dark-gray font-normal leading-5 ">
                      {inlineSwitch(
                        tabId,
                        ["inactive", "Recently inactive"],
                        ["at-risk", "At Risk"]
                      )}{" "}
                      &bull; {modals.toEngage.data?.daysSinceLastAppointment}{" "}
                      days since last visit
                    </Typography>
                  </div>
                </div>
              </div>
              <div className="w-full grid grid-cols-2 gap-x-3 gap-y-1 text-sm">
                {entries(
                  pick(modals.toEngage.data.client, "email", "phone")
                ).map(([key, value]) => (
                  <div key={key} className="flex flex-col gap-y-0.5 ">
                    <Typography className="text-dark-gray font-normal ">
                      {capitalizeFirstWord(key.replace("_", " "))}
                    </Typography>
                    <Typography className="text-paragraph font-normal flex-grow text-ellipsis line-clamp-1">
                      {value}
                    </Typography>
                  </div>
                ))}
                <div className="flex flex-col gap-y-0.5 ">
                  <Typography className="text-dark-gray font-normal leading-5">
                    Last Visit
                  </Typography>
                  <Typography className="text-paragraph font-normal line-clamp-1 overflow-ellipsis">
                    {modals.toEngage.data?.lastAppointment &&
                      formatDate(
                        modals.toEngage.data?.lastAppointment,
                        "MMM dd, yyyy"
                      )}
                  </Typography>
                </div>
              </div>
              <Tabs
                defaultValue="re-engage"
                className="w-full flex flex-col gap-y-2"
              >
                <Tabs.List className="grid w-full grid-cols-2 sticky top-0 z-50">
                  <Tabs.Trigger
                    value="re-engage"
                    className=" px-0 flex items-center justify-center gap-x-2"
                  >
                    Re-engagement
                  </Tabs.Trigger>
                  <Tabs.Trigger
                    value="history"
                    className=" px-0 flex items-center justify-center gap-x-2"
                  >
                    History
                  </Tabs.Trigger>
                </Tabs.List>
                <ReEngagementStrategies
                  {...omit(
                    modals.toEngage.data,
                    "appointmentHistory",
                    "message"
                  )}
                  isAtRisk={tabId === "at-risk"}
                />
                <AppointmemtHistory
                  appointmentHistory={modals.toEngage.data.appointmentHistory}
                />
              </Tabs>
            </div>
          </Modal.Body>
        </Modal>
      )}
    </Tabs.Content>
  );
};

const appointmentStatusMap = {
  completed: "Completed",
  canceled: "Canceled",
  "no-show": "No Show",
} as const;

const ReEngagementStrategies: React.FC<
  Omit<GetReEngagementStrategiesResponse, "appointmentHistory" | "message"> & {
    isAtRisk: boolean;
  }
> = (props) => {
  return (
    <Tabs.Content className="flex flex-col gap-y-2" value="re-engage">
      <div className="w-full space-y-3">
        <div className="w-full flex flex-col gap-y-1">
          <Typography className="font-medium whitespace-nowrap">
            Recommended Strategies
          </Typography>
          <div
            className="w-full flex items-center gap-x-1.5 group "
            data-isatrisk={props.isAtRisk}
          >
            <div className="min-w-1.5 min-h-1.5 rounded-full bg-yellow-400 group-data-[isatrisk=true]:bg-red-600 " />
            <Typography className="font-normal leading-5 text-xs text-yellow-400 group-data-[isatrisk=true]:text-red-600 ">
              {props.clientStatus}
            </Typography>
          </div>
        </div>
        <div className="w-full space-y-2">
          {props.recommendedStrategies.map((strategy, index) => (
            <div
              key={index}
              className="px-3 py-3 pb-3 rounded-xl border border-gray-300 flex flex-col gap-y-2 text-paragraph font-Satoshi"
            >
              <p className="font-medium text-sm flex items-start gap-x-2">
                <div>
                  <Gift className="text-primary-300" width={18} height={18} />
                </div>{" "}
                {strategy}
              </p>
              {/* noop here */}
              <Button className="text-xs rounded-lg py-2" onTap={noop}>
                Execute
              </Button>
            </div>
          ))}
        </div>
      </div>
      {props.favoriteServices.length > 0 && (
        <div className="w-full space-y-3">
          <Typography className="font-medium">Favourite Services</Typography>
          <div className="w-full space-y-2">
            {props.favoriteServices.map((service, index) => (
              <div
                key={index}
                className="px-3 py-2 rounded-xl border border-gray-300"
              >
                <div className="flex items-center justify-between text-paragraph font-Bricolage_Grotesque">
                  <Typography className="font-normal">
                    {service.service.name}
                  </Typography>
                  <Typography className="font-normal">
                    ${service.totalRevenue}
                  </Typography>
                </div>
                <div className="flex items-center justify-between text-gray-400 font-normal text-xs">
                  <Typography className="font-normal">
                    Booked {service.bookingCount} times
                  </Typography>
                  <Typography className="font-normal">Total Revenue</Typography>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      {props.unbookedServices.length > 0 && (
        <div className="w-full space-y-3">
          <Typography className="font-medium">Service Opportunities</Typography>
          <div className="w-full space-y-2">
            {props.unbookedServices.map((service, index) => (
              <div
                key={index}
                className="px-3 py-2 rounded-xl border border-gray-300"
              >
                <div className="flex items-center justify-between text-paragraph font-Bricolage_Grotesque">
                  <Typography className="font-normal">
                    {service.name}
                  </Typography>
                  {/* still a noop */}
                  <Button
                    className="text-xs rounded-lg py-1 pt-1.5"
                    variant="outline"
                    onTap={noop}
                  >
                    Recommend
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </Tabs.Content>
  );
};

const AppointmemtHistory: React.FC<
  Pick<GetReEngagementStrategiesResponse, "appointmentHistory">
> = (props) => {
  const [appointments, setAppointments] = useState(
    props.appointmentHistory.slice(0, 3)
  );
  return (
    <Tabs.Content value="history" className="!mt-0 flex flex-col gap-y-3">
      <div className="w-full flex items-center justify-between gap-x-1 text-paragraph">
        <Typography className="font-medium whitespace-nowrap">
          Appointment History
        </Typography>
        <Typography className="text-paragraph/80 font-normal leading-5 text-xs">
          {props.appointmentHistory.length} appointments
        </Typography>
      </div>
      <div className="w-full flex flex-col">
        {appointments.map((appointment, index) => (
          <div key={index} className="flex items-start gap-x-2 group">
            <div className="h-fit flex flex-col items-center justify-center">
              <div className="w-3 h-3 rounded-full bg-primary-400 mt-0.5" />
              <div className="w-0.5 h-28 bg-gray-100 group-last-of-type:hidden " />
            </div>
            <div className="space-y-1 -mt-0.5 flex-grow">
              <Typography className="text-paragraph text-sm flex items-center gap-x-1">
                <Calendar className="text-gray-500" width={18} height={18} />{" "}
                {formatDate(new Date(appointment.date), "MMM dd, yyyy")}
              </Typography>
              <div className="px-3 py-2 rounded-xl border border-gray-300">
                <div className="flex items-center justify-between text-paragraph font-Bricolage_Grotesque">
                  <Typography className="font-normal">
                    {appointment.service.name}
                  </Typography>
                </div>
                <div className="flex items-center justify-between text-gray-400 font-normal text-xs">
                  <Typography className="font-normal">
                    Revenue: ${appointment.revenue || 0}
                  </Typography>
                  <Typography
                    className={cn("font-normal text-green-500", {
                      "text-red-500": appointment.status !== "completed",
                    })}
                  >
                    {appointmentStatusMap[appointment.status]}
                  </Typography>
                </div>
              </div>
            </div>
          </div>
        ))}
        {props.appointmentHistory.length > 3 &&
          props.appointmentHistory.length !== appointments.length && (
            <Button
              onTap={() => setAppointments(props.appointmentHistory)}
              variant="ghost"
              className="mt-2 flex items-center justify-center gap-x-0 text-primary-500 text-sm"
            >
              View all appointments
              <ChevronRight className="mt-0.5" />
            </Button>
          )}
      </div>
    </Tabs.Content>
  );
};

export default ReEngagementTab;
