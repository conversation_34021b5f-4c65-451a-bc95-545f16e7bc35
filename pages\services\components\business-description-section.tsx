import { BookingSettingsByUsername } from "@/src/services/booking-settings.service"
import 'react-quill/dist/quill.snow.css'

type Props = {
  settings: BookingSettingsByUsername
}

const BusinessDescSection = ({ settings }: Props) => {
  return (
    <div className="w-full flex flex-col" >
      {settings.bookingInfo.type === 'image' ?
        <div className="w-full flex items-center justify-center" >
          <img src={settings.bookingInfo.url} className="object-contain w-full" />
        </div>
        :
        <div className="mx-auto flex flex-col gap-y-3 w-full max-w-2xl ">

          {settings.businessLogoImageUrl
            && <div className="w-full flex items-center justify-center" >
              <img src={settings.businessLogoImageUrl} className="object-contain size-40" />
            </div>
          }
          <div className="ql-editor w-full">
            <div className="w-full" dangerouslySetInnerHTML={{ __html: settings.bookingInfo.content }} />
          </div>
        </div>
      }
    </div>
  )
}

export default BusinessDescSection
