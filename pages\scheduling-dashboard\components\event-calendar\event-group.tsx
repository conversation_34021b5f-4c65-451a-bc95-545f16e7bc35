import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";
import { useModalsBuilder } from "@/lib/modals-builder";
import { cn } from "@/lib/utils";
import { CalendarEvent } from "@/src/interfaces/booking";
import { formatDate } from "date-fns";
import { motion } from "framer-motion";
import { X } from "lucide-react";
import React from "react";

const allColors = [
  "#87CEEB", // Sky Blue
  "#ADD8E6", // Light Blue
  "#B0E0E6", // Powder Blue
  "#FFB347",
  "#FFC0CB",
] as const;

function pickRandomColor() {
  const randomIndex = Math.floor(Math.random() * allColors.length);
  return allColors[randomIndex];
}

// Create a color with opacity for background
function getColorWithOpacity(hexColor: `#${string}`, opacity: number) {
  // Convert hex to RGB
  const r = parseInt(hexColor.slice(1, 3), 16);
  const g = parseInt(hexColor.slice(3, 5), 16);
  const b = parseInt(hexColor.slice(5, 7), 16);

  // Return rgba color with opacity
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}

type EventGroupProps = {
  /**
   * @dev events passed to `Monthly` or `Weekly` calendar should be sorted by date, any one that has only one event in it will not be grouped together.
   * events to display in ascending order e.g [Wed 8 Apr 2023, Wed 11 Apr 2023]
   */
  events: CalendarEvent[];
  /**
   * @dev the top position of the event group, should be calculated with the first event in the group
   */
  topPosition: number;
  onClickEvent: (event: CalendarEvent) => void;
};

/**
 * @dev shows all events within a given timeframe
 * when clicked, shows a popover of all the events, when click on event shows the
 */
const EventGroup: React.FC<EventGroupProps> = (props) => {
  const minHeight = 80;
  const color = pickRandomColor();
  const { modals, modalFunctions } = useModalsBuilder({
    events: {
      open: false,
    },
  });
  const [firstEvent, secondEvent, ...restEvents] = props.events;
  return (
    <div
      style={{
        top: `${props.topPosition}px`,
      }}
      className="w-full absolute px-1 left-0"
    >
      <motion.div
        whileTap={{ scale: 0.9 }}
        whileHover={{ scale: 1.05 }}
        onTap={() => modalFunctions.openModal("events", {})}
        className="w-full rounded-xl p-1.5 cursor-pointer flex flex-col gap-y-1 border-2 "
        style={{
          minHeight: `${minHeight}px`,
          backgroundColor: getColorWithOpacity(color, 0.2),
          borderColor: color,
        }}
      >
        <div className="flex items-center w-full h-fit gap-x-2">
          <div
            className="w-full rounded-[8px] p-1 px-1.5 cursor-pointer flex flex-col gap-y-0.5 "
            style={{
              background: pickRandomColor(),
            }}
          >
            <p className="text-gray-600 font-medium text-xs line-clamp-1">
              {firstEvent.extendedProps?.customerInfo?.firstName}{" "}
              {firstEvent.extendedProps?.customerInfo?.lastName}
            </p>
            <p className=" text-xs font-semibold">
              {formatDate(new Date(firstEvent.start), "dd MMM hh:mm a")}
            </p>
          </div>
        </div>
        <div className="flex justify-between gap-x-1 text-xs font-semibold ">
          <div
            className={cn(
              "w-full rounded-[6px] p-0.5 px-1.5 cursor-pointer flex flex-col gap-y-0.5 ",
              { "!w-[80%]": restEvents.length === 0 }
            )}
            style={{
              background: pickRandomColor(),
            }}
          >
            <p className={cn(" text-xs font-semibold ")}>
              {formatDate(
                new Date(secondEvent.start),
                "dd MMM hh:mm a"
              ).replace(/[\n\t]/g, "")}
            </p>
          </div>
          {restEvents.length > 0 && (
            <span className="pl-1 pr-2">+{restEvents.length}</span>
          )}
        </div>
      </motion.div>
      <Modal
        open={modals.events.open}
        onClose={() => modalFunctions.closeModal("events")}
      >
        <Modal.Body
          className={`min-h-fit min-w-80 h-fit w-fit flex flex-col gap-y-7 items-center justify-center rounded-[32px] bg-white `}
        >
          <div className="w-full flex justify-between items-center">
            <Typography variant={"h4"} className="font-bold ">
              Events
            </Typography>
            <Button
              onTap={() => modalFunctions.closeModal("events")}
              variant="icon"
              className="p-1 !rounded-full hover:bg-gray-100"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
          <div className="w-full ">
            <div className="w-full grid grid-cols-2 gap-4">
              {props.events.map((event) => (
                <motion.div
                  whileTap={{ scale: 0.9 }}
                  whileHover={{ scale: 1.05 }}
                  key={event.id}
                  className="w-full rounded-[8px] p-1 px-1.5 cursor-pointer flex flex-col gap-y-0.5 "
                  style={{
                    background: pickRandomColor(),
                  }}
                  onTap={() => props.onClickEvent(event)}
                >
                  <p className="text-[#171717]/80 font-medium text-xs line-clamp-1">
                    {event.extendedProps?.customerInfo?.firstName}{" "}
                    {event.extendedProps?.customerInfo?.lastName}
                  </p>
                  <p className="text-[#171717]/70 text-xs font-semibold max-w-[100px] ">
                    {event.title}
                  </p>
                  <p className=" text-xs font-semibold">
                    {formatDate(new Date(event.start), "dd MMM hh:mm a")}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
};

export default EventGroup;
