import { But<PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";
import TrackAnalyticsButton from "~/components/track-analytics-button";
import { useToast } from "~/contexts/hooks/toast";
import {
  type BlockedTimeI,
  DeleteBlockTime,
  GetBlockedTimes,
} from "~/services/calendar-settings.service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { formatDate } from "date-fns";
import { Pencil, PlusIcon, Trash2 } from "lucide-react";
import { useState } from "react";
import CreateTimeModal from "./create-time-modal";
import CircularLoader from "@/components/ui/circular-loader";
import Dialog from "@/components/ui/dialog";
import PendingOverlay from "@/components/ui/pending-overlay";

export const dummyBlockedTimes: BlockedTimeI[] = [
  {
    _id: "1234567890",
    userId: "user123",
    calendarId: "cal456",
    startTime: "09:00",
    endTime: "10:30",
    startDate: "2024-01-15T00:00:00.000Z",
    endDate: "2024-01-15T00:00:00.000Z",
    notes: "Morning team meeting",
  },
  {
    _id: "0987654321",
    userId: "user123",
    calendarId: "cal456",
    startTime: "14:00",
    endTime: "15:00",
    startDate: "2024-01-16T00:00:00.000Z",
    endDate: "2024-01-16T00:00:00.000Z",
    notes: "Client consultation",
  },
  {
    _id: "5678901234",
    userId: "user123",
    calendarId: "cal456",
    startTime: "11:30",
    endTime: "12:30",
    startDate: "2024-01-17T00:00:00.000Z",
    endDate: "2024-01-17T00:00:00.000Z",
    notes: "Lunch break",
  },
];

interface BlockTimeProps {
  open: boolean;
  onClose: () => void;
}

const BlockedTimesList = ({ open, onClose }: BlockTimeProps) => {
  const showToast = useToast();
  const queryClient = useQueryClient();
  const [modals, setModals] = useState({
    createTime: {
      open: false,
      blockedTime: null as BlockedTimeI | null,
    },
    deleteTime: {
      open: false,
      blockedTime: {} as BlockedTimeI | null,
    },
  });
  const modalFunctions = {
    openModal: <K extends keyof typeof modals>(
      modal: K,
      payload: Omit<(typeof modals)[K], "open">
    ) => {
      setModals((p) => ({
        ...p,
        [modal]: {
          ...p[modal],
          ...payload,
          open: true,
        },
      }));
    },
    closeModal: (modal: keyof typeof modals) => {
      setModals((p) => ({
        ...p,
        [modal]: {
          ...p[modal],
          open: false,
        },
      }));
    },
  };
  const DeleteBlockTimeMutation = useMutation({
    mutationFn: DeleteBlockTime,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["blocktimes"] });
      showToast("success", "Blocked time Deleted");
    },
    onError: (error: ApiError) => {
      showToast("error", error?.response?.data?.error || error?.message);
    },
  });
  const { data: blockedTimes = [], isFetching } = useQuery({
    queryFn: async () => (await GetBlockedTimes()).data.blockedTimes,
    queryKey: ["blocktimes"],
    enabled: open,
  });
  return (
    <>
      <Modal open={open} onClose={onClose}>
        <Modal.Body className="max-h-[85vh] overflow-y-auto w-[94vw] md:w-[700px] outline-none bg-white rounded-3xl shadow-lg no-scrollbar">
          <div className="flex gap-x-3 justify-between mb-4">
            <Typography variant={"h3"} className="text-xl">
              Block Times
            </Typography>
            {/* // call createTime modal with blockedTime set to null */}
            <TrackAnalyticsButton
              variant="icon"
              className="flex items-center px-3"
              onTap={() => {
                modalFunctions.openModal("createTime", { blockedTime: null });
              }}
            >
              <PlusIcon />
              <span className="hidden md:inline">Add Block Time</span>
            </TrackAnalyticsButton>
          </div>
          {blockedTimes.length === 0 && !isFetching && (
            <Typography variant={"p"} className="font-bold  text-center">
              No services found
            </Typography>
          )}
          {isFetching ? (
            <div className="w-full py-10 flex justify-center ">
              <CircularLoader />
            </div>
          ) : (
            <div className="space-y-4 ">
              {blockedTimes?.map((blockedTime) => (
                <div
                  key={blockedTime._id}
                  className="border p-4 rounded-2xl shadow-sm flex flex-col "
                >
                  <div>
                    <p>
                      <strong>Date:</strong>{" "}
                      {formatDate(
                        new Date(blockedTime.startDate),
                        "yyyy-MM-dd"
                      )}{" "}
                      -{" "}
                      {formatDate(new Date(blockedTime.endDate), "yyyy-MM-dd")}
                    </p>
                    <p>
                      <strong>Time:</strong> {blockedTime.startTime} -{" "}
                      {blockedTime.endTime}
                    </p>
                    <p>
                      <strong>Notes:</strong> {blockedTime.notes}
                    </p>
                  </div>
                  <div className="flex w-full justify-end items-center">
                    <Button
                      variant="icon"
                      className="bg-transparent "
                      onTap={() =>
                        modalFunctions.openModal("createTime", {
                          blockedTime: blockedTime,
                        })
                      }
                    >
                      <Pencil className="w-5 h-5" />
                    </Button>
                    <Button
                      variant="icon"
                      className="bg-transparent "
                      onTap={() =>
                        modalFunctions.openModal("deleteTime", {
                          blockedTime: blockedTime,
                        })
                      }
                    >
                      <Trash2 className="w-5 h-5" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </Modal.Body>
      </Modal>
      <CreateTimeModal
        open={modals.createTime.open}
        onClose={() => modalFunctions.closeModal("createTime")}
        blockedTime={modals.createTime.blockedTime} // Pass selected block time for editing
      />
      {/* Delete Confirmation Dialog */}
      <Dialog
        title="Delete Block Time?"
        description="Are you sure you want to delete this block time? This action cannot be undone."
        open={modals.deleteTime.open}
        onClose={() => modalFunctions.closeModal("deleteTime")}
        action={{
          title: "Delete",
          onConfirm: () => {
            modalFunctions.closeModal("deleteTime");
            if (modals.deleteTime.blockedTime) {
              DeleteBlockTimeMutation.mutate(modals.deleteTime.blockedTime._id);
            }
          },
        }}
      />
      <PendingOverlay isPending={DeleteBlockTimeMutation.isPending} />
    </>
  );
};

export default BlockedTimesList;
