import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import { Card } from "@/components/ui/card";
import Chip from "@/components/ui/chip";
import { isUnwantedInArray, omit } from "@/lib/helpers";
import { IBlogPayload, UploadBlogImage } from "@/src/services/blog.service";
import { Plus } from "@gravity-ui/icons";
import { useFormik } from "formik";
import { string, object, mixed, array } from "yup";
import AddBlogImage from "./add-image";
import { RevertError } from "@/lib/errors";
import { useAdminBlogApis } from "../use-admin";
import { useEffect, useRef, useState } from "react";
import PendingOverlay from "@/components/ui/pending-overlay";
import EditorJS from "@editorjs/editorjs";
import MarkdownConverter from "@vingeray/editorjs-markdown-converter";
import Quote from "@editorjs/quote";
import Header from "@editorjs/header";
import List from "@editorjs/list";
import { useParams } from "react-router-dom";
import CircularLoader from "@/components/ui/circular-loader";
import ErrorScreen from "@/components/ui/error-screen";
import { urlToFile } from "@/lib/url-to-file";
import ImageTool from "@editorjs/image";
import { ImageConfig } from "@/src/editor-js-image-types";
import { useToast } from "@/src/contexts/hooks/toast";
import RadioBox from "@/components/ui/radio-box";

// Create a custom File validation schema
const imageSchema = mixed()
  .test("is-file", "Please upload a file", (value) => {
    return value instanceof File;
  })
  .test("file-size", "File is larger than 1MB", (value) => {
    return value && value instanceof File && value.size <= 1 * 1024 * 1024; // 1MB limit
  })
  .test("file-type", "Unsupported file format", (value) => {
    return (
      value &&
      value instanceof File &&
      ["image/jpeg", "image/png", "image/gif"].includes(value.type)
    );
  });

export default function EditBlog() {
  const {
    tags: tagsList,
    deleteTagMutation,
    createTagMutation,
    createBlogMutation,
    updateBlogMutation,
    getBlogByUniqueKey,
  } = useAdminBlogApis();
  const [newTag, setNewTag] = useState("");
  const { uniqueKey: blogUniqueKey } = useParams<{ uniqueKey: string }>();
  const blogQuery = getBlogByUniqueKey(blogUniqueKey!);
  const formik = useFormik({
    initialValues: {
      title: "",
      authorName: "",
      longDescription: "",
      shortDescription: "",
      image: null as File | null,
      tags: [] as string[],
    } satisfies IBlogPayload,
    validationSchema: object({
      title: string().required("Title is required"),
      authorName: string().required("Author name is required"),
      longDescription: string().required("Long description is required"),
      shortDescription: string().required("Short description is required"),
      image: imageSchema.nullable(),
      tags: array()
        .of(string()) // Specify that the array contains strings
        .required("Tags are required")
        .min(1, "At least one tag is required"), // Optional: require at least one tag,
    }),
    onSubmit(_values, formikHelpers) {
      formikHelpers.validateForm();
    },
  });
  const editorRef = useRef<HTMLDivElement>(null);
  const editorJSRef = useRef<EditorJS>();
  // initialize blog editor and set formik values when THERE IS BLOG DATA.
  useEffect(() => {
    if (editorRef.current && blogQuery.data) {
      const blog = blogQuery.data;
      formik.setValues({
        title: blog.title,
        authorName: blog.authorName,
        longDescription: blog.longDescription,
        shortDescription: blog.shortDescription,
        image: null,
        tags: blog.tags || [],
      });
      formik.setFieldValue("longDescription", blog.longDescription);
      (async () => {
        let imageFile: File | null = null;
        if (blog.imageUrl) {
          try {
            // Extract filename from URL or use a default name
            const filename = blog.imageUrl.split("/").pop() || "blog-image.jpg";
            imageFile = await urlToFile(blog.imageUrl, filename);
          } catch (error) {
            console.error("Failed to convert image URL to file:", error);
          }
        }
        formik.setFieldValue("image", imageFile);
      })();
      if (!editorJSRef.current) {
        editorJSRef.current = new EditorJS({
          holder: editorRef.current as HTMLElement,
          placeholder: "Write something...",
          data: {
            blocks: MarkdownConverter.toBlocks(blog.longDescription),
          },
          async onChange(api) {
            const data = await api.saver.save();
            // turn all image blocks to follow the .data.url editorjs format
            const blocksSanitized = data["blocks"].map((block) => {
              if (block.type === "image") {
                return {
                  ...block,
                  data: {
                    ...block.data,
                    url: block.data.file.url || "",
                  },
                };
              } else return block;
            });
            formik.setFieldValue(
              "longDescription",
              MarkdownConverter.toMarkdown(blocksSanitized),
            );
          },
          minHeight: 100,
          tools: {
            quote: Quote,
            header: Header,
            list: List,
            image: {
              class: ImageTool,
              config: {
                uploader: {
                  async uploadByFile(file) {
                    // 1MB limit
                    if (
                      file &&
                      file instanceof File &&
                      file.size <= 1 * 1024 * 1024
                    ) {
                      showToast("info", "File is larger than 1MB");
                      return Promise.reject("File is larger than 1MB");
                    }
                    const formData = new FormData() as TypedFormData<{
                      image: Blob;
                    }>;
                    formData.set("image", file);
                    return UploadBlogImage(formData).then((val) => val.data);
                  },
                },
                types: "image/png, image/jpg",
                field: "image",
              } satisfies Partial<ImageConfig>,
            },
          },
        });
      }
    }

    return () => {
      // if we just set editor to `undefined` without this check, this useFootGun of a hook will set EditorJS twice, causing two instances 🙄
      if (blogQuery.data && editorRef.current && editorJSRef.current) {
        editorJSRef.current?.destroy?.();
        editorJSRef.current = undefined;
      }
    };
  }, [blogQuery.data]);
  const showToast = useToast();
  if (blogQuery.isPending)
    return (
      <div className="w-full py-20 flex justify-center">
        <CircularLoader />
      </div>
    );

  if (!blogUniqueKey)
    return (
      <ErrorScreen
        className="mt-20"
        title="Blog not found"
        message="The blog you are trying to edit does not exist."
      />
    );

  if (blogQuery.isError)
    return (
      <ErrorScreen
        className="mt-20"
        title="Blog not found"
        message="The blog you are trying to edit does not exist."
        onRetry={() => blogQuery.refetch()}
      />
    );

  if (blogQuery.data)
    return (
      <section className="shadow-none pb-20 w-full max-w-[720px] space-y-4 mx-auto ">
        <PendingOverlay
          isPending={
            createTagMutation.isPending ||
            deleteTagMutation.isPending ||
            createBlogMutation.isPending ||
            updateBlogMutation.isPending
          }
        />
        <Card className="py-6 mt-0 px-3 w-full ">
          <p className="font-medium mb-1">Availabe Tags</p>
          <p className="font-medium text-paragraph/40 text-sm mb-2">
            Tags are collection names which each blog article may belong to.
            Each has a maximum length of two words.
          </p>
          <div className="flex items-center gap-2 bg-white rounded-xl max-h-44 overflow-y-auto thin-scrollbar flex-wrap">
            {tagsList.length ? (
              tagsList.map((tag) => (
                <Chip
                  key={tag._id}
                  label={tag.name}
                  onDelete={() => {
                    deleteTagMutation.mutateAsync(tag._id!, {
                      onSuccess() {
                        formik.setFieldValue(
                          "tags",
                          formik.values.tags.filter((t) => t !== tag.name),
                        );
                      },
                    });
                  }}
                  className="line-clamp-1"
                />
              ))
            ) : (
              <span className="text-sm font-normal">No tags found</span>
            )}
          </div>

          <div
            className={
              "bg-[#DFE1DB]/30 shadow-sm mt-3 pl-2 pr-1.5 py-1.5 w-fit flex items-center gap-x-2 rounded-full text-sm font-medium"
            }
          >
            <input
              placeholder="Create tag"
              className="!rounded-full text-sm text-paragraph placeholder:text-paragraph/40 bg-transparent outline-none pl-1"
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
            />
            <Button
              type="button"
              className="rounded-full !p-1 bg-[#DFE1DB]/60 "
              variant="icon"
              onTap={() => {
                if (newTag.length === 0) return;
                // check if tag is more than two words
                if (newTag.trim().split(" ").length > 2)
                  return showToast("info", "Tags have a two word max length", {
                    description: "Please shorten your tag",
                  });
                createTagMutation.mutateAsync({
                  name: newTag,
                });
                setNewTag("");
              }}
            >
              <Plus className="text-gray-500 " />
            </Button>
          </div>
        </Card>
        <Typography className="text-paragraph pl-2 font-Bricolage_Grotesque text-3xl font-bold">
          Edit Blog
        </Typography>
        <div className="flex flex-col gap-y-4 md:flex-row-reverse md:gap-x-3">
          <Card className="py-6 pb-8 px-5 h-fit min-w-60 max-w-xs">
            <p className="font-medium mb-2">Tags</p>
            <div className="flex items-center gap-2 bg-white rounded-3xl flex-wrap">
              <RadioBox.Multiple
                label=""
                onChange={(vals) => {
                  formik.setFieldValue(
                    "tags",
                    vals.map((v) => v.value),
                  );
                }}
                values={formik.values.tags.map((v) => ({
                  label: v,
                  value: v,
                }))}
                options={tagsList.map(({ name: tag }) => ({
                  value: tag,
                  label: tag,
                }))}
              />
            </div>
          </Card>
          <Card className="py-6 px-4 md:flex-grow">
            <Input.Text
              label="Author Name"
              value={formik.values.authorName}
              onChange={(e) =>
                formik.setFieldValue("authorName", e.target.value)
              }
              name="authorName"
              required
            />
            <Input.Text
              label="Title"
              value={formik.values.title}
              onChange={(e) => formik.setFieldValue("title", e.target.value)}
              name="title"
              required
            />
            <Input.Text
              label="Short description"
              value={formik.values.shortDescription}
              onChange={(e) =>
                formik.setFieldValue("shortDescription", e.target.value)
              }
              name="shortDescription"
              required
            />
          </Card>
        </div>
        <AddBlogImage
          image={formik.values.image}
          onImageAddition={async (image) => {
            await formik.setFieldValue("image", image);
            await formik.validateField("image");
            if (formik.errors.image)
              throw new RevertError(
                formik.errors.image || "Image upload failed",
              );
          }}
        />
        <section ref={editorRef} className="w-full "></section>
        <div className="w-full flex gap-x-3 items-center">
          <Button
            variant="outline"
            className="w-full"
            onTap={() => {
              formik.resetForm();
              formik.setFieldValue("longDescription", "");
              editorJSRef.current?.clear();
            }}
          >
            Cancel
          </Button>
          <Button
            disabled={isUnwantedInArray(
              [
                ...Object.values(omit(formik.values, "tags")),
                formik.values.tags.length,
              ],
              ["", null, undefined, 0],
            )}
            className="w-full"
            onTap={() => {
              updateBlogMutation.mutateAsync(
                {
                  payload: {
                    ...formik.values,
                    timePosted: new Date(),
                  },
                  uniqueKey: blogUniqueKey!,
                },
                {
                  onSuccess() {
                    formik.resetForm();
                    editorJSRef.current?.clear();
                  },
                },
              );
            }}
          >
            Save
          </Button>
        </div>
      </section>
    );
}
