import { motion } from "framer-motion";

const SubMenuTrails = ({ branches }: { branches: number }) => {
  return (
    <div className="w-[40px] h-fit relative ">
      <div className="h-full w-4 ml-auto flex flex-col">
        {Array.from({ length: branches }).map((_, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: index * 0.1 }}
            style={{
              translateY: `-${index * 12}px`,
            }}
            className="w-full first:h-7 first:translate-y-0 h-16 rounded-bl-3xl border-l-2 border-b-2 border-sidebar_trails"
          />
        ))}
      </div>
    </div>
  );
};

export default SubMenuTrails;
