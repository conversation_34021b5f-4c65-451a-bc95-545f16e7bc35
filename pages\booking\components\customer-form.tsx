import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import MoreInfo from "@/components/ui/more-info";
import { getCurrencySign } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import { ServiceOption } from "@/src/services/booking.service";
import { ExternalBookingContextType, useBookingContext } from "./use-booking-context";

interface CustomerFormProps {
  className?: string;
  billingSettings: {
    preferredCurrency: string
  }
  bookingType: 'internal' | 'external'
  options: ServiceOption[] | null;
  onClickNext: () => void;
}

const CustomerForm: React.FC<CustomerFormProps> = ({
  className = "",
  onClickNext,
  options,
  bookingType,
  ...props
}) => {
  const { values, setValue } = useBookingContext(bookingType)
  const setBookingValue = setValue as ExternalBookingContextType['setValue']
  return (
    <section
      className={`h-fit w-full flex px-[18px] min-[360px]:px-8 py-10 flex-col gap-y-7 items-center justify-center rounded-[28px] bg-[#EAEBE5]/40 ${className} `}
    >
      <Typography variant={"h2"} className="font-bold">
        Enter Your Details
      </Typography>
      <div className="space-y-4">
        <div className="flex gap-x-1">
          <Input.Text
            label="First Name"
            name="firstName"
            required
            value={values.customerInfo.firstName}
            onChange={({ currentTarget: { name, value } }) => {
              setBookingValue('customerInfo', {
                ...values.customerInfo,
                [name]: value
              })
            }}
            className="mb-4"
          />
          <Input.Text
            label="Last Name"
            name="lastName"
            required
            value={values.customerInfo.lastName}
            onChange={({ currentTarget: { name, value } }) => {
              setBookingValue('customerInfo', {
                ...values.customerInfo,
                [name]: value
              })
            }}
            className="mb-4"
          />
        </div>
        <div className="flex gap-x-2">
          <Input.Email
            label="Email"
            name="email"
            required
            value={values.customerInfo.email}
            onChange={({ currentTarget: { name, value } }) => {
              setBookingValue('customerInfo', {
                ...values.customerInfo,
                [name]: value
              })
            }}
            className="mb-4"
          />
          <Input.Phone
            label="Phone"
            name="phone"
            required
            value={values.customerInfo.phone}
            onChange={(value, { currentTarget: { name } }) => {
              setBookingValue('customerInfo', {
                ...values.customerInfo,
                [name]: value
              })
            }}
            className="mb-4"
          />
        </div>
        <Input.TextArea
          label="Notes"
          name="notes"
          required={bookingType === 'internal'}
          value={values.notes}
          onChange={({ currentTarget: { name, value } }) => {
            setBookingValue(name as 'notes', value)
          }}
          className="mb-4"
        />
        {options && options.length > 0 && (
          <div className="flex flex-col gap-x-2">
            <div className="w-full flex items-center gap-x-2 mb-2">
              <Typography className=" text-paragraph">
                Service Add-ons{" "}
                <span className="px-2 py-0.5 text-sky-600 bg-sky-50 rounded-md text-sm">
                  optional
                </span>
              </Typography>
              <MoreInfo content="Add any additional services or variations you'd like to include with your booking" />
            </div>
            {options?.map((option, index) => (
              <div
                key={index}
                className="flex items-start gap-x-2 group mb-2 last:mb-0"
              >
                <div className="w-full px-3 py-2 rounded-xl border border-gray-300 bg-white">
                  <div className="flex items-center justify-between text-paragraph font-Bricolage_Grotesque">
                    <Typography className="">{option.name}</Typography>
                    <Typography className={cn("text-paragraph ")}>
                      {getCurrencySign(props.billingSettings?.preferredCurrency)}
                      {option.price}
                    </Typography>
                  </div>
                  <div className="w-full flex justify-between items-center text-gray-400 font-normal text-xs gap-x-2">
                    <Typography className="font-normal flex-grow line-clamp-1">
                      {option.description ?? "No description"}
                    </Typography>
                    <Button
                      onTap={() => {
                        setBookingValue(
                          "serviceOptions",
                          (() => {
                            const options = values.serviceOptions || [];
                            if (options.includes(option._id))
                              return options.filter((o) => o !== option._id);
                            else return [...options, option._id];
                          })()
                        );
                      }}
                      className={cn("px-6", {
                        "bg-green-400": !values.serviceOptions?.includes(
                          option._id
                        ),
                        "bg-red-600": values.serviceOptions?.includes(
                          option._id
                        ),
                      })}
                    >
                      {values.serviceOptions?.includes(option._id)
                        ? "Remove"
                        : "Add"}{" "}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        <Button
          type="button"
          onTap={() => {
            onClickNext()
          }}
          variant="full"
          className="w-full"
        >
          Continue
        </Button>
      </div>
    </section>
  );
};

export default CustomerForm;
