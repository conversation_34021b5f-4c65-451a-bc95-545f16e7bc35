import { Input } from "@/components/inputs";
import { motion } from "framer-motion";
import { string } from "yup";
import { StepProps } from "../types";

const fields = [
	{
		label: "First Name",
		name: "first_name",
		type: "text",
	},
	{
		label: "Last Name",
		name: "last_name",
		type: "text",
	},
	{
		label: "Em<PERSON>",
		name: "email",
		type: "email",
	},
	{
		label: "Username",
		name: "username",
		type: "text",
	},
] as const;

const FieldTypeMap = {
	text: Input.Text,
	password: Input.Password,
	email: Input.Email,
};

const passwordValidationSchema = string()
	.required("Password is required")
	.matches(
		/[A-Za-z\d@$!%*?&]{8,}/,
		"Must Match 8+ characters from this consisting of lowercase, uppercase and special characters",
	)
	.matches(/(?=.*\d)/, "Must contain at least one number")
	.matches(/^(?=.*[A-Z])/, "Must contain at least one uppercase letter")
	.matches(/^(?=.*[a-z])/, "Must contain at least one lowercase letter")
	.matches(/(?=.*[@$!%*?&])/, "Must contain at least one special character");

const PersonalInfoStep: React.FC<MakeRequired<StepProps, "values">> = ({
	onChange,
	values,
}) => {
	return (
		<motion.div
			initial={{ x: 300, opacity: 0 }}
			animate={{ x: 0, opacity: 1 }}
			exit={{ x: -300, opacity: 0 }}
			transition={{ type: "spring", stiffness: 300, damping: 30 }}
			className="w-full flex flex-col gap-y-5"
		>
			{fields.map((field, index) => {
				const InputType = FieldTypeMap[field.type];
				return (
					<InputType
						key={index}
						label={field.label}
						name={field.name}
						value={values[field.name]}
						required
						onChange={onChange}
					/>
				);
			})}
			<Input.Password
				label={"Password"}
				name={"password"}
				onChange={onChange}
				value={values["password"]}
				validationSchema={passwordValidationSchema}
			/>
		</motion.div>
	);
};

export default PersonalInfoStep;
