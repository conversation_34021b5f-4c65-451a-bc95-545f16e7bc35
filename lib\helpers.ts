import MasterCardpayment from "@/assets/images/svg-logos/mastercard-payment.svg";
import VisaPayment from "@/assets/images/svg-logos/visa-payment.svg";

/**
 * @dev removes duplicate elements in an array
 * */
export function deduplicateElements<T extends any[]>(array: T) {
  return Array.from(new Set(array))
}

export const convertTo24HourFormat = (timeString: string) => {
  const [hours, minutes] = timeString.split(":");
  const date = new Date();
  date.setHours(parseInt(hours, 10));
  date.setMinutes(parseInt(minutes, 10));
  return date.toTimeString().slice(0, 5); // Returns time in 'HH:mm'
};
export const convertTo12HourFormat = (timeString: string) => {
  const [hours, minutes] = timeString.split(":");
  let hourNumber = parseInt(hours, 10);
  const suffix = hourNumber >= 12 ? "PM" : "AM";
  // Convert hour to 12-hour format
  hourNumber = hourNumber % 12 || 12; // Converts '0' to '12'

  return `${hourNumber}:${minutes} ${suffix}`;
};

export const formatLargeNumber = (num: number) => {
  const labels: string[] = ["", "K", "M", "B", "T"];
  let magnitude = 0;
  while (Math.abs(num) >= 1000 && magnitude < labels.length - 1) {
    magnitude += 1;
    num /= 1000;
  }

  const formatted = Number.isInteger(num)
    ? Math.floor(num)
    : Number(num.toFixed(1));
  return `${formatted}${labels[magnitude]}`;
};

export const convertPeriodToNumber = (value: string): number => {
  const [amount, unit] = value.split(" ");
  const numericAmount = parseInt(amount, 10);
  switch (unit) {
    case "day":
    case "days":
      return numericAmount;
    case "week":
    case "weeks":
      return numericAmount * 7;
    case "month":
    case "months":
      return numericAmount * 30; // Approximation, as months vary in length
    default:
      return 0; // Return 0 for invalid inputs
  }
};

export const convertNumberToPeriod = (value: number): string => {
  if (value === 1) {
    return "1 day";
  } else if (value <= 7) {
    return `${value} days`;
  } else if (value <= 14) {
    return value === 14 ? "2 weeks" : "1 week";
  } else if (value <= 30) {
    return "1 month";
  } else if (value <= 60) {
    return "2 months";
  } else {
    const months = Math.floor(value / 30);
    return `${months} months`;
  }
};

export const isNegative = (num: number) => num < 0;

export function last<T>(value: T[]): T;

export function last(value: string): string;

export function last<T>(value: T[] | string) {
  return value[value.length - 1];
}
export const debounce = <T extends (...args: any[]) => void>(
  func: T,
  delay: number = 500
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

export const omit = <T extends object, K extends keyof T>(
  obj: T,
  ...keys: K[]
): Omit<T, K> => {
  const result = { ...obj };
  keys.forEach((key) => delete result[key]);
  return result;
};

export function objectKeys<T extends Record<string | number, any>, K extends keyof T>(
  obj: T
) {
  return Object.keys(obj) as K[]
}

export function objectValues<T extends Record<string | number, any>>(
  obj: T
) {
  return Object.values(obj)
}

type CaseArray<C, R> = [C, R];

/**
 * @dev take this as an inline switch
 * @param check value to check
 * @param cases the cases to check against
 * @returns the return value of the case that matches the check
 */
export function inlineSwitch<Return, Check = any, Case extends Check = any, Default extends { default: Return } | undefined = undefined>(
  check: Check,
  ...cases: Array<CaseArray<Case, Return> | Default>
) {
  let pickedReturn: Return | undefined = undefined;
  // separate the default case objects from the and return the a destructured array of [defaultCases, caseArrays]
  const [defaultCases, caseArrays] = cases.reduce(
    (acc, caseArray) => {
      if (Array.isArray(caseArray)) {
        acc[1].push(caseArray);
      } else {
        acc[0].push(caseArray);
      }
      return acc;
    },
    [[], []] as [Default[], CaseArray<Case, Return>[]]
  );

  for (let i = 0; i < caseArrays.length; i++) {
    const caseArray = caseArrays[i];
    if (caseArray[0] === check) {
      pickedReturn = caseArray[1];
      break;
    }
  }
  if (pickedReturn === undefined && defaultCases.length > 0)
    pickedReturn = last(defaultCases)?.default;
  return pickedReturn as Default extends { default: Return } ? Return : undefined;
}

/**
 * Convert a 2D array into a CSV string
 */
export function arrayToCsv(data: string[][]): string {
  return data
    .map(
      (row) =>
        row
          .map(String) // convert every value to String
          .map((v) => v.replace(/"/g, '""')) // escape double quotes
          .map((v) => `"${v}"`) // quote it
          .join(",") // comma-separated
    )
    .join("\r\n"); // rows starting on new lines
}

export const formatCardNumber = (value: string) => {
  const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
  const matches = v.match(/\d{4,16}/g);
  const match = (matches && matches[0]) || "";
  const parts = [];

  for (let i = 0, len = match.length; i < len; i += 4) {
    parts.push(match.substring(i, i + 4));
  }

  if (parts.length) {
    return parts.join(" ");
  } else {
    return value;
  }
};

// Format expiry date
export const formatExpiryDate = (value: string) => {
  const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
  if (v.length >= 2) {
    return v.slice(0, 2) + "/" + v.slice(2, 4);
  }
  return v;
};

export const prependSlash = (path: string): string => {
  return !path.startsWith("/") ? `/${path}` : path;
};

export const noop = () => { };

export const noopPreventDefault = (e: { preventDefault: () => void }) => {
  e.preventDefault();
};

export const px = <V extends string | number>(value: V): `${V}px` =>
  `${value}px`;

export const deg = <V extends string | number>(value: V): `${V}deg` =>
  `${value}deg`;

export const sec = <V extends string | number>(value: V): `${V}s` =>
  `${value}s`;

export const percentage = <V extends string | number>(value: V): `${V}%` =>
  `${value}%`;

export const getDims = ({ width, height }: CSSStyleDeclaration) => {
  return {
    width: Number(width.replace("px", "")),
    height: Number(height.replace("px", "")),
  };
};

export const round = Math.round;

export const pick = <O extends Record<string, any>, K extends keyof O>(
  obj: O,
  ...keys: K[]
) => {
  const emptyObj = {} as Record<K, any>;
  keys.forEach((k) => (emptyObj[k] = obj[k]));
  return emptyObj as Pick<O, K>;
};

type PickByTypeMap = {
  string: string;
  number: number;
  boolean: boolean;
  object: object;
  function: ((...args: any[]) => any);
};

export const pickByType = <VType extends keyof PickByTypeMap, O extends Record<string, any>>(
  obj: O,
  valueType: VType
) => {
  const emptyObj = {} as Record<keyof O, PickByTypeMap[VType]>;
  entries(obj).forEach(([k, v]) => {
    if (typeof v === valueType) emptyObj[k as keyof O] = v as any;
  });
  return emptyObj;
};

export function capitalizeWords(str: string) {
  return str.replace(/\b\w/g, (match) => match.toUpperCase());
}

export function capitalizeFirstWord(str: string) {
  return str.replace(/^\w/, (match) => match.toUpperCase());
}

export function getTimeFromNowInMonths(numberOfMonths: number): Date {
  return new Date(new Date().setMonth(new Date().getMonth() + numberOfMonths));
}

export function isSmallScreen() {
  return window.innerWidth <= 768;
}

export function truncateText(input: string, max: 128 | (number & {}) = 128) {
  const totalLength = input.length;
  if (totalLength > max) return `${input.slice(0, max)}...`;
  else return input;
}

export function toReversed<T>(array: T[]) {
  const newArrray = [...array];
  return newArrray.reverse();
}

export function toSorted<T>(array: T[], compareFn: (a: T, b: T) => number) {
  const newArrray = [...array];
  return newArrray.sort(compareFn);
}

/**
 * Removes the last element from the given array and returns a new array with the last element removed.
 * @param arr - The array to remove the last element from.
 * @returns A new array with the last element removed.
 */
export function removeLast<T>(arr: T[]) {
  const newArr = [...arr];
  newArr.pop();
  return newArr;
}

export function processPaymentMethods(
  paymentMethods: PaymentMethod[]
): PaymentMethod[] {
  // Find the default payment method and keep it in an array
  const defaultPaymentMethod = paymentMethods.filter(
    (payment) => payment.type === "default"
  );

  // Filter out duplicates based on stripePaymethodId and _id
  const uniquePaymentMethods = Array.from(
    new Map(
      paymentMethods
        .filter((payment) => payment.type !== "default")
        .map((payment) => [payment.stripePaymethodId, payment]) // use stripePaymethodId as the key for uniqueness
    ).values()
  );

  // Combine the default payment method and unique payment methods
  return [...defaultPaymentMethod, ...uniquePaymentMethods];
}

export const paymentSvgIconMap = new Map<string[], string>([
  [["Visa", "visa"], VisaPayment],
  [["Mastercard", "mastercard"], MasterCardpayment],
]);

export function isPositive(num: number) {
  return num >= 0;
}

/**
 * @dev helps check if an unwanted value is in a list of values with an array
 * @default [undefined,null,'']
 * @replaces {if(!first_name || !last_name || !username || !email || !password)}
 */
export function isUnwantedInArray(
  values: any[],
  unwantedValues = [undefined, null, ""] as Array<any>
) {
  return isPositive(
    unwantedValues.findIndex((unwantedValue) => values.includes(unwantedValue))
  );
}

export function isEmptyObject(obj: any) {
  return Object.keys(obj).length === 0;
}

export function entries<T extends Record<string, any>, K extends keyof T>(
  obj: T
) {
  return Object.entries(obj) as [K, T[K]][];
}

/**
 *
 * @param num int or string int
 */
export function getSuffix(num: number | string) {
  num = String(num);
  const lastDigit = last(num);
  return inlineSwitch(lastDigit, ["1", "st"], ["2", "nd"], ["3", "rd"], {
    default: "th",
  });
}

/**
 * @dev should just be awaited
 */
export async function sleep(delay: number) {
  return new Promise((res, _) => {
    setTimeout(() => res("done sleeping"), delay);
  });
}

/**
 * @dev this converts something like 700 to -> 699.99 for ui representation
 */
export function convertPriceToDecimalFormat(price: number) {
  if (price === 0) return price;
  else return price - 0.01
}

const CURRENCY_MAP = {
  'usd': '$',
  'gbp': '£'
}

/**
 * @dev handles null or undefined values
 * */
export function getCurrencySign(currency?: string | null) {
  return currency ? (CURRENCY_MAP[currency as 'usd'] || CURRENCY_MAP['usd']) : CURRENCY_MAP['usd']
}

export function hyphenSeparatedLowercase(str: string) {
  return str.toLowerCase().replace(/ /g, '-')
}

export function removeUndefined<T>(array: T[]) {
  return array.filter(val => !([undefined, null].includes(val as any))) as Array<NonNullable<T>>
}

export function getRandomString() {
  const desiredLength = 16; // Example: 16 bytes for a secure random string
  const byteArray = new Uint8Array(desiredLength);
  crypto.getRandomValues(byteArray);

  // Convert the Uint8Array to a string representation if needed
  // For example, to a hexadecimal string:
  let hexString = '';
  for (let i = 0; i < byteArray.length; i++) {
    hexString += byteArray[i].toString(16).padStart(2, '0');
  }

  return (hexString);
}

export function steps(count: number) {
  return Array.from(
    Array.from({ length: count }).keys()
  )
}

/**
 * @dev function implementation of logical `or ||` operator
 * @example
 * ```js
 * <PendingOverlay isPending={
 *   or<boolean>(
 *     [toggleIntakeFormStatus, intakeFormRest.updateIntakeFormMutation, ...(
 *       Object.values(
 *         omit(intakeFormRest.questionMutations, 'reorder')
 *       )
 *     )].map(mut => mut.isPending)
 *   )
 * } />
 * ```
 * */
export function or<T>(values: T[]) {
  return values.reduce((acc, curr) => acc || curr)
}

/**
 * @dev function implementation of logical `and &&` operator
 * @example
 * ```js
 * <PendingOverlay isPending={
 *   and<boolean>(
 *     [toggleIntakeFormStatus, intakeFormRest.updateIntakeFormMutation, ...(
 *       Object.values(
 *         omit(intakeFormRest.questionMutations, 'reorder')
 *       )
 *     )].map(mut => mut.isPending)
 *   )
 * } />
 * ```
 * */
export function and<T>(values: T[]) {
  return values.reduce((acc, curr) => acc && curr)
}

/**
 * @dev negates any JavaScript value
 * */
export function negate(value: any) {
  return !value;
}

/**
 * @dev this is only for testing purposes, it will never be used in prod
 * */
export function fillWithValues<T>(v: T[], count = 1) {
  const arr = v.length
    ? Array.from(v).concat(new Array(count).fill(v.at(0)))
    : v
  return arr as T[]
}
