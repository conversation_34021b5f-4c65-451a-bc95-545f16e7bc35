import { AxiosResponse } from "axios";
import { DigitalProfileType } from "@/pages/digital-profile-editor/types";
import { ThemeCollection } from "@/pages/digital-profile-editor/utils/theme-collection";
import { Staff } from "./interfaces/staff";
import { StaffPerformance, StaffRanking, StaffAnalytics, StaffRating } from "./services/staff-performance.service";

export interface CalendarEvent {
  id: string;
  title: string;
  start: string;
  end: string;
  backgroundColor?: string;
  extendedProps?: {
    bookedBy: string;
    status: string;
    notes: string;
    service: {
      _id: string;
      name: string;
    };
    customerInfo?: {
      name: string;
      email: string;
      phone: string;
    };
  };
}

export class FakeData {
  static digitalProfile: DigitalProfileType = {
    _id: "dp001",
    userId: "user001",
    profileUrl: "techstartup",
    websiteLinks: [
      {
        _id: "link001",
        title: "Start Free Trial",
        url: "https://techstartup.com/trial",
        icon: "🚀",
        isActive: true,
        buttonStyle: ThemeCollection.modernTech.buttonStyles[2].name,
        type: "link",
        category: "suggested",
        description: "Try our platform for 14 days free",
        clickCount: 342,
        order: 1,
        metadata: { priority: "high", conversionGoal: true },
      },
      {
        _id: "link002",
        title: "Product Demo",
        url: "https://techstartup.com/demo",
        icon: "🎭",
        isActive: true,
        buttonStyle: ThemeCollection.modernTech.buttonStyles[0].name,
        type: "link",
        category: "suggested",
        description: "See our platform in action",
        clickCount: 156,
        order: 2,
        metadata: { duration: "15min" },
      },
      {
        _id: "link003",
        title: "Case Studies",
        url: "https://techstartup.com/case-studies",
        icon: "📃",
        isActive: true,
        buttonStyle: ThemeCollection.modernTech.buttonStyles[0].name,
        type: "collection",
        category: "suggested",
        clickCount: 89,
        order: 3,
      },
    ],
    imageUrl: "https://images.unsplash.com/photo-1560250097-0b93528c311a",
    hasImage: true,
    imagePublicId: "profile_techstartup",
    coverImage: {
      url: "https://images.unsplash.com/photo-1451187580459-43490279c0fa",
      publicId: "cover_techstartup",
    },
    details: {
      title: "TechStartup",
      bio: "Revolutionary SaaS platform that helps businesses automate their workflows and increase productivity by 300%.",
      displayName: "TechStartup Inc.",
    },
    theme: ThemeCollection.minimalistMono,
    template: "saas-company",
    socialMedia: [
      {
        type: "youtube",
        url: "https://youtube.com/mich_thedev",
      },
      {
        type: "twitter",
        url: "https://x.com/mich_thedev",
      },
    ],
    settings: {
      isPublic: true,
      showAnalytics: true,
      socialMediaIconsPosition: "bottom",
    },
    analytics: {
      totalViews: 2847,
      totalClicks: 587,
      dailyStats: [
        {
          date: new Date("2024-12-20"),
          views: 127,
          clicks: 34,
          topLinks: [
            { linkId: "link001", clicks: 18 },
            { linkId: "link002", clicks: 12 },
          ],
        },
      ],
    },
    status: "active",
    lastViewedAt: new Date("2024-12-20T10:30:00Z"),
    featuredUntil: new Date("2025-01-20"),
  };

  static staff: Staff[] = [
    {
      _id: "staff001",
      teamId: {
        name: "Software Team",
        ownerId: "owner001",
        _id: "team001",
      },
      userId: "user002",
      name: "Sarah Johnson",
      email: "<EMAIL>",
      phone: "******-0123",
      role: "Senior Stylist",
      color: "#3B82F6",
      services: ["service001", "service002"],
      locations: ["location001"],
      workingHours: {
        monday: { start: "09:00", end: "17:00", isWorking: true },
        tuesday: { start: "09:00", end: "17:00", isWorking: true },
        wednesday: { start: "09:00", end: "17:00", isWorking: true },
        thursday: { start: "09:00", end: "17:00", isWorking: true },
        friday: { start: "09:00", end: "17:00", isWorking: true },
        saturday: { start: "10:00", end: "16:00", isWorking: true },
        sunday: { start: "10:00", end: "16:00", isWorking: false },
      },
      buffer: 15,
      travelTime: 30,
      capacity: 8,
      isActive: true,
      createdAt: "2024-01-15T08:00:00Z",
      updatedAt: "2024-12-20T10:30:00Z",
    },
    {
      _id: "staff002",
      teamId: {
        name: "Software Team",
        ownerId: "owner002",
        _id: "team002",
      },
      userId: "user003",
      name: "Michael Chen",
      email: "<EMAIL>",
      phone: "******-0124",
      role: "Massage Therapist",
      color: "#10B981",
      services: ["service003"],
      locations: ["location001", "location002"],
      workingHours: {
        monday: { start: "08:00", end: "16:00", isWorking: true },
        tuesday: { start: "08:00", end: "16:00", isWorking: true },
        wednesday: { start: "08:00", end: "16:00", isWorking: true },
        thursday: { start: "08:00", end: "16:00", isWorking: true },
        friday: { start: "08:00", end: "16:00", isWorking: true },
        saturday: { start: "09:00", end: "15:00", isWorking: false },
        sunday: { start: "09:00", end: "15:00", isWorking: false },
      },
      buffer: 20,
      travelTime: 45,
      capacity: 6,
      isActive: true,
      createdAt: "2024-02-01T08:00:00Z",
      updatedAt: "2024-12-19T14:20:00Z",
    },
    {
      _id: "staff003",
      teamId: {
        name: "Software Team",
        ownerId: "owner003",
        _id: "team003",
      },
      userId: "user004",
      name: "Emily Rodriguez",
      email: "<EMAIL>",
      phone: "******-0125",
      role: "Nail Technician",
      color: "#F59E0B",
      services: ["service004", "service005"],
      locations: ["location002"],
      workingHours: {
        monday: { start: "10:00", end: "18:00", isWorking: true },
        tuesday: { start: "10:00", end: "18:00", isWorking: true },
        wednesday: { start: "10:00", end: "18:00", isWorking: false },
        thursday: { start: "10:00", end: "18:00", isWorking: true },
        friday: { start: "10:00", end: "18:00", isWorking: true },
        saturday: { start: "09:00", end: "17:00", isWorking: true },
        sunday: { start: "11:00", end: "16:00", isWorking: true },
      },
      buffer: 10,
      travelTime: 15,
      capacity: 10,
      isActive: true,
      createdAt: "2024-03-10T08:00:00Z",
      updatedAt: "2024-12-18T16:45:00Z",
    },
    {
      _id: "staff004",
      teamId: {
        name: "Software Team",
        ownerId: "owner002",
        _id: "team002",
      },
      userId: "user005",
      name: "David Thompson",
      email: "<EMAIL>",
      phone: "******-0126",
      role: "Barber",
      color: "#EF4444",
      services: ["service006"],
      locations: ["location001"],
      workingHours: {
        monday: { start: "09:00", end: "17:00", isWorking: true },
        tuesday: { start: "09:00", end: "17:00", isWorking: true },
        wednesday: { start: "09:00", end: "17:00", isWorking: true },
        thursday: { start: "09:00", end: "17:00", isWorking: true },
        friday: { start: "09:00", end: "17:00", isWorking: true },
        saturday: { start: "08:00", end: "18:00", isWorking: true },
        sunday: { start: "10:00", end: "16:00", isWorking: false },
      },
      buffer: 5,
      travelTime: 0,
      capacity: 12,
      isActive: false,
      createdAt: "2024-04-05T08:00:00Z",
      updatedAt: "2024-12-15T09:30:00Z",
    },
  ];

  static calendarEvents: CalendarEvent[] = [
    {
      id: "event001",
      title: "Hair Styling - Sarah Johnson",
      start: "2024-12-20T09:00:00Z",
      end: "2024-12-20T10:30:00Z",
      backgroundColor: "#3B82F6",
      extendedProps: {
        bookedBy: "client001",
        status: "confirmed",
        notes: "Regular customer, prefers natural look",
        service: {
          _id: "service001",
          name: "Hair Styling"
        },
        customerInfo: {
          name: "Emma Wilson",
          email: "<EMAIL>",
          phone: "******-0101"
        }
      }
    },
    {
      id: "event002",
      title: "Massage Therapy - Michael Chen",
      start: "2024-12-20T14:00:00Z",
      end: "2024-12-20T15:00:00Z",
      backgroundColor: "#10B981",
      extendedProps: {
        bookedBy: "client002",
        status: "completed",
        notes: "Deep tissue massage requested",
        service: {
          _id: "service003",
          name: "Massage Therapy"
        },
        customerInfo: {
          name: "John Davis",
          email: "<EMAIL>",
          phone: "******-0102"
        }
      }
    }
  ];

  static staffPerformance: StaffPerformance[] = [
    {
      _id: "perf001",
      staffId: "staff001",
      userId: "user002",
      period: {
        startDate: "2024-12-01",
        endDate: "2024-12-31",
        type: "monthly"
      },
      metrics: {
        totalBookings: 85,
        completedBookings: 78,
        cancelledBookings: 5,
        noShowBookings: 2,
        totalRevenue: 4250,
        averageRating: 4.7,
        totalReviews: 72,
        totalHours: 156,
        efficiency: 0.92,
        customerSatisfaction: 0.94,
        reliability: 0.88
      },
      serviceBreakdown: [
        {
          serviceId: "service001",
          serviceName: "Hair Styling",
          bookings: 45,
          revenue: 2250,
          averageRating: 4.8
        },
        {
          serviceId: "service002",
          serviceName: "Hair Coloring",
          bookings: 33,
          revenue: 2000,
          averageRating: 4.6
        }
      ],
      staff: {
        _id: "staff001",
        name: "Sarah Johnson",
        role: "Senior Stylist",
        color: "#3B82F6"
      }
    },
    {
      _id: "perf002",
      staffId: "staff002",
      userId: "user003",
      period: {
        startDate: "2024-12-01",
        endDate: "2024-12-31",
        type: "monthly"
      },
      metrics: {
        totalBookings: 62,
        completedBookings: 58,
        cancelledBookings: 3,
        noShowBookings: 1,
        totalRevenue: 3720,
        averageRating: 4.5,
        totalReviews: 55,
        totalHours: 124,
        efficiency: 0.89,
        customerSatisfaction: 0.91,
        reliability: 0.94
      },
      serviceBreakdown: [
        {
          serviceId: "service003",
          serviceName: "Massage Therapy",
          bookings: 62,
          revenue: 3720,
          averageRating: 4.5
        }
      ],
      staff: {
        _id: "staff002",
        name: "Michael Chen",
        role: "Massage Therapist",
        color: "#10B981"
      }
    }
  ];

  static staffRankings: StaffRanking[] = [
    {
      _id: "rank001",
      staffId: {
        _id: "staff001",
        name: "Sarah Johnson",
        role: "Senior Stylist",
        color: "#3B82F6"
      },
      metrics: {
        totalBookings: 85,
        completedBookings: 78,
        cancelledBookings: 5,
        noShowBookings: 2,
        totalRevenue: 4250,
        averageRating: 4.7,
        totalReviews: 72,
        totalHours: 156,
        efficiency: 0.92,
        customerSatisfaction: 0.94,
        reliability: 0.88
      },
      period: {
        startDate: "2024-12-01",
        endDate: "2024-12-31",
        type: "monthly"
      }
    },
    {
      _id: "rank002",
      staffId: {
        _id: "staff002",
        name: "Michael Chen",
        role: "Massage Therapist",
        color: "#10B981"
      },
      metrics: {
        totalBookings: 62,
        completedBookings: 58,
        cancelledBookings: 3,
        noShowBookings: 1,
        totalRevenue: 3720,
        averageRating: 4.5,
        totalReviews: 55,
        totalHours: 124,
        efficiency: 0.89,
        customerSatisfaction: 0.91,
        reliability: 0.94
      },
      period: {
        startDate: "2024-12-01",
        endDate: "2024-12-31",
        type: "monthly"
      }
    }
  ];

  static staffAnalytics: StaffAnalytics = {
    totalStaff: 4,
    averageEfficiency: 0.87,
    averageReliability: 0.89,
    averageSatisfaction: 0.91,
    totalRevenue: 12450,
    totalBookings: 234,
    topPerformers: [
      {
        staffId: "staff001",
        name: "Sarah Johnson",
        role: "Senior Stylist",
        color: "#3B82F6",
        efficiency: 0.92,
        reliability: 0.88,
        customerSatisfaction: 0.94,
        totalRevenue: 4250
      },
      {
        staffId: "staff002",
        name: "Michael Chen",
        role: "Massage Therapist",
        color: "#10B981",
        efficiency: 0.89,
        reliability: 0.94,
        customerSatisfaction: 0.91,
        totalRevenue: 3720
      },
      {
        staffId: "staff003",
        name: "Emily Rodriguez",
        role: "Nail Technician",
        color: "#F59E0B",
        efficiency: 0.85,
        reliability: 0.87,
        customerSatisfaction: 0.89,
        totalRevenue: 2980
      }
    ],
    performanceTrends: [
      {
        period: "2024-12-01",
        efficiency: 0.85,
        reliability: 0.87,
        satisfaction: 0.89,
        revenue: 3200
      },
      {
        period: "2024-12-08",
        efficiency: 0.87,
        reliability: 0.88,
        satisfaction: 0.90,
        revenue: 3450
      },
      {
        period: "2024-12-15",
        efficiency: 0.89,
        reliability: 0.91,
        satisfaction: 0.92,
        revenue: 3800
      },
      {
        period: "2024-12-22",
        efficiency: 0.87,
        reliability: 0.89,
        satisfaction: 0.91,
        revenue: 2000
      }
    ]
  };

  static staffRatings: StaffRating[] = [
    {
      _id: "rating001",
      staffId: "staff001",
      bookingId: "booking001",
      rating: 5,
      comment: "Sarah did an amazing job with my hair! Very professional and friendly.",
      customerName: "Emma Wilson",
      customerEmail: "<EMAIL>",
      createdAt: "2024-12-20T10:30:00Z"
    },
    {
      _id: "rating002",
      staffId: "staff001",
      bookingId: "booking002",
      rating: 4,
      comment: "Great service, will definitely come back!",
      customerName: "Lisa Chen",
      customerEmail: "<EMAIL>",
      createdAt: "2024-12-19T14:15:00Z"
    },
    {
      _id: "rating003",
      staffId: "staff002",
      bookingId: "booking003",
      rating: 5,
      comment: "Michael's massage was exactly what I needed. Very relaxing!",
      customerName: "John Davis",
      customerEmail: "<EMAIL>",
      createdAt: "2024-12-18T16:45:00Z"
    },
    {
      _id: "rating004",
      staffId: "staff001",
      rating: 4,
      comment: "Good service overall, minor wait time but worth it.",
      customerName: "Anonymous",
      createdAt: "2024-12-17T11:20:00Z"
    }
  ];

  static faked<T>(fakeResponse: T) {
    return { data: fakeResponse } as AxiosResponse<T>;
  }

  static async asyncFaked<T>(fakeResponse: T) {
    return new Promise<AxiosResponse<T>>((resolve) => {
      setTimeout(
        () => {
          resolve(this.faked(fakeResponse));
        },
        Math.floor(Math.random() * (4000 - 1500) + 1500),
      );
    });
  }
}
