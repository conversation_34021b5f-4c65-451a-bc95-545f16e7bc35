"use client";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import CircularLoader from "@/components/ui/circular-loader";
import { GetAppointmentReturnRate } from "@/src/services/analytics.service";
import { useQuery } from "@tanstack/react-query";
import { formatDate } from "date-fns";
import { TrendingUp } from "lucide-react";
import { useEffect, useMemo } from "react";
import { Pie, Pie<PERSON>hart } from "recharts";
import { useAnalyticsInterval } from "../analytics-interval-context";
import { inlineSwitch } from "@/lib/helpers";
import { GetServices } from "@/src/services/services.service";

type ReturnRateDataType =
  | { name: "New Clients"; value: number; fill: "#ed437d" }
  | { name: "Returning Clients"; value: number; fill: "#f48db0" };

const chartConfig = {
  "Returning Clients": {
    label: "Returning Clients",
  },
  "New Clients": {
    label: "New Clients",
  },
} satisfies ChartConfig;

export function ReturnRateChart() {
  const { interval } = useAnalyticsInterval();
  const { data: services = [] } = useQuery({
    queryKey: ["services"],
    queryFn: async () => (await GetServices()).data.Services,
  });
  const {
    data: returnRate,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["appointment-return-rate"],
    queryFn: async () =>
      (
        await GetAppointmentReturnRate({
          timeInterval: interval,
          serviceId: services[0]._id,
        })
      ).data,
    enabled: services && services.length > 0,
  });
  useEffect(() => (refetch(), undefined), [interval]);
  const returnRateData = useMemo(() => {
    const dataArr: ReturnRateDataType[] = [];
    if (returnRate) {
      const { totalClients, totalReturningClients } = returnRate;
      dataArr.push(
        {
          name: "New Clients",
          value: totalClients - totalReturningClients,
          fill: "#ed437d",
        },
        {
          name: "Returning Clients",
          value: totalReturningClients,
          fill: "#f48db0",
        }
      );
    }
    return dataArr;
  }, [returnRate]);

  if (isLoading) {
    return (
      <div className="w-full py-12 flex justify-center ">
        <CircularLoader />
      </div>
    );
  }

  return (
    <Card className="flex flex-col !rounded-3xl ">
      <CardHeader className="items-center pb-0">
        <CardTitle>Client Return Rate</CardTitle>
        <CardDescription>{formatDate(new Date(), "MMMM, yyy")}</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[180px]"
        >
          <PieChart>
            <Pie data={returnRateData} dataKey="value" />
            <ChartLegend
              content={<ChartLegendContent nameKey="value" />}
              className="-translate-y-2 flex-wrap gap-2"
            />
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col !items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none ">
          Trending up by {returnRate?.returnRate}{" "}
          {inlineSwitch(
            interval,
            ["today", "today."],
            ["weekly", "this week."],
            ["monthly", "this month."],
            ["yearly", "this year."]
          )}
          <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          Showing total client return rate for{" "}
          {inlineSwitch(
            interval,
            ["today", "today."],
            ["weekly", "this week."],
            ["monthly", "this month."],
            ["yearly", "this year."]
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
