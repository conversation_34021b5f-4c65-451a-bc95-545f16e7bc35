import { useMutation } from "@tanstack/react-query";
import { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";
import { TrackPageSession } from "../services/analytics.service";

/**
 * @dev tracks each session on each page to know where the user spends most time on.
 * @dev should be called inside a component
 * @dev this should be in one place -> root component -> App.tsx
 * @example
 * ```jsx
 * const { trackPageSession } = usePageSessionAnalytics();
 * trackPageSession()
 * ```
 */

export const usePageSessionAnalytics = () => {
  const { pathname } = useLocation();
  const startTimeRef = useRef<Date>();
  const lastPathRef = useRef<string>();

  const { mutateAsync } = useMutation({
    mutationFn: TrackPageSession,
    mutationKey: ["track-page-session"],
  });

  const trackPageSession = () => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useEffect(() => {
      // If this is the first page load
      if (!startTimeRef.current) {
        startTimeRef.current = new Date();
        lastPathRef.current = pathname;
        return;
      }

      // When pathname changes, send the previous page's session data
      const endTime = new Date();
      const sessionData = {
        pageUrl: lastPathRef.current!,
        startTime: startTimeRef.current!,
        endTime: endTime,
      };

      mutateAsync(sessionData);

      // Reset for new page
      startTimeRef.current = new Date();
      lastPathRef.current = pathname;

      // Cleanup function for when component unmounts
      return () => {
        const finalEndTime = new Date();
        mutateAsync({
          pageUrl: pathname,
          startTime: startTimeRef.current!,
          endTime: finalEndTime,
        });
      };
    }, [pathname]);
  };

  return { trackPageSession };
};
