import PendingOverlay from "@/components/ui/pending-overlay";
import { useAdminBlogApis } from "../use-admin";
import CircularLoader from "@/components/ui/circular-loader";
import { CircleMinus, EllipsisVertical, FontCursor } from "@gravity-ui/icons";
import Menu from "@/components/ui/menu";
import { Typography } from "@/components/typography";
import { Button } from "@/components/buttons";
import { cn } from "@/lib/utils";
import { useNavigate } from "react-router-dom";
import { Plus } from "lucide-react";
import { Card } from "@/components/ui/card";
import Chip from "@/components/ui/chip";
import { useState } from "react";
import { useToast } from "@/src/contexts/hooks/toast";

const Blogs = () => {
  const {
    tags: tagsList,
    deleteTagMutation,
    createTagMutation,
    blogs,
    isBlogsPending,
    deleteBlogMutation,
    // dont use isTagsFetching here because a `PendingOverlay` is already being used
  } = useAdminBlogApis();
  const [newTag, setNewTag] = useState("");
  const navigate = useNavigate();
  const showToast = useToast()
  if (isBlogsPending)
    return (
      <div className="w-full py-20 flex justify-center">
        <CircularLoader />
      </div>
    );
  return (
    <section className="shadow-none pb-20 w-full max-w-screen-2xl mx-auto flex flex-col gap-y-6 ">
      <PendingOverlay
        isPending={
          createTagMutation.isPending ||
          deleteTagMutation.isPending ||
          deleteBlogMutation.isPending
        }
      />
      <section className="flex flex-col gap-y-6 lg:flex-row gap-x-3">
        <div className="flex flex-col md:flex-col-reverse gap-y-3">
          {blogs.length > 0 && (
            <Button
              onTap={() => navigate("/blog/create")}
              className="font-medium text-sm flex items-center justify-center gap-2"
            >
              Create Blog
            </Button>
          )}
          <Card className="py-6 mt-0 px-3 w-full max-w-[32rem] ">
            <p className="font-medium mb-1">Availabe Tags</p>
            <p className="font-medium text-paragraph/40 text-sm mb-2">
              Tags are collection names which each blog article may belong to. Each has a maximum length of two words.
            </p>
            <div className="flex items-center gap-2 bg-white rounded-xl max-h-44 overflow-y-auto thin-scrollbar flex-wrap">
              {tagsList.length ? tagsList.map((tag) => (
                <Chip
                  key={tag._id}
                  label={tag.name}
                  onDelete={() => deleteTagMutation.mutateAsync(tag._id!)}
                  className="line-clamp-1"
                />
              ))
                : <span className="text-sm font-normal" >No tags found</span>
              }
            </div>

            <div
              className={
                "bg-[#DFE1DB]/30 shadow-sm mt-3 pl-2 pr-1.5 py-1.5 w-fit flex items-center gap-x-2 rounded-full text-sm font-medium"
              }
            >
              <input
                placeholder="Create tag"
                className="!rounded-full text-sm text-paragraph placeholder:text-paragraph/40 bg-transparent outline-none pl-1"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
              />
              <Button
                type="button"
                className="rounded-full !p-1 bg-[#DFE1DB]/60 "
                variant="icon"
                onTap={() => {
                  if (newTag.length === 0) return
                  // check if tag is more than two words
                  if (newTag.trim().split(' ').length > 2)
                    return showToast('info', 'Tags have a two word max length', {
                      description: "Please shorten your tag"
                    })
                  createTagMutation.mutateAsync({
                    name: newTag,
                  });
                  setNewTag("");
                }}
              >
                <Plus className="text-gray-500 " />
              </Button>
            </div>
          </Card>
        </div>

        {!isBlogsPending && blogs.length === 0 ? (
          <div className="w-full flex flex-col items-center justify-center text-center">
            <div className="">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Plus className="w-8 h-8 text-gray-400" />
              </div>
            </div>

            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No blogs found
            </h3>

            <p className="text-gray-500 mb-6 max-w-sm">
              Get started by creating your first blog.
            </p>

            <Button
              onTap={() => navigate("/blog/create")}
              className="font-medium px-6 text-sm flex items-center gap-2"
            >
              <Plus className="w-5 h-5" />
              Create Blog
            </Button>
          </div>
        ) : (
          <section className="pb-4 w-full grid grid-cols-1 md:grid-cols-2 gap-4 gap-y-8 lg:grid-cols-2">
            {blogs.map((blog, index) => (
              <div key={index} className="flex flex-col gap-y-2 md:bg-white">
                <img
                  src={blog.imageUrl}
                  alt="Blog Image"
                  className="w-full aspect-[5/3]  object-cover bg-sky-100"
                />
                <div className="flex items-center justify-between md:px-2.5 md:pb-3">
                  <div className="flex flex-col gap-y-0.5 ">
                    <Typography className="!font-Bricolage_Grotesque text-paragraph line-clamp-1">
                      {blog.title}
                    </Typography>
                    <Typography className="text-paragraph/50 text-sm">
                      {blog.authorName}
                    </Typography>
                  </div>
                  <Menu
                    transformOrigin={
                      innerWidth >= 480 ? "top-right" : "bottom-right"
                    }
                  >
                    <Menu.Trigger>
                      <Button
                        className="bg-transparent text-paragraph p-0"
                        variant="icon"
                      >
                        <EllipsisVertical width={22} height={22} />
                      </Button>
                    </Menu.Trigger>
                    <Menu.Content
                      className={cn(
                        "h-fit min-w-[200px] !min-h-0 right-0 whitespace-nowrap",
                        {
                          "-top-[380%] right-3 ": innerWidth <= 480,
                        }
                      )}
                    >
                      <Menu.Item
                        className="flex items-center gap-x-4 pr-5"
                        onClick={async () => {
                          navigate(`/blog/edit/${blog.uniqueKey}`);
                        }}
                      >
                        Edit <FontCursor />
                      </Menu.Item>
                      <Menu.Item
                        className="flex items-center gap-x-4 pr-5"
                        onClick={() =>
                          deleteBlogMutation.mutateAsync(blog.uniqueKey!)
                        }
                      >
                        Delete <CircleMinus />
                      </Menu.Item>
                    </Menu.Content>
                  </Menu>
                </div>
              </div>
            ))}
          </section>
        )}
      </section>
    </section>
  );
};

export default Blogs;
