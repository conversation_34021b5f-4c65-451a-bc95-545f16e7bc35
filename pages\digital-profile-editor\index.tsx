import CircularLoader from "@/components/ui/circular-loader";
import { and, isEmptyObject, noop } from "@/lib/helpers";
import ProfileHeader from "./components/profile-header";
import { useFormik } from "formik";
import { DigitalProfileContext, useDigitalProfile } from "./hooks/use-digital-profile";
import { DigitalProfileType, IWebsiteLink } from "./types";
import PreviewSection from "./components/preview-section";
import Settings from "./components/settings";
import SocialMediaSettings from "./components/social-media";
import LinkEditor from "./components/link-editor";

const DigitalProfileEditor = () => {
  const { digitalProfileQuery, actions } = useDigitalProfile()
  const digitalProfileFormik = useFormik({
    initialValues: digitalProfileQuery.data || {} as DigitalProfileType,
    enableReinitialize: true,
    onSubmit: noop
  })
  function setValue<K extends keyof DigitalProfileType>(prop: K, value: DigitalProfileType[K]) {
    digitalProfileFormik.setFieldValue(prop, value)
  }
  const reorderLinks = (links: IWebsiteLink[], idOrder: string[]) => {
    const orderMap = new Map(idOrder.map((id, idx) => [id, idx]))
    return links.sort((a, b) => {
      const idxA = orderMap.get(a._id) ?? Infinity;
      const idxB = orderMap.get(b._id) ?? Infinity
      return idxA - idxB
    })
  }
  if (digitalProfileQuery.isPending || isEmptyObject(digitalProfileFormik.values))
    return (
      <div className="w-full flex items-center justify-center py-40">
        <CircularLoader />
      </div>
    );
  return (
    <DigitalProfileContext.Provider
      value={{
        profile: digitalProfileFormik.values,
        basicDetailsSettings: {
          update(payload) {
            setValue('details', payload)
            actions.updateProfile({ details: payload })
          }
        },
        imageSettings: {
          uploadAnyImage(image) {
            const data = new FormData() as TypedFormData<{ image: File }>
            data.set('image', image)
            return actions.uploadAnyImage(data)
          },
          uploadProfileImage(image) {
           const data = new FormData() as TypedFormData<{ image: File }>
            data.set('image', image)
            actions.uploadProfileImage(data)
          },
        },
        themeSettings: {
          update(theme) {
            digitalProfileFormik.setValues({
              ...digitalProfileFormik.values,
              theme: theme
            })
            actions.updateProfile({ theme: theme })
          },
        },
        linkSettings: {
          add(link) {
            setValue('websiteLinks', [
              {
                ...link as any,
                _id: `link_00${digitalProfileFormik.values.websiteLinks?.length + 1}`
              },
              ...digitalProfileFormik.values.websiteLinks
            ])
            actions.addLink(link)
          },
          update(linkId, link) {
            const links = digitalProfileFormik.values.websiteLinks;
            const toUpdate = links.find(l => l._id === linkId)
            if (!toUpdate) return;
            const updatedLink = { ...toUpdate, ...link };
            setValue('websiteLinks',
              links.map(l => (l._id === updatedLink._id ? updatedLink : l))
            )
            actions.updateLink(linkId, link)
          },
          delete(linkId) {
            setValue('websiteLinks',
              digitalProfileFormik.values.websiteLinks.filter(l => l._id !== linkId)
            )
            actions.deleteLink(linkId)
          },
          reorder(order) {
            const newOrder = reorderLinks(digitalProfileFormik.values.websiteLinks, order)
            setValue('websiteLinks',
              newOrder
            )
            actions.reorderLinks(order)
          },
        },
        socialsSettings: {
          add(payload) {
            const { socialMedia = [] } = digitalProfileFormik.values
            setValue('socialMedia', [...socialMedia, payload])
            actions.addSocial(payload)
          },
          update(index, payload) {
            const { socialMedia = [] } = digitalProfileFormik.values
            setValue('socialMedia',
              socialMedia.map((s, idx) => (
                index === idx ? payload : s
              ))
            )
            actions.updateSocial(index, payload)
          },
          delete(index, url) {
            const { socialMedia = [] } = digitalProfileFormik.values
            setValue('socialMedia', socialMedia.filter((s, idx) => !and([index === idx, s.url === url])))
            actions.deleteSocial(index)
          },
        },
        settingSettings: {
          update(payload) {
            setValue('settings', payload)
            actions.updateProfile({ settings: payload })
          },
        }
      }}
    >
      <section className="h-full w-full grid grid-cols-1 items-start relative overflow-y-hidden bxl:grid-cols-2 bxl:overflow-y-hidden bxl:px-3 ">
        <section className="w-full overflow-y-auto overflow-x-hidden no-scrollbar pt-0 pr-0 space-y-5 relative bxl:min-h-fit h-fit px-2 bxl:pr-3 bxl:border-r-2 bxl:max-h-[86.6vh] pb-10 bxl:border-gray-100 ">
          <ProfileHeader />
          <SocialMediaSettings />
          <LinkEditor />
          <Settings />
        </section>
        <PreviewSection />
      </section>
    </DigitalProfileContext.Provider>
  );
};

export default DigitalProfileEditor;
