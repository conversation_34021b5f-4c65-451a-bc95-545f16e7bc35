import ConversionAnalytics from "./components/conversion-chart.tsx";
import PageViewByUrl from "./components/url-pageview-analytics.tsx";
import SessionDurationBarChart from "./components/session-duration-chart.tsx";
import InteractionAnalytics from "./components/interaction-analytics.tsx";

/**
 * @dev put a call here to fetch page views, unique visitors and avg time on page
 */
export default function Analytics() {
  return (
    <section className="shadow-none pb-20 w-full max-w-screen-2xl space-y-4 mx-auto ">
      <div className="w-full grid grid-cols-1 gap-y-6 md:grid-cols-3 md:gap-x-4 ">
        <div className="rounded-3xl px-6 py-6 space-y-4 bg-subtle-gray  ">
          <h1 className="text-dark-gray text-lg">Total Page Views</h1>
          <div>
            <div className="text-3xl font-bold">3,050</div>
            <p className="text-sm text-gray-400">Last 7 days</p>
          </div>
        </div>
        <div className="rounded-3xl px-6 py-6 space-y-4 bg-subtle-gray  ">
          <h1 className="text-subtext text-lg">Unique Visitors</h1>
          <div>
            <div className="text-3xl font-bold">2,000</div>
            <p className="text-sm text-gray-400">Distinct Users</p>
          </div>
        </div>
        <div className="rounded-3xl px-6 py-6 space-y-4 bg-subtle-gray  ">
          <h1 className="text-subtext text-lg">Avg. Time on Page</h1>
          <div>
            <div className="text-3xl font-bold">72s</div>
            <p className="text-sm text-gray-400">Per session</p>
          </div>
        </div>
      </div>
      <div className="w-full grid grid-cols-1 gap-y-6 md:grid-cols-[60%_40%] md:gap-x-4 ">
        <PageViewByUrl />
        <SessionDurationBarChart />
      </div>
      <div className="w-full grid grid-cols-1 gap-y-6 md:grid-cols-[40%_60%] md:gap-x-4 ">
        <InteractionAnalytics />
        <ConversionAnalytics />
      </div>
    </section>
  );
}
