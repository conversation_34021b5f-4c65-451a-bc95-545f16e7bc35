"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import CircularLoader from "@/components/ui/circular-loader";
import { getCurrencySign } from "@/lib/helpers";
import useAuthStore from "@/pages/auth/components/auth-store";
import {
  GetCurrentMonthAppointmentRevenue,
  GetTodayAppointmentRevenue,
} from "@/src/services/analytics.service";
import { useQueries } from "@tanstack/react-query";
import { formatDate } from "date-fns";
import { useMemo } from "react";
import { Bar, BarChart, CartesianGrid, LabelList, XAxis } from "recharts";

const chartConfig = {
  desktop: {
    label: "Revenue",
    color: "hsl(var(--chart-1))",
  },
} satisfies ChartConfig;

export function RevenueChart() {
  const revenueResults = useQueries({
    queries: [
      {
        queryKey: ["today-appointment-revenue"],
        queryFn: async () => (await GetTodayAppointmentRevenue()).data,
      },
      {
        queryKey: ["current-month-appointment-revenue"],
        queryFn: async () => (await GetCurrentMonthAppointmentRevenue()).data,
      },
    ],
  });
  const { initialState: { userSettings } } = useAuthStore()
  const revenueData = useMemo(() => {
    if (revenueResults.some((result) => result.isError)) {
      return [];
    }
    return [
      {
        name: "Today",
        revenue:
          revenueResults[0].data?.totalRevenue || 0
      },
      {
        name: "This Month",
        revenue:
          (revenueResults[1].data?.totalRevenue || 0)

      },
    ];
  }, [revenueResults]);
  if (revenueResults.some((result) => result.isLoading)) {
    return (
      <div className="w-full py-12 flex justify-center ">
        <CircularLoader />
      </div>
    );
  }
  return (
    <Card className="!rounded-3xl ">
      <CardHeader>
        <CardTitle>Revenue Overview</CardTitle>
        <CardDescription className="font-medium">
          {formatDate(new Date(), "MMMM, yyy")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <BarChart
            accessibilityLayer
            data={revenueData}
            margin={{
              top: 20,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="name"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent formatter={(value, name) =>
                <span className="w-full flex items-center justify-between" >
                  <span className="text-primary-600 font-medium" >{name}</span>
                  {getCurrencySign(userSettings?.billingSettings?.preferredCurrency || 'usd').concat(String(value))}`
                </span>
              } hideLabel />
              }
            />
            <Bar dataKey="revenue" fill="var(--color-desktop)" radius={12}>
              <LabelList
                position="top"
                offset={12}
                className="fill- "
                fontSize={12}
              />
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col !items-start gap-2 text-sm">
        <div className="leading-none text-muted-foreground">
          Showing total revenue accrued for today and this month
        </div>
      </CardFooter>
    </Card>
  );
}
