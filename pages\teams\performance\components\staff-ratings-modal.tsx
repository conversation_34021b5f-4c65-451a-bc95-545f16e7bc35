import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import Modal from "@/components/ui/modal";
import { Typography } from "@/components/typography";
import { Button } from "@/components/buttons";
import CircularLoader from "@/components/ui/circular-loader";
import { StarIcon, X } from "lucide-react";
import { GetStaffRatings } from "@/src/services/staff-performance.service";
import { formatDate } from "date-fns";

interface StaffRatingsModalProps {
  open: boolean;
  onClose: () => void;
  staffId: string;
  staffName: string;
  staffColor: string;
}

const StaffRatingsModal: React.FC<StaffRatingsModalProps> = ({
  open,
  onClose,
  staffId,
  staffName,
}) => {
  const [page, setPage] = useState(1);
  const limit = 10;

  const ratingsQuery = useQuery({
    queryKey: ["staff-ratings", staffId, page],
    queryFn: async () => (await GetStaffRatings(staffId, { page, limit })).data,
    enabled: open && !!staffId
  });

  const ratings = ratingsQuery.data?.ratings || [];
  const summary = ratingsQuery.data?.summary;
  const pagination = ratingsQuery.data?.pagination;

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIcon
        key={i}
        className={`w-4 h-4 ${
          i < rating ? 'text-yellow-500 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Modal.Body className="min-w-[600px] max-w-2xl max-h-[80vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            {/*<Avatar
              name={staffName}
              size="md"
              backgroundColor={staffColor}
            />*/}
            <div>
              <Typography variant="h3" className="font-semibold">
                {staffName} - Ratings
              </Typography>
              {summary && (
                <Typography className="text-gray-600">
                  {summary.averageRating.toFixed(1)}/5 • {summary.totalRatings} reviews
                </Typography>
              )}
            </div>
          </div>
          <Button variant="icon" onTap={onClose} className="p-1">
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Summary */}
        {summary && (
          <div className="p-6 border-b bg-gray-50">
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              {summary.ratingDistribution.map((dist) => (
                <div key={dist.rating} className="text-center">
                  <div className="flex items-center justify-center gap-1 mb-1">
                    <Typography className="text-sm font-medium">
                      {dist.rating}
                    </Typography>
                    <StarIcon className="w-3 h-3 text-yellow-500" />
                  </div>
                  <Typography className="text-2xl font-bold">
                    {dist.count}
                  </Typography>
                  <Typography className="text-xs text-gray-600">
                    {dist.percentage.toFixed(1)}%
                  </Typography>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Ratings List */}
        <div className="flex-1 overflow-y-auto p-6">
          {ratingsQuery.isLoading ? (
            <div className="flex justify-center py-8">
              <CircularLoader />
            </div>
          ) : ratings.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No ratings available
            </div>
          ) : (
            <div className="space-y-4">
              {ratings.map((rating) => (
                <div key={rating._id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <div className="flex">{renderStars(rating.rating)}</div>
                        <Typography className="text-sm text-gray-600">
                          {formatDate(new Date(rating.createdAt), 'MMM d, yyyy')}
                        </Typography>
                      </div>
                      {rating.customerName && (
                        <Typography className="text-sm font-medium">
                          {rating.customerName}
                        </Typography>
                      )}
                    </div>
                  </div>
                  {rating.comment && (
                    <Typography className="text-gray-700 mt-2">
                      "{rating.comment}"
                    </Typography>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Pagination */}
        {pagination && pagination.pages > 1 && (
          <div className="flex items-center justify-between p-6 border-t">
            <Typography className="text-sm text-gray-600">
              Page {pagination.page} of {pagination.pages} • {pagination.total} total
            </Typography>
            <div className="flex gap-2">
              <Button
                variant="outline"
                onTap={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                onTap={() => setPage(p => Math.min(pagination.pages, p + 1))}
                disabled={page === pagination.pages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </Modal.Body>
    </Modal>
  );
};

export default StaffRatingsModal;
