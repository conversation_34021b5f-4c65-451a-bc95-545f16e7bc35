import ClientToEngageInformation from "./reengagement-client-info";
import { ClientToReEngage } from "@/src/services/analytics.service";

interface Props {
  clients: ClientToReEngage[];
  onClickClient: (clientId: ClientToReEngage) => void;
}

const tableColumns = ["Name", "Appointment", "Average Return Interval", "Email", "Phone", 'Actions'] as const;
const ClientListTable = (props: Props) => {
  const { clients, onClickClient } = props;
  return (
    <div className="w-fit flex flex-col ">
      {/* columns */}
      <div className="flex-grow rounded-t-lg bg-subtle-gray grid grid-cols-[repeat(5,160px)_80px] md:grid-cols-[repeat(5,200px)_80px]">
        {tableColumns.map((key, index) => (
          <div
            key={index}
            className="px-1 pl-4 pb-2.5 text-sm "
          >
            <div className="w-full pt-2.5 rounded-lg whitespace-nowrap ">{key}</div>
          </div>
        ))}
      </div>
      {clients?.map((client, index) => (
        <ClientToEngageInformation
          key={index}
          client={client}
          index={index}
          onClick={onClickClient}
        />
      ))}
    </div>
  );
};

export default ClientListTable;
