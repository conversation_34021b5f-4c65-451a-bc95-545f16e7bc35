import CircularLoader from "@/components/ui/circular-loader";
import PendingOverlay from "@/components/ui/pending-overlay";
import { useToast } from "@/src/contexts/hooks/toast";
import { SubscriptionPlan } from "@/src/interfaces/subscription-plan";
import { UpgradeSubscription } from "@/src/services/auth.service";
import { GetCurrentUserSubscriptionPlan } from "@/src/services/payment.service";
import { loadStripe } from "@stripe/stripe-js";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import PaymentMethods from "./components/payment-methods";
import PricingPlans from "./components/plans";
import ConfirmPlanPaymentModal from "./confirm-plan-change-payment-modal";
import { useSearchParams } from "react-router-dom";
import { capitalizeFirstWord } from "@/lib/helpers";
import { useModalsBuilder } from "@/lib/modals-builder";
import BillingSettings from "./components/billing-settings";

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

const handlePlanPayment = async (variables: {
	clientSecret: string;
	stripePaymentMethodId: string;
	showToast: ReturnType<typeof useToast>;
}) => {
	const stripe = await stripePromise;
	if (!stripe) return;
	try {
		const { error, paymentIntent } = await stripe.confirmCardPayment(
			variables.clientSecret,
			{
				payment_method: variables.stripePaymentMethodId,
			},
		);
		error &&
			variables.showToast("error", "Payment failed", {
				description:
					error.message || "An error occurred while confirming the payment",
			});
		if (paymentIntent?.status === "succeeded")
			variables.showToast("success", "Payment completed successfully");
	} catch (error) {}
};

// change plan type to show less data
const Billing = () => {
	const { data: userCurrentPlan, isFetching } = useQuery({
		queryKey: ["user-current-plan"],
		queryFn: async () => (await GetCurrentUserSubscriptionPlan()).data.plan,
		refetchOnWindowFocus: false,
	});
	const upgradePlanMutation = useMutation({
		mutationFn: UpgradeSubscription,
	});
	const handlePaymentMutation = useMutation({
		mutationFn: handlePlanPayment,
	});
	const { modals, modalFunctions } = useModalsBuilder({
		confirmPlanPayment: {
			open: false,
			plan: null as SubscriptionPlan | null,
		},
		upgradePlan: {
			open: false,
			plan: null as SubscriptionPlan | null,
		},
	} as const);
	const [searchParams] = useSearchParams();
	const showToast = useToast();
	useEffect(() => {
		const upgradeTo = searchParams.get("upgradeTo");
		if (upgradeTo)
			showToast("info", `Plan Upgrade Required`, {
				description: `You need to upgrade to ${capitalizeFirstWord(
					upgradeTo,
				)} plan to access this feature.`,
				duration: 7000,
			});
	}, [searchParams.get("upgradeTo")]);
	useEffect(() => {
		if (!isFetching) {
			if (!userCurrentPlan) showToast("info", "Current Plan not found");
		}
	}, [userCurrentPlan, isFetching]);
	return (
		<section className="overflow-auto no-scrollbar py-0 px-2">
			<div className="space-y-4 max-w-2xl ">
				{isFetching ? (
					<div className="mx-auto py-20">
						<CircularLoader className="" />
					</div>
				) : (
					<PricingPlans
						onPlanChange={(plan) => {
							upgradePlanMutation.mutateAsync(plan._id, {
								onSuccess() {
									modalFunctions.openModal("confirmPlanPayment", {
										plan: plan,
									});
								},
								onError: (error) => {
									showToast(
										"error",
										(error as unknown as ApiError).response?.data.error ||
											"Unknown error occured",
									);
								},
							});
						}}
						currentPlanId={userCurrentPlan?._id || ""}
					/>
				)}
				<PaymentMethods />
				<BillingSettings />
			</div>
			{modals.confirmPlanPayment.plan && (
				<ConfirmPlanPaymentModal
					open={modals.confirmPlanPayment.open}
					plan={modals.confirmPlanPayment.plan}
					onClose={() => modalFunctions.closeModal("confirmPlanPayment")}
					onConfirm={(selectedPaymentMethod) => {
						handlePaymentMutation.mutateAsync(
							{
								clientSecret: upgradePlanMutation.data?.data.clientSecret!,
								showToast,
								stripePaymentMethodId: selectedPaymentMethod.stripePaymethodId,
							},
							{
								onSuccess() {
									modalFunctions.closeModal("confirmPlanPayment");
								},
								onError() {
									modalFunctions.closeModal("confirmPlanPayment");
								},
							},
						);
					}}
				/>
			)}
			<PendingOverlay
				isPending={
					upgradePlanMutation.isPending || handlePaymentMutation.isPending
				}
			/>
		</section>
	);
};

export default Billing;
