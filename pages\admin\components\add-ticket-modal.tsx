import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import { isUnwantedInArray } from "@/lib/helpers";
import { AdminNS } from "@/src/services/admin.service";
import { X } from "lucide-react";

type Props = {
  open: boolean;
  onClose: () => void;
  onSubmit: () => void;
  onChange: (
    name: keyof AdminNS.CreateSupportTicketPayload,
    value: string
  ) => void;
  values: AdminNS.CreateSupportTicketPayload | null;
};

const fields = [
  {
    label: "User Id",
    name: "userId",
    type: "text",
  },
  {
    label: "Title",
    name: "title",
    type: "text",
  },
  {
    label: "IP",
    name: "ip",
    type: "text",
  },
] as const;

const FieldTypeMap = {
  text: Input.Text,
};

const AddTicketModal = (props: Props) => {
  return (
    <Modal open={props.open} onClose={props.onClose}>
      <Modal.Body
        className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Create ticket
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="w-full grid gap-y-3">
          {fields.map((field) => {
            const InputType = FieldTypeMap[field.type];
            return (
              <InputType
                {...field}
                required
                onChange={({ target: { value } }) => {
                  props.onChange(field.name, value);
                }}
                value={props.values?.[field.name]}
                key={field.label}
                className="w-full"
              />
            );
          })}
          <Input.TextArea
            label="Description"
            onChange={({ target: { value } }) => {
              props.onChange("description", value);
            }}
            required
            name="description"
            value={props.values?.["description"]}
            className="w-full"
          />
          <SearchableDropdown
            value={{
              value: props.values?.["priority"] || "",
              label: props.values?.["priority"] || "",
            }}
            options={[
              { value: "low", label: "Low" },
              { value: "medium", label: "Medium" },
              { value: "high", label: "High" },
            ]}
            fullWidth
            onChange={(option) => {
              props.onChange("priority", option?.value || "");
            }}
          />
        </div>
        <div className="w-full flex justify-between items-center mt-6">
          <Button onTap={props.onClose} className="w-full" variant="ghost">
            Cancel
          </Button>
          <Button
            disabled={isUnwantedInArray(Object.values(props.values || {}))}
            onTap={props.onSubmit}
            className="w-full"
          >
            Create
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default AddTicketModal;
