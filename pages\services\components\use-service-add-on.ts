import { useToast } from "@/src/contexts/hooks/toast";
import { AddonsApi } from "@/src/services/services.service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useServiceAddons = () => {
  const queryclient = useQueryClient();
  const showToast = useToast();
  const { data: serviceAddons = [], isFetching } = useQuery({
    queryKey: ["service-add-ons"],
    queryFn: async () => {
      const res = await AddonsApi.GetAll();
      return res.data.serviceOptions
    },
  })
  const updateMutation = useMutation({
    mutationFn: AddonsApi.Update,
    onSuccess() {
      queryclient.invalidateQueries({
        queryKey: ["service-add-ons"],
      });
      showToast("info", "Add on updated successfully");
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  const deleteMutation = useMutation({
    mutationFn: AddonsApi.Delete,
    onSuccess() {
      queryclient.invalidateQueries({
        queryKey: ["service-add-ons"],
      });
      showToast("info", "Add on deleted successfully");
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  const createMutation = useMutation({
    mutationFn: AddonsApi.Create,
    onSuccess() {
      queryclient.invalidateQueries({
        queryKey: ["service-add-ons"],
      });
      showToast("info", "Add on Created successfully");
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });

  return {
    create: createMutation,
    delete: deleteMutation,
    serviceAddons: serviceAddons,
    update: updateMutation,
    isFetching: isFetching
  }
}

