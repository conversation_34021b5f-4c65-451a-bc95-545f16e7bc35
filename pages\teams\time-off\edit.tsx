import { useNavigate, useSearch<PERSON>ara<PERSON>, useParams } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useQuery } from "@tanstack/react-query";
import { ActionButtonGroup, Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import Toggle from "@/components/ui/toggle-2";
import DatePicker from "@/components/ui/calendar";
import PendingOverlay from "@/components/ui/pending-overlay";
import CircularLoader from "@/components/ui/circular-loader";
import { ArrowLeft02Icon } from "hugeicons-react";
import { useStaff } from "../hooks/use-staff";
import { useTimeOff } from "../hooks/use-time-off";
import { useToast } from "~/contexts/hooks/toast";
import { UpdatePTORequest, PTOType } from "@/src/interfaces/time-off";
import * as TimeOffService from "@/src/services/time-off.service";
import RecurringPatternForm from "./components/recurring-pattern-form";
import { noop, pickByType } from "@/lib/helpers";

const PTO_TYPES: { value: PTOType; label: string }[] = [
  { value: "vacation", label: "Vacation" },
  { value: "sick", label: "Sick Leave" },
  { value: "personal", label: "Personal" },
  { value: "meeting", label: "Meeting" },
  { value: "training", label: "Training" },
  { value: "other", label: "Other" },
];

const EditTimeOff: React.FC = () => {
  const { ptoId } = useParams<{ ptoId: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const showToast = useToast();
  const teamId = searchParams.get("teamId") || "";

  const { staffQuery } = useStaff(teamId);
  const { updateTimeOffMutation } = useTimeOff();

  const ptoQuery = useQuery({
    queryKey: ["timeOff", ptoId],
    queryFn: async () => {
      if (!ptoId) throw new Error("PTO ID is required");
      return (
        await TimeOffService.GetAllPTORequests({ staffId: undefined })
      ).data.ptos.find((p) => p._id === ptoId);
    },
    enabled: !!ptoId,
  });

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      startDate: ptoQuery.data?.startDate || "",
      endDate: ptoQuery.data?.endDate || "",
      startTime: ptoQuery.data?.startTime || "",
      endTime: ptoQuery.data?.endTime || "",
      type: ptoQuery.data?.type || ("vacation" as PTOType),
      description: ptoQuery.data?.description || "",
      isRecurring: ptoQuery.data?.isRecurring || false,
      recurringPattern: ptoQuery.data?.recurringPattern || {
        frequency: "weekly" as const,
        interval: 1,
        daysOfWeek: [],
        endDate: "",
      },
    },
    validationSchema: Yup.object({
      startDate: Yup.string().required("Start date is required"),
      endDate: Yup.string().required("End date is required"),
      type: Yup.string().required("Type is required"),
      description: Yup.string().required("Description is required"),
    }),
    validateOnMount: true,
    onSubmit: noop,
  });

  const updateTimeOffHandler = async () => {
    if (!ptoId) return;

    formik.validateForm().then((val) => {
      const errors = Object.values(pickByType(val, "string"));
      if (errors.length === 0) {
        const { values } = formik;
        const payload: UpdatePTORequest = {
          startDate: values.startDate,
          endDate: values.endDate,
          startTime: values.startTime || undefined,
          endTime: values.endTime || undefined,
          type: values.type,
          description: values.description,
          isRecurring: values.isRecurring,
          recurringPattern: values.isRecurring
            ? values.recurringPattern
            : undefined,
        };

        updateTimeOffMutation.mutateAsync(
          {
            ptoId,
            payload,
          },
          {
            onSuccess() {
              navigate(`/teams/time-off?teamId=${teamId}`);
            },
          },
        );
      } else {
        showToast("error", "Invalid Field", {
          description: errors[0],
        });
      }
    });
  };

  if (ptoQuery.isLoading || staffQuery.isLoading) {
    return (
      <div className="flex w-full justify-center py-10">
        <CircularLoader />
      </div>
    );
  }

  if (!ptoQuery.data) {
    return (
      <div className="text-center py-20">
        <Typography className="text-red-500">
          Time-off request not found
        </Typography>
        <Button
          onTap={() => navigate(`/teams/time-off?teamId=${teamId || ""}`)}
          className="mt-4"
        >
          Go Back
        </Button>
      </div>
    );
  }

  const staff = staffQuery.data?.find((s) => s._id === ptoQuery.data?.staffId);

  return (
    <div className="w-full max-w-2xl mx-auto space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="icon"
          className="p-1.5"
          onTap={() => navigate(`/teams/time-off?teamId=${teamId}`)}
        >
          <ArrowLeft02Icon size={20} />
        </Button>
        <div>
          <Typography variant="h3" className="font-semibold">
            Edit Time-Off Request
          </Typography>
          <Typography variant="p" className="text-gray-600">
            Update time-off request for {staff?.name}
          </Typography>
        </div>
      </div>

      <div className="space-y-6 bg-white p-6 rounded-lg border">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DatePicker.FormControl
            label="Start Date"
            name="startDate"
            value={formik.values.startDate as any}
            onChange={(value) => formik.setFieldValue("startDate", value)}
          />
          <DatePicker.FormControl
            label="End Date"
            name="endDate"
            value={formik.values.endDate as any}
            onChange={(value) => formik.setFieldValue("endDate", value)}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DatePicker.Time
            label="Start Time (Optional)"
            name="startTime"
            value={formik.values.startTime}
            onChange={(value) => formik.setFieldValue("startTime", value)}
          />
          <DatePicker.Time
            label="End Time (Optional)"
            name="endTime"
            value={formik.values.endTime}
            onChange={(value) => formik.setFieldValue("endTime", value)}
          />
        </div>

        <SearchableDropdown
          label="Type"
          name="type"
          value={formik.values.type}
          onChange={(option) => {
            if (!option) return;
            formik.setFieldValue("type", option.value);
          }}
          options={PTO_TYPES}
        />

        <Input.TextArea
          label="Description"
          name="description"
          value={formik.values.description}
          onChange={formik.handleChange}
          placeholder="Describe the reason for time off"
        />

        <div className="w-full flex items-center justify-between ">
          <Typography className=" text-paragraph">Recurring Request</Typography>
          <Toggle
            checked={formik.values.isRecurring}
            onChange={(checked) => formik.setFieldValue("isRecurring", checked)}
          />
        </div>

        {formik.values.isRecurring && (
          <RecurringPatternForm
            pattern={formik.values.recurringPattern}
            onChange={(pattern) =>
              formik.setFieldValue("recurringPattern", pattern)
            }
          />
        )}

      </div>

      <ActionButtonGroup
        cancel={{
          title: "Cancel",
          disabled: updateTimeOffMutation.isPending,
          onTap: () => navigate(`/teams/time-off?teamId=${teamId}`),
        }}
        next={{
          loading: updateTimeOffMutation.isPending,
          hasChanges: formik.isValid,
          disabled: updateTimeOffMutation.isPending || !formik.isValid,
          title: innerWidth <= 480 ? "Save" : "Update Request",
          onTap: updateTimeOffHandler,
        }}
      />
      <PendingOverlay isPending={updateTimeOffMutation.isPending} />
    </div>
  );
};

export default EditTimeOff;
