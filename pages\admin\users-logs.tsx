import { pick } from "@/lib/helpers";
import { useAdminUserLogsApis } from "./use-admin";
import { ExternalLink } from "lucide-react";
import { AdminNS } from "@/src/services/admin.service";
import BasicTooltip from "@/components/ui/basic-tooltip";
import { motion } from "framer-motion";
import CircularLoader from "@/components/ui/circular-loader";
import { Link } from "react-router-dom";

const properties: Array<keyof AdminNS.UserWithLogs> = [
  "_id",
  "name",
  "email",
  "logCount",
] as const;

const UsersWithLogs = () => {
  const { usersWithLogs, isUsersWithLogsFetching } = useAdminUserLogsApis();
  return (
    <section className=" md:max-w-[760px] overflow-x-auto no-scrollbar ">
      <div className=" w-full mr-6 grid grid-cols-[64px_repeat(4,minmax(240px,350px))]">
        <div className="border-b-2"></div>
        {properties.map((key, index) => (
          <div
            key={index}
            className=" border-b-2 last:border-r-0 px-1 pb-2 text-center text-sm"
          >
            <div className="w-full bg-gray-100 py-2 rounded-lg">{key}</div>
          </div>
        ))}
      </div>
      {isUsersWithLogsFetching && (
        <div className="w-full flex items-center justify-center py-12">
          <CircularLoader />
        </div>
      )}
      {usersWithLogs.map((userWithLogs, index) => {
        const toBeShown = pick(userWithLogs, ...properties);
        return (
          <div
            key={index}
            className=" w-full max-w-fit pr-6 grid grid-cols-[64px_repeat(4,minmax(240px,350px))] group relative "
          >
            <div
              key={index}
              className="first:border-x-2 border-r-2 border-b-2 flex items-center justify-center px-4"
            >
              <BasicTooltip asChild={false} content="View Logs">
                <motion.div
                  whileTap={{ scale: 0.9 }}
                  whileHover={{ scale: 1.05 }}
                  className="bg-transparent p-0"
                >
                  <Link to={`/admin/users/logs/${toBeShown._id}`}>
                    <ExternalLink size={16} className="" />
                  </Link>
                </motion.div>
              </BasicTooltip>
            </div>
            {Object.entries(toBeShown as AdminNS.UserWithLogs).map(
              ([key, value], index) => {
                const userWithLogsKey = key as (typeof properties)[number];
                if (userWithLogsKey !== "logCount")
                  return (
                    <div
                      key={index}
                      className="first:border-x-2 border-r-2 border-b-2 pt-2 pb-3 px-4 group relative cursor-pointer"
                    >
                      <p className="text-center line-clamp-1 text-sm">
                        {!value ? "----" : value}
                      </p>
                    </div>
                  );
                else
                  return (
                    <div
                      key={index}
                      className="first:border-x-2 border-r-2 border-b-2 pt-2 pb-3 px-4 cursor-pointer"
                    >
                      <p className="text-center line-clamp-1 text-sm">
                        {!value ? "----" : value}
                      </p>
                    </div>
                  );
              }
            )}
          </div>
        );
      })}
    </section>
  );
};

export default UsersWithLogs;
