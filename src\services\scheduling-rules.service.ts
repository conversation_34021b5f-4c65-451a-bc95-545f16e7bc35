import { api } from "./api.service";
import {
  SchedulingRules,
  CreateSchedulingRulesPayload,
  UpdateSchedulingRulesPayload,
  ServiceId,
  SchedulingPolicy,
} from "../interfaces/scheduling-rules";

export const CreateSchedulingRules = async (
  serviceId: ServiceId,
  payload: CreateSchedulingRulesPayload
) => {
  const response = await api.post<{
    success: true;
    message: string;
    schedulingRules: SchedulingRules;
  }>(`/services/${serviceId}/scheduling-rules`, payload);
  return response;
};

export const GetSchedulingRules = async (serviceId: ServiceId) => {
  const response = await api.get<{
    success: true;
    schedulingRules: SchedulingRules;
    isDefault: boolean;
  }>(`/services/${serviceId}/scheduling-rules`);
  return response;
};

export const GetAllSchedulingRules = async () => {
  const response = await api.get<{
    success: true;
    schedulingRules: SchedulingRules[];
  }>("/scheduling-rules");
  return response;
};

export const UpdateSchedulingRules = async (
  serviceId: ServiceId,
  payload: UpdateSchedulingRulesPayload
) => {
  const response = await api.put<{
    success: true;
    message: string;
    schedulingRules: SchedulingRules;
  }>(`/services/${serviceId}/scheduling-rules`, payload);
  return response;
};

export const GetPublicSchedulingPolicy = async (
  username: string,
  serviceName: string
) => {
  const response = await api.get<{
    success: true;
    policy: SchedulingPolicy;
  }>(`/public/${username}/${serviceName}/scheduling-policy`);
  return response;
};
