/* eslint-disable react-hooks/rules-of-hooks */
import { useMutation } from "@tanstack/react-query";
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { TrackPageView } from "../services/analytics.service";

/**
 * @dev should be called inside a component
 * @dev this should be in one place -> root component -> App.tsx
 */
export const usePageViewAnalytics = () => {
  const { pathname } = useLocation();
  const { mutateAsync } = useMutation({
    mutationFn: TrackPageView,
    mutationKey: ["track-page-view"],
  });
  return {
    trackPageView: () => {
      useEffect(() => {
        mutateAsync({
          pageUrl: window.location.pathname,
        });
      }, [pathname]);
    },
  };
};
