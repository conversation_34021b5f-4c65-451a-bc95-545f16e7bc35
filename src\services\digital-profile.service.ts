import { AxiosResponse } from "axios";
import { api } from "./api.service";
import { DigitalProfileType } from "@/pages/digital-profile-editor/types";

export type PublicProfileResponse = {
  success: boolean;
  profile: DigitalProfileType;
  additionalData: { isPremiumPlan: boolean };
};

export type PrivateProfileResponse = {
  success: boolean;
  profile: DigitalProfileType;
};

/** 
 * @dev all keys here are optionalllll
 * */
export type UpdateDigitalProfilePayload = {
  details?: Partial<DigitalProfileType['details']>
  theme?: Partial<DigitalProfileType["theme"]>;
  settings?: Partial<DigitalProfileType["settings"]>;
};

export type UploadImageResponse = {
  success: boolean;
  message?: string;
  profile?: DigitalProfileType;
  imageUrl?: string;
};

/**
 * Public: fetch a digital profile by username
 */
export const GetDigitalProfileByUsername = async (
  username: string
): Promise<AxiosResponse<PublicProfileResponse>> => {
  try {
    return await api.get<PublicProfileResponse>(
      `/digital-profiles/username/${encodeURIComponent(username)}`
    );
  } catch (error) {
    console.error("GetDigitalProfileByUsername error:", error);
    throw error;
  }
};

/**
 * Private: fetch authenticated user's digital profile
 */
export const GetDigitalProfile = async (): Promise<
  AxiosResponse<PrivateProfileResponse>
> => {
  try {
    return await api.get<PrivateProfileResponse>(`/digital-profiles/me`);
  } catch (error) {
    console.error("GetDigitalProfile error:", error);
    throw error;
  }
};

/**
 * Private: update authenticated user's digital profile
 */
export const UpdateDigitalProfile = async (
  payload: UpdateDigitalProfilePayload
): Promise<AxiosResponse<PrivateProfileResponse>> => {
  try {
    return await api.put<PrivateProfileResponse>(
      `/digital-profiles/me`,
      payload
    );
  } catch (error) {
    console.error("UpdateDigitalProfile error:", error);
    throw error;
  }
};

/**
 * Private: add a website link to the current user's profile
 */
export const AddWebsiteLink = async (
  settings: Omit<DigitalProfileType["websiteLinks"][number], '_id'>
): Promise<AxiosResponse<PrivateProfileResponse>> => {
  try {
    return await api.post<PrivateProfileResponse>(
      `/digital-profiles/me/links`,
      settings
    );
  } catch (error) {
    console.error("AddWebsiteLink error:", error);
    throw error;
  }
};

/**
 * Private: update a website link by linkId
 */
export const UpdateWebsiteLinkSettings = async (
  linkId: string,
  settings: Partial<DigitalProfileType["websiteLinks"][number]> & {
    newTitle?: string;
  }
): Promise<AxiosResponse<PrivateProfileResponse>> => {
  try {
    return await api.put<PrivateProfileResponse>(
      `/digital-profiles/me/links/${encodeURIComponent(linkId)}`,
      settings
    );
  } catch (error) {
    console.error("UpdateWebsiteLinkSettings error:", error);
    throw error;
  }
};

/**
 * Private: remove a website link by linkId
 */
export const RemoveWebsiteLink = async (
  linkId: string
): Promise<AxiosResponse<PrivateProfileResponse>> => {
  try {
    return await api.delete<PrivateProfileResponse>(
      `/digital-profiles/me/links/${encodeURIComponent(linkId)}`
    );
  } catch (error) {
    console.error("RemoveWebsiteLink error:", error);
    throw error;
  }
};

/**
 * Private: reorder website links (body: { order: string[] })
 */
export const ReorderWebsiteLinks = async (
  order: string[]
): Promise<AxiosResponse<PrivateProfileResponse>> => {
  try {
    return await api.patch<PrivateProfileResponse>(
      `/digital-profiles/me/links/reorder`,
      { order }
    );
  } catch (error) {
    console.error("ReorderWebsiteLinks error:", error);
    throw error;
  }
};

/**
 * Private: upload digital profile image
 */
export const UploadAnyImage = async (
  formData: FormData
) => {
  try {
    return await api.post<{
      imageUrl: string;
      imagePublicId: string;
    }>(
      `/upload-image`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
  } catch (error) {
    console.error("UploadAnyImage error:", error);
    throw error;
  }
};

/**
 * Private: upload digital profile image
 */
export const UploadProfileImage = async (
  formData: FormData
): Promise<AxiosResponse<UploadImageResponse>> => {
  try {
    return await api.post<UploadImageResponse>(
      `/digital-profiles/me/image`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
  } catch (error) {
    console.error("UploadProfileImage error:", error);
    throw error;
  }
};

