import { motion } from "framer-motion";
import React, { useEffect } from "react";

const AddToCartToast: React.FC<{
  isOpen: boolean;
  onClick: () => void;
  onClose: () => void;
}> = ({ isOpen, onClick, onClose }) => {
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => {
        onClose();
      }, 3000);
    }
  }, [isOpen]);
  return (
    <motion.div
      variants={{
        hidden: {
          opacity: 0,
          x: 120,
        },
        shown: {
          opacity: 1,
          x: 0,
          transition: {
            duration: 0.2,
          },
        },
      }}
      initial="hidden"
      animate={isOpen ? "shown" : "hidden"}
      className="fixed  bottom-4 right-4  "
    >
      <div className="flex items-center justify-between min-w-[300px] bg-white rounded-lg shadow-lg p-4">
        <div className="flex flex-col">
          <span className="font-medium text-gray-900">Lead added to cart</span>
          <span className="text-sm text-gray-500">Checkout to cart</span>
        </div>
        <button
          onClick={onClick}
          className="px-3 py-1 text-sm font-medium text-white bg-gray-900 rounded-md ml-4 hover:bg-gray-800"
        >
          View
        </button>
      </div>
    </motion.div>
  );
};

export default AddToCartToast;
