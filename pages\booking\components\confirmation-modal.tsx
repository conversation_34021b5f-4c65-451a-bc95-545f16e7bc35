import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import { getCurrencySign } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import { formatDate } from "date-fns/format";
import React from "react";
import { useBookingContext } from "./use-booking-context";
import { ServiceOption } from "@/src/services/booking.service";

interface Props {
  billingSettings: Pick<UserSettings['billingSettings'], 'preferredCurrency'>
  onCancel: VoidFunction
  onConfirm: VoidFunction;
  serviceOptions: ServiceOption[]
  isBookingPending: boolean;
  className?: string;
  displayPrice?: boolean
  bookingType: 'internal' | 'external'
}

const ConfirmationScreen: React.FC<Props> = ({
  onConfirm,
  serviceOptions,
  bookingType,
  onCancel,
  displayPrice,
  ...rest
}) => {
  const { values: { serviceName, timeSlot, notes, date, customerInfo, ...payloadRest } } = useBookingContext(bookingType)
  return (
    <section
      className={`h-fit w-full flex px-[18px] min-[360px]:px-8 py-10 flex-col gap-y-7 items-center justify-center rounded-[28px] bg-[#EAEBE5]/40 ${rest.className} `}
    >
      <div className="w-full h-full flex flex-col gap-y-3">
        <div className="w-full flex items-start justify-between">
          <Typography variant={"h4"} className=" ">
            Confirm your appointment
          </Typography>
        </div>
        <div className=" flex flex-col gap-y-4  ">
          <div className="flex flex-col gap-y-3 pt-2">
            <Typography className="px-2 bg-white rounded-md">
              Service Information
            </Typography>
            <Typography variant="p" className="px-2 flex items-center justify-between">
              <strong>Service:</strong> {serviceName}
            </Typography>
            <Typography variant="p" className="px-2 flex items-start gap-x-2 justify-between">
              <strong>Time:</strong> {timeSlot}{" "}
              {date && formatDate(new Date(date), "EEEE, do MMMM yyyy")}
            </Typography>
            <Typography variant="p" className="px-2 flex items-center justify-between">
              <strong>Notes:</strong> {notes || "N/A"}
            </Typography>

            <Typography className="px-2 bg-white rounded-md">
              Customer Information
            </Typography>
            <Typography variant="p" className="px-2 flex items-center justify-between">
              <strong>Name:</strong> {customerInfo?.firstName}{" "}
              {customerInfo?.lastName}
            </Typography>
            <Typography variant="p" className="px-2 flex items-center justify-between">
              <strong>Email:</strong> {customerInfo?.email}
            </Typography>
            <Typography variant="p" className="px-2 flex items-center justify-between">
              <strong>Phone:</strong> {customerInfo?.phone}
            </Typography>
            {serviceOptions.length > 0 && (
              <>
                <Typography className="px-2 bg-white rounded-md">
                  Service Add-ons 
                </Typography>
                <div
                  className={cn(
                    "w-full flex flex-wrap items-center gap-x-2 mb-3",
                    {
                      hidden: serviceOptions.length === 0,
                    }
                  )}
                >
                  {serviceOptions.map((option, index) => (
                    <Typography
                      key={index}
                      className="py-1 px-3 text-xs !mt-0 text-gray-600 bg-green-100 rounded-full "
                    >
                      {option.name}{" "}
                      <span className="text-green-500 font-bold">
                        {getCurrencySign(rest.billingSettings?.preferredCurrency)}
                        {option.price}
                      </span>
                    </Typography>
                  ))}
                </div>
              </>
            )}
            {
              bookingType === 'external' &&
              <>
                <Typography className="px-2 bg-green-100 text-green-800 rounded-md text-center">
                  Price Information
                </Typography>
                <Typography
                  variant="p"
                  className="px-2 w-full flex items-center justify-between "
                >
                  {payloadRest.price === 0 ||
                    payloadRest.price === undefined || !displayPrice ? (
                    <span>Free of charge</span>
                  ) : (
                    <>
                      <strong>Total:</strong>{" "}
                      <span className="text-green-800">
                        {getCurrencySign(rest.billingSettings?.preferredCurrency)}
                        {payloadRest.price +
                          serviceOptions.reduce(
                            (acc, option) => acc + option.price,
                            0
                          )}{' '}
                      </span>
                    </>
                  )}
                </Typography>
              </>
            }
          </div>
          <div className="w-full flex justify-center gap-x-2">
            <Button onTap={onCancel} variant="ghost" className="flex-1">
              Cancel
            </Button>
            <Button
              onTap={onConfirm}
              variant="full"
              className={cn({
                "flex-1": true,
              })}
            >
              {rest.isBookingPending ? (
                <CircularLoader color="white" />
              ) : (
                "Confirm"
              )}
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ConfirmationScreen;
