"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>A<PERSON><PERSON> } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { formatDate } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import {
  GetServiceRevenueValue,
  Month,
  ServiceRevenueValueMonthMapping,
} from "@/src/services/analytics.service";
import { useEffect, useMemo } from "react";
import { objectKeys, omit } from "@/lib/helpers";
import { GetServices } from "@/src/services/services.service";
import { useAnalyticsInterval } from "../analytics-interval-context";

export const allColors = [
  "#FF7F50", // Coral
  "#00BFFF", // Deep Sky Blue
  "#FFA07A", // Light Salmon
  "#DB7093", // Pale Violet Red
  "#FFB347", // Pastel Orange
  "#FFC0CB", // Pink Rose
  "#87CEEB", // Sky Blue
  "#6495ED", // Cornflower Blue
  "#CD853F", // Peru
  "#1E90FF", // Dodger Blue
  "#DEB887", // Burlywood
  "#ADD8E6", // Light Blue
  "#FF8C00", // Dark Orange
  "#B0E0E6", // Powder Blue
  "#D2691E", // Chocolate
  "#87CEFA", // Light Sky Blue
];

/**
 * Finds the object with the highest number of properties from an array of objects
 * @param objects Array of objects to analyze
 * @returns The object with the most properties, or undefined if array is empty
 */
export function findObjectWithMostProperties<T extends object>(
  objects: T[]
): T {
  if (!objects.length) return {} as T;

  return objects.reduce((maxObject, currentObject) => {
    const maxPropertiesCount = Object.keys(maxObject).length;
    const currentPropertiesCount = Object.keys(currentObject).length;

    return currentPropertiesCount > maxPropertiesCount
      ? currentObject
      : maxObject;
  }, objects[0]);
}

export default function HighestValueService() {
  const { interval } = useAnalyticsInterval();
  const { data: services = [] } = useQuery({
    queryKey: ["services"],
    queryFn: async () => (await GetServices()).data.Services,
  });
  const { data: serviceRevenuesForEachMonth, refetch } = useQuery({
    queryKey: ["highest-value-service"],
    queryFn: async () =>
      (
        await GetServiceRevenueValue({
          timeInterval: interval,
          serviceId: services[0]._id,
        })
      ).data.data,
    enabled: !!services.length,
  });
  useEffect(() => (refetch(), void 0), [interval]);
  const chartDataMemo = useMemo(() => {
    if (serviceRevenuesForEachMonth) {
      return objectKeys(serviceRevenuesForEachMonth).map((month) => {
        const chartData: { month: Month } & Record<string, number | string> = {
          month,
        };
        const serviceRevenues =
          serviceRevenuesForEachMonth[month]?.averageRevenues || [];
        serviceRevenues.forEach((serviceRevenue) => {
          chartData[serviceRevenue.serviceName] = serviceRevenue.averageRevenue;
        });
        return chartData;
      });
    } else return [];
  }, [serviceRevenuesForEachMonth]);
  const chartConfigMemo = useMemo(() => {
    const config = {} as ChartConfig;
    chartDataMemo.forEach((chartData) => {
      config[chartData.month] = {
        label: chartData.month,
        color: allColors[chartDataMemo.indexOf(chartData)],
      };
    });
    return config;
  }, [chartDataMemo]);
  const getHighestValueServiceForMonth = () => {
    if (!serviceRevenuesForEachMonth) return "";
    const thisMonth = formatDate(new Date(), "MMMM");
    const serviceValueForThisMonth = serviceRevenuesForEachMonth
      ? serviceRevenuesForEachMonth[thisMonth as "January"]
      : ({} as ServiceRevenueValueMonthMapping[keyof ServiceRevenueValueMonthMapping]);
    return (
      serviceValueForThisMonth?.highestAverageRevenueService.serviceName || ""
    );
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle>Highest Value Service</CardTitle>
        <CardDescription className="font-medium">
          {formatDate(new Date(), "MMMM, yyy")}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfigMemo}>
          <LineChart
            accessibilityLayer
            data={chartDataMemo}
            margin={{
              top: 20,
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="line" />}
            />
            <Line
              dataKey={"Barbing"}
              type="monotone"
              stroke={"blue"}
              strokeWidth={2}
              dot={{
                fill: "blue",
              }}
              activeDot={{
                r: 6,
              }}
            />
            {objectKeys(
              omit(findObjectWithMostProperties(chartDataMemo), "month")
            ).map((key, i) => (
              <Line
                key={i}
                dataKey={key}
                type="monotone"
                stroke={allColors[i]}
                strokeWidth={2}
                dot={{
                  fill: allColors[i],
                }}
                activeDot={{
                  r: 6,
                }}
              />
            ))}
          </LineChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none">
          Highest value service for this month ({formatDate(new Date(), "MMMM")}
          ) is {getHighestValueServiceForMonth()}
        </div>
        <div className="leading-none text-muted-foreground">
          Showing each service and highest value service through revenue
          generated for each month
        </div>
      </CardFooter>
    </Card>
  );
}
