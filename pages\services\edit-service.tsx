import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import Toggle from "@/components/ui/toggle-2";
import {
  CreateServicePayload,
  GetCategories,
  GetServiceByUrlName,
  GetServices,
  UpdateService,
  VerifyStripeAccount,
} from "~/services/services.service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useFormik } from "formik";
import { PlusIcon } from "lucide-react";
import * as Yup from "yup";
import { useToast } from "~/contexts/hooks/toast";
import CreateCategoryModal from "./components/create-category-modal";
import { useModalsBuilder } from "@/lib/modals-builder";
import { capitalizeFirstWord, inlineSwitch, noop, noopPreventDefault, omit } from "@/lib/helpers";
import MoreInfo from "@/components/ui/more-info";
import { Picture } from "@gravity-ui/icons";
import { cn } from "@/lib/utils";
import {
  CreateServiceOptionModal,
} from "./components/service-options-modals";
import { usePlanRole } from "@/src/contexts/use-plan-role";
import { useMemo, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import useAuthStore from "@/pages/auth/components/auth-store";
import Dialog from "@/components/ui/dialog";
import PendingOverlay from "@/components/ui/pending-overlay";
import ErrorScreen from "@/components/ui/error-screen";
import { useServiceAddons } from "./components/use-service-add-on";
import { useFormattedPrice } from "@/lib/use-formatted-price";
import { ArrowLeft02Icon, Delete02Icon, Edit02Icon } from "hugeicons-react";

interface EditServiceProps {
}

const EditServicePage = (_props: EditServiceProps) => {
  const queryClient = useQueryClient();
  const showToast = useToast();
  const { urlName } = useParams<{ urlName: string }>()
  const [hasPrice, setHasPrice] = useState(false);
  const { serviceAddons } = useServiceAddons()
  const { data: service, isFetching: isServiceLoading, refetch: refetchService, isError } = useQuery({
    queryKey: ["edit-service", urlName],
    queryFn: async () => {
      const service = (await GetServiceByUrlName(urlName!)).data.Service
      verifyStripeAccountMutation.mutateAsync()
      setHasPrice(service.price ? service.price > 0 : false)
      return service
    },
    enabled: Boolean(urlName),
  });
  const { data: categories } = useQuery({
    queryKey: ["categories"],
    queryFn: async () => (await GetCategories()).data.categories,
  });
  const updateServiceMutation = useMutation({
    mutationFn: UpdateService,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["services"] });
      modalFunctions.openModal("serviceUpdated", {});
      formik.resetForm();
    },
    onError: (error: ApiError) => {
      showToast("error", error?.response?.data?.error || error?.message);
    },
  });
  const formik = useFormik({
    enableReinitialize: true, // Allow formik to update with new data
    initialValues: {
      name: service?.name || "",
      description: service?.description || "",
      messageAfterScheduling: service?.messageAfterScheduling || "",
      duration: service?.duration || 30,
      price: service?.price || 0,
      category: categories?.find(cg => (cg._id === (service?.category as any))) || { name: '', _id: '' },
      color: service?.color || "",
      picture: null as File | null,
      access: service?.access || "public",
      isGroupEvent: service?.isGroupEvent || false,
      blockExtraTimeBefore: service?.blockExtraTimeBefore || 0,
      blockExtraTimeAfter: service?.blockExtraTimeAfter || 0,
      serviceOptions:
        (service?.serviceOptions?.map(opt => opt._id) || []) as NonNullable<CreateServicePayload["serviceOptions"]>,
    },
    validationSchema: Yup.object({
      name: Yup.string().required("Service name is required"),
      duration: Yup.number().min(0).required("Duration is required"),
      price: Yup.number().min(0).required("Price is required"),
      category: Yup.object({
        name: Yup.string().required("Category name is required"),
        _id: Yup.string().required("Category id is required"),
      }).required("Category is required"),
    }),
    validateOnMount: true,
    onSubmit: noop
  });
  const { isFetching: isPictureFetching } = useQuery({
    queryKey: ['service-picture'],
    queryFn: async () => {
      return await fetch(service?.picture!)
        .then(async (val) => {
          if (val.ok) {
            const blob = await val.blob()
            formik.setFieldValue('picture', await val.blob())
            return blob
          } else return null;
        })
        .catch((err) => {
          console.error(err)
          return null
        })
    },
    enabled: !!service?.picture
  })
  const { modals, modalFunctions } = useModalsBuilder({
    createCategory: {
      open: false,
    },
    createServiceOption: {
      open: false,
    },
    serviceUpdated: {
      open: false
    },
    editServiceOption: {
      open: false,
      data: null as {
        name: string;
        description?: string;
        price: number;
        optionIndex: number;
      } | null,
    },
  });
  const { data: services = [] } = useQuery({
    queryKey: ["services"],
    queryFn: async () => (await GetServices()).data.Services,
  })
  const [hasStripeConnected, setHasStripeConnected] = useState(false);
  const verifyStripeAccountMutation = useMutation({
    mutationFn: VerifyStripeAccount,
    onSuccess: () => {
      showToast("success", "Stripe account verified", {
        description: "You can now create services with prices",
      });
      setHasStripeConnected(true)
    },
    onError: (_error: ApiError) => {
      showToast("info", "Stripe not connected", {
        description: "Connect Stripe in the billing settings page",
        duration: 7500
      });
      setHasStripeConnected(false)
    },
  });
  // if user selects it should have a price, we verify if they have a stripe account connected, if they don't, we show a toast, if they do, alright, we continue
  const handlePriceToggle = async (shouldHavePrice: boolean) => {
    if (shouldHavePrice) verifyStripeAccountMutation.mutateAsync();
  };
  const {
    planRole: { shortName: planShortName },
  } = usePlanRole();
  const canCreateServiceWithOptionsMemo = useMemo(() => {
    if (planShortName === "none" || planShortName === "free") {
      // check if two services in the array have options
      const servicesWithOptions = services.filter((service) => {
        return service.serviceOptions?.length;
      });
      const servicesWithOptionsCount = servicesWithOptions.length;
      // if the current service has options already, return true;
      if (servicesWithOptionsCount === 2) {
        const currentService = servicesWithOptions.find(
          (ser) => ser._id === service?._id,
        );
        if (currentService && currentService.serviceOptions?.length) return true;
      }
      return servicesWithOptionsCount < 2;
    } else return true;
  }, [planShortName, services]);
  const updateServiceHandler = () => {
    if (formik.values.price > 0)
      if (hasStripeConnected === false)
        showToast('info', "Service will be updated without price", {
          description: "Connect Stripe in billing settings page to enable price",
          duration: 7500
        })
    formik.validateForm().then((val) => {
      const errors = Object.values(val);
      if (errors.length === 0) {
        const { values } = formik;
        const actualPayload: Record<string, any> = omit(values, 'category');
        // the user may want to change category
        if (values.category?._id.length !== 0)
          actualPayload.category = values.category._id;
        const payloadData = {
          urlName: service!.urlName,
          payload: {
            ...actualPayload
          },
        };
        updateServiceMutation.mutateAsync(payloadData);
      } else
        errors.forEach((error, i) =>
          setTimeout(() => {
            showToast("error", "Invalid Field", {
              description: typeof error === "object" ? (error as any)?.name : error,
            });
          }, i * 500),
        );
    });
  };
  const [hasExtraTime, setHasExtraTime] = useState(Boolean(service?.blockExtraTimeBefore || service?.blockExtraTimeAfter));
  const { initialState: { userSettings } } = useAuthStore()
  const navigate = useNavigate();
  const { formatPrice } = useFormattedPrice()
  const inputRef = useRef<HTMLInputElement>(null);
  function attemptDeletePicture() {
    formik.setFieldValue("picture", null);
  }
  function attemptReplacePicture() {
    inputRef.current?.click();
  }
  if (isServiceLoading)
    return <div className="py-20" ><CircularLoader /></div>
  if (isError)
    return (
      <ErrorScreen title="Service Not found" onRetry={() => refetchService()} message="Check the service name is correct and try again" />
    )
  return (
    <section className="w-full h-full max-w-2xl mx-auto flex flex-col gap-y-10 pb-32">
      <div className="w-full flex items-center gap-x-4">
        <Button
          variant="icon"
          onTap={() => navigate("/scheduling/services")}
          className="p-1.5"
        >
          <ArrowLeft02Icon width={20} height={20} />
        </Button>
        <Typography
          variant={"h1"}
          className="font-bold font-Bricolage_Grotesque  "
        >
          Edit Service
        </Typography>
      </div>
      <form
        onSubmit={noopPreventDefault}
        className="w-full flex flex-col gap-y-6 overflow-y-auto no-scrollbar"
      >
        <Input.Text
          label="Name"
          name="name"
          required
          value={formik.values.name}
          onChange={formik.handleChange}
        />
        <Input.TextArea
          label="Description"
          name="description"
          value={formik.values.description}
          onChange={formik.handleChange}
        />
        {/* Message After Scheduling */}
        <Input.TextArea
          label="Message after scheduling"
          name="messageAfterScheduling"
          value={formik.values.messageAfterScheduling}
          onChange={formik.handleChange}
        />
        <Input.Numeric
          label="Duration (minutes)"
          name="duration"
          defaultValue={formik.values.duration}
          onChange={formik.handleChange}
        />
        <div className="space-y-3">
          <div className="w-full flex items-center justify-between ">
            <Typography className=" text-paragraph">
              Block Extra time
            </Typography>
            <Toggle
              checked={hasExtraTime}
              onChange={(checked) => setHasExtraTime(checked)}
            />
          </div>
          {hasExtraTime && (
            <div className="w-full flex flex-col gap-y-3">
              <Input.Numeric
                label="Block time before appointment (minutes)"
                name="blockExtraTimeBefore"
                value={formik.values.blockExtraTimeBefore}
                onChange={({ currentTarget: { name, value } }) => {
                  formik.setFieldValue(name, value ? Number(value) : 0)
                }}
              />
              <Input.Numeric
                label="Block time after appointment (minutes)"
                name="blockExtraTimeAfter"
                value={formik.values.blockExtraTimeAfter}
                onChange={({ currentTarget: { name, value } }) => {
                  formik.setFieldValue(name, value ? Number(value) : 0)
                }}
              />
            </div>
          )}
        </div>
        {/* Price */}
        <div className="space-y-3">
          <div className="w-full flex items-center justify-between ">
            <Typography className=" text-paragraph">Enable Price</Typography>
            <Toggle
              checked={hasPrice}
              onChange={(checked) => {
                handlePriceToggle(checked);
                setHasPrice(checked);
                if (!checked) formik.setFieldValue("price", 0);
              }}
              disabled={verifyStripeAccountMutation.isPending}
            />
          </div>
          {hasPrice && (
            <Input.Numeric
              label={`Price (${inlineSwitch(userSettings?.billingSettings?.preferredCurrency, ['usd', 'USD'], ['gbp', 'GBP'], { default: 'USD' })})`}
              name="price"
              disabled={hasStripeConnected === false}
              defaultValue={formik.values.price}
              onChange={(e) => {
                formik.setFieldValue("price", parseInt(e.target.value || '0'));
              }}
            />
          )}
        </div>
        {/* Category */}
        <div className="w-full flex flex-col gap-y-2  ">
          <Typography className=" text-paragraph">Category</Typography>
          <div className="w-full grid grid-cols-[1fr_auto] gap-x-2 ">
            <SearchableDropdown
              fullWidth
              value={{
                value: formik.values.category._id,
                label: formik.values.category.name,
              }}
              options={
                categories?.map((category) => ({
                  label: category?.name,
                  value: category?._id,
                })) || []
              }
              name="category"
              onChange={(option) => {
                if (!option) return;
                formik.setFieldValue("category", {
                  name: option.label,
                  _id: option.value,
                });
              }}
            />
            <Button
              type="button"
              variant="icon"
              className="px-4"
              onTap={() => modalFunctions.openModal("createCategory", {})}
            >
              <PlusIcon />
            </Button>
          </div>
        </div>
        <div className="flex flex-col gap-y-6">
          <div className="w-full flex flex-col gap-y-1  ">
            <div className="w-full flex items-center gap-x-2 mb-2">
              <Typography className=" text-paragraph">
                Service Add-ons{" "}
                <span className="px-2 py-0.5 text-sky-600 bg-sky-50 rounded-md text-sm">
                  optional
                </span>
              </Typography>
              <MoreInfo content="Service Add-ons (Add additional services or variations that customers can choose alongside the main service. Each add-on can have its own price and description)" />
            </div>
            {serviceAddons?.map((addon, index) => {
              const addonIsInPayload = formik.values.serviceOptions?.includes(addon._id)
              return (
                <div
                  key={index}
                  className="flex items-start gap-x-2 group mb-2 last:mb-0"
                >
                  <div className="w-full px-3 py-2 rounded-xl bg-stone-100">
                    <div className="flex items-center justify-between text-paragraph font-Bricolage_Grotesque">
                      <Typography className="">{addon.name}</Typography>
                      <Typography className={cn("text-paragraph ")}>
                        {formatPrice(addon.price)}
                      </Typography>
                    </div>
                    <div className="w-full flex justify-between items-center text-gray-400 font-normal text-xs gap-x-2">
                      <Typography className="font-normal flex-grow line-clamp-1">
                        {addon.description ?? "No description"}
                      </Typography>
                      <Button
                        onTap={() => {
                          if (!addonIsInPayload) {
                            // do check for blocking of more services 
                            if (canCreateServiceWithOptionsMemo)
                              formik.setFieldValue('serviceOptions', [...formik.values.serviceOptions, addon._id])
                            else {
                              showToast("info", "Upgrade plan to add options", {
                                description:
                                  "You can add options to 2 services on the free plan.",
                                action: {
                                  title: "Upgrade",
                                  onClick() {
                                    navigate("/profile/billing");
                                    return true;
                                  },
                                },
                                duration: 7000,
                              });
                            }
                          } else {
                            formik.setFieldValue('serviceOptions',
                              formik.values.serviceOptions.filter(v => v !== addon._id)
                            )
                          }
                        }
                        }
                        className={cn("px-6", {
                          "bg-green-400": !addonIsInPayload,
                          "bg-red-600": addonIsInPayload
                        })}
                      >
                        {addonIsInPayload
                          ? "Remove"
                          : "Attach"}{" "}
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
          <div className="w-full flex items-start justify-between ">
            <Typography className=" text-paragraph">
              Color
            </Typography>
            <Input.ColorPicker name="color" value={formik.values.color} onChange={formik.handleChange} />
          </div>
          <div className="w-full flex flex-col gap-y-2 ">
            <Typography className=" text-paragraph">Picture</Typography>
            <div className="cursor-pointer w-full active:scale-[0.95] ">
              {!formik.values.picture ? (
                <div
                  onClick={() => !isPictureFetching && inputRef.current?.click()}
                  className="w-full relative border border-dashed border-gray-400 rounded-3xl flex items-center justify-center aspect-[5/3] active:scale-[0.95] "
                >
                  <Picture className="text-gray-300 size-20 " />
                  {isPictureFetching ??
                    <div className="absolute top-0 left-0 w-full flex items-center justify-center aspect-[5/3] z-20 " >
                      <CircularLoader />
                    </div>}
                </div>
              ) : (
                <div className="relative w-full group rounded-3xl aspect-[5/3] ">
                  <img
                    className="w-full h-full bg-orange-300 object-cover rounded-3xl "
                    src={
                      formik.values.picture
                        ? URL.createObjectURL(formik.values.picture)
                        : ""
                    }
                  />
                  {innerWidth <= 480 ? (
                    <div className="w-fit absolute right-2 bottom-2 flex items-center justify-center gap-x-2">
                      <Button
                        variant="icon"
                        className="rounded-full"
                        onTap={attemptDeletePicture}
                      >
                        <Delete02Icon size={18} />
                      </Button>
                      <Button
                        variant="icon"
                        className="rounded-full"
                        onTap={attemptReplacePicture}
                      >
                        <Edit02Icon size={18} />
                      </Button>
                    </div>
                  ) : (
                    <div className="w-full aspect-[5/3] absolute z-20 top-0 bg-white/20 backdrop-blur-lg rounded-3xl hidden items-center justify-center gap-x-3 group-hover:flex ">
                      <Button onTap={attemptDeletePicture}>Delete</Button>
                      <Button onTap={attemptReplacePicture}>Replace</Button>
                    </div>
                  )}
                </div>
              )}
            </div>
            <input
              type="file"
              name="picture"
              className="hidden"
              accept="image/jpeg, image/png"
              ref={inputRef}
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                const files = event.target.files;
                if (files && files.length > 0) {
                  formik.setFieldValue("picture", files[0]);
                }
              }}
            />
            {formik.values.picture ? (
              <MoreInfo.Static
                className="text-sm hidden md:flex"
                content="Hover on the image to see more options"
              />
            ) : null}
          </div>
          <div className="w-full flex flex-col gap-y-2  ">
            <Typography className=" text-paragraph">Access</Typography>
            <SearchableDropdown
              fullWidth
              value={{
                value: formik.values.access,
                label: capitalizeFirstWord(formik.values.access),
              }}
              options={[
                { label: "Private", value: "private" },
                { label: "Public", value: "public" },
              ]}
              name="access"
              onChange={(option) => {
                if (!option) return;
                formik.setFieldValue("access", option.value);
              }}
            />
          </div>
          <div className="w-full flex items-center justify-between">
            <Typography className=" text-paragraph">Group Event</Typography>
            <Toggle
              name="isGroupEvent"
              checked={formik.values.isGroupEvent}
              onChange={(checked) =>
                formik.setFieldValue("isGroupEvent", checked)
              }
            />
          </div>
        </div>
        <SaveButton
          disabled={updateServiceMutation.isPending || isServiceLoading || !formik.isValid}
          onTap={() => {
            updateServiceHandler()
          }}
          loading={updateServiceMutation.isPending}
          title="Edit Service"
        >
        </SaveButton>
      </form>
      <CreateCategoryModal
        open={modals.createCategory.open}
        onClose={() => modalFunctions.closeModal("createCategory")}
      />
      <CreateServiceOptionModal
        hasStripeConnected={hasStripeConnected === false}
        open={modals.createServiceOption.open}
        onClose={() => modalFunctions.closeModal("createServiceOption")}
        onSave={(option) => {
          option;
          modalFunctions.closeModal("createServiceOption");
        }}
      />
      <Dialog
        title="Service updated"
        description="Your service has been updated successfully, return to the services page"
        open={modals.serviceUpdated.open}
        onClose={() => modalFunctions.closeModal("serviceUpdated")}
        action={{
          title: "OK",
          onConfirm: () => {
            modalFunctions.closeModal("serviceUpdated");
            navigate("/scheduling/services");
          },
        }}
      />
      <PendingOverlay isPending={updateServiceMutation.isPending} />
    </section>
  );
};

export default EditServicePage
