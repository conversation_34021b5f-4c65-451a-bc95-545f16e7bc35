import { But<PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Avatar from "@/components/ui/avatar";
import { cn } from "@/lib/utils";
import {
  ChartMixed,
  ChevronRight,
  CircleQuestion,
  CreditCard,
  LogoStackOverflow,
  Persons,
  Ticket,
} from "@gravity-ui/icons";
import { ArrowLeft } from "lucide-react";
import { useMemo } from "react";
import { Link, Outlet, useLocation } from "react-router-dom";
import useAuthStore from "../auth/components/auth-store";
import FaqPage from "./faq";
import SubscriptionPlans from "./subscription-plans";
import SupportTickets from "./support-tickets";
import Analytics from "./usage-analytics";
import UserLogs from "./user-logs";
import Users from "./users";
import UsersWithLogs from "./users-logs";

const adminLinks: {
  link: `/admin/${string}`;
  icon: JSX.Element;
  title: string;
}[] = [
  {
    link: "/admin/users",
    icon: <Persons width={22} height={22} />,
    title: "Users",
  },
  {
    link: "/admin/users/logs",
    icon: <LogoStackOverflow width={22} height={22} />,
    title: "Users Logs",
  },
  {
    link: "/admin/support-tickets",
    icon: <Ticket width={22} height={22} />,
    title: "Support Tickets",
  },
  {
    link: "/admin/analytics",
    icon: <ChartMixed width={22} height={22} />,
    title: "Usage Analytics",
  },
  {
    link: "/admin/subscription-plans",
    icon: <CreditCard width={22} height={22} />,
    title: "Subscription Plans",
  },
  {
    link: "/admin/faq",
    icon: <CircleQuestion width={22} height={22} />,
    title: "FAQ",
  },
];

const AdminPage = () => {
  const {
    initialState: { user },
  } = useAuthStore();
  const { pathname } = useLocation();
  const linkTitle = useMemo(() => {
    return adminLinks.find((link) => link.link === pathname)?.title || "Admin";
  }, [pathname]);
  return (
    <section className="w-full h-fit flex flex-col flex-grow pb-20 md:!bg-[#DFE1DB]/15 md:mb-6 md:py-8 md:pb-10 md:px-4 md:rounded-[32px] ">
      <div className="w-full flex items-center md:hidden">
        <Button
          variant="icon"
          onTap={() => {
            // go back one step in navigation
            window.history.back();
          }}
          className={cn(
            "bg-transparent border-2 !border-subtle-gray !rounded-full text-black ",
            {
              "invisible pointer-events-none": pathname === "/admin",
            }
          )}
        >
          <ArrowLeft />
        </Button>
        <Typography variant={"p"} className="!mt-0 mx-auto text-lg">
          {linkTitle}
        </Typography>
        {/* just here to center align linkTitle, don't remove though */}
        <Button
          variant="icon"
          className="bg-transparent border-2 !border-subtle-gray !rounded-full  pointer-events-none invisible"
        >
          <ArrowLeft />
        </Button>
      </div>
      {window.innerWidth < 768 && (
        <div className="w-full py-4">
          <Outlet />
        </div>
      )}
      {window.innerWidth >= 768 && (
        <div className="hidden md:grid grid-cols-[auto_auto_1fr] gap-x-10">
          <ul className="flex flex-col w-fit pt-0 px-4 gap-y-6">
            {adminLinks.map(({ link, title }, index) => (
              <li key={index} className="pt-2">
                <Link
                  className={cn(" px-2 py-2.5 rounded-full", {
                    "bg-primary-50/40 px-5 ": link === pathname,
                  })}
                  to={link}
                >
                  <span
                    className={cn("text-base font-bold !mt-0 text-subtext", {
                      "!text-red-500 inline-block !mt-8":
                        link.includes("delete-"),
                      "!text-primary-500 ": link === pathname,
                    })}
                  >
                    {title}
                  </span>
                </Link>
              </li>
            ))}
          </ul>
          {/* desktop divider */}
          <div className="w-0.5 bg-gray-100" />
          {/* desktop outlet */}
          <div className="flex-grow flex flex-col gap-y-4 pr-4">
            <div className="flex items-center gap-x-4 ">
              <div className="w-fit h-fit ">
                <Avatar
                  src={user?.user_photo}
                  alt="User Avatar"
                  className="!ml-0 !w-12 !h-12"
                />
              </div>
              <div className="h-fit flex flex-col gap-y-">
                <Typography variant={"h4"} className="!text-base">
                  Admin
                </Typography>
                <p className="">{user?.business_bio || "Bio"}</p>
              </div>
            </div>
            <Outlet />
          </div>
        </div>
      )}
    </section>
  );
};

/**
 * @dev this page shows first on the mobile view
 */
const AdminLinksPage = () => {
  const {
    initialState: { user },
  } = useAuthStore();
  return (
    <div className="w-full h-fit mt-3 md:hidden">
      <div className="w-full flex flex-col items-center gap-y-4 ">
        <Avatar
          src={user?.user_photo}
          alt="User Avatar"
          className="!ml-0 !w-32 !h-32"
        />
        <Typography variant={"h4"} className="!mt-0">
          Admin User
        </Typography>
      </div>
      <div className="w-full flex flex-col pt-4 px-2 ">
        {adminLinks.map(({ link, icon, title }, index) => (
          <Link
            key={index}
            to={link}
            className="w-full cursor-pointer border-b last:border-none"
          >
            <div className="w-full flex gap-x-5 items-center py-4 text-primary-400  ">
              {icon}
              <Typography
                variant={"p"}
                className="!mt-0 text-lg text-paragraph"
              >
                {title}
              </Typography>
              <ChevronRight
                width={20}
                height={20}
                className="ml-auto text-paragraph"
              />
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

const Admin = Object.assign(AdminPage, {
  MobileAdminLinks: AdminLinksPage,
  Users: Users,
  UsersWithLogs,
  UserLogs,
  SupportTickets,
  Analytics,
  SubscriptionPlans,
  FaqPage,
});

export default Admin;
