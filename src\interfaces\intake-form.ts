// frontend/src/interfaces/intake-form.ts
export type QuestionType = 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'email' | 'phone' | 'date' | 'number' | 'url';

export interface FormControl {
  type: QuestionType;
  question: string;
  required: boolean;
  options?: string[];
  placeholder?: string;
  validation?: {
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: string;
  };
  order: number;
}

export interface IntakeFormQuestion extends FormControl {
  PP_Id: string;
}

export interface IntakeForm {
  _id?: string;
  serviceId: string;
  isActive: boolean;
  questions: IntakeFormQuestion[];
}

export interface IntakeFormResponse {
  questionId: string;
  question: string;
  answer: string | string[] | boolean | number | Date;
  questionType: QuestionType;
  required: boolean;
}

export interface CreateIntakeFormRequest {
  isActive: boolean;
  questions: FormControl[];
}

export interface UpdateIntakeFormRequest {
  isActive?: boolean;
  questions?: FormControl[];
}

export interface AddQuestionRequest extends FormControl {}

export interface UpdateQuestionRequest extends AddQuestionRequest {}

export interface ReorderQuestionsRequest {
  questionIds: string[];
}
