import {
  <PERSON>,
  CardContent,
  Card<PERSON>es<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { formatLargeNumber } from "@/lib/helpers";
import { motion } from "framer-motion";
import { TrendingUp } from "lucide-react";

const findHighestViews = (
  data: { url: string; views: number }[]
): { url: string; views: number } => {
  return data.reduce((max, current) =>
    current.views > max.views ? current : max
  );
};

const sortByViews = (
  data: { url: string; views: number }[]
): { url: string; views: number }[] => {
  return [...data].sort((a, b) => b.views - a.views);
};

const PageViewByUrl = (_props: {}) => {
  const data = [
    { url: "/pricing/pro", views: 320 },
    { url: "/pricing/basic", views: 4000 },
    { url: "/pricing/enterprise", views: 5200 },
  ];
  const highestViewedUrl = findHighestViews(data);
  return (
    <Card>
      <CardHeader>
        <CardTitle>Page view analytics</CardTitle>
        <CardDescription>January - June 2024</CardDescription>
      </CardHeader>
      <CardContent className="px-6 py-6 rounded-3xl space-y-3 ">
        <div className="w-full space-y-4 items-start justify-between">
          {sortByViews(data).map((item) => (
            <div key={item.url} className="flex flex-col gap-2">
              <span className="text-sm text-gray-600">{item.url}</span>
              <div className="flex items-center justify-between w-full gap-x-8">
                <div className="flex-grow h-2">
                  <motion.div
                    initial={{ width: "0px" }}
                    animate={{
                      width: `${(item.views / highestViewedUrl.views) * 100}%`,
                    }}
                    transition={{ delay: 0.2 }}
                    className="h-full bg-chart-1 rounded-full"
                  />
                </div>
                <span className="text-sm font-medium">
                  {formatLargeNumber(item.views)}
                </span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          Showing total page views by url for the last 6 months
        </div>
      </CardFooter>
    </Card>
  );
};

export default PageViewByUrl;
