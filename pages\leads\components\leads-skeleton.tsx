const LeadSkeleton = () => {
  return (
    <div className="border-t py-4 px-2 flex flex-col border-b animate-pulse">
      {/* Lead Header Section Skeleton */}
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center">
          <div className="w-10 h-10 rounded-full bg-gray-200" />
          <div className="ml-4">
            <div className="h-6 w-32 bg-gray-200 rounded" />
            <div className="h-4 w-24 bg-gray-200 rounded mt-2" />
          </div>
        </div>
      </div>

      {/* Lead Description Skeleton */}
      <div className="space-y-2 mb-4">
        <div className="h-4 bg-gray-200 rounded w-full" />
        <div className="h-4 bg-gray-200 rounded w-3/4" />
      </div>

      {/* Lead Footer Skeleton */}
      <div className="flex justify-between mt-5 items-center">
        <div className="h-5 w-24 bg-gray-200 rounded" />
        <div className="h-10 w-32 bg-gray-200 rounded" />
      </div>
    </div>
  );
};

export default LeadSkeleton;
