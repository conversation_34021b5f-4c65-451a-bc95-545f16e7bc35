import { useToast } from "@/src/contexts/hooks/toast";
import { AddQuestion, DeleteQuestion, GetIntakeForm, ReorderQuestions, ToggleIntakeFormStatus, UpdateIntakeForm, UpdateQuestion } from "@/src/services/intake-form.service";
import { GetServiceByUrlName } from "@/src/services/services.service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const useIntakeForm = (urlName: string | null | undefined) => {
  const queryClient = useQueryClient();
  const showToast = useToast();
  const QUERY_KEY = ['intake-form', urlName]
  const intakeFormQuery = useQuery({
    queryKey: QUERY_KEY,
    queryFn: async () => {
      const { data } = await GetServiceByUrlName(urlName!);
      const { data: { intakeForm } } = await GetIntakeForm(data.Service._id)
      return { intakeForm, serviceDetails: data.Service }
    },
    enabled: Bo<PERSON>an(urlName)
  })

  const updateIntakeFormMutation = useMutation({
    mutationFn: UpdateIntakeForm,
    onSuccess() {
      queryClient.invalidateQueries({
        queryKey: QUERY_KEY,
      });
      showToast("info", "Intake Form Updated");
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  const toggleIntakeFormStatus = useMutation({
    mutationFn: ToggleIntakeFormStatus,
    onSuccess() {
      queryClient.invalidateQueries({
        queryKey: QUERY_KEY,
      });
      showToast("info", "Intake form updated");
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  const questionMutations = {
    add: useMutation({
      mutationFn: AddQuestion,
      onSuccess() {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEY,
        });
        showToast("success", "Question added");
      },
      onError(error: ApiError) {
        showToast("error", error.response?.data.error || "Something went wrong");

        queryClient.invalidateQueries({
          queryKey: QUERY_KEY,
        });
      },
    }),
    update: useMutation({
      mutationFn: UpdateQuestion,
      onSuccess() {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEY,
        });
        showToast("success", "Question updated");
      },
      onError(error: ApiError) {
        showToast("error", error.response?.data.error || "Something went wrong");

        queryClient.invalidateQueries({
          queryKey: QUERY_KEY,
        });
      },
    }),
    delete: useMutation({
      mutationFn: DeleteQuestion,
      onSuccess() {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEY,
        });
        showToast("success", "Question deleted");
      },
      onError(error: ApiError) {
        showToast("error", error.response?.data.error || "Something went wrong");

        queryClient.invalidateQueries({
          queryKey: QUERY_KEY,
        });
      },
    }),
    reorder: useMutation({
      mutationFn: ReorderQuestions,
      onSuccess() {
        queryClient.invalidateQueries({
          queryKey: QUERY_KEY,
        });
        showToast("success", "Question re-ordered");
      },
      onError(error: ApiError) {
        showToast("error", error.response?.data.error || "Something went wrong");

        queryClient.invalidateQueries({
          queryKey: QUERY_KEY,
        });
      },
    }),
  }
  return {
    intakeFormQuery,
    updateIntakeFormMutation,
    toggleIntakeFormStatus,
    questionMutations,
  }
}

