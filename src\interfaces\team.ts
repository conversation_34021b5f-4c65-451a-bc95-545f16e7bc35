export type TeamId = string;

export interface TeamType {
  _id: TeamId;
  name: string;
  ownerId: string; // UserId of the team owner
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTeamPayload {
  name: string;
  description?: string;
}

export interface UpdateTeamPayload {
  name?: string;
  description?: string;
  isActive?: boolean;
}

// Team statistics and analytics interfaces
export interface TeamStats {
  _id: TeamId;
  name: string;
  totalStaff: number;
  activeStaff: number;
  totalBookings: number;
  totalRevenue: number;
  averageRating: number;
  staffPerformance: Array<{
    staffId: string;
    name: string;
    bookings: number;
    revenue: number;
    rating: number;
  }>;
}

// Team rules and settings interfaces
export interface TeamRules {
  _id: string;
  teamId: TeamId;
  rules: Array<{
    _id: string;
    title: string;
    description: string;
    isActive: boolean;
    createdAt: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface CreateTeamRulePayload {
  teamId: TeamId;
  title: string;
  description: string;
}

export interface UpdateTeamRulePayload {
  title?: string;
  description?: string;
  isActive?: boolean;
}
