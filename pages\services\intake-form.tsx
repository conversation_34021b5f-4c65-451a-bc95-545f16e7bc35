import React from 'react';
import { Typography } from '@/components/typography';
import { useParams } from 'react-router-dom';
import CircularLoader from '@/components/ui/circular-loader';
import { PlusSignIcon } from 'hugeicons-react';
import { Button } from '@/components/buttons';
import { useIntakeForm } from './components/use-intake-form';
import PendingOverlay from '@/components/ui/pending-overlay';
import { debounce, noop, omit, or } from '@/lib/helpers';
import Toggle from '@/components/ui/toggle-2';
import { useFormik } from 'formik';
import { IntakeFormQuestion } from '@/src/interfaces/intake-form';
import { useModalsBuilder } from '@/lib/modals-builder';
import SelectQuestionModal from './components/select-question-modal';
import { FormQuestionMutationsContext, useFormQuestionMutations } from './components/intake-form-context';
import Question from './components/question';
import { Reorder } from 'framer-motion';


const IntakeForm: React.FC = () => {
  const { urlName } = useParams<{ urlName: string }>();
  const { intakeFormQuery, toggleIntakeFormStatus, ...intakeFormRest } = useIntakeForm(urlName)
  const {
    intakeForm: { isActive, questions, _id: intakeFormId } = {},
    serviceDetails: { _id: serviceId, name: serviceName } = {}
  } = intakeFormQuery.data || {}
  const isActiveFormik = useFormik({
    enableReinitialize: true,
    initialValues: { isActive: Boolean(isActive) },
    onSubmit: noop
  })
  const questionsFormik = useFormik({
    enableReinitialize: true,
    initialValues: {
      questions: questions || []
    },
    onSubmit: noop
  })
  const debouncedReorderMutation = debounce((serviceId: Nullable<string> | undefined, questions: IntakeFormQuestion[]) => {
    intakeFormRest.questionMutations
      .reorder
      .mutateAsync({
        serviceId: serviceId!,
        data: {
          questionIds: questions.map(q => q.PP_Id)
        }
      })
  }, 1000)
  if (intakeFormQuery.isLoading)
    return (
      <div className="flex w-full justify-center py-10">
        <CircularLoader />
      </div>
    )
  return (
    <FormQuestionMutationsContext.Provider
      value={{
        addQuestion: (data) => {
          return intakeFormRest
            .questionMutations.add
            .mutateAsync({ serviceId: serviceId!, data })
        },
        deleteQuestion: (questionId) => {
          // optimistic updates 
          questionsFormik.setFieldValue('questions',
            questionsFormik.values.questions.filter(q => q.PP_Id !== questionId)
          )
          intakeFormRest
            .questionMutations.delete
            .mutateAsync({ serviceId: serviceId!, questionId })
        },
        updateQuestion: (questionId, data) => {
          return intakeFormRest
            .questionMutations.update
            .mutateAsync({ serviceId: serviceId!, questionId, data })
        }
      }}
    >
      <section className="w-full max-w-2xl px-2 flex flex-col gap-y-5 pb-32 ">
        <div className="w-full flex flex-col gap-y-2 justify-between items-start">
          <Typography
            variant={"h1"}
            className="font-bold font-Bricolage_Grotesque  "
          >
            {serviceName} Intake Form
          </Typography>
          <Typography className="text-sm text-subtext pl-1">
            Create a personalized intake form to gather essential information from clients when they book appointments.
          </Typography>
        </div>
        {
          // if there's no _id that means the backend sent a fallback object, in that case we need to create/update (upsert a new intake form and start adding questions to it)
          Boolean(intakeFormId)
            ? (
              <IntakeFormImpl
                isActiveFormik={isActiveFormik}
                onToggleStatus={(isActive) => {
                  isActiveFormik.setFieldValue('isActive', isActive)
                  toggleIntakeFormStatus.mutateAsync({
                    isActive,
                    serviceId: serviceId!
                  })
                }}
                onReorder={(questions) => {
                  questionsFormik.setFieldValue('questions', questions)
                  debouncedReorderMutation(serviceId, questions)
                }}
                questionsFormik={questionsFormik}
              />
            )
            : (
              <div className="flex flex-col items-center justify-center text-center">
                <div className="mb-6">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <PlusSignIcon className="w-8 h-8 text-gray-400" />
                  </div>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No form found
                </h3>
                <p className="text-gray-500 mb-6 max-w-sm">
                  Click the Button below to create your form.
                </p>
                <Button
                  onTap={() => intakeFormRest
                    .updateIntakeFormMutation
                    .mutateAsync({
                      serviceId: serviceId!,
                      data: { questions: [], isActive: true }
                    })
                  }
                  className="font-medium py-3 px-6 flex items-center gap-2"
                >
                  <PlusSignIcon className="w-5 h-5" />
                  Create Form
                </Button>
              </div>
            )
        }
        <PendingOverlay isPending={
          or<boolean>(
            [intakeFormRest.updateIntakeFormMutation, ...(
              Object.values(
                // don't show pending overlay for re-order call;
                omit(intakeFormRest.questionMutations, 'reorder', 'delete')
              )
            )].map(mut => mut.isPending)
          )
        } />
      </section>
    </FormQuestionMutationsContext.Provider>
  );
};

type FormProps = {
  onToggleStatus(isActive: boolean): void;
  isActiveFormik: ReturnType<typeof useFormik<{ isActive: boolean }>>
  questionsFormik: ReturnType<typeof useFormik<{ questions: IntakeFormQuestion[] }>>
  onReorder(questions: IntakeFormQuestion[]): void
}

const IntakeFormImpl: React.FC<FormProps> = (props) => {
  const { isActiveFormik, questionsFormik, onToggleStatus, onReorder } = props;
  const { modals, modalFunctions } = useModalsBuilder({
    addQuestion: {
      open: false,
      data: null as Nullable<{ order: number }>
    }
  })
  const { addQuestion } = useFormQuestionMutations()
  return (
    <section className="w-full max-w-2xl flex flex-col pb-32 ">
      <div className="flex flex-col">
        <div className="w-full flex items-start justify-between ">
          <Typography className=" text-paragraph">
            Toggle Intake Form
          </Typography>
          <Toggle
            checked={isActiveFormik.values.isActive}
            onChange={(checked) => {
              onToggleStatus(checked)
            }}
          />
        </div>
      </div>
      {isActiveFormik.values.isActive && (
        <>
          {/* this should be a re-ordergroup */}
          <Reorder.Group
            as='div'
            axis='y'
            values={questionsFormik.values.questions}
            className='flex flex-col mt-9 gap-y-8 mb-9'
            onReorder={onReorder}
          >
            {questionsFormik.values.questions.map((q, index) => (
              <Reorder.Item 
                as='p' 
                dragElastic={0.1} 
                key={q.PP_Id} 
                value={q}
                layoutId={`item-${q.PP_Id}`} // Helps with complex animations
                layout
              >
                <Question
                  index={index}
                  question={q}
                />
              </Reorder.Item>
            ))}
          </Reorder.Group>
          <div className='flex flex-col items-center gap-y-4'>
            <Button
              className="text-sm w-fit whitespace-nowrap flex items-center gap-x-2 pl-2"
              onTap={() => modalFunctions.openModal('addQuestion', {
                data: { order: questionsFormik.values.questions.length }
              })}
            >
              <PlusSignIcon size={18} /> Add Question
            </Button>
          </div>
          {modals.addQuestion.data && (
            <SelectQuestionModal
              open={modals.addQuestion.open}
              onClose={modalFunctions.returnClose('addQuestion')}
              onSelect={(data) => {
                addQuestion(data)
                  .then(_ =>
                    modalFunctions.closeModal('addQuestion')
                  )
              }}
              order={modals.addQuestion.data.order!}
            />)}
        </>
      )
      }
    </section>

  )
}


export default IntakeForm;
