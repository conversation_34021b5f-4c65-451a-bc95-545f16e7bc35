
import { api } from "./api.service";

interface IBookingInfo {
  isImage: boolean;
  data: string; // image URL or HTML tags as string
}

interface IThemeSettings {
  primaryColor: string; // Primary color for the booking page
  secondaryColor: string; // Seconday color for the booking page
  backgroundColor: string; // Background color
  textColor: string; // Text color
  buttonColor: string; // Button color
  buttonTextColor: string; // Button text color
  fontFamily: string; // Font family
  borderRadius: number; // Border radius for elements
  shadow: string; // Box shadow for elements
}

export interface IBookingSettings {
  userId: string;
  bookingInfo: IBookingInfo;
  themeSettings: IThemeSettings;
  isActive: boolean;
  customCSS?: string; // Optional custom CSS for advanced styling
}

type BookingSettingsBaseResponse = {
  bookingSettings: IBookingSettings
}

export const GetBookingSettings = async () => {
  try {
    const response = await api.get<BookingSettingsBaseResponse>(`/booking-settings`);
    return response;
  } catch (error) {
    console.error("bookiing setting error:", error);
    throw error;
  }
};

export const UpdateBookingSettingsHTML = async (payload: { isImage: boolean, data: HTMLString }) => {
  try {
    const response = await api.put<BookingSettingsBaseResponse>(`/booking-settings/info`, payload);
    return response;
  } catch (error) {
    console.error("bookiing setting error:", error);
    throw error;
  }
};

export const UploadBookingSettingsImage = async (payloadFormData: TypedFormData<{ image: File }>) => {
  try {
    const response = await api.post<BookingSettingsBaseResponse & {
      imageUrl: string;
      imagePublickId: string;
    }>(`/booking-settings/upload-image`, payloadFormData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response;
  } catch (error) {
    console.error("bookiing setting error:", error);
    throw error;
  }
};

type HTMLString = string;

export interface BookingSettingsByUsername {
  bookingInfo: {
    type: 'image' | 'html';
    url?: string;
    content: HTMLString;
  };
  themeSettings: IThemeSettings
  customCSS: string;
  businessLogoImageUrl: string | undefined
}

/** 
 * @dev this should be called in a non-protected route 
 * */
export const GetBookingSettingsByUsername = async (username: string) => {
  try {
    const response = await api.get<{
      bookingSettings: BookingSettingsByUsername
    }>(`/booking-settings/${username}`);
    return response;
  } catch (error) {
    console.error("bookiing setting error:", error);
    throw error;
  }
};

export const ToggleBookingSettingsStatus = async (enabled: boolean) => {
  try {
    const response = await api.patch<BookingSettingsBaseResponse>(`/booking-settings/toggle-status`, { isActive: enabled });
    return response;
  } catch (error) {
    console.error("bookiing setting error:", error);
    throw error;
  }
};

export const UpdateBookingThemeSettings = async (payload: IThemeSettings) => {
  try {
    const response = await api.put(`/booking-settings/theme`, payload);
    return response;
  } catch (error) {
    console.error("bookiing setting error:", error);
    throw error;
  }
};

