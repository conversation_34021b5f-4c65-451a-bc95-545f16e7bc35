import React from "react";
import { Typography } from "@/components/typography";
import { InformationSquareIcon } from "hugeicons-react";

interface DefaultRulesBannerProps {
  serviceName?: string;
}

const DefaultRulesBanner: React.FC<DefaultRulesBannerProps> = ({ serviceName }) => {
  return (
    <div className="bg-amber-50 border border-amber-200 rounded-xl p-4 space-y-3">
      <div className="flex items-start gap-3">
        <InformationSquareIcon className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
        <div className="space-y-2">
          <Typography className="font-medium text-amber-900">
            Using Default Rules
          </Typography>
          <Typography className="text-sm text-amber-800">
            {serviceName ? `${serviceName} is` : "This service is"} currently using the system default scheduling rules.
          </Typography>
        </div>
      </div>

      <div className="bg-white rounded-lg p-3 space-y-2">
        <Typography className="text-sm font-medium text-gray-900">
          Default Policy:
        </Typography>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• No deposit required</li>
          <li>• Free cancellation up to 24 hours before</li>
          <li>• Rescheduling allowed up to 2 hours before</li>
          <li>• No fees for no-shows or late cancellations</li>
        </ul>
      </div>

      <Typography className="text-xs text-amber-700">
        Create custom rules to override these defaults and better suit your business needs.
      </Typography>
    </div>
  );
};

export default DefaultRulesBanner;
