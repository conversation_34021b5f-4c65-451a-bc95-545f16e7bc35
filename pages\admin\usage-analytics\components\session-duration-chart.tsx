import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { TrendingUp } from "lucide-react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Cartesian<PERSON>,
  LabelList,
  XAxis,
  YAxis,
} from "recharts";

const chartData = [
  { pageUrl: "/price", high: 45, low: 5 },
  { pageUrl: "/scheduling/dashboard", high: 52, low: 7 },
  { pageUrl: "/leads", high: 48, low: 0 },
  { pageUrl: "/digital-profile/edit", high: 70, low: 5 },
  { pageUrl: "/digital", high: 65, low: 0 },
];

const chartConfig = {
  high: {
    label: "High",
  },
  low: {
    label: "Low",
  },
} satisfies ChartConfig;

/**
 * @todo make api calls to backend
 */
export default function SessionDurationBarChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Session Duration</CardTitle>
        <CardDescription>January - June 2024</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <BarChart accessibilityLayer data={chartData} layout="vertical">
            <CartesianGrid horizontal={false} />
            <YAxis
              dataKey="pageUrl"
              type="category"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              hide
            />
            <XAxis dataKey="high" type="number" hide />
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent indicator="dashed" />}
            />
            <Bar
              dataKey="high"
              layout="vertical"
              className="fill-chart-4"
              radius={4}
            >
              <LabelList
                dataKey="pageUrl"
                position="insideLeft"
                offset={8}
                className="fill-black font-semibold"
                fontSize={12}
              />
              <LabelList
                dataKey="high"
                position="right"
                offset={8}
                className="fill-black"
                fontSize={12}
              />
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          Showing how long each user stayed on a page.
        </div>
      </CardFooter>
    </Card>
  );
}
