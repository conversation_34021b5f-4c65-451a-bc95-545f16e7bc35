import { TClient } from "~/services/clients.service";

export const filterClients = (clients: TClient[], searchTerm: string) => {
  const term = searchTerm.toLowerCase().trim();

  return clients.filter((client) => {
    return (
      client.clientName?.toLowerCase().includes(term) ||
      client.clientEmail?.toLowerCase().includes(term) ||
      client.phoneNumber?.toLowerCase().includes(term)
    );
  });
};
