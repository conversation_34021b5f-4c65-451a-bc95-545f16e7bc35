import axios, {
  AxiosHeaders,
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
} from "axios";

// Define the backend URL
const BackendURL = import.meta.env.VITE_BACKEND_URL;

const BASE_URL = `${BackendURL}/api`;

const getAccessToken = (): string | null => localStorage.getItem("PPToken");

// Create an Axios instance with the base URL
export const api: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add the authorization header
api.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    const accessToken = getAccessToken();
    if (accessToken) {
      // Ensure headers are of type AxiosHeaders, if they exist
      if (!config.headers) {
        config.headers = new AxiosHeaders();
      }
      config.headers.set("Authorization", `Bearer ${accessToken}`);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export type UserFeedbackPayload = {
  fullName: string;
  email: string;
  feedback: string;
  type: "problem" | "suggestion" | "other";
};

export const SubmitUserFeedback = async (
  payload: UserFeedbackPayload
): Promise<AxiosResponse> => {
  try {
    const response = await api.post(`/submit-feedback`, payload);
    return response;
  } catch (error) {
    throw error;
  }
};
