import {
	BaseEmailStage,
	<PERSON>minderEmailStage,
} from "@/src/services/email-settings.service";

export const defaultEmailTemplate = {
	base: (name: string): Omit<BaseEmailStage, "_id"> => {
		return {
			name: name,
			emailTemplate: "<h1>{{first}} Thank you for booking</h1>",
			emailSubject: "{{first}}, Thank you for booking",
			attachedServices: [],
		};
	},
	reminder: function reminder(name: string): Omit<ReminderEmailStage, "_id"> {
		return {
			...this.base(name),
			hoursBeforeAppointment: 1,
		};
	},
};
