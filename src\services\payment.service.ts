import { AxiosResponse } from "axios";
import { api } from "./api.service";
import { SubscriptionPlan } from "../interfaces/subscription-plan";

export const CreateSubscription = async (payload: {
  userId: string;
  planId: string;
  paymentMethodId: string;
}): Promise<
  AxiosResponse<{
    clientSecret: string;
  }>
> => {
  try {
    const response = await api.put(`/create-subscription`, payload);
    return response.data;
  } catch (error) {
    console.error("post-create-subscription error:", error);
    throw error;
  }
};

type UserSubscription = {
  user: string;
  subscriptionPlan: string;
  stripeSubscriptionId: string;
  status:
    | "incomplete"
    | "incomplete_expired"
    | "trialing"
    | "active"
    | "past_due"
    | "canceled"
    | "unpaid"
    | "paused";
  startDate: Date;
  endDate: Date;
  nextBillingDate?: Date;
};

export const GetCurrentUserSubscriptionPlan = async (): Promise<
  AxiosResponse<{
    plan: SubscriptionPlan;
  }>
> => {
  try {
    const response = await api.get(`/user-subscription-plan`);
    return response;
  } catch (error) {
    console.error(error)
    throw error;
  }
};

type ChangeSubscriptionPlanResponse = {
  success: boolean;
  message: string;
  subscription: UserSubscription;
  clientSecret: string;
};

/**
 * @uiflow show the pricing plans -> user selects a plan -> user is prompted to pay -> user pays -> user is subscribed to the new plan
 * @dev responds a 404 with error property if things go right
 * @dev can return a 200
 * @dev the clientSecret is used to pay with stripe for the new plan.
 */
export const ChangeSubscriptionPlan = async (newPlanId: string): Promise<AxiosResponse<ChangeSubscriptionPlanResponse>> => {
  try {
    const response = await api.post(`/create-subscription`, {
      planId: newPlanId,
    });
    return response;
  } catch (error) {
    console.error(error)
    throw error;
  }
};
