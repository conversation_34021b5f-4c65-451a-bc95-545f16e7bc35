import { api } from './api.service';
import {
  IntakeForm,
  CreateIntakeFormRequest,
  AddQuestionRequest,
  ReorderQuestionsRequest,
  UpdateQuestionRequest
} from '@/src/interfaces/intake-form';

export const GetIntakeForm = async (serviceId: string) => {
  const response = await api.get<{ intakeForm: IntakeForm }>(`/intake-form/${serviceId}`);
  return response;
};

export const UpdateIntakeForm = async (p: { serviceId: string, data: CreateIntakeFormRequest }) => {
  const { serviceId, data } = p
  const response = await api.put<{ intakeForm: IntakeForm }>(`/intake-form/${serviceId}`, data);
  return response;
};

export const AddQuestion = async (p: { serviceId: string, data: AddQuestionRequest }) => {

  const { serviceId, data } = p
  const response = await api.post<{ intakeForm: IntakeForm }>(`/intake-form/${serviceId}/questions`, data);
  return response;
};

export const UpdateQuestion = async (p: { serviceId: string, data: UpdateQuestionRequest, questionId: string }) => {

  const { serviceId, data, questionId } = p
  const response = await api.put<{ intakeForm: IntakeForm }>(`/intake-form/${serviceId}/questions/${questionId}`, data);
  return response;
};

export const DeleteQuestion = async (p: { serviceId: string, questionId: string }) => {
  const { questionId, serviceId } = p;
  const response = await api.delete<{ intakeForm: IntakeForm }>(`/intake-form/${serviceId}/questions/${questionId}`);
  return response;
};

export const ReorderQuestions = async (p: { serviceId: string, data: ReorderQuestionsRequest }) => {
  const { serviceId, data } = p;
  const response = await api.put<{ intakeForm: IntakeForm }>(`/intake-form/${serviceId}/reorder`, data);
  return response;
};

export const ToggleIntakeFormStatus = async (p: { serviceId: string, isActive: boolean }) => {
  const { serviceId, isActive } = p
  const response = await api.patch<{ intakeForm: IntakeForm }>(`/intake-form/${serviceId}/toggle-status`, { isActive });
  return response;
};

export const GetPublicIntakeForm = async (payload: { username: string, serviceUrlName: string }) => {
  const { username, serviceUrlName } = payload
  const response = await api.get<{ intakeForm: IntakeForm }>(`/intake-form/public/${username}/${serviceUrlName}`);
  return response;
};
