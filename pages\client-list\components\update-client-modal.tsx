import { TClient } from "~/services/clients.service";
import { X } from "lucide-react";
import Modal from "@/components/ui/modal";
import { Typography } from "@/components/typography";
import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";

type Props = {
  open: boolean;
  client: TClient | null;
  setClient: (client: TClient) => void;
  onClose: () => void;
  onSave: (client: TClient) => void;
};

export default function UpdateClientModal({
  open,
  onClose,
  client,
  setClient,
  onSave,
}: Props) {
  if (!client) return null;
  return (
    <Modal open={open} onClose={onClose}>
      <Modal.Body
        className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Edit Client
          </Typography>
          <Button
            variant="icon"
            onTap={onClose}
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        {/* Form */}
        <div className="py-6 space-y-4">
          <Input.Text
            label="Client Name"
            name="client_name"
            value={client.clientName}
            onChange={(e) => {
              setClient({ ...client, clientName: e.target.value });
            }}
          />
          <Input.Email
            label="Email Address"
            name="client_email"
            value={client.clientEmail}
            onChange={(e) => {
              setClient({ ...client, clientEmail: e.target.value });
            }}
          />
          <Input.Phone
            label="Phone Number"
            name="client_phone"
            value={client.phoneNumber}
            onChange={(value) => {
              setClient({ ...client, phoneNumber: value });
            }}
          />
        </div>
        <div className="w-full flex justify-between items-center mt-6">
          <Button
            onTap={() => {
              onSave(client);
              onClose();
            }}
            className="w-full"
          >
            Save
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
}
