import { Library } from "lucide-react";
import "./buttons.css";

const Buttons = () => {
  return (
    <div className="flex flex-col gap-y-7 pb-6">
      <button id="wavy-button" className="flex !pl-5 items-center gap-x-3">
        <span className="flex items-center justify-center gap-x-3  ">
          <Library /> Whatsapp
        </span>
        <span></span>
      </button>
      <button id="background-move" className="flex !pl-5 items-center gap-x-3">
        <span className="flex items-center justify-center gap-x-3  ">
          <Library /> Whatsapp
        </span>
        <span></span>
      </button>
      <button id="perspective" className="flex !pl-5 items-center gap-x-3">
        <span className="flex items-center justify-center gap-x-3  ">
          <Library /> Whatsapp
        </span>
        <span></span>
      </button>
      <button id="simple" className="flex !pl-5 items-center gap-x-3">
        <span className="flex items-center justify-center gap-x-3  ">
          <Library /> Whatsapp
        </span>
        <span></span>
      </button>
      <button id="cover" role="button">
        <span className="flex items-center justify-center gap-x-3  ">
          <Library /> Whatsapp
        </span>
        <span>https://wa.me/+2348103597492</span>
      </button>
    </div>
  );
};

export default Buttons;
