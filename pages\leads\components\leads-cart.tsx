import { Button } from "@/components/buttons";
import Avatar from "@/components/ui/avatar";
import CircularLoader from "@/components/ui/circular-loader";
import useAuthStore from "@/pages/auth/components/auth-store";
import { useMutation } from "@tanstack/react-query";
import { X } from "lucide-react";
import { useMemo } from "react";
import { useToast } from "~/contexts/hooks/toast";
import {
  CheckoutCart,
  LeadsCart as TLeadsCart,
} from "~/services/leads.service";
import { CreditIcon } from "./badges";
import Popover from "@/components/ui/popover";

type Props = {
  onRemoveLead: (leadId: string) => void;
  onLeadsPurchased: VoidFunction;
  leadsCart: TLeadsCart["leads"];
  cartId: string;
};

const LeadsCartPopoverContent = (props: Props) => {
  const totalPriceMemo = useMemo(() => {
    return props.leadsCart.reduce((acc, lead) => {
      return acc + lead.requiredCredit;
    }, 0);
  }, [props.leadsCart]);
  const showToast = useToast();
  const { isPending, mutateAsync } = useMutation({
    mutationFn: async () => {
      return await CheckoutCart(props.cartId);
    },
    onSuccess() {
      props.onLeadsPurchased();
      showToast("success", "Purchase successful", {
        description: "You have successfully purchased the leads in your cart.",
      });
    },
    onError() {
      showToast("error", "Purchase failed", {
        description:
          "There was an error while processing your purchase. Please try again later.",
      });
    },
  });
  const {
    initialState: { user },
  } = useAuthStore();
  /**
   * @todo that purchase check is too naive, it should be more robust.
   */
  const checkoutCart = async () => {
    if (props.leadsCart.length === 0)
      return showToast("error", "There are no leads in your cart", {
        description: "Please add leads to your cart before checking out.",
      });
    await mutateAsync();
  };

  return (
    <Popover.Content className="w-80 min-h-fit -right-14 ">
      <section className="p-4">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Avatar
              alt="User"
              size={32}
              className="text-white min-w-[32px] "
              src={user?.user_photo}
            />
            <div className="space-y-1">
              <p className="text-sm ">
                Hi, {user?.first_name} {user?.last_name}{" "}
              </p>
              {props.cartId && (
                <p className="text-xs text-gray-500 line-clamp-1 ">
                  Cart ID: {props.cartId}
                </p>
              )}
            </div>
          </div>
          <Popover.Close className="cursor-pointer">
            <Button
              variant="icon"
              className="text-gray-500 bg-transparent hover:text-gray-700"
            >
              <X size={20} />
            </Button>
          </Popover.Close>
        </div>

        <div className="max-h-44 overflow-y-auto no-scrollbar  pr-2 relative">
          {props.leadsCart.map((lead) => (
            <div key={lead._id} className="mb-4">
              <div className="flex items-start justify-between">
                <div
                  className={`min-w-10 w-10 h-10 rounded-full flex items-center justify-center text-white font-bold ${
                    lead.name.charAt(0) === "M"
                      ? "bg-yellow-500"
                      : "bg-green-500"
                  }`}
                >
                  {lead.name.charAt(0)}
                </div>
                <div className="w-[60%] ml-3">
                  <div className="text-lg font-semibold line-clamp-1 w-full overflow-hidden overflow-ellipsis whitespace-nowrap ">
                    {lead.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {lead.requiredCredit} credits
                  </div>
                </div>
                <Button
                  variant="ghost"
                  onTap={() => props.onRemoveLead(lead._id)}
                  className="text-xs ml-auto px-0 text-blue-500 cursor-pointer"
                >
                  Remove
                </Button>
              </div>
            </div>
          ))}
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center text-lg font-semibold">
            <span>Total:</span>
            <span className="flex gap-x-2 items-center">
              <CreditIcon /> {totalPriceMemo}
            </span>
          </div>
          <div className="flex justify-between items-center text-lg font-semibold">
            <span>Available:</span>
            <span className="flex gap-x-2 items-center">
              <CreditIcon /> {user?.totalCredit || 0}
            </span>
          </div>
          <Button
            disabled={props.leadsCart.length === 0}
            onTap={checkoutCart}
            className="w-full flex items-center justify-center capitalize"
          >
            {isPending ? (
              <CircularLoader color="white" />
            ) : (
              "Checkout"
            )}
          </Button>
        </div>
      </section>
    </Popover.Content>
  );
};

export default LeadsCartPopoverContent;
