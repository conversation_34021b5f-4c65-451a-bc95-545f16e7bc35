import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from "recharts";
import { StaffAnalytics } from "@/src/services/staff-performance.service";

interface PerformanceTrendsChartProps {
  analytics: StaffAnalytics | undefined;
  periodType: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
}

const PerformanceTrendsChart: React.FC<PerformanceTrendsChartProps> = ({
  analytics,
  periodType
}) => {
  if (!analytics?.performanceTrends) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Performance Trends</CardTitle>
        </CardHeader>
        <CardContent className="p-6 text-center text-gray-500">
          No trend data available
        </CardContent>
      </Card>
    );
  }

  const chartData = analytics.performanceTrends.map(trend => ({
    period: trend.period,
    efficiency: trend.efficiency * 100,
    reliability: trend.reliability * 100,
    satisfaction: trend.satisfaction,
    revenue: trend.revenue
  }));

  const formatPeriod = (period: string) => {
    const date = new Date(period);
    switch (periodType) {
      case 'daily':
        return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
      case 'weekly':
        return `Week ${Math.ceil(date.getDate() / 7)}`;
      case 'monthly':
        return date.toLocaleDateString('en-US', { month: 'short' });
      case 'quarterly':
        return `Q${Math.ceil((date.getMonth() + 1) / 3)}`;
      case 'yearly':
        return date.getFullYear().toString();
      default:
        return period;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Performance Trends</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="period" 
              tickFormatter={formatPeriod}
              tick={{ fontSize: 12 }}
            />
            <YAxis 
              yAxisId="percentage"
              domain={[0, 100]}
              label={{ value: 'Percentage (%)', angle: -90, position: 'insideLeft' }}
            />
            <YAxis 
              yAxisId="rating"
              orientation="right"
              domain={[0, 5]}
              label={{ value: 'Rating (1-5)', angle: 90, position: 'insideRight' }}
            />
            <Tooltip 
              formatter={(value: number, name: string) => {
                if (name === 'satisfaction') return [`${value.toFixed(1)}/5`, 'Satisfaction'];
                return [`${value.toFixed(1)}%`, name.charAt(0).toUpperCase() + name.slice(1)];
              }}
              labelFormatter={formatPeriod}
            />
            <Legend />
            <Line 
              yAxisId="percentage"
              type="monotone" 
              dataKey="efficiency" 
              stroke="#3B82F6" 
              strokeWidth={2}
              name="Efficiency"
            />
            <Line 
              yAxisId="percentage"
              type="monotone" 
              dataKey="reliability" 
              stroke="#10B981" 
              strokeWidth={2}
              name="Reliability"
            />
            <Line 
              yAxisId="rating"
              type="monotone" 
              dataKey="satisfaction" 
              stroke="#F59E0B" 
              strokeWidth={2}
              name="Satisfaction"
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default PerformanceTrendsChart;