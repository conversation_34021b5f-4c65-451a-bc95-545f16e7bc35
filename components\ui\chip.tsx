import { cn } from "@/lib/utils";
import type { FC, ReactNode } from "react";
import { Button } from "../buttons";
import { Xmark } from "@gravity-ui/icons";

type Props = {
  label?: string;
  onDelete: () => void;
  className?: string;
  children?: ReactNode;
};

const Chip: FC<Props> = ({
  label,
  onDelete,
  className,
  children,
  ...props
}) => {
  return (
    <div
      {...props}
      className={cn(
        "bg-[#DFE1DB]/30 shadow-sm pl-3 pr-1.5 py-1.5 w-fit flex items-center gap-x-2 rounded-full text-sm font-medium",
      )}
    >
      <div className={cn("flex-grow truncate", className)}>
        {label ?? children}
      </div>
      <Button
        onTap={onDelete}
        variant="icon"
        className="rounded-full !p-1 bg-[#DFE1DB]/60 "
      >
        <Xmark className="text-gray-500 " width={12} height={12} />
      </Button>
    </div>
  );
};

export default Chip;
