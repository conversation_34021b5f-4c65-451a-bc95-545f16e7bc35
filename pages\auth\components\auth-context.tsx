import { SubscriptionPlan } from '~/interfaces/subscription-plan';
import { createContext, Dispatch, SetStateAction, useContext } from 'react';

export type AuthContextType = {
  // add more things here
  clientSecret: string | null;
  setClientSecret: Dispatch<SetStateAction<AuthContextType['clientSecret']>>
  plan: (SubscriptionPlan & {
    selectedInterval: 'week' | 'month' | 'year'
  }) | null;
  setPlan: Dispatch<SetStateAction<AuthContextType['plan']>>
};

const AuthContext = createContext<AuthContextType | null>(null);

const useAuth = () => {
  const authContext = useContext(AuthContext);
  if (!authContext) {
    throw new Error('useAuth must be used within a AuthContextProvider');
  }
  return authContext;
}

const AuthContextProvider = ({ children, value }: { children: React.ReactNode, value: AuthContextType }) => {
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export { AuthContextProvider, useAuth };

