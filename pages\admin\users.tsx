import { pick } from "@/lib/helpers";
import { useAdminUserApis } from "./use-admin";
import Toggle from "@/components/ui/toggle-2";
import PendingOverlay from "@/components/ui/pending-overlay";
import { Copy, PencilIcon, Trash2Icon } from "lucide-react";
import { Button } from "@/components/buttons";
import { useToast } from "@/src/contexts/hooks/toast";
import Modal from "@/components/ui/modal";
import { Typography } from "@/components/typography";
import { useState } from "react";
import { Input } from "@/components/inputs";
import { string } from "yup";
import CircularLoader from "@/components/ui/circular-loader";
import Dialog from "@/components/ui/dialog";

const properties: Array<keyof ExtendedUser> = [
  "isDeactivated",
  "_id",
  "first_name",
  "last_name",
  "username",
  "email",
  "role",
  "business_bio",
  "business_name",
  "planId",
  "bookingLink",
  "isEmailVerified",
  "isDeleted",
  "bookingLink",
  "totalCredit",
  "createdAt",
  "updatedAt",
] as const;

const fields = [
  {
    label: "First Name",
    name: "first_name",
    type: "text",
  },
  {
    label: "Last Name",
    name: "last_name",
    type: "text",
  },
  {
    label: "Email",
    name: "email",
    type: "email",
  },
  {
    label: "Username",
    name: "username",
    type: "text",
  },
] as const;

const FieldTypeMap = {
  text: Input.Text,
  email: Input.Email,
};

const passwordValidationSchema = string()
  .required("Password is required")
  .matches(/.{8,}$/, "Must contain at least 8 characters")
  .matches(/(?=.*\d)/, "Must contain at least one number")
  .matches(/^(?=.*[A-Z])/, "Must contain at least one uppercase letter");

const allowEditList = ["first_name", "last_name", "username", "email"] as const;

const Users = () => {
  const {
    allUsers,
    isAllUsersFetching,
    toggleUserStatusMutation,
    deleteUserMutation,
    editUserMutation,
  } = useAdminUserApis();
  const showToast = useToast();
  const [modals, setModals] = useState<
    Record<
      "deleteUser" | "editUser",
      { open: boolean; user: ExtendedUser | null }
    >
  >({
    deleteUser: {
      open: false,
      user: null,
    },
    editUser: {
      open: false,
      user: null,
    },
  });
  const modalFunctions = {
    openModal: (modal: keyof typeof modals, user: ExtendedUser) => {
      setModals((p) => ({
        ...p,
        [modal]: {
          ...p[modal],
          user: user,
          open: true,
        },
      }));
    },
    closeModal: (modal: keyof typeof modals) => {
      setModals((p) => ({
        ...p,
        [modal]: {
          ...p[modal],
          open: false,
        },
      }));
    },
  };
  return (
    <section className=" md:max-w-[760px] overflow-x-auto no-scrollbar ">
      <div className=" w-full mr-6 grid grid-cols-[180px_repeat(16,minmax(240px,350px))]">
        {properties.map((key, index) => (
          <div
            key={index}
            className=" border-b-2 last:border-r-0 px-1 pb-2 text-center text-sm"
          >
            <div className="w-full bg-gray-100 py-2 rounded-lg">{key}</div>
          </div>
        ))}
      </div>
      {isAllUsersFetching && (
        <div className="w-full flex items-center justify-center py-12">
          <CircularLoader />
        </div>
      )}
      {allUsers.map((user, index) => {
        const toBeShown = pick(user, ...properties);
        return (
          <div
            key={index}
            className=" w-full max-w-fit pr-6 grid grid-cols-[180px_repeat(16,minmax(240px,350px))] "
          >
            {Object.entries(toBeShown as ExtendedUser).map(
              ([key, value], index) => {
                const extendedUserKey = key as (typeof properties)[number];
                if (extendedUserKey === "isDeactivated")
                  return (
                    <div
                      key={index}
                      className="border-b-2 first:border-x-2 border-r-2 px-1 py-2 flex justify-center"
                    >
                      <Toggle
                        checked={Boolean(toBeShown.isDeactivated)}
                        onChange={(checked) => {
                          toggleUserStatusMutation.mutateAsync({
                            userId: toBeShown._id,
                            isActive: checked,
                          });
                        }}
                      />
                    </div>
                  );
                else
                  return (
                    <div
                      key={index}
                      className="first:border-x-2 border-r-2 border-b-2 pt-2 pb-3 px-4 group relative cursor-pointer"
                    >
                      <div className="absolute w-full h-full bg-gray-50/70 backdrop-blur-3xl top-0 left-0 flex items-center justify-center gap-x-4 z-40 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        {allowEditList.includes(
                          key as (typeof allowEditList)[number]
                        ) && (
                          <Button
                            onTap={() =>
                              modalFunctions.openModal("editUser", user)
                            }
                            variant="icon"
                            className="bg-gray-200"
                          >
                            <PencilIcon size={16} className="" />
                          </Button>
                        )}
                        <Button
                          onTap={() => {
                            navigator.clipboard
                              .writeText(value as string)
                              .then(() =>
                                showToast("success", "Copied to clipboard")
                              )
                              .catch((err) =>
                                showToast(
                                  "error",
                                  "Failed to copy to clipboard",
                                  {
                                    description: err.message,
                                  }
                                )
                              );
                          }}
                          variant="icon"
                          className="bg-gray-200"
                        >
                          <Copy size={16} className="" />
                        </Button>
                        <Button
                          onTap={() =>
                            modalFunctions.openModal("deleteUser", toBeShown)
                          }
                          variant="icon"
                          className="bg-gray-200"
                        >
                          <Trash2Icon size={16} className="" />
                        </Button>
                      </div>
                      <p className="text-center line-clamp-1 text-sm">
                        {!value ? "----" : value}
                      </p>
                    </div>
                  );
              }
            )}
          </div>
        );
      })}
      <PendingOverlay
        isPending={
          toggleUserStatusMutation.isPending ||
          deleteUserMutation.isPending ||
          editUserMutation.isPending
        }
      />
      {/* edit user modal */}
      <Modal
        open={modals.editUser.open}
        onClose={() => modalFunctions.closeModal("editUser")}
      >
        <Modal.Body
          className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
        >
          <div className="w-full flex justify-between items-center">
            <Typography variant={"h4"} className="font-bold ">
              Edit user
            </Typography>
          </div>
          <div className="w-full grid gap-y-3">
            {fields.map((field) => {
              const InputType = FieldTypeMap[field.type];
              return (
                <InputType
                  {...field}
                  onChange={({ target: { value } }) => {
                    // @ts-ignore
                    setModals((p) => ({
                      ...p,
                      editUser: {
                        ...p.editUser,
                        user: {
                          ...p.editUser.user,
                          [field.name]: value,
                        },
                      },
                    }));
                  }}
                  value={modals.editUser.user?.[field.name] || ""}
                  key={field.label}
                  className="w-full"
                />
              );
            })}
            <Input.Password
              label={"Password"}
              name={"password"}
              onChange={({ target: { value } }) => {
                // @ts-ignore
                setModals((p) => ({
                  ...p,
                  editUser: {
                    ...p.editUser,
                    user: {
                      ...p.editUser.user,
                      password: value,
                    },
                  },
                }));
              }}
              required={false}
              value={modals.editUser.user?.password || ""}
              validationSchema={passwordValidationSchema}
            />
          </div>
          <div className="w-full flex justify-between items-center mt-6">
            <Button
              onTap={() => modalFunctions.closeModal("editUser")}
              className="w-full"
              variant="ghost"
            >
              Cancel
            </Button>
            <Button
              onTap={() => {
                if (!modals.editUser.user) return;
                editUserMutation.mutateAsync(modals.editUser.user);
                modalFunctions.closeModal("editUser");
              }}
              className="w-full"
            >
              Save
            </Button>
          </div>
        </Modal.Body>
      </Modal>
      {/* delete user modal */}
      <Dialog
        onClose={() => modalFunctions.closeModal("deleteUser")}
        open={modals.deleteUser.open}
        title={`Delete user`}
        description={`Are you sure you want to delete user ${modals.deleteUser.user?._id}?`}
        action={{
          title: `Yes, I'm sure`,
          onConfirm: () => {
            if (!modals.deleteUser.user) return;
            deleteUserMutation.mutateAsync(modals.deleteUser.user?._id);
            modalFunctions.closeModal("deleteUser");
          },
        }}
      />
    </section>
  );
};

export default Users;
