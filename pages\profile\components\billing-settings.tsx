import { SaveButton } from "@/components/buttons";
import { Typography } from "@/components/typography";
import PendingOverlay from "@/components/ui/pending-overlay";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import { noop } from "@/lib/helpers";
import useAuthStore from "@/pages/auth/components/auth-store";
import { useToast } from "@/src/contexts/hooks/toast";
import { UpdateBillingSettings } from "@/src/services/settings.service";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import * as Yup from "yup";

/**
 * @dev this is the real actual settings here
 * */
const BillingSettings = () => {
  const { setUser, initialState } = useAuthStore();
  const updateBillingSettingsMutation = useMutation({
    mutationFn: UpdateBillingSettings,
    onSuccess(data) {
      showToast("success", data.data?.message);
      if (data.data)
        setUser((p) => ({
          ...p,
          userSettings: {
            ...p.userSettings,
            ...data.data.settings,
          },
        }));
    },
  });
  const formik = useFormik({
    validationSchema: Yup.object({
      preferredCurrency: Yup.string()
        .required("Preferred currency is required")
        .oneOf(["usd", "gbp"], "Currency must be either usd or gbp"),
    }),
    initialValues: {
      preferredCurrency:
        initialState.userSettings?.billingSettings?.preferredCurrency ||
        ("usd" as "usd" | "gbp"),
    },
    onSubmit: noop,
  });
  const showToast = useToast();
  const handleSubmit = async () => {
    formik.validateForm().then((val) => {
      const errors = Object.values(val);
      if (errors.length === 0) {
        const { values } = formik;
        updateBillingSettingsMutation.mutateAsync({
          ...values,
        });
      } else
        errors.forEach((error, i) =>
          setTimeout(() => {
            showToast("error", "Invalid Field", {
              description:
                typeof error === "object" ? (error as any).name : error,
            });
          }, i * 500),
        );
    });
  };
  const supportedCurrencies = ["usd", "gbp"] as const;
  return (
    <section className="flex flex-col gap-y-3 pb-20">
      <PendingOverlay isPending={updateBillingSettingsMutation.isPending} />
      <div className="w-full flex flex-wrap gap-y-3 items-center justify-between ">
        <Typography className=" text-paragraph">Preferred Currency</Typography>
        <SearchableDropdown
          options={supportedCurrencies.map((cur) => ({
            label: cur.toUpperCase(),
            value: cur,
          }))}
          value={{
            value: formik.values.preferredCurrency || "",
            label: formik.values.preferredCurrency.toUpperCase() || "",
          }}
          onChange={(option) => {
            formik.setFieldValue("preferredCurrency", option?.value || "");
          }}
          className="max-w-[240px] text-sm"
          fullWidth
        />
      </div>
      <SaveButton loading={updateBillingSettingsMutation.isPending} disabled={updateBillingSettingsMutation.isPending} onTap={() => handleSubmit()}>
      </SaveButton>
    </section>
  );
};

export default BillingSettings;
