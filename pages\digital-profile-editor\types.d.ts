type URLString = string;

export interface IWebsiteLink extends MongoDocument {
  title: string;
  url: string;
  icon: string | null;
  isActive: boolean;
  /** 
   * @dev this is the name of the button style in the theme array
   * */
  buttonStyle: string
  thumbNail?: URLString
  thumbNailPublicId?: string
  /** 
   * @dev this is the checker for link or collection of links
   * */
  type?: 'link' | 'collection'
  category?: 'suggested' | 'instagram' | 'tiktok' | 'youtube' | 'spotify' | 'digital-products' | 'custom';
  description?: string;
  clickCount?: number;
  order: number;
  metadata?: any; // Flexible for different link types
}

export interface ITheme {
  name: string;
  colors: {
    primary?: string;
    secondary?: string;
    accent?: string;
    background?: string;
    text?: string;
  };
  fonts?: {
    heading?: string;
    body?: string;
  };
  layout?: 'default' | 'centered' | 'grid' | 'list';
  backgroundType?: 'solid' | 'gradient' | 'image' | 'video';
  backgroundValue?: string;
  customCSS?: string;
  buttonStyles:
  {
    name: string;
    style:
    {
      background: string;
      color: string;
      borderColor?: string;
      borderRadius: string;
      borderWidth?: string;
      fontWeight: string;
      padding: string;
      fontSize: string;
      hoverEffect?: string;
      boxShadow?: string;
      gradient?: string;
    };
  }[]

}

/** 
 * @deprecated 
 * @notimplemented
 * */
export interface IAnalytics {
  totalViews: number;
  totalClicks: number;
  dailyStats?: Array<{
    date: Date;
    views: number;
    clicks: number;
    topLinks?: Array<{
      linkId: string;
      clicks: number;
    }>;
  }>;
  referrers?: Array<{
    source: string;
    count: number;
  }>;
  countries?: Array<{
    country: string;
    count: number;
  }>;
  devices?: Array<{
    type: 'desktop' | 'mobile' | 'tablet';
    count: number;
  }>;
}

/** 
 * @dev this is for a list of all the social links, it will be displayed in section;
 * @see https://claude.ai/share/61aa5194-df28-4763-b8b3-f45a5f3214f7
 * */
export type ISocialMedia = {
  type: 'facebook'
  | 'twitter'
  | 'instagram'
  | 'linkedin'
  | 'youtube'
  | 'tiktok'
  | 'pinterest'
  | 'snapchat'
  | 'whatsapp';
  url: string
} | {
  type: 'custom'
  url: string;
  icon: '' | (string & {})
}

export interface ISettings {
  isPublic: boolean;
  showAnalytics: boolean;
  socialMediaIconsPosition: 'top' | 'bottom'
}

export interface DigitalProfileType extends MongoDocument {
  userId: string;
  profileUrl: string;

  // Enhanced website links
  websiteLinks: IWebsiteLink[];

  // Media
  imageUrl: string | null;
  hasImage: boolean;
  imagePublicId: string;
  coverImage?: {
    url?: string;
    publicId?: string;
  };

  // Basic details (enhanced)
  details: {
    title: string;
    bio: string;
    displayName?: string;
  };

  theme?: ITheme;

  // Template & Industry
  template?: 'professional-services' | 'legal-firm' | 'accounting-finance' | 'saas-company' |
  'development-agency' | 'it-services' | 'medical-practice' | 'wellness-coach' |
  'dental-specialty' | 'lead-generation' | 'content-marketing' | 'sales-ecommerce' |
  'event-networking' | 'custom';

  // Social
  socialMedia?: ISocialMedia[];

  // Settings
  settings?: ISettings;

  // Analytics
  analytics?: IAnalytics;

  // Status & Plan
  status?: 'active' | 'inactive' | 'suspended';

  // Metadata
  lastViewedAt?: Date;
  featuredUntil?: Date;
}

