import { <PERSON><PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Menu from "@/components/ui/menu";
import { cn } from "@/lib/utils";
import { PTORequest } from "@/src/interfaces/time-off";
import { Staff } from "@/src/interfaces/staff";
import { useTimeOff } from "../../hooks/use-time-off";
import { useModalsBuilder } from "@/lib/modals-builder";
import { format } from "date-fns";
import {
  Edit02Icon,
  Delete02Icon,
  MoreVerticalIcon,
  CheckmarkCircle02Icon,
  Cancel01Icon,
} from "hugeicons-react";
import ApproveRejectModal from "./approve-reject-modal";

interface TimeOffCardProps {
  pto: PTORequest;
  staff?: Staff;
  onEdit: (ptoId: string) => void;
}

const TYPE_COLORS = {
  vacation: "bg-blue-100 text-blue-800",
  sick: "bg-red-100 text-red-800",
  personal: "bg-purple-100 text-purple-800",
  meeting: "bg-green-100 text-green-800",
  training: "bg-yellow-100 text-yellow-800",
  other: "bg-gray-100 text-gray-800",
};

const TimeOffCard: React.FC<TimeOffCardProps> = ({ pto, staff, onEdit }) => {
  const { deleteTimeOffMutation } = useTimeOff();
  const { modals, modalFunctions } = useModalsBuilder({
    approveReject: {
      open: false,
      data: null as PTORequest | null,
    },
  });

  const getStatusBadge = () => {
    if (pto.isApproved) {
      return (
        <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckmarkCircle02Icon size={12} />
          Approved
        </span>
      );
    } else if (pto.approvedAt && !pto.isApproved) {
      return (
        <span className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <Cancel01Icon size={12} />
          Rejected
        </span>
      );
    } else {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          Pending
        </span>
      );
    }
  };

  const formatDateRange = () => {
    const startDate = format(new Date(pto.startDate), "MMM dd, yyyy");
    const endDate = format(new Date(pto.endDate), "MMM dd, yyyy");

    if (pto.startDate === pto.endDate) {
      return startDate;
    }
    return `${startDate} - ${endDate}`;
  };

  const formatTimeRange = () => {
    if (pto.startTime && pto.endTime) {
      return `${pto.startTime} - ${pto.endTime}`;
    }
    return "All day";
  };

  return (
    <>
      <div className="bg-white rounded-lg border p-4 space-y-3 hover:shadow-md transition-shadow">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <span className={cn("px-2 py-1 rounded-full text-xs font-medium", TYPE_COLORS[pto.type])}>
                {pto.type.charAt(0).toUpperCase() + pto.type.slice(1)}
              </span>
              {getStatusBadge()}
              {pto.isRecurring && (
                <span className="px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                  Recurring
                </span>
              )}
            </div>

            <Typography variant="h4" className="font-medium mb-1">
              {staff?.name || "Unknown Staff"}
            </Typography>

            <Typography variant="p" className="text-sm text-gray-600 mb-2">
              {pto.description}
            </Typography>

            <div className="space-y-1">
              <Typography variant="p" className="text-sm font-medium">
                {formatDateRange()}
              </Typography>
              <Typography variant="p" className="text-xs text-gray-500">
                {formatTimeRange()}
              </Typography>
            </div>
          </div>

          <Menu>
            <Menu.Trigger className="cursor-pointer p-1">
              <Button className="bg-transparent w-fit text-gray-500 rounded-lg p-0">
                <MoreVerticalIcon size={20} strokeWidth={3} />
              </Button>
            </Menu.Trigger>
            <Menu.Content className="h-fit focus:outline-none min-w-fit !min-h-0 right-0 whitespace-nowrap">
              <Menu.Item
                className="flex items-center gap-x-4 pr-5"
                onClick={() => onEdit(pto._id!)}
              >
                Edit <Edit02Icon width={16} height={16} />
              </Menu.Item>

              {!pto.isApproved && !pto.approvedAt && (
                <Menu.Item
                  className="flex items-center gap-x-4 pr-5"
                  onClick={() => modalFunctions.openModal("approveReject", { data: pto })}
                >
                  Approve/Reject <CheckmarkCircle02Icon width={16} height={16} />
                </Menu.Item>
              )}

              <Menu.Item
                className="flex items-center gap-x-4 pr-5 text-red-600"
                onClick={() => {
                  if (confirm("Are you sure you want to delete this time-off request?")) {
                    deleteTimeOffMutation.mutate(pto._id!);
                  }
                }}
              >
                Delete <Delete02Icon width={16} height={16} />
              </Menu.Item>
            </Menu.Content>
          </Menu>
        </div>

        {pto.approvedBy && (
          <div className="pt-2 border-t">
            <Typography variant="p" className="text-xs text-gray-500">
              {pto.isApproved ? "Approved" : "Rejected"} by {pto.approvedBy}
              {pto.approvedAt && ` on ${format(new Date(pto.approvedAt), "MMM dd, yyyy")}`}
            </Typography>
          </div>
        )}
      </div>

      {modals.approveReject.open && modals.approveReject.data && (
        <ApproveRejectModal
          open={modals.approveReject.open}
          onClose={modalFunctions.returnClose("approveReject")}
          pto={modals.approveReject.data}
        />
      )}
    </>
  );
};

export default TimeOffCard;
