import React, { useState } from "react";
import { type SubscriptionPlanPayload } from "./index";
import { Input } from "@/components/inputs";
import { isUnwantedInArray, pick } from "@/lib/helpers";
import { Plus } from "@gravity-ui/icons";
import { But<PERSON> } from "@/components/buttons";
import CheckIcon from "@/src/icons/check-icon";
import PendingOverlay from "@/components/ui/pending-overlay";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  CreateProduct,
  ProductPayload,
  UpdateProduct,
} from "@/src/services/admin/subscription.service";
import { useToast } from "@/src/contexts/hooks/toast";
import { X } from "lucide-react";
import Toggle from "@/components/ui/toggle-2";

type Props =
  | {
      type: "create";
    }
  | {
      type: "update";
      plan: Omit<SubscriptionPlanPayload, 'prices'> & {
        id: string;
        onClose: VoidFunction;
      };
    };

const PlanTemplate: React.FC<Props> = (props) => {
  const [newPlan, setNewPlan] = useState<SubscriptionPlanPayload>(
    props.type === "create"
      ? {
          description: "",
          name: "",
          features: [],
          active: true,
          prices: [],
        }
      : {
          ...props.plan,
          prices: [],
        }
  );
  const createProductMutation = useMutation({
    mutationFn: CreateProduct,
  });
  const updateProductMutation = useMutation({
    mutationFn: ({ id, payload }: { id: string; payload: ProductPayload }) =>
      UpdateProduct(id, payload),
  });
  const queryClient = useQueryClient();
  const showToast = useToast();
  return (
    <div className="min-w-full md:min-w-0 max-w-[320px] h-full overflow-y-auto no-scrollbar border-2 rounded-lg relative transition-[height] duration-500 ease-[ease] border-primary-500/50 ">
      <PendingOverlay isPending={createProductMutation.isPending || updateProductMutation.isPending} />
      <div className="w-full h-1/2 px-4 pt-4 pb-5 gap-y-2 flex flex-col ">
        <Input.Text
          label="Name"
          name="name"
          value={newPlan.name}
          onChange={({ currentTarget: { value } }) =>
            setNewPlan((p) => ({
              ...p,
              name: value as string,
            }))
          }
          required
        />
        <Input.TextArea
          label="Description"
          name="description"
          value={newPlan.description}
          onChange={({ currentTarget: { value } }) =>
            setNewPlan((p) => ({
              ...p,
              description: value as string,
            }))
          }
          required
        />
      </div>
      <div className="w-full mt-auto pt-2 p-4 flex justify-between text-sm ">
        <p className="font-bold">Active:</p>
        <Toggle
          checked={newPlan.active}
          onChange={(checked) => {
            setNewPlan((p) => ({
              ...p,
              active: checked,
            }));
          }}
        />
      </div>
      <div className="w-full mt-auto pt-2 p-4 flex flex-col gap-y-2 text-sm ">
        <p className="font-bold">Includes:</p>
        <div className="w-full flex-grow flex flex-col gap-y-2">
          {newPlan?.features.map((feature, index) => (
            <div className="w-full flex items-center gap-x-3" key={index}>
              <div className="w-4 h-4">
                <CheckIcon height={16} width={16} />
              </div>
              <Input.Text
                label={`Feature ${index + 1}`}
                name={`feature-${index + 1}`}
                value={feature}
                onChange={({ currentTarget: { value } }) =>
                  setNewPlan((p) => ({
                    ...p,
                    features: p.features.map((f, i) =>
                      i === index ? value : f
                    ),
                  }))
                }
                required
              />
              <Button
                onTap={() => {
                  setNewPlan((p) => ({
                    ...p,
                    features: p.features.filter((_, i) => i !== index),
                  }));
                }}
                variant="icon"
                className="rounded-full !p-1 bg-[#DFE1DB]/60 "
              >
                <X className="text-gray-500 " size={15} />
              </Button>
            </div>
          ))}
          <Button
            onTap={() =>
              setNewPlan((p) => ({
                ...p,
                features: [...p.features, ""],
              }))
            }
            className="w-full flex justify-center mt-5 py-2 px-4 text-sm font-medium border rounded-[8px] bg-subtle-gray text-paragraph  "
          >
            <Plus width={18} height={18} />
          </Button>
        </div>
      </div>
      <div className="w-full mt-auto pt-2 p-4 flex flex-col gap-y-2 text-sm ">
        <Button
          disabled={isUnwantedInArray(
            [newPlan.description, newPlan.name, newPlan.features[0]],
            [undefined, null, "", 0]
          )}
          onTap={() => {
            if (props.type === "create") {
              createProductMutation.mutateAsync(
                {
                  ...pick(newPlan, "name", "description", "active"),
                  features: newPlan.features.filter((f) => f !== ""),
                },
                {
                  onSuccess() {
                    showToast(
                      "success",
                      `${newPlan.name} product created successfully`
                    );
                    setNewPlan((p) => ({
                      ...p,
                      name: "",
                      description: "",
                      features: [],
                      prices: [],
                    }));
                    queryClient.invalidateQueries({
                      queryKey: ["admin-subscription-plans"],
                    });
                  },
                }
              );
            } else {
              updateProductMutation.mutateAsync(
                {
                  id: props.plan.id,
                  payload: {
                    ...pick(newPlan, "name", "description", "active"),
                    features: newPlan.features.filter((f) => f !== ""),
                  },
                },
                {
                  onSuccess() {
                    showToast(
                      "success",
                      `${newPlan.name} product updated successfully`
                    );
                    setNewPlan((p) => ({
                      ...p,
                      name: "",
                      description: "",
                      features: [],
                      prices: [],
                    }));
                    queryClient.invalidateQueries({
                      queryKey: ["admin-subscription-plans"],
                    });
                    props.plan.onClose();
                  },
                }
              );
            }
          }}
          className="w-full mt-2 py-2 px-4 text-sm font-medium  rounded-[8px] "
        >
          {props.type === "create" ? "Create" : "Edit"} Product
        </Button>
      </div>
    </div>
  );
};

export default PlanTemplate;
