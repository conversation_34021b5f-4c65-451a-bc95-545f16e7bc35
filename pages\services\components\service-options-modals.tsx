import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";
import MoreInfo from "@/components/ui/more-info";
import { capitalizeFirstWord, inlineSwitch, isUnwantedInArray, noop } from "@/lib/helpers";
import useAuthStore from "@/pages/auth/components/auth-store";
import { useFormik } from "formik";
import { X } from "lucide-react";
import * as Yup from "yup";
import { ServiceAddon } from "~/services/services.service";

type AddonType = Pick<ServiceAddon, 'name' | 'description' | 'price'>

type Props = {
  open: boolean;
  onClose: VoidFunction;
  onSave: (
    option: AddonType
  ) => void;
  /** 
   * @dev boolean value for disabling price inputs
   * */
  hasStripeConnected: boolean
};
export const CreateServiceOptionModal = (props: Props) => {
  const formik = useFormik({
    initialValues: {
      name: "",
      description: "" as string | undefined,
      price: 0,
    },
    validationSchema: Yup.object({
      name: Yup.string().required("Name is required"),
      description: Yup.string().required("Description is required"),
      price: Yup.number().min(0).required("Price is required"),
    }),
    onSubmit: noop,
  });
  const { initialState: { userSettings } } = useAuthStore()
  return (
    <Modal open={props.open} onClose={props.onClose}>
      <Modal.Body
        className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Create Service Add-on
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="w-full grid gap-y-3 my-3">
          <Input.Text
            name="name"
            label="Name"
            required
            value={formik.values.name}
            onChange={({ target: { name, value } }) => {
              formik.setFieldValue(name, capitalizeFirstWord(value));
            }}
          />
          <Input.TextArea
            name="description"
            label="Description"
            value={formik.values.description}
            onChange={({ target: { name, value } }) => {
              formik.setFieldValue(name, capitalizeFirstWord(value));
            }}
          />
          <div className="flex flex-col gap-y-2" >
            <Input.Numeric
              name="price"
              disabled={props.hasStripeConnected === false}
              label={`Price (${inlineSwitch(userSettings?.billingSettings?.preferredCurrency, ['usd', 'USD'], ['gbp', 'GBP'], { default: 'USD' })})`}
              defaultValue={formik.values.price}
              onChange={({ target: { name, value } }) => {
                formik.setFieldValue(name, parseInt(value || '0'));
              }}
            />
            <MoreInfo.Static className="text-xs text-paragraph/50"
              content="Add a service add-on price if you have Stripe connected"></MoreInfo.Static>
          </div>
        </div>
        <div className="w-full ">
          <Button
            className="w-full"
            disabled={isUnwantedInArray(
              [formik.values.name, formik.values.price],
              ["", undefined, NaN, null]
            )}
            onTap={() => {
              props.onSave(formik.values as any);
              formik.resetForm();
            }}
          >
            Create
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export const EditServiceOptionModal = (props: Props & {
  option: AddonType;
}) => {
  const formik = useFormik({
    initialValues: {
      name: props.option.name,
      description: props.option.description as string | undefined,
      price: props.option.price,
    },
    validationSchema: Yup.object({
      name: Yup.string().required("Name is required"),
      description: Yup.string(),
      price: Yup.number().min(0).required("Price is required"),
    }),
    onSubmit: noop,
  });
  const { initialState: { userSettings } } = useAuthStore()

  return (
    <Modal open={props.open} onClose={props.onClose}>
      <Modal.Body
        className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Edit Service Add-on
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="w-full grid gap-y-3 my-3">
          <Input.Text
            name="name"
            label="Name"
            required
            value={formik.values.name}
            onChange={({ target: { name, value } }) => {
              formik.setFieldValue(name, capitalizeFirstWord(value));
            }}
          />
          <Input.TextArea
            name="description"
            label="Description"
            value={formik.values.description}
            onChange={({ target: { name, value } }) => {
              formik.setFieldValue(name, capitalizeFirstWord(value));
            }}
          />
          <Input.Numeric
            name="price"
            disabled={props.hasStripeConnected === false}
            label={`Price (${inlineSwitch(userSettings?.billingSettings?.preferredCurrency, ['usd', 'USD'], ['gbp', 'GBP'], { default: 'USD' })})`}
            onChange={({ target: { name, value } }) => {
              formik.setFieldValue(name, parseInt(value || '0'));
            }}
            defaultValue={formik.values.price}
          />
        </div>
        <div className="w-full ">
          <Button
            className="w-full"
            disabled={isUnwantedInArray(
              [formik.values.name, formik.values.price],
              ["", undefined, NaN, null]
            )}
            onTap={() => {
              props.onSave(formik.values as any);
              formik.resetForm();
            }}
          >
            Edit
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};
