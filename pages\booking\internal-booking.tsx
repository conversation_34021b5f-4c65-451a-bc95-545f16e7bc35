import DatePicker from "@/components/ui/calendar";
import CircularLoader from "@/components/ui/circular-loader";
import { capitalizeFirstWord, noop, pick, sleep } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import {
  BookingPayload,
} from "@/src/services/booking.service";
import { useFormik } from "formik";
import { AnimatePresence, motion } from "framer-motion";
import { useParams } from "react-router-dom";
import * as Yup from "yup";
import { useToast } from "~/contexts/hooks/toast";
import CustomerForm from "./components/customer-form";
import SuccessSection from "./components/success-section";
import { Typography } from "@/components/typography";
import { useInternalBooking } from "./components/use-booking";
import { useStepper } from "@/components/ui/stepper";
import { InternalBookingContext } from "./components/use-booking-context";
import ServiceReview from "./components/service-review";
import TimeSlotZonePicker from "./components/time-slot-zone-picker";
import { flushSync } from "react-dom";
import ConfirmationScreen from "./components/confirmation-modal";
import { formatDate } from "date-fns";
import { useEffect, useRef } from "react";

type RouteParams = {
  username: string | undefined;
  urlName: string | undefined
};

const daysOfWeek = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

const InternalBooking = () => {
  const { username, urlName } = useParams<RouteParams>();
  // First, let's combine the form values interface
  interface BookingFormValues extends BookingPayload {
    timeZone: string;
  }
  const { serviceQuery: { data: service, ...serviceQuery } } = useInternalBooking.getService({
    username,
    urlName
  })
  const bookingFormik = useFormik<BookingFormValues>({
    enableReinitialize: true,
    initialValues: {
      serviceId: service?.service._id || "",
      serviceName: service?.service.name || "",
      userId: service?.user._id || "",
      date: "",
      timeSlot: "",
      timeZone: "Europe/London", // Added from dateTimeFormik
      notes: "",
      price: service?.service.price,
      customerInfo: {
        lastName: "",
        firstName: "",
        email: "",
        phone: "",
      },
      serviceOptions: [],
    },
    validationSchema: Yup.object({
      customerInfo: Yup.object({
        firstName: Yup.string().required("First Name is required"),
        lastName: Yup.string().required("Last Name is required"),
        email: Yup.string()
          .email("Invalid email address")
          .required("Email is required"),
        phone: Yup.string()
          .required("Phone number is required")
          .min(10, "Phone number must be at least 10 digits"),
      }),
      date: Yup.string().required("Date is required"),
      timeSlot: Yup.string().required("Time slot is required"),
      timeZone: Yup.string().required("Time zone is required"),
    }),
    onSubmit: noop,
  });
  const formRef = useRef(bookingFormik);

  // Update the ref whenever formik changes
  useEffect(() => {
    formRef.current = bookingFormik;
  }, [bookingFormik]);
  const {
    workdaysQuery: { data: workDays },
    createBookingMut,
    timeslotsQuery: { data: timeSlots = [], ...timeslotsQuery }
  } = useInternalBooking({
    username, urlName,
    dateTimeState: {
      date: bookingFormik.values.date,
      timeZone: bookingFormik.values.timeZone,
    },
    serviceData: service,
  })
  const showToast = useToast()
  const { matchStep, prevStep, nextStep, Stepper } = useStepper([
    { id: 'review', title: 'Review' },
    {
      id: 'customer_form', title: 'Details',
      shouldGoNext: async () => {
        try {
          const currentFormik = formRef.current;
          await currentFormik.validateField("customerInfo");
          const customerInfoErrors = Object.values(
            currentFormik.errors.customerInfo || {}
          );
          if (customerInfoErrors.length > 0) {
            showToast("error", customerInfoErrors[0] as string);
            return false;
          }
          return true;
        } catch (error) {
          showToast("error", "Please fill in all required fields");
          return false;
        }
      },
    },
    {
      id: 'date_picker', title: 'Select Date',
      shouldGoNext: async () => {
        try {
          const currentFormik = formRef.current;
          await currentFormik.validateField("date");
          await currentFormik.validateField("timeSlot");
          await currentFormik.validateField("timeZone");
          const dateTimeErrors = Object.values(
            pick(currentFormik.errors || {}, 'date', 'timeSlot', 'timeZone')
          ).filter(Boolean);
          if (dateTimeErrors.length > 0) {
            showToast("error", dateTimeErrors[0] as string);
            return false;
          }
          return true;
        } catch (error) {
          showToast("error", "Please fill in all required fields");
          return false;
        }
      },
    },
    { id: 'confirmation', title: 'Confirmation' },
    {
      id: 'success', title: 'Success 🎉🎉',
      toShow() {
        return Boolean(createBookingMut.data?.data.success)
      }
    }
  ] as const, [service, createBookingMut.data])
  const onChangeDate = async (date: Date) => {
    bookingFormik.setValues((prev) => ({
      ...prev,
      date: date.toISOString(),
      timeSlot: "",
    }));
    await sleep(800);
    timeslotsQuery.refetch();
  };
  if (serviceQuery.isLoading) return (
    <div className="py-20">
      <CircularLoader />
    </div>
  )
  return (
    <InternalBookingContext.Provider
      value={{
        values: bookingFormik.values,
        setValue(key, newValue) {
          bookingFormik.setFieldValue(key as string, newValue)
        }
      }}
    >
      <section className="w-full h-full max-w-4xl mx-auto flex flex-col pb-10">
        <div className="w-full flex flex-col gap-y-2 justify-between items-start">
          <Typography
            variant={"h1"}
            className="font-bold font-Bricolage_Grotesque  "
          >
            Book {service?.service?.name}
          </Typography>
          <Typography className="text-sm text-subtext pl-1">
            We won't charge you for internal appointment bookings
          </Typography>
        </div>
        <section className="w-full flex flex-col justify-center items-center pb-40 relative ">
          <div className="w-full flex items-center justify-center py-7">
            <Stepper />
          </div>
          <motion.section
            className={cn(
              "w-fit h-fit bg-subtle-gray rounded-[28px] grid grid-cols-1 gap-y-4 p-6 md:px-8 md:py-8 ",
              { "!p-0": matchStep('customer_form', 'date_picker', 'confirmation', 'success') }
            )}
          >
            <AnimatePresence mode="wait" presenceAffectsLayout>
              {matchStep('review') && (
                <ServiceReview
                  bookingType="internal"
                  service={service}
                  displayPrice={false}
                  billingSettings={{
                    preferredCurrency:
                      service?.user?.billingSettings.preferredCurrency || "usd",
                  }}
                />
              )}
              {
                matchStep('customer_form') &&
                <motion.div
                  layout
                  layoutId="customer_form"
                  key={"customer-form"}
                  initial={{ opacity: 0, x: -100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, scale: 0 }}
                  className="w-full "
                >
                  <CustomerForm
                    billingSettings={{
                      preferredCurrency:
                        service?.user?.billingSettings.preferredCurrency || "usd",
                    }}
                    bookingType="internal"
                    onClickNext={() => nextStep()}
                    options={service?.service.serviceOptions || null}
                  />
                </motion.div>
              }
              {matchStep('date_picker') &&
                <motion.div
                  key={"date_picker"}
                  layout
                  layoutId="date_picker"
                  initial={{ opacity: 0, x: -100 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, scale: 0 }}
                  className="flex flex-col gap-y-5"
                >
                  <DatePicker
                    className="rounded-[28px] w-full bg-[#EAEBE5]/40 "
                    disableOldDates
                    disabledDays={
                      daysOfWeek
                        .map((day) => day.toLowerCase())
                        .filter((day) => !workDays?.includes(day as any))
                        .map((day) =>
                          capitalizeFirstWord(day.toLowerCase()),
                        ) as any
                    }
                    value={
                      bookingFormik.values.date
                        ? new Date(
                          formatDate(bookingFormik.values.date, "yyyy-MM-dd")
                        )
                        : undefined
                    }
                    onChange={onChangeDate}
                  />
                  <TimeSlotZonePicker
                    dateTime={{
                      date: bookingFormik.values.date,
                      timeSlot: bookingFormik.values.timeSlot,
                      timeZone: bookingFormik.values.timeZone,
                    }}
                    onChangeTimeZone={async (zone) => {
                      flushSync(() => {
                        bookingFormik.setValues(p => ({
                          ...p,
                          timeZone: zone,
                          timeSlot: ''
                        }));
                      });
                      await sleep(800);
                      timeslotsQuery.refetch();
                    }}
                    onChangeTimeSlot={async (slot) => {
                      flushSync(() => {
                        bookingFormik.setFieldValue("timeSlot", slot);
                      });
                    }}
                    isFetching={timeslotsQuery.isFetching}
                    timeSlots={timeSlots}
                    onClickNext={() => {
                      nextStep()
                    }}
                  />
                </motion.div>
              }
              {matchStep('confirmation') && (
                <ConfirmationScreen
                  isBookingPending={createBookingMut.isPending}
                  billingSettings={{
                    preferredCurrency:
                      service?.user?.billingSettings?.preferredCurrency || "usd",
                  }}
                  bookingType="internal"
                  serviceOptions={bookingFormik.values.serviceOptions
                    ?.map(id => service?.service.serviceOptions?.find(s => s._id === id)!)
                    || []
                  }
                  onCancel={prevStep}
                  onConfirm={() => {
                    bookingFormik.validateForm()
                      .then((val) => {
                        const errors = Object.values(val.customerInfo || {})
                        if (errors.length === 0) {
                          createBookingMut
                            .mutateAsync(bookingFormik.values, {
                              onSuccess: async () => {
                                await sleep(1000);
                                flushSync(() => {
                                  nextStep();
                                })
                              }
                            })
                        }
                        else {
                          showToast("error", errors[0])
                        }
                      })
                      .catch(err => {
                        console.error(err)
                        showToast('error', String(err))
                      })
                  }}
                />
              )}
              {matchStep('success') && (
                <SuccessSection
                  email={bookingFormik.values.customerInfo.email || ""}
                />
              )}
            </AnimatePresence>
          </motion.section>
        </section>
      </section>
    </InternalBookingContext.Provider>
  );
};

export default InternalBooking;
