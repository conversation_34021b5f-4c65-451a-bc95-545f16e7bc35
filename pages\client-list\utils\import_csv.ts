import { TClient } from "~/services/clients.service";

const removeCSVHeaderIfExists = (csvContent: string) => {
  const lines = csvContent.split("\n");
  // Only remove first line if it matches our header pattern
  if (
    /^["']?(?:Full Name|Name|Client|Client Name)["']?,["']?(?:Email|E-mail)["']?,["']?(?:Phone|Phone Number|Contact)["']?,["']?(?:Appointment|Date|Appointment Date)["']?$/i.test(
      lines[0].trim()
    )
  ) {
    // removes first element from array
    lines.shift();
  }

  return lines;
};

export function importFromCSV(
  e: React.ChangeEvent<HTMLInputElement>,
  onFinish: (clients: TClient[]) => void,
  onError: (reason: string) => void
) {
  if (e.target.files && e.target.files.length > 0) {
    const file = e.target.files[0];
    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result;
      if (typeof text === "string") {
        const lines = removeCSVHeaderIfExists(text);
        const clients: Omit<TClient, "_id">[] = lines.map((line) => {
          const [name, email, phone, date] = line.split(",");
          return {
            clientName: name?.replace(/['"]/g, ""),
            clientEmail: email?.replace(/['"]/g, ""),
            phoneNumber: phone?.replace(/['"]/g, ""),
            lastAppointmentDate:
              date &&
              date.trim() &&
              !["--", "-", "null", ""].includes(date.trim())
                ? null
                : new Date(date),
          };
        });
        onFinish(clients as TClient[]);
      } else onError("Invalid file format");
    };
    reader.readAsText(file);
  } else onError("No file selected");
}
