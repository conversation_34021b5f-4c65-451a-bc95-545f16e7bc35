
export default function LeadDetailsSkeleton() {
  return (
    <section className="w-full max-w-7xl mx-auto px-4 space-y-8 md:px-8">
      {/* Header Section */}
      <div className="flex justify-between items-center">
        <div className="h-8 w-48 bg-gray-200 rounded-md animate-pulse" />
      </div>

      {/* Service and Location */}
      <div className="w-full flex flex-col gap-y-2">
        <div className="h-6 w-36 bg-gray-200 rounded-md animate-pulse" />
        <div className="h-4 w-64 bg-gray-200 rounded-md animate-pulse mb-4" />
        <div className="h-4 w-48 bg-gray-200 rounded-md animate-pulse mb-4" />
      </div>

      {/* Contact Information */}
      <div className="flex flex-col gap-y-4">
        <div className="flex items-center gap-2">
          <div className="h-5 w-5 bg-gray-200 rounded-full animate-pulse" />
          <div className="h-4 w-32 bg-gray-200 rounded-md animate-pulse" />
        </div>
        <div className="flex items-center gap-2">
          <div className="h-5 w-5 bg-gray-200 rounded-full animate-pulse" />
          <div className="h-4 w-32 bg-gray-200 rounded-md animate-pulse" />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-4">
        <div className="h-10 w-32 bg-gray-200 rounded-md animate-pulse" />
        <div className="h-10 w-32 bg-gray-200 rounded-md animate-pulse" />
      </div>

      {/* Details Section */}
      <div className="flex flex-col gap-y-4">
        <div className="h-6 w-24 bg-gray-200 rounded-md animate-pulse" />
        <div className="space-y-4">
          {[1, 2].map((item) => (
            <div key={item} className="space-y-2">
              <div className="h-4 w-64 bg-gray-200 rounded-md animate-pulse" />
              <div className="h-4 w-full bg-gray-200 rounded-md animate-pulse" />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
