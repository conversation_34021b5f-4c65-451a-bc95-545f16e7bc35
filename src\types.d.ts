import { AxiosError } from "axios";
import { TUserSettings } from "./services/settings.service";

declare module "editorjs-md-parser" {
  export function MDtoBlocks(markdown: string): any[];
  export function MDfromBlocks(blocks: any[]): string;
}

export { };

declare global {
  type ObjectId<T extends {}> = {
    _id: string;
    __v: number;
  } & T

  type UserPreferredCurrency = 'usd' | 'gbp'

  /**
   * @dev this is the type for the error object returned from the server
   */
  type ApiError<T extends {} = {}> = AxiosError<{ error: string, details?: string } & T>;

  /**
   * @dev FormData with extended types for strong typing
   */
  type TypedFormData<T extends {}> = Omit<
    FormData,
    "set" | "append" | "delete" | "get"
  > & {
    [key in keyof T]: string | Blob;
  } & {
    set<K extends keyof T>(key: K, value: T[K]): void;
    append<K extends keyof T>(key: K, value: T[K]): void;
    delete(key: keyof T): void;
    get<K extends keyof T>(key: K): T[K];
  };

  type Nullable<T> = T | null

  // Helper type to create a range union from 1 to N
  type BuildTuple<
    N extends number,
    Acc extends unknown[] = [],
  > = Acc["length"] extends N ? Acc : BuildTuple<N, [...Acc, unknown]>;

  type Add1<N extends number> = [...BuildTuple<N>, unknown]["length"];

  type Steps<N extends number, Current extends number = 1> = Current extends N
    ? Current
    : Current | Steps<N, Add1<Current>>;

  type Replace<O extends Record<string, any>, K extends keyof O, N extends string> =
    Prettify<
      Omit<O, K> & { [index in N]: O[K] }
    >

  interface User {
    _id: string;
    first_name: string;
    last_name: string;
    username: string;
    email: string;
    phone: string;
    planId: string;
    role?: "user" | "admin" | "superadmin" | "blogger";
    user_photo?: string;
    isEmailVerified: boolean;
    bookingLink: string;
    urlToken: string;
    address: string;
    address_2?: string;
    postcode: string;
    town: string;
    country: string;
    business_bio: string;
    timeFormat: "24h" | "12h";
    dateFormat: string;
    industry: string;
    createdAt: string;
    updatedAt: string;
    business_name: string;
    leadSettings: {
      service: string;
      leadLocation?: string[];
    };
    totalCredit: number;
  }

  // type PaymentMethod = {
  //   userId: string; // Reference to the user/business
  //   type: "default" | "others";
  //   paymentType: "card" | "paypal";
  //   provider?: "visa" | "Mastercard"; // Provider name, e.g., 'Visa', 'Mastercard', 'PayPal'
  //   stripePaymethodId: string;
  //   last4Digits?: string;
  //   expiryMonth?: string;
  //   expiryYear?: string;
  //   createdAt: Date;
  //   updatedAt: Date;
  // };

  interface ExtendedUser extends User {
    stripesConsumerId: string;
    hasProfileImage: boolean;
    isEmailVerified: boolean;
    isDeactivated: boolean;
    isDeleted: boolean;
    createdAt: string;
    password?: string;
    updatedAt: string;
    emailConfirmationToken: string;
    userSubscription: string;
  }

  interface Workday {
    day: string;
    startTime: string; // HH:MM format
    closingTime: string; // HH:MM format
  }
  interface CalenderSettings {
    workdays: Workday[];
    advanceBookingLimit: number;
    timezone: string;
    customTimeslotDuration: number;
    _id: string; // Changed from _id for consistency
    timeslots: string; // Can be either a number or a string (e.g., "10")
    userId: string;
    createdAt: string;
    updatedAt: string;
    __v: number;
  }

  interface UserSettings extends TUserSettings { }

  interface MongoDocument {
    _id: string;
  }

  type Prettify<T> = {
    [index in keyof T]: T[index];
  } & {}

  type TypedOmit<T extends Record<string, any>, K extends keyof T> = Omit<T, K>

  /**
   * @dev makes a certain key non-nullable in an object
   * */
  type MakePropNonNullable<
    T extends {},
    K extends keyof T = keyof T,
  > = Prettify<
    Omit<T, K> & {
      [index in K]: NonNullable<T[K]>;
    }
  >;

  type MakeRequired<T extends {}, K extends keyof T> = Prettify<
    T & { [P in K]-?: T[P] }
  >;

  /**
   * @dev opposite of Readonly
   */
  type Mutable<T> = {
    -readonly [P in keyof T]: T[P];
  };

  type ArrayOfLength<
    N extends number,
    T extends any[] = [],
  > = T["length"] extends N ? T : ArrayOfLength<N, [...T, any]>;

  type AddOne<N extends number> = [...ArrayOfLength<N>, any]["length"];

  type NumericRange<
    N extends number,
    T extends number[] = [],
  > = T["length"] extends N
    ? T[number] | AddOne<T["length"]>
    : Range<NamedCurve, [...T, T["length"]]>;

  interface PaymentMethod {
    _id: string;
    type: "default" | "others";
    paymentType: "card" | "paypal";
    provider?: "visa" | "mastercard"; // Provider name, e.g., 'Visa', 'Mastercard', 'PayPal'
    stripePaymethodId: string;
    last4Digits?: string;
    expiryMonth?: string;
    expiryYear?: string;
    createdAt: Date;
    updatedAt: Date;
  }

  type UserSubscription = {
    user: string;
    subscriptionPlan: string;
    stripeSubscriptionId: string;
    status:
    | "incomplete"
    | "incomplete_expired"
    | "trialing"
    | "active"
    | "past_due"
    | "canceled"
    | "unpaid"
    | "paused";
    startDate: Date;
    endDate: Date;
    nextBillingDate?: Date;
  };


  // from type-fest
  /**
  Methods to exclude.
  */
  type ArrayLengthMutationKeys = 'splice' | 'push' | 'pop' | 'shift' | 'unshift';

  /**
  Create a type that represents an array of the given type and length. The array's length and the `Array` prototype methods that manipulate its length are excluded in the resulting type.

  Please participate in [this issue](https://github.com/microsoft/TypeScript/issues/26223) if you want to have a similar type built into TypeScript.

  Use-cases:
  - Declaring fixed-length tuples or arrays with a large number of items.
  - Creating a range union (for example, `0 | 1 | 2 | 3 | 4` from the keys of such a type) without having to resort to recursive types.
  - Creating an array of coordinates with a static length, for example, length of 3 for a 3D vector.

  Note: This type does not prevent out-of-bounds access. Prefer `ReadonlyTuple` unless you need mutability.

  @example
  ```
  import type {FixedLengthArray} from 'type-fest';

  type FencingTeam = FixedLengthArray<string, 3>;

  const guestFencingTeam: FencingTeam = ['Josh', 'Michael', 'Robert'];

  const homeFencingTeam: FencingTeam = ['George', 'John'];
  //=> error TS2322: Type string[] is not assignable to type 'FencingTeam'

  guestFencingTeam.push('Sam');
  //=> error TS2339: Property 'push' does not exist on type 'FencingTeam'
  ```

  @category Array
  @see ReadonlyTuple
  */
  type FixedLengthArray<Element, Length extends number, ArrayPrototype = [Element, ...Element[]]> = Pick<
	ArrayPrototype,
	Exclude<keyof ArrayPrototype, ArrayLengthMutationKeys>
  > & {
	[index: number]: Element;
	[Symbol.iterator]: () => IterableIterator<Element>;
	readonly length: Length;
  };

  /**
  Returns a boolean for whether the given number is a float, like `1.5` or `-1.5`.

  Use-case:
  - If you want to make a conditional branch based on the result of whether a number is a float or not.

  @example
  ```
  import type {IsFloat, PositiveInfinity} from 'type-fest';

  type A = IsFloat<1.5>;
  //=> true

  type B = IsFloat<-1.5>;
  //=> true

  type C = IsFloat<1e-7>;
  //=> true

  type D = IsFloat<1.0>;
  //=> false

  type E = IsFloat<PositiveInfinity>;
  //=> false

  type F = IsFloat<1.23e+21>;
  //=> false
  ```

  @category Type Guard
  @category Numeric
  */
  type IsFloat<T> = T extends number
	? `${T}` extends `${number}e${infer E extends '-' | '+'}${number}`
		? E extends '-'
			? true
			: false
		: `${T}` extends `${number}.${number}`
			? true
			: false
	: false;

  /**
  A `number` that is not an integer.

  Use-case: Validating and documenting parameters.

  It does not accept `Infinity`.

  @example
  ```
  import type {Float} from 'type-fest';

  declare function setPercentage<T extends number>(length: Float<T>): void;
  ```

  @see Integer

  @category Numeric
  */
  type Float<T> =
  T extends unknown // To distributive type
	? IsFloat<T> extends true ? T : never
	: never; // Never happens


  // end of type-fest
}
