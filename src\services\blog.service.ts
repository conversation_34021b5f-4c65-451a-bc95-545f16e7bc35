import { AxiosResponse } from "axios";
import { api } from "./api.service";
import { UploadResponseFormat } from "../editor-js-image-types";

// Types based on the mongoose models
interface IBlog {
  _id?: string;
  title: string;
  shortDescription: string;
  authorName: string;
  longDescription: string;
  timePosted?: Date;
  imageUrl?: string;
  hasImage?: boolean;
  imagePublicId?: string;
  tags?: string[];
  uniqueKey: string;
}

export interface IBlogPayload
  extends Pick<
    IBlog,
    | "title"
    | "shortDescription"
    | "authorName"
    | "longDescription"
    | "tags"
    | "timePosted"
  > {
  image: File | null;
}

interface ITag {
  _id?: string;
  name: string;
}

export interface ITagPayload extends Pick<ITag, "name"> {}

interface BlogResponse {
  success: boolean;
  message?: string;
  post?: IBlog;
}

interface BlogsResponse {
  success: boolean;
  blogPosts: IBlog[];
}

interface BlogByIdResponse {
  success: boolean;
  blogPost: IBlog;
}

interface TagResponse {
  success: boolean;
  message?: string;
  tag?: ITag;
}

interface TagsResponse {
  success: boolean;
  tagsList: ITag[];
}

// Create a new blog post
export const CreateBlog = async (
  payload: FormData
): Promise<AxiosResponse<BlogResponse>> => {
  try {
    const response = await api.post(`/admin/blogs`, payload, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response;
  } catch (error) {
    console.error("admin-blogs", error);
    throw error;
  }
};

/**
 * @dev this is for uploading single blog image
 */
export const UploadBlogImage = async (
  formData: TypedFormData<{ image: Blob }>
) => {
  try {
    const response = await api.post<UploadResponseFormat>(
      `/admin/blogs/image`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response;
  } catch (error) {
    console.error("admin-blogs-image-upload", error);
    throw error;
  }
};

// Create a new tag
export const CreateTag = async (payload: {
  name: string;
}): Promise<AxiosResponse<TagResponse>> => {
  try {
    const response = await api.post(`/admin/tags`, payload);
    return response;
  } catch (error) {
    console.error("admin-blogs", error);
    throw error;
  }
};

// Update an existing blog post
export const UpdateBlog = async (
  uniqueKey: string,
  payload: FormData
): Promise<AxiosResponse<BlogResponse>> => {
  try {
    const response = await api.put(`/admin/blogs/${uniqueKey}`, payload, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response;
  } catch (error) {
    console.error("admin-blogs", error);
    throw error;
  }
};

// Get a blog post by ID
export const GetBlogByUniqueKey = async (
  uniqueKey: string
): Promise<AxiosResponse<BlogByIdResponse>> => {
  try {
    const response = await api.get(`/admin/blogs/${uniqueKey}`);
    return response;
  } catch (error) {
    console.error("admin-blogs", error);
    throw error;
  }
};

// Get all blog posts
export const GetBlogs = async (): Promise<AxiosResponse<BlogsResponse>> => {
  try {
    const response = await api.get(`/admin/blogs`);
    return response;
  } catch (error) {
    console.error("admin-blogs", error);
    throw error;
  }
};

// Get all tags
export const GetTags = async (): Promise<AxiosResponse<TagsResponse>> => {
  try {
    const response = await api.get(`/admin/tags`);
    return response;
  } catch (error) {
    console.error("admin-blogs", error);
    throw error;
  }
};

// Delete a blog post
export const DeleteBlog = async (
  uniqueKey: string
): Promise<AxiosResponse<{ success: boolean; message: string }>> => {
  try {
    const response = await api.delete(`/admin/blogs/${uniqueKey}`);
    return response;
  } catch (error) {
    console.error("admin-blogs", error);
    throw error;
  }
};

// Delete a tag
export const DeleteTag = async (
  id: string
): Promise<AxiosResponse<{ success: boolean; message: string }>> => {
  try {
    const response = await api.delete(`/admin/tag/${id}`);
    return response;
  } catch (error) {
    console.error("admin-blogs", error);
    throw error;
  }
};
