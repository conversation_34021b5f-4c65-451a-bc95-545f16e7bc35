import { Typography } from "@/components/typography";
import Tabs from "@/components/ui/tabs";
import ReEngagementTab from "./components/reengagement-tab";
import { GetServices } from "@/src/services/services.service";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useMemo, useState } from "react";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import {
  ClientToReEngage,
  GetClientsToReEngage,
} from "@/src/services/analytics.service";
import { omit } from "@/lib/helpers";
import { ClientSelectionContext } from "./components/client-selection-context";
import ReEngagementPieChart from "./components/clients-pie-chart";

const ClientReEngagement = () => {
  const { data: services = [] } = useQuery({
    queryKey: ["services"],
    queryFn: async () => (await GetServices()).data.Services,
  });
  const [selectedServiceId, setSelectedServiceId] = useState<string>(
    services[0]?._id || ""
  );
  // when services array is not empty, put the first service id in selectedServiceId
  useEffect(() => {
    if (services.length > 0) setSelectedServiceId(services[0]?._id || "");
  }, [services]);
  const clientsToReEngageQuery = useQuery({
    queryKey: ["client-to-re-engage", selectedServiceId],
    queryFn: async () => {
      const data = (
        await GetClientsToReEngage({ serviceId: selectedServiceId })
      ).data;
      return omit(data, "message");
    },
    enabled: !!selectedServiceId,
    refetchOnWindowFocus: false,
  });
  const tabsMemo = useMemo(() => {
    if (!clientsToReEngageQuery.data)
      return {
        all: [],
        inactive: [],
        atRisk: [],
      };
    else
      return {
        all: clientsToReEngageQuery.data.clientsForReEngagement,
        inactive: clientsToReEngageQuery.data.clientsForReEngagement.filter(
          (client) =>
            (client.daysSinceLastAppointment || 0) <=
            client.averageReturnInterval
        ),
        atRisk: clientsToReEngageQuery.data.clientsForReEngagement.filter(
          (client) =>
            (client.daysSinceLastAppointment || 0) >=
            client.averageReturnInterval
        ),
      };
  }, [clientsToReEngageQuery.data]);
  const [selectedClients, setSelectedClients] = useState(
    [] as ClientToReEngage[]
  );
  return (
    <ClientSelectionContext.Provider
      value={{
        selectedClients,
        addClient(client) {
          setSelectedClients([...selectedClients, client]);
        },
        removeClient(client) {
          setSelectedClients(
            selectedClients.filter((c) => c.client._id !== client.client._id)
          );
        },
      }}
    >
      <section className="pb-20 md:pb-80 w-full relative">
        <div className="flex flex-col gap-y-6">
          <div className="w-full ">
            <Typography
              variant={"h1"}
              className="font-bold font-Bricolage_Grotesque "
            >
              Client <br />
              Re - Engagement
            </Typography>
          </div>
          <div className="w-full h-fit">
            <div className="w-full h-fit md:max-w-[560px] ">
              <ReEngagementPieChart
                clientsCount={tabsMemo.all.length}
                data={{
                  atRisk: {
                    value: tabsMemo.atRisk.length,
                  },
                  inactive: {
                    value: tabsMemo.inactive.length,
                  },
                }}
              />
            </div>
            <Tabs
              onValueChange={() => {
                setSelectedClients([]);
              }}
              defaultValue="inactive"
              className="w-full "
            >
              <div className="w-full h-fit flex flex-col gap-y-3 md:flex-row-reverse md:gap-x-3 md:items-end md:max-w-[560px] ">
                <div className="w-full flex flex-col gap-y-2 ">
                  <Typography className=" text-paragraph text-sm md:hidden">
                    Select Service
                  </Typography>
                  <div className="w-full grid grid-cols-[1fr_auto] gap-x-2 ">
                    <SearchableDropdown
                      fullWidth
                      value={{
                        value: selectedServiceId || "",
                        label:
                          services.find(
                            (value) => value._id === selectedServiceId
                          )?.name || "",
                      }}
                      options={
                        services?.map((service) => ({
                          label: service.name,
                          value: service._id,
                        })) || []
                      }
                      className="max-w-[320px] md:max-w-none w-full"
                      name="service"
                      placeholder=""
                      onChange={(option) => {
                        if (!option) return;
                        setSelectedServiceId(option.value);
                      }}
                    />
                  </div>
                </div>
                <Tabs.List className="grid w-full max-w-[320px] grid-cols-3 md:flex-grow ">
                  <Tabs.Trigger
                    value="all"
                    className=" px-0 flex items-center justify-center gap-x-2"
                  >
                    All
                  </Tabs.Trigger>
                  <Tabs.Trigger
                    value="inactive"
                    className=" px-0 flex items-center justify-center gap-x-2"
                  >
                    <div className="min-w-1.5 min-h-1.5 mt-0.5 rounded-full bg-yellow-400" />{" "}
                    Inactive
                  </Tabs.Trigger>
                  <Tabs.Trigger
                    value="at-risk"
                    className=" px-0 flex items-center justify-center gap-x-2"
                  >
                    <div className="min-w-1.5 min-h-1.5 mt-0.5 rounded-full bg-red-600" />{" "}
                    At Risk
                  </Tabs.Trigger>
                </Tabs.List>
              </div>
              <ReEngagementTab
                tabId="all"
                clientsToReEngage={tabsMemo.all}
                dataQuery={clientsToReEngageQuery}
              />
              <ReEngagementTab
                tabId="inactive"
                clientsToReEngage={tabsMemo.inactive}
                dataQuery={clientsToReEngageQuery}
              />
              <ReEngagementTab
                tabId="at-risk"
                clientsToReEngage={tabsMemo.atRisk}
                dataQuery={clientsToReEngageQuery}
              />
            </Tabs>
          </div>
        </div>
      </section>
    </ClientSelectionContext.Provider>
  );
};

export default ClientReEngagement;
