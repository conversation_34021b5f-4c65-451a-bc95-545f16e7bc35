import { Typography } from "@/components/typography";
import { Check } from "lucide-react";

const SuccessSection: React.FC<{ email: string }> = (props) => {
  return (
    <section
      className={`h-fit w-fit flex px-8 py-10 flex-col gap-y-7 items-center max-w-[400px] justify-center rounded-[28px] bg-white `}
    >
      <Typography variant={"h4"} className=" ">
        Thank you for booking
      </Typography>
      <div className=" flex flex-col gap-y-4  ">
        <div className="flex flex-col gap-y-4 pt-2 px-3">
          <div className="p-8 w-fit h-fit bg-green-500 rounded-full mx-auto">
            <Check className="w-8 h-8 text-white " />
          </div>
          <Typography variant="p" className="!mt-0 text-center">
            Your booking has been confirmed! You'll receive a confirmation email
            shortly at <span className="text-sky-600">{props.email}</span>. You
            may close this window now.
          </Typography>
        </div>
      </div>
    </section>
  );
};

export default SuccessSection;
