
import React from "react";
import { useSearchParams, Link } from "react-router-dom";
import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import { useSchedulingRules } from "../hooks/use-scheduling-rules";
import RulesCard from "./components/rules-card";
import { PlusSignIcon } from "hugeicons-react";
import { useQuery } from "@tanstack/react-query";
import { GetServices } from "@/src/services/services.service";
import { Service } from "@/src/interfaces/services";

const SchedulingRules: React.FC = () => {
  const [searchParams] = useSearchParams();
  const teamId = searchParams.get("teamId");

  const { allSchedulingRulesQuery } = useSchedulingRules();
  const servicesQuery = useQuery({
    queryKey: ["services"],
    queryFn: async () => (await GetServices()).data.Services,
  });

  const services = (servicesQuery.data || []) as Service[];
  const rules = allSchedulingRulesQuery.data || [];

  const isLoading = servicesQuery.isLoading || allSchedulingRulesQuery.isLoading;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <CircularLoader />
      </div>
    );
  }

  return (
    <div className="space-y-6 pb-20">
      <div className="flex items-center justify-between">
        <div>
          <Typography variant="h2" className="font-semibold">
            Scheduling Rules
          </Typography>
          <Typography className="text-gray-600 mt-1">
            Manage deposit, cancellation, and rescheduling policies for your services
          </Typography>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {services.map((service) => {
          const serviceRules = rules.find(rule => rule.serviceId._id === service._id);

          return (
            <RulesCard
              key={service._id}
              service={service}
              rules={serviceRules}
              teamId={teamId}
            />
          );
        })}
      </div>

      {services.length === 0 && (
        <div className="text-center py-12">
          <Typography className="text-gray-500 mb-4">
            No services found. Create services first to set up scheduling rules.
          </Typography>
          <Link to="/services">
            <Button variant="dormant">
              <PlusSignIcon className="w-4 h-4 mr-2" />
              Manage Services
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
};

export default SchedulingRules;
