import {
  Toolt<PERSON>,
  Too<PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const BasicTooltip: React.FC<{
  children?: React.ReactNode;
  trigger?: React.ReactNode;
  content: string;
  asChild?: boolean
}> = ({ trigger, children, content, asChild = true, ...props }) => {
  return (
    <TooltipProvider delayDuration={300} {...props}>
      <Tooltip>
        <TooltipTrigger asChild={asChild}>{trigger || children}</TooltipTrigger>
        <TooltipContent>
          <p className="font-medium text-sm">{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default BasicTooltip;
