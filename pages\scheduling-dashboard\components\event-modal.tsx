import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";
import { cn } from "@/lib/utils";
import { CalendarEvent } from "~/interfaces/booking";
import { formatDate } from "date-fns";
import { Button } from "@/components/buttons";
import { X } from "lucide-react";
import { useRef, useState, useEffect } from "react";

const EventModal = ({
  event,
  ...props
}: {
  event: Required<CalendarEvent>;
  open: boolean;
  onClose: () => void;
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [showShadow, setShowShadow] = useState(false);

  useEffect(() => {
    const checkScroll = () => {
      if (scrollContainerRef.current) {
        const { scrollHeight, scrollTop, clientHeight } =
          scrollContainerRef.current;
        // Show shadow if there's more content to scroll
        setShowShadow(scrollHeight > scrollTop + clientHeight);
      }
    };

    // Check initially
    checkScroll();

    // Add scroll event listener
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", checkScroll);
      return () => scrollContainer.removeEventListener("scroll", checkScroll);
    }
  }, [event]);
  return (
    <Modal open={props.open} onClose={props.onClose}>
      <Modal.Body
        className={`min-h-fit min-w-80 h-fit w-fit flex flex-col gap-y-7 items-center justify-center rounded-[32px] bg-white relative `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Events
          </Typography>
          <Button
            onTap={props.onClose}
            variant="icon"
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>{" "}
        <section
          ref={scrollContainerRef}
          className="max-h-80 overflow-y-auto thin-scrollbar flex flex-col gap-y-7 relative"
        >
          <div className="w-full flex flex-col gap-y-2 ">
            <Typography className="px-2 bg-gray-100 rounded-md">
              Customer Information
            </Typography>

            <div className="w-full grid gap-x-2 gap-y-5">
              <Typography className={cn(`font-semibold w-full !mt-0  `)}>
                Name: {event.extendedProps.customerInfo?.firstName}{" "}
                {event.extendedProps.customerInfo?.lastName}
              </Typography>
              <Typography className={cn(`font-semibold w-full !mt-0  `)}>
                Phone: {event.extendedProps.customerInfo?.phone}
              </Typography>
              <Typography className={cn(`font-semibold w-full !mt-0  `)}>
                Email: {event.extendedProps.customerInfo?.email}
              </Typography>
            </div>
          </div>
          <div className="w-full flex flex-col gap-y-2 ">
            <Typography className="px-2 bg-gray-100 rounded-md">
              Service Details
            </Typography>

            <div className="w-full grid gap-x-2 gap-y-5">
              <Typography className={cn(`font-semibold w-full !mt-0  `)}>
                Status: {event.extendedProps.status}
              </Typography>
              <Typography className={cn(`font-semibold w-full !mt-0  `)}>
                Service: {event.extendedProps.service.name}
              </Typography>
              <Typography className={cn(`font-semibold w-full !mt-0  `)}>
                Notes: {event.extendedProps.notes}
              </Typography>
            </div>
          </div>
          <div className="w-full flex flex-col gap-y-2 ">
            <Typography className="px-2 bg-gray-100 rounded-md">
              Appointment Time
            </Typography>
            <div className="w-full grid gap-x-2 gap-y-5">
              <Typography className={cn(`font-semibold w-full !mt-0  `)}>
                Start: {formatDate(event.start, "EEE MMM dd, yyyy hh:mm a")}
              </Typography>
              <Typography className={cn(`font-semibold w-full !mt-0  `)}>
                End: {formatDate(event.end, "EEE MMM dd, yyyy hh:mm a")}
              </Typography>
            </div>
          </div>
        </section>
        {showShadow && (
          <div
            className="absolute bottom-8 w-full h-8 bg-gradient-to-t from-white to-transparent pointer-events-none"
            aria-hidden="true"
          />
        )}
      </Modal.Body>
    </Modal>
  );
};

export default EventModal;
