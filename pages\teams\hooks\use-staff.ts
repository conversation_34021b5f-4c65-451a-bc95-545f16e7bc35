import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/src/contexts/hooks/toast";
import * as StaffService from "@/src/services/staff.service";
import { TeamId } from "@/src/interfaces/team";
import { UpdateStaffPayload } from "@/src/interfaces/staff";

export const useStaff = (teamId?: TeamId) => {
  const queryClient = useQueryClient();
  const showToast = useToast();

  const staffQuery = useQuery({
    queryKey: ["staff", teamId],
    queryFn: async () => (await StaffService.GetStaff(teamId)).data.staff,
    enabled: !!teamId,
  });

  const createStaffMutation = useMutation({
    mutationFn: StaffService.CreateStaffUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["staff"] });
      showToast("success", "Staff member created successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to create staff member"
      );
    },
  });

  const updateStaffMutation = useMutation({
    mutationFn: ({ staffId, payload }: { staffId: string; payload: UpdateStaffPayload }) =>
      StaffService.UpdateStaff(staffId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["staff"] });
      showToast("success", "Staff member updated successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to update staff member"
      );
    },
  });

  const deleteStaffMutation = useMutation({
    mutationFn: StaffService.DeleteStaff,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["staff"] });
      showToast("success", "Staff member deleted successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to delete staff member"
      );
    },
  });

  const toggleStaffActivation = {
    activate: useMutation({
      mutationFn: StaffService.ActivateStaff,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["staff"] });
        showToast("info", "Staff member activated successfully");
      },
      onError: (error: any) => {
        showToast(
          "error",
          error.response?.data?.error || "Failed to activate staff member"
        );
      },
    }),
    deactivate: useMutation({
      mutationFn: StaffService.DeactivateStaff,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["staff"] });
        showToast("info", "Staff member deactivated successfully");
      },
      onError: (error: any) => {
        showToast(
          "error",
          error.response?.data?.error || "Failed to de-activate staff member"
        );
      },
    })
  }


  return {
    staffQuery,
    deleteStaffMutation,
    updateStaffMutation,
    createStaffMutation,
    toggleStaffActivation
  }
}
