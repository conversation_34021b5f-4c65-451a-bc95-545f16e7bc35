import { Typography } from "@/components/typography";
import Circular<PERSON>oader from "@/components/ui/circular-loader";
import ErrorScreen from "@/components/ui/error-screen";
import { GetDigitalProfileByUsername } from "@/src/services/digital-profile.service";
import { useQuery } from "@tanstack/react-query";
import { useNavigate, useParams } from "react-router-dom";
import "~/data/digital-profile/buttons.css";
import { pick } from "@/lib/helpers";
import LayoutPreview from "../digital-profile-editor/components/layout-preview";

/**
 * @dev add some animation on load of this page, buttons falling in opacity from top
 */
const DigitalProfile = () => {
  const { username } = useParams();
  const {
    data: { profile: digitalProfile, additionalData: userData } = {},
    isFetching,
    error,
  } = useQuery({
    queryKey: ["digital-profile"],
    queryFn: async () =>
      pick((await GetDigitalProfileByUsername(username!)).data, 'profile', 'additionalData'),
    enabled: !!username && typeof username === "string",
    refetchOnWindowFocus: false,
  });
  const navigate = useNavigate();
  if (isFetching)
    return (
      <div className="w-screen h-screen flex justify-center ">
        <CircularLoader />
      </div>
    );
  if (error)
    return (
      <section className="w-screen h-screen flex flex-col">
        <ErrorScreen
          className="mt-32 mx-auto "
          title="The page you are looking for doesn't exist"
          message={`Want this to be your username?`}
          onRetry={{
            title: "Create a profile",
            action: () => navigate("/onboarding"),
          }}
        />
        <Typography className="font-Aeonik_Fono text-lg font-semibold mt-auto mb-7 mx-auto">
          Puzzle Piece Inc.
        </Typography>
      </section>
    );
  return (
    <div className="w-screen h-screen">
      <LayoutPreview
        userDetails={{
          isPremium() {
            return Boolean(userData?.isPremiumPlan)
          },
          username: username || '',
          isOnPublicPage: true
        }}
        profile={digitalProfile!} />
    </div>
  );
};

export default DigitalProfile;
