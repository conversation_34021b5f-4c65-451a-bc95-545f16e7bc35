import { Typography } from "@/components/typography";
import React, { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Xmark } from "@gravity-ui/icons";
import { Button } from "@/components/buttons";
import { useToast } from "@/src/contexts/hooks/toast";
import { DigitalProfileType, ISocialMedia } from "../types";
import { getCSSVariables } from "../utils/get-css-variables";
import { socialMediaMatcher } from "../hooks/use-digital-profile";

const Avatar = (props: React.JSX.IntrinsicElements["img"]) => {
  return (
    <img
      src={props.src}
      alt={props.alt}
      className="size-24 rounded-full object-cover"
    />
  );
};

const SocialMediaIcon = (props: ISocialMedia) => {
  const Icon = useMemo(() => {
    if (props.type === 'custom')
      return () => props.icon ? <span className="text-[22px] ">{props.icon}</span> : null
    else {
      const socialMediaIcon = socialMediaMatcher.find(([regExp]) => regExp.test(props.url))
      if (socialMediaIcon) {
        const Icon = socialMediaIcon[1].icon;
        return () => <Icon className="size-[22px] md:size-[28px] "
          style={{
            fill: 'var(--profile-body-text)'
          }}
        />
      } else return () => null
    }
  }, [props.url])
  return <Icon />
}

type Props = {
  profile: DigitalProfileType,
  userDetails: {
    username: string
    isPremium: () => boolean;
    isOnPublicPage: boolean
  },
}

const LayoutPreview = ({ profile, userDetails }: Props) => {
  const {
    imageUrl,
    details,
    websiteLinks,
    settings,
    socialMedia = [],
    theme
  } = profile
  const showToast = useToast()
  const navigate = useNavigate()
  const cssVariables = getCSSVariables({ theme })
  return (
    <section
      style={{
        ...cssVariables,
        background: theme?.backgroundValue
      }}
      className="relative flex flex-col h-full overflow-y-auto no-scrollbar">
      <div className="w-full flex justify-center pt-4 md:pt-16 ">
        {imageUrl ? (
          <Avatar
            src={imageUrl as string | undefined}
            alt="profile image"
            className="size"
          />
        ) : (
          <div className="size-24 rounded-full bg-gray-200 flex items-center justify-center text-white font-medium">
            <span className="text-gray-600 text-5xl">
              {details.title.charAt(0)}
            </span>
          </div>
        )}
      </div>
      <div className="w-full flex flex-col items-center justify-center gap-y-1 pt-2 px-6 pb-4">
        <Typography variant={"h4"} className="font-medium md:text-3xl font-Work_Sans"
          style={{
            color: `var(--profile-body-text)`
          }}
        >
          {details.title}
        </Typography>
        <Typography
          style={{
            color: `var(--profile-body-text)`
          }}
          variant={"span"} className="text-center text-sm md:text-base !mt-0 font-bold">
          {details.bio}
        </Typography>
        {settings?.socialMediaIconsPosition === 'top' &&
          <div className="flex items-center mt-7 justify-center gap-4 md:gap-6 w-full flex-wrap">
            {socialMedia.map((link, idx) => {
              return (
                <a
                  key={idx}
                  href={link.url}
                  target="_blank"
                  className="w-fit"
                >
                  <SocialMediaIcon {...link} />
                </a>
              )
            })
            }
          </div>
        }
      </div>

      <section
        style={{
          width: "100%",
          marginInline: 'auto',
          maxWidth: '540px',
          display: "flex",
          flexDirection: "column",
          paddingInline: "20px",
          gap: "20px",
          alignItems: "center",
        }}
      >
        {websiteLinks.filter(l => l.isActive).map((link, index) => {
          let buttonStyleIndex = theme?.buttonStyles?.findIndex(l => l.name === link.buttonStyle)
          if (buttonStyleIndex === -1) buttonStyleIndex = 0;
          return <a
            key={index}
            href={link.url}
            target="_blank"
            className="w-full"
          >
            <button
              style={{
                color: `var(--profile-button-${buttonStyleIndex}-color)`,
                fontSize: `var(--profile-button-${buttonStyleIndex}-fontSize)`,

                boxShadow: `var(--profile-button-${buttonStyleIndex}-boxShadow)`,
                borderColor: `var(--profile-button-${buttonStyleIndex}-borderColor)`,
                borderWidth: `var(--profile-button-${buttonStyleIndex}-borderWidth)`,
                fontWeight: `var(--profile-button-${buttonStyleIndex}-fontWeight)`,

                borderRadius: `var(--profile-button-${buttonStyleIndex}-borderRadius)`,

                background: `var(--profile-button-${buttonStyleIndex}-background)`,

                padding: `var(--profile-button-${buttonStyleIndex}-padding)`,
              }}
              className="grid grid-cols-[1fr_auto_1fr] w-full items-center gap-x-3"
            >
              <div
                className="size-8 bg-orange-300"
                style={{
                  borderRadius: `var(--profile-button-${buttonStyleIndex}-borderRadius)`,
                }}
              />
              <span
                style={{
                  color: `var(--profile-button-${buttonStyleIndex}-color)`,
                }}
                className="flex items-cente">
                {link.title}
              </span>
              <div />
            </button>
          </a>
        })}
      </section>
      {settings?.socialMediaIconsPosition === 'bottom' &&
        <div className="flex items-center mt-8 justify-center gap-4 w-full flex-wrap">
          {socialMedia.map((link, idx) => {
            return (
              <a
                key={idx}
                href={link.url}
                target="_blank"
                className="w-fit"
              >
                <SocialMediaIcon {...link} />
              </a>
            )
          })
          }
        </div>
      }
      {/* little x button so say pay for premium */}
      {!userDetails.isPremium() &&
        <div
          className="mt-auto pb-5 w-full flex flex-col gap-y-1 items-center "
        >
          <div
            /* to={`/onboarding`} */
            /* target="_blank" */
            className="w-full relative max-w-fit whitespace-nowrap bg-stone-500/20 backdrop-blur-2xl px-3 rounded-full py-3 hover:text-white hover:opacity-75 text-white font-semibold text-xs min-[360px]:text-sm "
          >
            Join{" "}
            <span className="relative z-20 after:w-full after:h-full after:absolute after:py-3 after:left-0 after:-z-10 after:-rotate-3 after:inline-block after:bg-green-500  ">
              {userDetails.username}
            </span>{" "}
            on Puzzle Piece 🧩
            {!userDetails.isOnPublicPage &&
              <Button
                onTap={() => {
                  showToast('info', 'Upgrade to Premium to remove banner', {
                    action: {
                      title: 'Upgrade',
                      onClick() {
                        navigate('/profile/billing')
                        return true
                      },
                    }
                  })
                }}
                variant="icon"
                className="rounded-full !p-1 bg-[#DFE1DB] absolute top-2 right-[6px] "
              >
                <Xmark className="text-gray-500 " width={16} height={16} />
              </Button>
            }
          </div>
        </div>
      }
    </section>
  );
};

export default LayoutPreview;
