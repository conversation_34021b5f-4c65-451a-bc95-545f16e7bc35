import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/src/contexts/hooks/toast";
import * as TeamService from "@/src/services/team.service";
import { pick } from "@/lib/helpers";

export const useTeam = () => {
  const queryClient = useQueryClient();
  const showToast = useToast();

  const teamsQuery = useQuery({
    queryKey: ["teams", 'limits'],
    queryFn: async () => pick((await TeamService.GetTeams()).data, 'teams', 'limits'),
  });

  const createTeamMutation = useMutation({
    mutationFn: TeamService.CreateTeam,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["teams"] });
      showToast("success", "Team created successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to create team"
      );
    },
  });

  const updateTeamMutation = useMutation({
    mutationFn: ({ teamId, payload }: { teamId: string; payload: any }) =>
      TeamService.UpdateTeam(teamId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["teams"] });
      showToast("success", "Team updated successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to update team"
      );
    },
  });

  const deleteTeamMutation = useMutation({
    mutationFn: TeamService.DeleteTeam,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["teams"] });
      showToast("success", "Team deleted successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to delete team"
      );
    },
  });

  return {
    teamsQuery,
    createTeamMutation,
    updateTeamMutation,
    deleteTeamMutation,
  };
};
