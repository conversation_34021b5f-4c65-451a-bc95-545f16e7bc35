import { ITheme } from "../types";

export class ThemeCollection {

  // 1. MODERN TECH - For SaaS, Tech Companies
  static modernTech: ITheme = {
    name: "Modern Tech",
    colors: {
      primary: "#3b82f6",
      secondary: "#1e40af",
      accent: "#06b6d4",
      background: "#0f172a",
      text: "#f8fafc"
    },
    fonts: {
      heading: "Inter",
      body: "Inter"
    },
    layout: "centered",
    backgroundType: "gradient",
    backgroundValue: "linear-gradient(135deg, #1e293b 0%, #0f172a 100%)",
    buttonStyles: [{
      name: "primary-cta",
      style: {
        background: "#3b82f6",
        color: "#ffffff",
        borderRadius: "12px",
        fontWeight: "600",
        padding: "16px 24px",
        fontSize: "16px",
        gradient: "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",
        boxShadow: "0 4px 14px 0 rgba(59, 130, 246, 0.39)"
      }
    },
    {
      name: "secondary",
      style: {
        background: "transparent",
        color: "#3b82f6",
        borderColor: "#3b82f6",
        borderWidth: "2px",
        borderRadius: "12px",
        fontWeight: "500",
        padding: "14px 24px",
        fontSize: "16px",
        hoverEffect: "bg-blue-500/10"
      }
    },
    {
      name: "outline",
      style: {
        background: "transparent",
        color: "#94a3b8",
        borderColor: "#334155",
        borderWidth: "1px",
        borderRadius: "8px",
        fontWeight: "400",
        padding: "12px 20px",
        fontSize: "14px"

      }
    }]
  };

  // 2. PROFESSIONAL CLEAN - For Law Firms, Consulting
  static professionalClean: ITheme = {
    name: "Professional Clean",
    colors: {
      primary: "#1e40af",
      secondary: "#374151",
      accent: "#d97706",
      background: "#f8fafc",
      text: "#1e293b"
    },
    fonts: {
      heading: "Playfair Display",
      body: "Source Sans Pro"
    },
    layout: "default",
    backgroundType: "solid",
    backgroundValue: "#f8fafc",
    buttonStyles: [
      {
        name: "professional",
        style: {

          background: "#1e40af",
          color: "#ffffff",
          borderRadius: "6px",
          fontWeight: "600",
          padding: "14px 28px",
          fontSize: "16px",
          boxShadow: "0 2px 8px rgba(30, 64, 175, 0.25)"
        }
      },
      {
        name: "secondary",
        style: {
          background: "#f1f5f9",
          color: "#1e40af",
          borderColor: "#e2e8f0",
          borderWidth: "1px",
          borderRadius: "6px",
          fontWeight: "500",
          padding: "14px 28px",
          fontSize: "16px"
        }
      },
      {
        name: "outline",
        style: {
          background: "transparent",
          color: "#64748b",
          borderColor: "#cbd5e1",
          borderWidth: "1px",
          borderRadius: "6px",
          fontWeight: "400",
          padding: "12px 24px",
          fontSize: "14px"
        }
      }
    ]
  }
  // 3. CREATIVE AGENCY - For Design, Marketing Agencies
  static creativeAgency: ITheme = {
    name: "Creative Agency",
    colors: {
      primary: "#ec4899",
      secondary: "#8b5cf6",
      accent: "#f59e0b",
      background: "#fafafa",
      text: "#171717"
    },
    fonts: {
      heading: "Poppins",
      body: "Poppins"
    },
    layout: "grid",
    backgroundType: "gradient",
    backgroundValue: "linear-gradient(135deg, #fdf2f8 0%, #f3e8ff 100%)",
    buttonStyles: [
      {
        name: "gradient",
        style: {
          background: "#ec4899",
          color: "#ffffff",
          borderRadius: "24px",
          fontWeight: "600",
          padding: "16px 32px",
          fontSize: "16px",
          gradient: "linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%)",
          boxShadow: "0 8px 32px rgba(236, 72, 153, 0.35)"
        }
      },
      {
        name: "creative",
        style: {
          background: "#f59e0b",
          color: "#ffffff",
          borderRadius: "16px",
          fontWeight: "700",
          padding: "14px 24px",
          fontSize: "15px",
          boxShadow: "0 4px 16px rgba(245, 158, 11, 0.4)"
        }
      },
      {
        name: "soft",
        style: {
          background: "#f3e8ff",
          color: "#8b5cf6",
          borderRadius: "20px",
          fontWeight: "500",
          padding: "12px 20px",
          fontSize: "14px"
        }
      }
    ]
  };

  // 4. HEALTHCARE CALM - For Medical, Wellness
  static healthcareCalm: ITheme = {
    name: "Healthcare Calm",
    colors: {
      primary: "#059669",
      secondary: "#0d9488",
      accent: "#0ea5e9",
      background: "#f0fdfa",
      text: "#134e4a"
    },
    fonts: {
      heading: "Nunito",
      body: "Open Sans"
    },
    layout: "default",
    backgroundType: "solid",
    backgroundValue: "#f0fdfa",
    buttonStyles: [
      {
        name: "medical-primary",
        style: {
          background: "#059669",
          color: "#ffffff",
          borderRadius: "10px",
          fontWeight: "600",
          padding: "16px 24px",
          fontSize: "16px",
          boxShadow: "0 3px 12px rgba(5, 150, 105, 0.3)"
        }
      },
      {
        name: "appointment",
        style: {
          background: "#0ea5e9",
          color: "#ffffff",
          borderRadius: "10px",
          fontWeight: "600",
          padding: "16px 24px",
          fontSize: "16px",
          boxShadow: "0 3px 12px rgba(14, 165, 233, 0.3)"
        }
      },
      {
        name: "emergency",
        style: {
          background: "#dc2626",
          color: "#ffffff",
          borderRadius: "8px",
          fontWeight: "700",
          padding: "12px 20px",
          fontSize: "14px",
          boxShadow: "0 3px 12px rgba(220, 38, 38, 0.4)"
        }
      },
      {
        name: "soft-green",
        style: {
          background: "#d1fae5",
          color: "#059669",
          borderRadius: "10px",
          fontWeight: "500",
          padding: "14px 20px",
          fontSize: "15px"
        }
      }
    ]
  }

  // 5. DARK LUXURY - For High-end Services, Premium Brands
  static darkLuxury: ITheme = {
    name: "Dark Luxury",
    colors: {
      primary: "#d4af37",
      secondary: "#b8860b",
      accent: "#cd7f32",
      background: "#0a0a0a",
      text: "#f5f5f5"
    },
    fonts: {
      heading: "Playfair Display",
      body: "Lato"
    },
    layout: "centered",
    backgroundType: "solid",
    backgroundValue: "#0a0a0a",
    buttonStyles: [
      {
        name: "luxury-gold",
        style: {
          background: "#d4af37",
          color: "#0a0a0a",
          borderRadius: "4px",
          fontWeight: "700",
          padding: "16px 32px",
          fontSize: "16px",
          boxShadow: "0 4px 20px rgba(212, 175, 55, 0.4)"
        }
      },
      {
        name: "premium",
        style: {
          background: "transparent",
          color: "#d4af37",
          borderColor: "#d4af37",
          borderWidth: "2px",
          borderRadius: "4px",
          fontWeight: "600",
          padding: "14px 28px",
          fontSize: "16px"
        }
      },
      {
        name: "subtle",
        style: {
          background: "#1a1a1a",
          color: "#f5f5f5",
          borderRadius: "6px",
          fontWeight: "400",
          padding: "12px 24px",
          fontSize: "14px"
        }
      }
    ]
  };

  // 6. WARM CORPORATE - For Finance, Real Estate, Business Services
  static warmCorporate: ITheme = {
    name: "Warm Corporate",
    colors: {
      primary: "#c2410c",
      secondary: "#ea580c",
      accent: "#0ea5e9",
      background: "#fffbeb",
      text: "#431407"
    },
    fonts: {
      heading: "Merriweather",
      body: "Source Sans Pro"
    },
    layout: "default",
    backgroundType: "solid",
    backgroundValue: "#fffbeb",
    buttonStyles: [
      {
        name: "corporate",
        style: {
          background: "#c2410c",
          color: "#ffffff",
          borderRadius: "8px",
          fontWeight: "600",
          padding: "14px 24px",
          fontSize: "16px",
          boxShadow: "0 3px 10px rgba(194, 65, 12, 0.3)"
        }
      },
      {
        name: "trustworthy",
        style: {
          background: "#0ea5e9",
          color: "#ffffff",
          borderRadius: "8px",
          fontWeight: "500",
          padding: "14px 24px",
          fontSize: "16px"
        }
      },
      {
        name: "warm-outline",
        style: {
          background: "transparent",
          color: "#c2410c",
          borderColor: "#c2410c",
          borderWidth: "2px",
          borderRadius: "8px",
          fontWeight: "500",
          padding: "12px 24px",
          fontSize: "15px"
        }
      }
    ]
  };

  // 7. MINIMALIST MONO - For Architects, Designers, Modern Brands  
  static minimalistMono: ITheme = {
    name: "Minimalist Mono",
    colors: {
      primary: "#171717",
      secondary: "#525252",
      accent: "#a3a3a3",
      background: "#ffffff",
      text: "#171717"
    },
    fonts: {
      heading: "JetBrains Mono",
      body: "Inter"
    },
    layout: "list",
    backgroundType: "solid",
    backgroundValue: "#ffffff",
    buttonStyles: [
      {
        name: "minimal",
        style: {
          background: "#171717",
          color: "#ffffff",
          borderRadius: "2px",
          fontWeight: "500",
          padding: "14px 24px",
          fontSize: "16px"
        }
      },
      {
        name: "ghost",
        style: {
          background: "transparent",
          color: "#171717",
          borderColor: "#171717",
          borderWidth: "1px",
          borderRadius: "2px",
          fontWeight: "400",
          padding: "14px 24px",
          fontSize: "16px"
        }
      },
      {
        name: "subtle",
        style: {
          background: "#f5f5f5",
          color: "#171717",
          borderRadius: "4px",
          fontWeight: "400",
          padding: "12px 20px",
          fontSize: "14px"
        }
      }
    ]
  };

  // Helper method to get all themes
  static getAllThemes(): Array<ITheme> {
    return [
      this.modernTech,
      this.professionalClean,
      this.creativeAgency,
      this.healthcareCalm,
      this.darkLuxury,
      this.warmCorporate,
      this.minimalistMono
    ];
  }

  // Get theme by name
  static getThemeByName(name: string): ITheme | undefined {
    return this.getAllThemes().find(theme => theme.name.toLowerCase() === name.toLowerCase());
  }

  // Get themes by industry
  static getThemesByIndustry(industry: string): Array<ITheme> {
    const industryMap: { [key: string]: Array<ITheme> } = {
      'technology': [this.modernTech, this.minimalistMono],
      'healthcare': [this.healthcareCalm, this.professionalClean],
      'legal': [this.professionalClean, this.darkLuxury],
      'finance': [this.warmCorporate, this.professionalClean],
      'consulting': [this.professionalClean, this.warmCorporate],
      'marketing': [this.creativeAgency, this.modernTech],
      'real-estate': [this.warmCorporate, this.professionalClean]
    };

    return industryMap[industry] || [this.professionalClean];
  }
}

// Usage Examples with CSS Classes
export const ThemeCSS = {

  // Modern Tech Button Styles
  modernTech: `
    .btn-primary-cta {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: #ffffff;
      border-radius: 12px;
      font-weight: 600;
      padding: 16px 24px;
      font-size: 16px;
      border: none;
      box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.39);
      transition: all 0.3s ease;
    }
    .btn-primary-cta:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px 0 rgba(59, 130, 246, 0.5);
    }
    
    .btn-secondary {
      background: transparent;
      color: #3b82f6;
      border: 2px solid #3b82f6;
      border-radius: 12px;
      font-weight: 500;
      padding: 14px 24px;
      font-size: 16px;
      transition: all 0.3s ease;
    }
    .btn-secondary:hover {
      background: rgba(59, 130, 246, 0.1);
    }
  `,

  // Professional Clean Button Styles
  professionalClean: `
    .btn-professional {
      background: #1e40af;
      color: #ffffff;
      border-radius: 6px;
      font-weight: 600;
      padding: 14px 28px;
      font-size: 16px;
      border: none;
      box-shadow: 0 2px 8px rgba(30, 64, 175, 0.25);
      transition: all 0.2s ease;
    }
    .btn-professional:hover {
      background: #1d4ed8;
      box-shadow: 0 4px 12px rgba(30, 64, 175, 0.35);
    }

    .btn-warm-outline {
      background: transparent;
      color: #c2410c;
      border: 2px solid #c2410c;
      border-radius: 8px;
      font-weight: 500;
      padding: 12px 24px;
      font-size: 15px;
      transition: all 0.2s ease;
    }
    .btn-warm-outline:hover {
      background: #c2410c;
      color: #ffffff;
    }
  `,

  // Creative Agency Button Styles
  creativeAgency: `
    .btn-gradient {
      background: linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%);
      color: #ffffff;
      border-radius: 24px;
      font-weight: 600;
      padding: 16px 32px;
      font-size: 16px;
      border: none;
      box-shadow: 0 8px 32px rgba(236, 72, 153, 0.35);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .btn-gradient:hover {
      transform: translateY(-3px) scale(1.02);
      box-shadow: 0 12px 40px rgba(236, 72, 153, 0.5);
    }

    .btn-creative {
      background: #f59e0b;
      color: #ffffff;
      border-radius: 16px;
      font-weight: 700;
      padding: 14px 24px;
      font-size: 15px;
      border: none;
      box-shadow: 0 4px 16px rgba(245, 158, 11, 0.4);
      transition: all 0.3s ease;
    }
    .btn-creative:hover {
      transform: rotate(-1deg) scale(1.05);
    }
  `,

  // Healthcare Calm Button Styles
  healthcareCalm: `
    .btn-medical-primary {
      background: #059669;
      color: #ffffff;
      border-radius: 10px;
      font-weight: 600;
      padding: 16px 24px;
      font-size: 16px;
      border: none;
      box-shadow: 0 3px 12px rgba(5, 150, 105, 0.3);
      transition: all 0.2s ease;
    }
    .btn-medical-primary:hover {
      background: #047857;
    }

    .btn-emergency {
      background: #dc2626;
      color: #ffffff;
      border-radius: 8px;
      font-weight: 700;
      padding: 12px 20px;
      font-size: 14px;
      border: none;
      box-shadow: 0 3px 12px rgba(220, 38, 38, 0.4);
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.8; }
    }
  `,

  // Dark Luxury Button Styles
  darkLuxury: `
    .btn-luxury-gold {
      background: #d4af37;
      color: #0a0a0a;
      border-radius: 4px;
      font-weight: 700;
      padding: 16px 32px;
      font-size: 16px;
      border: none;
      box-shadow: 0 4px 20px rgba(212, 175, 55, 0.4);
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
    .btn-luxury-gold:hover {
      background: #b8860b;
      box-shadow: 0 6px 30px rgba(212, 175, 55, 0.6);
    }

    .btn-premium {
      background: transparent;
      color: #d4af37;
      border: 2px solid #d4af37;
      border-radius: 4px;
      font-weight: 600;
      padding: 14px 28px;
      font-size: 16px;
      transition: all 0.3s ease;
    }
    .btn-premium:hover {
      background: rgba(212, 175, 55, 0.1);
    }
  `,

  // Warm Corporate Button Styles
  warmCorporate: `
    .btn-corporate {
      background: #c2410c;
      color: #ffffff;
      border-radius: 8px;
      font-weight: 600;
      padding: 14px 24px;
      font-size: 16px;
      border: none;
      box-shadow: 0 3px 10px rgba(194, 65, 12, 0.3);
      transition: all 0.2s ease;
    }
    .btn-corporate:hover {
      background: #ea580c;
    }

    .btn-trustworthy {
      background: #0ea5e9;
      color: #ffffff;
      border-radius: 8px;
      font-weight: 500;
      padding: 14px 24px;
      font-size: 16px;
      border: none;
      transition: all 0.2s ease;
    }
  `,

  // Minimalist Mono Button Styles
  minimalistMono: `
    .btn-minimal {
      background: #171717;
      color: #ffffff;
      border-radius: 2px;
      font-weight: 500;
      padding: 14px 24px;
      font-size: 16px;
      border: none;
      transition: all 0.2s ease;
    }
    .btn-minimal:hover {
      background: #262626;
    }

    .btn-ghost {
      background: transparent;
      color: #171717;
      border: 1px solid #171717;
      border-radius: 2px;
      font-weight: 400;
      padding: 14px 24px;
      font-size: 16px;
      transition: all 0.2s ease;
    }
    .btn-ghost:hover {
      background: #171717;
      color: #ffffff;
    }
  `
};

// Template to Theme Mapping
export const TemplateThemeMapping = {
  'saas-company': ['modernTech', 'minimalistMono'],
  'legal-firm': ['professionalClean', 'darkLuxury'],
  'development-agency': ['modernTech', 'creativeAgency'],
  'medical-practice': ['healthcareCalm', 'professionalClean'],
  'wellness-coach': ['healthcareCalm', 'warmCorporate'],
  'accounting-finance': ['warmCorporate', 'professionalClean'],
  'content-marketing': ['creativeAgency', 'modernTech'],
  'professional-services': ['professionalClean', 'warmCorporate']
};
