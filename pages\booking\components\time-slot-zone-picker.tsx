import { Button } from "@/components/buttons";
import CircularLoader from "@/components/ui/circular-loader";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import { removeLast } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import { TimeSlots } from "@/src/services/booking.service";
import moment from "moment-timezone";

type TimeSlotZonePickerProps = {
  onChangeTimeSlot(slot: string): void
  onChangeTimeZone(zone: string): void
  dateTime: {
    timeSlot: string;
    date: string;
    timeZone: string;
  }
  isFetching: boolean;
  timeSlots: TimeSlots | undefined;
  onClickNext: VoidFunction;
}

const timeZones = moment.tz.names();

const TimeSlotZonePicker = (props: TimeSlotZonePickerProps) => {
  const { dateTime, isFetching, timeSlots } = props
  return (
    <div
      className={cn("flex flex-col gap-y-3 gap-x-4 items-center px-4")}
    >
      <div className="grid grid-cols-2 gap-x-4">
        <div className="flex flex-col gap-y-0 ">
          <SearchableDropdown
            className="!w-36 md:!w-48 "
            placeholder="Europe/London"
            label="Select a timezone"
            required
            value={{
              label: dateTime.timeZone,
              value: dateTime.timeZone,
            }}
            options={timeZones.map((timeZone) => ({
              label: timeZone,
              value: timeZone,
            }))}
            onChange={(option) => {
              props.onChangeTimeZone(option!.value)
            }}
          />
        </div>
        {isFetching ? (
          <div className="px-5 flex-grow ">
            <CircularLoader />
          </div>
        ) : timeSlots?.length === 0 ? null : (
          <div
            className={cn("flex flex-col gap-y-0", {
              hidden: timeSlots?.length === 0,
            })}
          >
            <SearchableDropdown
              label="Select a time slot"
              placeholder="10:30-11:00"
              className="!w-36 md:!w-48 "
              fullWidth
              required
              value={{
                label: dateTime.timeSlot,
                value: dateTime.timeSlot,
              }}
              // remove last fake timeslot from this
              options={removeLast(
                timeSlots?.map((slot, index) => {
                  const nextSlot = timeSlots[index + 1];
                  if (!nextSlot) return { label: "", value: "" };
                  else {
                    const range = `${slot}-${nextSlot}`;
                    return {
                      label: range,
                      value: range,
                    };
                  }
                }) ?? [],
              )}
              onChange={(option) => {
                props.onChangeTimeSlot(option!.value)
              }}
            />
          </div>
        )}
      </div>
      <Button
        className="w-full mb-5"
        onTap={() => {
          props.onClickNext()
        }}
      >
        Continue
      </Button>
    </div>
  )
}

export default TimeSlotZonePicker
