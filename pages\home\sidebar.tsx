import Crown from "@/assets/images/crown.png";
import Logo from "@/assets/logo.png";
import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Avatar from "@/components/ui/avatar";
import { and, inlineSwitch, truncateText } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import { usePlanRole } from "@/src/contexts/use-plan-role";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import {
    AnalyticsUpIcon,
    BloggerIcon,
    Briefcase01Icon,
    CalendarMinus01Icon,
    ChartBreakoutSquareIcon,
    DashboardBrowsingIcon,
    DashboardSquare01Icon,
    Edit01Icon,
    Link02Icon,
    MicrosoftAdminIcon,
    MoneySendSquareIcon,
    PackageAddIcon,
    Settings02Icon,
    SidebarLeft01Icon,
    SmartPhone01Icon,
    Target02Icon,
    TaskDaily01Icon,
    UserGroupIcon,
    UserShield02Icon,
    Wallet02Icon,
} from "hugeicons-react";
import { useNavigate } from "react-router-dom";
import { GetUserDetails } from "~/services/auth.service";
import { emailSettingsLinksMap } from "../email-settings";
import { MenuItem } from "./components/menu";
import { MenuItem as TMenuItem } from "./types";

const DashboardSidebar = ({
  toggleSidebar,
  isSidebarOpen,
  ...props
}: {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
  zIndexClass: "z-[500000]" | (string & {});
}) => {
  const { data: user } = useQuery({
    queryFn: async () => (await GetUserDetails()).data.data.user,
    queryKey: ["user-photo"],
    refetchOnWindowFocus: false,
  });
  const {
    planRole: { shortName: planRole },
  } = usePlanRole();
  const menuItems: TMenuItem[] = [
    {
      toShow() {
        return user?.role === "user";
      },
      title: "Overview",
      icon: "",
      subItems: [
        {
          title: "Dashboard",
          icon: <DashboardSquare01Icon />,
          link: `/dashboard/${user?.username}`,
        },
        {
          title: "Analytics",
          icon: <AnalyticsUpIcon />,
          link: "/analytics",
          planTag: () => {
            if (planRole === "premium") return null;
            else
              return {
                tag: "Premium",
                link: "/profile/billing",
              };
          },
        },
        {
          title: "Re-Engagement Stats",
          icon: <UserShield02Icon />,
          link: "/analytics/re-engagement",
          planTag: () => {
            if (planRole === "premium") return null;
            else
              return {
                tag: "Premium",
                link: "/profile/billing",
              };
          },
        },
      ],
    },
    {
      toShow() {
        return user?.role === "user";
      },
      title: "Business Management",
      icon: "",
      subItems: [
        {
          title: "Services",
          icon: <Briefcase01Icon />,
          link: "/scheduling/services",
        },
        {
          title: "Service Add-ons",
          icon: <PackageAddIcon />,
          link: "/scheduling/services/#add-ons",
        },
        {
          title: "Client List",
          icon: <UserGroupIcon />,
          link: "/clients",
        },
        {
          title: "Calendar Settings",
          icon: <Settings02Icon />,
          link: `/profile/calendar-settings`,
        },
      ],
    },
    {
      toShow() {
        return user?.role === "user";
      },
      title: "Teams & Staff",
      icon: "",
      subItems: [
        {
          title: "Overview",
          icon: <UserGroupIcon />,
          link: "/teams",
        },
        {
          title: "Staff Management",
          icon: <UserGroupIcon />,
          link: "/teams/staff",
        },
        {
          title: "Scheduling Rules",
          icon: <TaskDaily01Icon />,
          link: "/teams/rules",
        },
        {
          title: "Performance",
          icon: <ChartBreakoutSquareIcon />,
          link: "/teams/performance",
          planTag: () => {
            if (planRole === "premium") return null;
            else
              return {
                tag: "Premium",
                link: "/profile/billing",
              };
          },
        },
        {
          title: "Time Off",
          icon: <CalendarMinus01Icon />,
          link: "/teams/time-off",
        },
      ],
    },
    {
      toShow() {
        return user?.role === "user";
      },
      title: "Online Presence",
      icon: "",
      subItems: [
        {
          title: "Digital Profile",
          icon: <Link02Icon />,
          link: `/digital-profile/${user?.username}`,
        },
        {
          title: "Edit Profile",
          icon: <Edit01Icon />,
          link: "/digital-profile/edit",
        },
        {
          title: "Preview Booking Page",
          icon: <SmartPhone01Icon />,
          link: "/scheduling/services/preview",
        },
      ],
    },
    {
      toShow() {
        return user?.role === "user";
      },
      title: "Lead Generation",
      icon: "",
      subItems: [
        {
          title: "Leads Dashboard",
          icon: <DashboardSquare01Icon />,
          link: "/leads/dashboard",
        },
        {
          title: "View All Leads",
          icon: <Target02Icon />,
          link: "/leads",
        },
        {
          title: "Purchase Credits",
          icon: <Wallet02Icon />,
          link: "/leads/buy-credits",
        },
        {
          title: "Credit History",
          icon: <MoneySendSquareIcon />,
          link: "/leads/purchases",
        },
      ],
    },
    {
      toShow() {
        return user?.role === "user";
      },
      title: "Email Settings",
      icon: "",
      subItems: Object.entries(emailSettingsLinksMap).map(([k, v]) => ({
        icon: "",
        link: `/email-settings/${k}`,
        title: v.title,
      })),
    },
    {
      title: "Admin Panel",
      icon: <MicrosoftAdminIcon />,
      toShow() {
        return ["admin"].includes(user?.role || "");
      },
      link: "/admin",
    },
    {
      title: "Blog Management",
      icon: "",
      toShow() {
        return ["admin", "blogger"].includes(user?.role || "");
      },
      subItems: [
        {
          title: "All Posts",
          icon: <BloggerIcon />,
          link: "/blog",
        },
        {
          title: "New Post",
          icon: <BloggerIcon />,
          link: "/blog/create",
        },
      ],
    },
    {
      toShow() {
        return user?.role === "user";
      },
      title: "Account Settings",
      icon: <Settings02Icon />,
      link: "/profile",
    },
    {
      title: "Component Showcase",
      icon: <DashboardBrowsingIcon />,
      toShow() {
        return import.meta.env.DEV;
      },
      link: "/showcase",
    },
  ];
  const navigate = useNavigate();

  return (
    <motion.section
      variants={{
        closed: {
          x: -100,
          opacity: 0,
        },
        open: {
          x: 0,
          opacity: 1,
        },
      }}
      initial={"closed"}
      animate={isSidebarOpen ? "open" : "closed"}
      transition={{
        type: "keyframes",
      }}
      // formerly had py-4 pl-3 and didn't have a background color
      className={`w-full h-screen fixed top-0 left-0 ${
        props.zIndexClass
      } md:w-fit lg:md:w-[320px] ${
        isSidebarOpen && "bg-white/25 backdrop-blur-[8px]"
      } `}
      onClick={(e) => {
        if (e.target === e.currentTarget) toggleSidebar();
      }}
    >
      {/* formerly had rounded-[28px] */}
      <section className="w-72 bg-[#fff] border-r-2 border-[#f2f2f2] h-full p-6 px-0 no-scrollbar flex flex-col gap-y-1.5 select-none md:max-w-none md:w-fit lg:md:w-[320px] ">
        <div className="relative flex justify-between mb-5 px-5">
          <img src={Logo} className="size-10 object-contain " />
          <Button
            className="absolute top-0 right-3 md:right-5 rounded-full p-1 bg-transparent text-paragraph md:hidden "
            variant="icon"
            onTap={toggleSidebar}
          >
            <SidebarLeft01Icon width={18} height={18} />
          </Button>
        </div>
        <div className="flex-grow overflow-y-auto no-scrollbar px-3 pb-20 flex flex-col gap-y-5 md:px-5">
          {menuItems.map((item, index) => {
            if (!item.toShow || item.toShow())
              return (
                <div
                  key={index}
                  className={cn({
                    "last:mt-auto": user?.role === "user",
                  })}
                >
                  <MenuItem
                    key={index}
                    {...item}
                    closeSidebar={() => toggleSidebar()}
                  />
                </div>
              );
            else return null;
          })}
        </div>
        {and([user?.role === "user", planRole !== "premium"]) && (
          <div className="w-full border-sidebar_text px-3 md:px-5">
            <div
              className="bg-transparent text-start px-4 py-2 flex items-center gap-x-3 cursor-pointer group rounded-2xl "
              style={{
                background: "linear-gradient(135deg, #e91e63 0%, #f06897 100%)",
                boxShadow: "0 8px 32px rgba(233, 30, 99, 0.3)",
              }}
            >
              <img
                src={Crown}
                className="object-contain"
                width={42}
                height={42}
              />
              <div className="flex flex-col gap-y-0.5">
                <Button
                  onTap={() => navigate("/profile/billing?upgradeTo=Premium")}
                  variant="full"
                  className="w-fit underline text-white bg-transparent !p-0"
                >
                  Unlock Premium
                </Button>
                <Typography className="text-gray-50 max-w-[140px] text-sm whitespace-nowrap overflow-x-hidden overflow-ellipsis ">
                  Get access to Premium tools
                </Typography>
              </div>
            </div>
          </div>
        )}
        <Button
          onTap={() => navigate("/profile")}
          className="bg-transparent py-0 text-start px-5 flex items-center gap-x-3 cursor-pointer group"
        >
          <Avatar src={user?.user_photo} alt="User Avatar" size={42} />
          <div className="flex flex-col gap-y-0.5">
            <Typography className="text-paragraph group-hover:underline ">
              {inlineSwitch(user?.role, ["user", user?.business_name], {
                default: truncateText(
                  `${user?.first_name || ""} ${user?.last_name || ""}`,
                  20,
                ),
              })}
            </Typography>
            <Typography className="text-paragraph/50 text-sm group-hover:underline ">
              {inlineSwitch(
                user?.role,
                ["admin", "Admin"],
                ["blogger", "Blogger"],
                { default: "Business" },
              )}
            </Typography>
          </div>
        </Button>
      </section>
    </motion.section>
  );
};

export default DashboardSidebar;
