"use client";

import { TrendingUp } from "lucide-react";
import { Pie, Pie<PERSON><PERSON> } from "recharts";

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

const elementData = [
  { elementId: "submit-button", clicks: 450, name: "Submit" },
  { elementId: "menu-toggle", clicks: 380, name: "<PERSON><PERSON>" },
  { elementId: "profile-link", clicks: 280, name: "Profile" },
  { elementId: "settings-btn", clicks: 220, name: "Settings" },
  { elementId: "logout-btn", clicks: 150, name: "Logout" },
];

elementData;
const pieChartColours = [
  "hsl(173 58% 39%)",
  "hsl(12 76% 61%)",
  "hsl(347 77% 50%)",
  "hsl(197 37% 24%)",
  "hsl(352 83% 91%)",
  "hsl(43 74% 66%)",
  "hsl(350 80% 72%)",
  "hsl(27 87% 67%)",
  "hsl(349 77% 62%)",
];

const chartData = [
  { type: "click", value: 450 },
  { type: "hover", value: 300 },
  { type: "focus", value: 200 },
  { type: "focus", value: 200 },
].map((data, index) => ({
  ...data,
  fill: pieChartColours[index % pieChartColours.length],
}));

const chartConfig = {
  click: {
    label: "Click",
  },
  hover: {
    label: "Hover",
  },
  focus: {
    label: "Focus",
  },
  other: {
    label: "Other",
  },
} satisfies ChartConfig;

function InteractionAnalyticsChart() {
  return (
    <Card className="flex flex-col">
      <CardHeader className="items-center pb-0">
        <CardTitle>Interaction Analytics</CardTitle>
        <CardDescription>January - June 2024</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[250px] pb-0 [&_.recharts-pie-label-text]:fill-foreground"
        >
          <PieChart>
            <ChartTooltip content={<ChartTooltipContent hideLabel />} />
            <Pie data={chartData} dataKey="value" label nameKey="type" />
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 font-medium leading-none">
          Trending up by 5.2% this month <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">
          Showing total visitors for the last 6 months
        </div>
      </CardFooter>
    </Card>
  );
}

export default InteractionAnalyticsChart;
