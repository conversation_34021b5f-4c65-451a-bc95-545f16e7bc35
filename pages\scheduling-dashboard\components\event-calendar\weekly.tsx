import { Typography } from "@/components/typography";
import { cn } from "@/lib/utils";
import { CalendarEvent } from "~/interfaces/booking";
import { formatDate } from "date-fns";
import { motion } from "framer-motion";
import { memo, useEffect } from "react";
import MarkerTooltip from "./marker-tooltip";
import { type Weeks } from "../../use-calendar";
import EventGroup from "./event-group";

const allColors = [
  "#87CEEB", // Sky Blue
  "#ADD8E6", // Light Blue
  "#B0E0E6", // Powder Blue
  "#FFB347",
  "#FFC0CB",
];

export const businessDays = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

const dayBlockHeight = 80;

function calculateTopPosition(startDate: string) {
  // Parse the event start time
  const eventDay = new Date(startDate).getDay();

  // Calculate position: each day block is 80px (h-20)
  const topPosition =
    // this is because original ordering of .getDay() is [Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday] and ordering of businessDays array is [Monday, Tuesday, Wednesday, Thursday, Friday, Saturday, Sunday]
    (eventDay === 0 ? 6 : eventDay - 1) * dayBlockHeight;
  return topPosition;
}

export function calculateEventHeight(startTime: string, endTime: string) {
  const start = new Date(startTime);
  const end = new Date(endTime);

  // Get difference in milliseconds and convert to days
  const diffInDays = (end.getTime() - start.getTime()) / (1000 * 60 * 60);

  return diffInDays * dayBlockHeight;
}

function pickRandomColor() {
  const randomIndex = Math.floor(Math.random() * allColors.length);
  return allColors[randomIndex];
}

/**
 * Checks if a given date falls within a week range
 * @param date The date to check
 * @param week A tuple [startDate, endDate] representing Monday and Sunday of a week
 * @returns Boolean indicating whether the date is within the week range
 */
function isDayWithinWeek(date: Date, week: [Date, Date]): boolean {
  // Create copies to avoid modifying original dates
  const checkDate = new Date(date);
  const weekStart = new Date(week[0]);
  const weekEnd = new Date(week[1]);

  // Set all dates to midnight to compare only the date part
  checkDate.setHours(0, 0, 0, 0);
  weekStart.setHours(0, 0, 0, 0);
  weekEnd.setHours(0, 0, 0, 0);

  // Check if the date is greater than or equal to the start of the week
  // and less than or equal to the end of the week
  return (
    checkDate.getTime() >= weekStart.getTime() &&
    checkDate.getTime() <= weekEnd.getTime()
  );
}

/**
 * Groups events by week based on the monthWeeks ranges
 * @param events Array of calendar events to group
 * @returns Array of event arrays, where each inner array contains events from the same week
 */
function getEventGroups(events: CalendarEvent[]): CalendarEvent[][] {
  // Create an array to hold events for each day
  const dayGroups: CalendarEvent[][] = Array(businessDays.length)
    .fill(null)
    .map(() => []);

  // Sort events by start date
  const sortedEvents = [...events].sort(
    (a, b) => new Date(a.start).getTime() - new Date(b.start).getTime()
  );

  // Iterate through each event and assign to appropriate day group
  for (const event of sortedEvents) {
    const eventDay = formatDate(event.start, "eee")
      .replace(/[\n\t]/g, "")
      .toLowerCase();

    // Find which day this event belongs to
    const dayIndex = businessDays.findIndex(
      (day) => eventDay === day.toLowerCase()
    );

    // If we found a matching day, add the event to that day's group
    if (dayIndex !== -1) {
      dayGroups[dayIndex].push(event);
    }
  }

  // Filter out empty day groups
  return dayGroups.filter((group) => group.length > 0);
}

/**
 * @dev do not use directly, memoize it
 */
const EventCard = ({
  event: { start, end, ...rest },
  ...props
}: {
  event: CalendarEvent;
  onClick?: (event: CalendarEvent) => void;
}) => {
  const topPosition = calculateTopPosition(start);
  // const minHeight = calculateEventHeight(start, end);
  return (
    <div
      style={{
        top: `${topPosition}px`,
      }}
      className="w-full absolute px-1 left-0"
    >
      <motion.div
        whileTap={{ scale: 0.9 }}
        whileHover={{ scale: 1.05 }}
        onClick={() => props.onClick?.({ start, end, ...rest })}
        className="w-full rounded-xl py-2 px-2.5 cursor-pointer flex flex-col gap-y-1 "
        style={{
          minHeight: "fit-content",
          background: pickRandomColor(),
        }}
      >
        <p className="text-[#171717]/80 font-medium text-xs line-clamp-1">
          {rest.extendedProps?.customerInfo?.firstName}{" "}
          {rest.extendedProps?.customerInfo?.lastName}
        </p>
        <p className="text-[#171717]/70 text-xs font-semibold line-clamp-1">
          {rest.title}
        </p>
        <p className=" text-xs font-semibold">
          {new Date(start).toLocaleTimeString([], {
            hour: "numeric",
            minute: "2-digit",
          })}{" "}
          -{" "}
          {new Date(end).toLocaleTimeString([], {
            hour: "numeric",
            minute: "2-digit",
          })}
        </p>
      </motion.div>
    </div>
  );
};

const MemoizedEventCard = memo(EventCard);

type Props = {
  events: CalendarEvent[];
  onEventClick: (event: CalendarEvent) => void;
  selectedDate: Date;
  setSelectedDate: React.Dispatch<React.SetStateAction<Date>>;
  weeks: Weeks;
};

const WeeklyCalendar = ({
  weeks,
  selectedDate,
  setSelectedDate,
  events,
  ...props
}: Props) => {
  const today = Object.freeze(new Date());
  useEffect(() => {
    // when ever weeks change, we check the first set it as selectedDate month
    const [month, fullYear] = [
      weeks[0][0].getMonth(),
      weeks[0][0].getFullYear(),
    ];
    if (month !== selectedDate.getMonth()) {
      selectedDate.setMonth(month);
      selectedDate.setFullYear(fullYear);
      setSelectedDate(new Date(selectedDate));
    }
  }, [weeks]);

  return (
    <section className="w-full h-fit space-y-10 select-none ">
      <section className="w-full flex gap-x-2 pb-10 md:gap-x-0">
        <div className="whitespace-nowrap h-full pt-28 flex flex-col gap-y-10 md:w-20 ">
          {businessDays.map((day) => (
            <Typography
              key={day}
              variant={"p"}
              className="text-[#AFAFAF] font-bold text-center !min-h-10 flex flex-col items-center justify-center text-sm !mt-0 md:text-base "
            >
              {day}
            </Typography>
          ))}
        </div>
        <div className="w-full h-full flex flex-col gap-y-4 items-start flex-grow overflow-x-auto no-scrollbar">
          <div className="min-w-full h-fit flex items-center ">
            {weeks.map(([firstWeekDay, lastWeekDay], index) => (
              <MarkerTooltip
                content={`
                  ${formatDate(
                    firstWeekDay,
                    "EEE dd MMM, yyyy"
                  )} - ${formatDate(lastWeekDay, "EEE dd MMM, yyyy")}`}
                key={index}
              >
                <div
                  data-iscurrent={isDayWithinWeek(today, [
                    firstWeekDay,
                    lastWeekDay,
                  ])}
                  className="w-44 h-24 gap-y-0 text-[#AFAFAF] flex flex-col items-center justify-center data-[iscurrent=true]: cursor-pointer"
                >
                  <Typography variant={"p"} className="text-inherit text-xs">
                    {formatDate(firstWeekDay, "MMM dd yyyy")}
                  </Typography>
                  |
                  <Typography
                    variant={"p"}
                    className="text-inherit text-xs !font-bold !mt-0 "
                  >
                    {formatDate(lastWeekDay, "MMM dd yyyy")}
                  </Typography>
                </div>
              </MarkerTooltip>
            ))}
          </div>
          {/* fixed width here */}
          <div className="w-[77rem] relative ">
            <div className="w-full flex-grow flex flex-col gap-y-10 ">
              {businessDays.map((businessDay) => (
                <div
                  key={businessDay}
                  className="w-full h-10 flex flex-col justify-center items-center"
                >
                  <div className="w-full h-0.5 bg-subtle-gray " />
                </div>
              ))}
            </div>
            {/* events container */}
            <div className="min-w-full h-full flex-grow absolute left-0 top-0 z-10 flex pt-5 ">
              {weeks.map((week, index) => {
                const matchingEvents = events.filter((event) =>
                  isDayWithinWeek(new Date(event.start), week)
                );
                const groupedEvents = getEventGroups(matchingEvents);
                return (
                  <div key={index} className={cn("h-full relative w-44 ")}>
                    {groupedEvents.map((eventGroup, eventGroupIndex) => {
                      if (eventGroup.length === 1)
                        return (
                          <MemoizedEventCard
                            onClick={() => {
                              props.onEventClick(eventGroup[0]);
                            }}
                            key={eventGroupIndex}
                            event={eventGroup[0]}
                          />
                        );
                      else if (eventGroup.length > 1)
                        return (
                          <EventGroup
                            key={eventGroupIndex}
                            events={eventGroup}
                            topPosition={calculateTopPosition(
                              eventGroup[0].start
                            )}
                            onClickEvent={props.onEventClick}
                          />
                        );
                      else return null;
                    })}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>
    </section>
  );
};

export default WeeklyCalendar;
