import { Input } from "@/components/inputs";
import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { useFormik } from "formik";
import { object, string } from "yup";
import { useToast } from "~/contexts/hooks/toast";
import { useSearchParams } from "react-router-dom";
import CircularLoader from "@/components/ui/circular-loader";
import { useMutation } from "@tanstack/react-query";
import { PasswordReset } from "@/src/services/auth.service";
import { noop } from "@/lib/helpers";
import Logo from '@/assets/logo.png'
import { Tick02Icon } from 'hugeicons-react'

export default function RequestResetPassword() {
  const showToast = useToast();
  const [searchParams] = useSearchParams()
  const email = searchParams.get('email')
  const validationSchema = object({
    email: string().email("Invalid email").required("Email is required"),
  });
  const requestPasswordResetMutation = useMutation({
    mutationFn: PasswordReset.RequestReset
  })
  const formik = useFormik({
    enableReinitialize: true,
    initialValues: { email: email || '' },
    validationSchema,
    onSubmit: noop
  });

  return (
    <section className="w-screen h-screen flex flex-col items-center justify-center px-4">
      <section className="w-screen h-screen md:max-w-[400px] px-4 overflow-y-auto pt-10 gap-y-5 flex flex-col no-scrollbar ">
        <div className="w-full flex flex-col items-center gap-y-3 justify-center">
          <img src={Logo} className="w-full size-28 object-contain  " />
          <Typography variant={'h3'} className="font-bold text-3xl text-center">
            Puzzle Piece <br /> Solutions
          </Typography>
        </div>

        {requestPasswordResetMutation.data ? (<div className=" flex flex-col items-center gap-y-4 ">
          <Typography variant={"h1"}
            className="text-[28px] font-Bricolage_Grotesque overflow-hidden text-start font-bold "
          >
            Email sent!
          </Typography>
          <div className="flex flex-col gap-y-4 pt-2 px-3">
            <div className="p-8 w-fit h-fit bg-green-500 rounded-full mx-auto">
              <Tick02Icon className="w-8 h-8 text-white " />
            </div>
            <Typography variant="p" className="!mt-0 text-center">
              An email has been sent to {email}.
            </Typography>
            <Typography variant="p" className="!mt-0 text-center text-paragraph/50 text-sm ">
              Please check your inbox
              and follow the instructions to complete the reset process.
            </Typography>
          </div>
        </div>) : (
          <>
            <Typography variant="h1"
              className="text-[28px] font-Bricolage_Grotesque overflow-hidden text-start font-bold "
            >
              Forgot Password?
            </Typography>
            <form
              onSubmit={formik.handleSubmit}
              className="w-full max-w-sm flex flex-col gap-4"
            >
              <Input.Email
                label="Email"
                name="email"
                required
                value={formik.values.email}
                onChange={(e) => formik.setFieldValue("email", e.target.value)}
              />
              <Button onTap={() => {
                requestPasswordResetMutation.mutateAsync({ email: formik.values.email }, {
                  onSuccess() {
                    showToast("success", "Reset Link Sent", {
                      description: "Check your email for a reset link.",
                    });
                  },
                  onError(error) {
                    showToast("error",
                      (error as unknown as ApiError).response?.data.error ||
                      "Something went wrong",
                      {
                        description: "Please try again later.",
                      });
                  }
                })
              }} disabled={requestPasswordResetMutation.isPending}>
                {requestPasswordResetMutation.isPending ? <CircularLoader color="white" /> : "Send Reset Link"}
              </Button>
            </form>
          </>
        )}
      </section>
    </section>
  );
}

