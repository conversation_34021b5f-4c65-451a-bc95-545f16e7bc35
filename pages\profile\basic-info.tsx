import { UpdateUserInfo } from "~/services/auth.service";
import { But<PERSON>, SaveButton } from "@/components/buttons";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useToast } from "~/contexts/hooks/toast";
import useAuthStore from "../auth/components/auth-store";
import { Input } from "@/components/inputs";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import { noop } from "@/lib/helpers";
import UploadUserProfileImage from "./components/upload-image";
import { Copy } from "@gravity-ui/icons";
import { Country, State } from "country-state-city";
import { useEffect, useMemo, useState } from "react";
import industries from "@/src/data/all_industries.json";
import PendingOverlay from "@/components/ui/pending-overlay";

const basicInfoValidationSchema = Yup.object({
  first_name: Yup.string().required("First name is required"),
  last_name: Yup.string().required("Last name is required"),
  username: Yup.string().required("Username is required"),
  email: Yup.string().required("Email address is required"),
  address: Yup.string().required("Address is required"),
  address_2: Yup.string(),
  industry: Yup.string().required("Industry is allowed"),
  postcode: Yup.string().required("Post code is required"),
  town: Yup.string().required("Town is required"),
  country: Yup.string().required("Country is required"),
  phone: Yup.string().required("Phone number is required"),
  business_bio: Yup.string(),
  business_name: Yup.string().required("Business name is required"),
  timeFormat: Yup.string().default("24h"),
  dateFormat: Yup.string(),
});

const fields = [
  {
    label: "First Name",
    name: "first_name",
    type: "text",
    required: true,
  },
  {
    label: "Last Name",
    name: "last_name",
    type: "text",
    required: true,
  },
  {
    label: "Email",
    name: "email",
    type: "email",
    required: true,
  },
  {
    label: "Username",
    name: "username",
    type: "text",
    required: true,
  },
  {
    label: "Business name",
    name: "business_name",
    type: "text",
    required: true,
  },
  {
    label: "Postcode",
    name: "postcode",
    type: "text",
    required: true,
  },
  {
    label: "Address",
    name: "address",
    type: "text",
    required: true,
  },
  {
    label: "Address 2",
    name: "address_2",
    type: "text",
    required: false,
  },
] as const;

const FieldTypeMap = {
  text: Input.Text,
  password: Input.Password,
  email: Input.Email,
  phone: Input.Phone,
};

const BasicInfoForm = () => {
  const {
    initialState: { user },
  } = useAuthStore();
  const queryclient = useQueryClient();
  const showToast = useToast();
  const updateBasicInfoMutation = useMutation({
    mutationFn: UpdateUserInfo,
    onSuccess: () => {
      queryclient.invalidateQueries({ queryKey: ["userDetails"] });
      showToast("success", "Basic information updated successfully");
    },
    onError: (error: ApiError) => {
      showToast("error", "Error occured", {
        description:
          error.response?.data.error || "Failed to update basic information",
      });
    },
  });
  const handleBasicInfoUpdate = () => {
    formik.validateForm().then((val) => {
      const errors = Object.values(val);
      if (errors.length === 0) {
        updateBasicInfoMutation.mutateAsync({
          ...formik.values,
        });
      } else
        errors.forEach((error, i) =>
          setTimeout(() => {
            showToast("error", "Invalid Field", {
              description: error,
            });
          }, i * 500),
        );
    });
  };
  const formik = useFormik({
    validationSchema: basicInfoValidationSchema,
    initialValues: {
      first_name: user?.first_name || "",
      last_name: user?.last_name || "",
      username: user?.username || "",
      email: user?.email || "",
      address: user?.address || "",
      industry: user?.industry || "",
      address_2: user?.address_2 || "",
      town: user?.town || "",
      postcode: user?.postcode || "",
      country: user?.country || "",
      phone: user?.phone || "",
      business_name: user?.business_name || "",
      business_bio: user?.business_bio || "", // textarea
      timeFormat: (user?.timeFormat || "") as "12h",
      dateFormat: user?.dateFormat || "",
    },
    onSubmit: noop,
  });
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const businessServicesLink = useMemo(() => {
    return import.meta.env.VITE_FRONTEND_URL + '/services/' + user?.username
  }, [user?.username])
  useEffect(() => {
    if (user?.country) {
      const userCountry = Country.getAllCountries().find(
        (country) => country.name === user?.country,
      );
      if (userCountry) setSelectedCountry(userCountry.isoCode);
    }
  }, [user?.country]);

  return (
    <section className="">
      <div className="w-full py-2 flex items-center justify-center">
        <UploadUserProfileImage />
      </div>
      <form className="w-full h-fit">
        <div className="w-full grid grid-cols-2 gap-x-2 gap-y-4">
          {fields.map((field, index) => {
            const InputType = FieldTypeMap[field.type];
            return (
              <InputType
                key={index}
                label={field.label}
                name={field.name}
                value={formik.values[field.name]}
                required={field.required}
                onChange={({ currentTarget: { name, value } }) => {
                  formik.setFieldValue(name, value);
                }}
              />
            );
          })}
          <Input.Phone
            label="Phone Number"
            name="phone"
            value={formik.values["phone"]}
            required
            onChange={(value, e) => {
              formik.setFieldValue(e.currentTarget.name, value);
            }}
          />
          {/* booking link disabled now because it leads to internal booking, which that api is not stable yet */}
          <div className="flex items-end gap-x-2">
            <Input.Text
              label="Services Link"
              name="services_link"
              disabled
              className="flex-grow"
              value={businessServicesLink}
              onChange={noop}
            />
            <Button
              variant="icon"
              type="button"
              className="rounded-lg bg-gray-200 "
              onTap={() => {
                navigator.clipboard
                  .writeText(businessServicesLink || "")
                  .then(() => showToast("success", "Copied to clipboard"))
                  .catch((err) =>
                    showToast("error", "Failed to copy to clipboard", {
                      description: err.message,
                    }),
                  );
              }}
            >
              <Copy className="text-paragraph" />
            </Button>
          </div>
        </div>
        <div className="w-full flex flex-col gap-y-3 py-5">
          <Input.TextArea
            label="Business Bio"
            name="business_bio"
            value={formik.values.business_bio}
            onChange={({ currentTarget: { name, value } }) => {
              formik.setFieldValue(name, value);
            }}
          />
        </div>
        <div className="w-full flex flex-col gap-y-3 pt-3 pb-5">
          <div className="w-full grid grid-cols-2 gap-x-2 gap-y-3 ">
            <div className="w-full space-y-2">
              <label
                htmlFor="town"
                className="text-sm font-semibold text-paragraph"
              >
                Country
              </label>
              <SearchableDropdown
                options={Country.getAllCountries().map((option) => ({
                  label: option.name,
                  value: option.name,
                  data: option,
                }))}
                inputProps={{
                  className: "py-3",
                }}
                fullWidth
                placeholder="Select a Country"
                value={{
                  label: formik.values.country,
                  value: formik.values.country,
                }}
                onChange={(value) => {
                  formik.setFieldValue("country", value?.value || "");
                  setSelectedCountry(value?.data?.isoCode || null);
                }}
              />
            </div>
            {selectedCountry && formik.values.country && (
              <div className="w-full space-y-2 ">
                <label
                  htmlFor="town"
                  className="text-sm font-semibold text-paragraph"
                >
                  Town
                </label>
                <SearchableDropdown
                  options={State.getStatesOfCountry(selectedCountry).map(
                    (option) => ({
                      label: option.name,
                      value: option.name,
                    }),
                  )}
                  inputProps={{
                    className: "py-3",
                  }}
                  fullWidth
                  placeholder="Select a Town"
                  value={{
                    label: formik.values.town,
                    value: formik.values.town,
                  }}
                  onChange={(value) => {
                    formik.setFieldValue("town", value?.value || "");
                  }}
                />
              </div>
            )}
            <div className="w-full space-y-2">
              <label
                htmlFor="dateFormat"
                className="text-sm font-semibold text-paragraph"
              >
                Date format
              </label>
              <SearchableDropdown
                options={["YYYY-MM-DD", "DD-MM-YYYY"].map((option) => ({
                  label: option,
                  value: option,
                }))}
                fullWidth
                placeholder="YYYY-MM-DD"
                value={{
                  label: formik.values.dateFormat || "",
                  value: formik.values.dateFormat || "",
                }}
                onChange={(value) => {
                  if (!value) return;
                  formik.setFieldValue("dateFormat", value.value);
                }}
              />
            </div>
            <div className="w-full space-y-2">
              <label
                htmlFor="timeFormat"
                className="text-sm font-semibold text-paragraph"
              >
                Time format
              </label>
              <SearchableDropdown
                options={["12h", "24h"].map((option) => ({
                  label: option,
                  value: option,
                }))}
                fullWidth
                placeholder="Select a Time Format"
                value={{
                  label: formik.values.timeFormat || "",
                  value: formik.values.timeFormat || "",
                }}
                onChange={(value) => {
                  if (!value) return;
                  formik.setFieldValue("timeFormat", value.value);
                }}
              />
            </div>
          </div>

          <div className="w-full space-y-2">
            <label
              htmlFor="timeFormat"
              className="text-sm font-semibold text-paragraph"
            >
              Industry
            </label>
            <SearchableDropdown
              options={industries.map((option) => ({
                label: option,
                value: option,
              }))}
              fullWidth
              placeholder="Select an Industry"
              value={{
                label: formik.values.industry || "",
                value: formik.values.industry || "",
              }}
              onChange={(value) => {
                formik.setFieldValue("industry", value?.value || "");
              }}
            />
          </div>
        </div>
      </form>
      <SaveButton
        disabled={updateBasicInfoMutation.isPending}
        loading={updateBasicInfoMutation.isPending}
        onTap={handleBasicInfoUpdate}
        title="Save Changes"
      >
      </SaveButton>
      <PendingOverlay isPending={updateBasicInfoMutation.isPending} />
    </section>
  );
};

export default BasicInfoForm;
