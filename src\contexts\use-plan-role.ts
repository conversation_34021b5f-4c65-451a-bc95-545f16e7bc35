// write the context for the plan role, plan role types are 'free', 'basic', 'premium'
import { createContext, useContext } from "react";

export type PlanRole = {
  /**
   * @dev in special cases plan shortname can be none
   */
  shortName: 'none' | "free" | "basic" | "premium"
  fullName: 'None' | "Free" | "Basic" | "Premium"
  id: string
};

type PlanRoleContextType = {
  planRole: PlanRole;
  isPremium: () => boolean
};

const PlanRoleContext = createContext<PlanRoleContextType | null>(null);

const usePlanRole = () => {
  const planRoleContext = useContext(PlanRoleContext);
  if (!planRoleContext) {
    throw new Error("usePlanRole must be used within a PlanRoleProvider");
  }
  return planRoleContext;
};

const PlanRoleProvider = PlanRoleContext.Provider;

export { PlanRoleProvider, usePlanRole };
