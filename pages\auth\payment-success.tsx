import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { useToast } from "~/contexts/hooks/toast";
import {
  SaveDefaultPaymentMethod,
  SaveDefaultPaymentMethodPayload,
} from "~/services/stripe.service";
import { Elements, useElements, useStripe } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { GetUserDetails } from "@/src/services/auth.service";
import Modal from "@/components/ui/modal";
import { noop } from "@/lib/helpers";
import { GetCurrentUserSubscriptionPlan } from "@/src/services/payment.service";
import CircularLoader from "@/components/ui/circular-loader";
import { Landmark } from "lucide-react";
import { formatDate } from "date-fns";
import ErrorScreen from "@/components/ui/error-screen";
import Logo from "@/assets/images/Puzzle-Pc-White-bg.png";

// Initialize Stripe outside the component to avoid re-initializing on every render
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

const PaymentSuccess = () => {
  const stripe = useStripe();
  const elements = useElements();
  const [searchParam] = useSearchParams();
  const { data: userData, error: userDataError } = useQuery({
    queryKey: ["payment-success"],
    queryFn: async () => (await GetUserDetails()).data.data.user,
  });
  const clientSecret = searchParam.get(
    "payment_intent_client_secret"
  ) as string;
  const showToast = useToast();
  const savePaymentId = async (paymentMethodId: string) => {
    if (!userData)
      return showToast("info", "Please log in to save payment method");
    const userId = userData?._id;
    try {
      const payload: SaveDefaultPaymentMethodPayload = {
        userId: userId as string,
        paymentId: paymentMethodId,
        type: "default",
        paymentType: "card",
      };
      const response = await SaveDefaultPaymentMethod(payload)
        .then((res) => res)
        .catch((error) => {
          throw error;
        });
      if ([200, 201].includes(response.status)) {
        showToast("success", "Payment method saved", {
          description:
            response.data.message ||
            "Your payment method has been saved successfully.",
        });
      }
    } catch (error) {
      showToast("error", "An error occured", {
        description:
          (error as any).response?.data?.message ||
          "An error occured while saving your payment method. Try reloading the page",
      });
    }
  };
  const retrieveAndSavePaymentMethod = async () => {
    if (!stripe || !elements) return;
    const { error, paymentIntent } = await stripe.retrievePaymentIntent(
      clientSecret
    );
    if (paymentIntent?.payment_method) {
      await savePaymentId(paymentIntent?.payment_method as string);
    } else if (error) {
      showToast("error", "An error occured", {
        description:
          error.message ||
          "An error occured while saving your payment method. Try reloading the page",
      });
    }
  };
  const navigate = useNavigate();
  const {
    data: userCurrentPlan,
    isFetching,
    error: userCurrentPlanError,
  } = useQuery({
    queryKey: ["user-current-plan"],
    queryFn: async () => (await GetCurrentUserSubscriptionPlan()).data.plan,
  });
  useEffect(() => {
    if (clientSecret && userData) retrieveAndSavePaymentMethod();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clientSecret, stripe, elements, userData]);
  if (userDataError || userCurrentPlanError || !clientSecret)
    return (
      <ErrorScreen
        className="mt-20"
        title="Unknown error occured"
        message="An error occurred while retrieving user data or subscription plan. Please ensure you are logged in and have a stable network connection. If the issue persists, try again later."
        onRetry={{
          title: "Reload page",
          action() {
            location.reload();
          },
        }}
      />
    );
  if (isFetching)
    return (
      <div className="mx-auto py-20">
        <CircularLoader className="" />
      </div>
    );
  return (
    <section className="w-full h-screen ">
      <Modal open={true} onClose={noop} className="bg-transparent">
        <Modal.Body
          className={`min-h-fit min-w-80 max-w-[360px] shadow-md h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
        >
          <div className="w-full flex justify-center">
            <img src={Logo} className="w-full max-w-[150px] object-contain  " />
          </div>
          <div className="w-full flex justify-center items-center">
            <Typography variant={"h4"} className="font-bold ">
              Payment Successful
            </Typography>
          </div>
          <div className="py-6 w-full space-y-5">
            <Typography
              variant={"p"}
              className="text-paragraph text-center text-xl font-bold"
            >
              USD {userCurrentPlan?.price.toFixed(2)}
            </Typography>
            <div className="flex items-start gap-x-3">
              <Landmark
                size={28}
                className="bg-primary-50/50 rounded-full p-1.5 min-w-fit"
              />
              <div className="flex flex-col gap-y-1">
                <Typography className="text-sm font-semibold">
                  Payment Made Successfully!
                </Typography>
                <Typography className="text-xs font-normal">
                  {formatDate(new Date(), "dd MMM yyyy, hh:mm a")}
                </Typography>
              </div>
            </div>
            <div className="py-2 px-3 bg-sky-50/30 rounded-xl space-y-1">
              <div className="w-full flex items-center justify-between text-sm">
                <Typography variant={"p"} className="font-normal">
                  Plan Name
                </Typography>
                <Typography variant={"p"} className="font-semibold">
                  {userCurrentPlan?.name}
                </Typography>
              </div>
              <div className="w-full flex items-center justify-between text-sm">
                <Typography variant={"p"} className="font-normal">
                  Plan Price
                </Typography>
                <Typography variant={"p"} className="font-semibold">
                  ${userCurrentPlan?.price}
                </Typography>
              </div>
            </div>
          </div>
          <div className="w-full flex justify-between items-center mt-0">
            <Button
              onTap={() => {
                navigate("/signin");
              }}
              className="w-full"
            >
              Login
            </Button>
          </div>
        </Modal.Body>
      </Modal>
    </section>
  );
};

export default function PaymentSuccessPage() {
  return (
    <Elements stripe={stripePromise}>
      <PaymentSuccess />
    </Elements>
  );
}
