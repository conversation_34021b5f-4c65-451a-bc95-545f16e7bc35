#calendar2 .fc-daygrid-event {
    display: none; 
}
  
#calendar2 .fc-daygrid-day {
    height: 40px;
}
  
#calendar2 .fc-daygrid-event .fc-daygrid-body .fc-scrollgrid-sync-table {
    width: 100% !important; 
}

.fc-daygrid-day.fc-day-today {
    background-color: rgba(0, 0, 0, 0.1);
}

.circle-date .fc-daygrid-day-number{
    border: 2px solid black !important;
    border-radius: 50% !important;
    box-sizing: border-box !important;
    height: 32px;
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}
/* .fc {
    width: 100% !important;
} */
/* Override min-height for day events */
#calendar2.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events {
    min-height: unset !important;
}

/* Override height for day cells */
#calendar2.fc .fc-daygrid-day {
    height: unset !important;
}

/* style.css */
.highlight-week {
    background-color: #d3f4ff !important; /* Light blue background */
    color: #000; /* Dark text color */
    border-radius: 50%;
  }
  
  /* style.css */
/* .highlight-week { */
    /* background-color: #d3f4ff !important; Light blue background */
    /* color: #000 !important; Dark text color */
    /* border-radius: 50%; Make the cell circular */
    /* border: 2px solid black; Add a 2px black border */
    /* padding: 10px; Add some padding to space out the border */
    /* box-sizing: border-box; Ensure padding doesn't increase cell size */
