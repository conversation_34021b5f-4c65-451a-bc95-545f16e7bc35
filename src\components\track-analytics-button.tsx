import { But<PERSON> } from "@/components/buttons";
import { useMutation, useQuery } from "@tanstack/react-query";
import { TrackUserInteraction } from "../services/analytics.service";
import { GetUserDetails } from "../services/auth.service";

/**
 * @note this button should be used on most pages
 * @dev get userId from start with query and consistent query key thourhgout the app
 * @see https://tanstack.com/query/latest/docs/framework/react/guides/query-keys for more details on query keys
 * @dev see if this function should be debounced to reduce api calls
 */
const TrackAnalyticsButton: typeof Button = ({
  onTap,
  onMouseOver,
  type,
  ...props
}) => {
  const { data: user } = useQuery({
    queryFn: async () => (await GetUserDetails()).data.data.user,
    queryKey: ["user"],
    refetchOnWindowFocus: false,
  });
  const { mutateAsync } = useMutation({
    mutationFn: TrackUserInteraction,
    mutationKey: ["track-interaction"],
  });
  return (
    <Button
      {...props}
      onTap={(e, info) => {
        onTap?.(e, info);
        if (user?._id)
          mutateAsync({
            userId: user._id,
            actionType: "click",
            elementId: type ? `${type as "submit"}-button` : "button",
          });
      }}
      onMouseOver={(e) => {
        onMouseOver?.(e);
        if (user?._id)
          mutateAsync({
            userId: user._id,
            actionType: "hover",
            elementId: type ? `${type as "submit"}-button` : "button",
          });
      }}
    >
      {props.children}
    </Button>
  );
};

export default TrackAnalyticsButton;
