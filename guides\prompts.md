## Backend

```
read everthiing about Staff multi location feature and how it's managed here, explain it to me, see @c:\Users\<USER>\Documents\swe\puzzle-piece-backend/src\models\location.ts @c:\Users\<USER>\Documents\swe\puzzle-piece-backend/src\routes\location.ts  @c:\Users\<USER>\Documents\swe\puzzle-piece-backend/src\validators\location.ts  @c:\Users\<USER>\Documents\swe\puzzle-piece-backend/src\controllers\location.ts @c:\Users\<USER>\Documents\swe\puzzle-piece-backend/README.MD
```

## Frontend

```
I want to work on the pto (paid time off) (here we just call it `time-off`), we will generate the services in the @c:\Users\<USER>\Documents\swe\puzzle-piece-app/src\services/ folder called `time_off.service.ts` if there isn't one yet, then we go to insert the necessary tanstack queries and mutations inside  @c:\Users\<USER>\Documents\swe\puzzle-piece-app/pages\teams\hooks\use-time-off.ts just like it is done in @c:\Users\<USER>\Documents\swe\puzzle-piece-app/pages\teams\hooks\use-staff.ts , genereate necessary types and interfaces where needed in @c:\Users\<USER>\Documents\swe\puzzle-piece-app/src\interfaces\time-off.ts .

DONT GENERATE ANYTHING YET, I HAVE SOMETHING TO SHOW YOU FIRST, WHEN I SAY `GO`, THEN YOU CAN START
```

```
This is the PTO Design system of the backend summarized with all response types and input types, read it very carefully

**backend explanation copy**
```

```
Familiarize yourself with @c:\Users\<USER>\Documents\swe\puzzle-piece-app/src\services\staff.service.ts and the patterns here, also take a very long look at @c:\Users\<USER>\Documents\swe\puzzle-piece-app/pages\teams\hooks\use-staff.ts , after that GO - generate the services and hook for paid time off

NOTE: Please look at the old code I pasted here, dont make all keys optional, FOLLOW THE OBJECT AND INTERFACE PATTERNS IN THE DOCUMENT I PASTED HERE
```

```
Great, now lets look into the user interfaces for calling these apis,

DONT GENERATE ANYTHING NOW, UNTIL I SAY 'GO'

Read every file in  @c:\Users\<USER>\Documents\swe\puzzle-piece-app/components/ directory @c:\Users\<USER>\Documents\swe\puzzle-piece-app/components\index.tsx and , this will teach you the components

Read every file and understand the pattern in @c:\Users\<USER>\Documents\swe\puzzle-piece-app/pages\teams\staff/ directory, the @c:\Users\<USER>\Documents\swe\puzzle-piece-app/pages\services/ directory and @c:\Users\<USER>\Documents\swe\puzzle-piece-app/pages\digital-profile-editor/ , get enough understandiing of the tailwiindcss classes, breakpoints design components, color scheme
```

```
what will the structure for the the time-off routes be in @c:\Users\<USER>\Documents\swe\puzzle-piece-app/src\app.tsx and the folder structure in @c:\Users\<USER>\Documents\swe\puzzle-piece-app/pages\teams\time-off/ folder be like?
```

```
Now GO - generate the required frontend pages, learn from the code I have traiined you on, don't forget to use formik + (the necessary useTimeOff hook) for optimistic updates, any need for modals? learn from @c:\Users\<USER>\Documents\swe\puzzle-piece-app/pages\teams\staff\components\working-hours-modal.tsx
```
