import { useToast } from "@/src/contexts/hooks/toast";
import {
  AdminFaqServices,
  AdminMaintenanceModeServices,
  type AdminNS,
  AdminSupportTicketServices,
  AdminUserLogsServices,
  AdminUserServices,
} from "@/src/services/admin.service";
import {
  CreateBlog,
  CreateTag,
  DeleteBlog,
  DeleteTag,
  GetBlogByUniqueKey,
  GetBlogs,
  GetTags,
  IBlogPayload,
  UpdateBlog,
} from "@/src/services/blog.service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";

export const useAdminUserApis = () => {
  const queryclient = useQueryClient();
  const showToast = useToast();
  const { data: allUsers = [], isFetching: isAllUsersFetching } = useQuery({
    queryKey: ["admin-all-users"],
    queryFn: async () => {
      const res = await AdminUserServices.GetUsers();
      return res.data.users;
    },
  });
  const editUserMutation = useMutation({
    mutationKey: ["admin-edit-user"],
    mutationFn: async (payload: AdminNS.EditUserPayload) => {
      const res = await AdminUserServices.EditUser(payload);
      return res;
    },
    onSuccess(data) {
      queryclient.invalidateQueries({
        queryKey: ["admin-all-users"],
      });
      showToast("info", data.data.message);
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  const deleteUserMutation = useMutation({
    mutationKey: ["admin-delete-user"],
    mutationFn: async (userId: string) => {
      const res = await AdminUserServices.DeleteUser(userId);
      return res;
    },
    onSuccess(data) {
      queryclient.invalidateQueries({
        queryKey: ["admin-all-users"],
      });
      showToast("info", data.data.message);
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  const toggleUserStatusMutation = useMutation({
    mutationKey: ["admin-toggle-user-status"],
    mutationFn: async (payload: AdminNS.ToggleUserStatusPayload) => {
      const res = await AdminUserServices.ToggleUserStatus(payload);
      return res;
    },
    onSuccess(data) {
      queryclient.invalidateQueries({
        queryKey: ["admin-all-users"],
      });
      showToast("info", data.data.message);
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });

  return {
    allUsers,
    isAllUsersFetching,
    editUserMutation,
    deleteUserMutation,
    toggleUserStatusMutation,
  };
};

/**
 * @dev fetch all users with logs, display count, when the row is hovered, show a button to fetch logs and display in a separate table
 * @todo remove fake data and timeout
 */
export const useAdminUserLogsApis = () => {
  const getUserLogsMutation = useMutation({
    mutationFn: async (userId: string) => {
      const res = await AdminUserLogsServices.GetUserLogs(userId);
      return res.data.logs;
    },
  });
  const { data: usersWithLogs = [], isFetching: isUsersWithLogsFetching } =
    useQuery({
      queryKey: ["admin-all-user-logs"],
      queryFn: async () => {
        const res = await AdminUserLogsServices.GetAllUsersWithLogs();
        return res.data.logs;
      },
    });
  return {
    getUserLogsMutation,
    usersWithLogs,
    isUsersWithLogsFetching,
  };
};

/**
 * @dev toasting after each mutation is handled here
 * @dev query invalidation is handled here too.
 */
export const useAdminFaqApis = () => {
  const showToast = useToast();
  const queryclient = useQueryClient();
  const { data: faqs = [] } = useQuery({
    queryKey: ["admin-all-faqs"],
    queryFn: async () => {
      const res = await AdminFaqServices.GetAllFaqs();
      return res.data.faqs;
    },
  });
  // we don't need this but we'll keep it for now
  const { data: _faq } = useQuery({
    queryKey: ["admin-faq-by-id"],
    queryFn: async () => {
      const res = await AdminFaqServices.GetFaqById("");
      return res.data.faq;
    },
    // dont run this
    enabled: false,
  });
  const createFaqMutation = useMutation({
    mutationKey: ["admin-create-faq"],
    mutationFn: async (payload: AdminNS.CreateFaqPayload) => {
      const res = await AdminFaqServices.CreateFaq(payload);
      return res;
    },
    onSuccess() {
      showToast("info", "New Faq created");
      queryclient.invalidateQueries({
        queryKey: ["admin-all-faqs"],
      });
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  const deleteFaqMutation = useMutation({
    mutationKey: ["admin-delete-faq"],
    mutationFn: async (faqId: string) => {
      const res = await AdminFaqServices.DeleteFaq(faqId);
      return res;
    },
    onSuccess() {
      showToast("info", "Faq deleted");
      queryclient.invalidateQueries({
        queryKey: ["admin-all-faqs"],
      });
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  const editFaqMutation = useMutation({
    mutationKey: ["admin-edit-faq"],
    mutationFn: async (payload: {
      faqId: string;
      faqDetails: AdminNS.EditFaqPayload;
    }) => {
      const res = await AdminFaqServices.EditFaq(
        payload.faqId,
        payload.faqDetails
      );
      return res;
    },
    onSuccess() {
      showToast("info", "Faq edited");
      queryclient.invalidateQueries({
        queryKey: ["admin-all-faqs"],
      });
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });

  return {
    faqs,
    createFaqMutation,
    deleteFaqMutation,
    editFaqMutation,
    queryKey: "admin-all-faqs" as const,
  };
};

export const useAdminMaintenanceModeApis = () => {
  const showToast = useToast();
  const queryclient = useQueryClient();
  const { data: maintenanceMode } = useQuery({
    queryKey: ["admin-maintenance-mode"],
    queryFn: async () => {
      const res = await AdminMaintenanceModeServices.GetMaintenanceMode();
      return res.data.maintenance;
    },
  });
  const maintenanceModeMutation = useMutation({
    mutationKey: ["admin-set-maintenance-mode"],
    mutationFn: async (
      payload: Pick<AdminNS.MaintenanceMode, "isActive" | "message">
    ) => {
      const res = await AdminMaintenanceModeServices.SetMaintenanceMode(
        payload
      );
      return res;
    },
    onSuccess() {
      showToast("info", "Maintenance mode set");
      queryclient.invalidateQueries({
        queryKey: ["admin-maintenance-mode"],
      });
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });

  return {
    maintenanceMode,
    maintenanceModeMutation,
  };
};

export const useAdminSupportTicketsApis = () => {
  const showToast = useToast();
  const queryclient = useQueryClient();
  const { data: supportTickets = [], isFetching: isSupportTicketsFetching } =
    useQuery({
      queryKey: ["admin-support-tickets"],
      queryFn: async () => {
        const res = await AdminSupportTicketServices.GetSupportTickets();
        return res.data.tickets;
      },
    });
  const createSupportTicketMutation = useMutation({
    mutationKey: ["admin-create-support-ticket"],
    mutationFn: async (payload: AdminNS.CreateSupportTicketPayload) => {
      const res = await AdminSupportTicketServices.CreateSupportTicket(payload);
      return res;
    },
    onSuccess(data) {
      showToast("info", `Support ticket ${data.data.ticket.userId} created.`);
      queryclient.invalidateQueries({
        queryKey: ["admin-support-tickets"],
      });
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  const deleteSupportTicketMutation = useMutation({
    mutationKey: ["admin-delete-support-ticket"],
    mutationFn: async (ticketId: string) => {
      const res = await AdminSupportTicketServices.DeleteSupportTicket(
        ticketId
      );
      return res;
    },
    onSuccess(data) {
      showToast("info", `Support ticket ${data.data.ticket.userId} deleted.`);
      queryclient.invalidateQueries({
        queryKey: ["admin-support-tickets"],
      });
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  return {
    supportTickets,
    createSupportTicketMutation,
    deleteSupportTicketMutation,
    isSupportTicketsFetching,
  };
};

// Add this helper function to the blog service
export const createBlogFormData = (blogData: IBlogPayload) => {
  const formData = new FormData() as TypedFormData<IBlogPayload>;
  // Add text fields
  formData.set("title", blogData.title);
  formData.set("shortDescription", blogData.shortDescription);
  formData.set("authorName", blogData.authorName);
  formData.set("longDescription", blogData.longDescription);
  formData.set(
    "timePosted",
    // @ts-expect-error this will work
    blogData.timePosted?.toISOString() || new Date().toISOString()
  );
  // Add tags as JSON string if they exist
  if (blogData.tags && blogData.tags.length > 0) {
    // @ts-expect-error this will work
    formData.set("tags", JSON.stringify(blogData.tags));
  }
  // Add the file if it exists
  if (blogData.image) {
    formData.set("image", blogData.image);
  }
  return formData;
};

export const useAdminBlogApis = () => {
  const showToast = useToast();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  // Get all blogs
  const { data: blogs = [], isPending: isBlogsPending } = useQuery({
    queryKey: ["admin-blogs"],
    queryFn: async () => {
      const res = await GetBlogs();
      return res.data.blogPosts;
    },
    refetchOnWindowFocus: false,
  });

  // Get all tags
  const { data: tags = [], isFetching: isTagsFetching } = useQuery({
    queryKey: ["admin-tags"],
    queryFn: async () => {
      const res = await GetTags();
      return res.data.tagsList;
    },
    refetchOnWindowFocus: false,
  });

  // Create blog mutation
  const createBlogMutation = useMutation({
    mutationKey: ["admin-create-blog"],
    mutationFn: async (payload: IBlogPayload) => {
      const formData = createBlogFormData(payload);
      const res = await CreateBlog(formData as FormData);
      return res;
    },
    onSuccess(data) {
      showToast(
        "success",
        `Blog post "${data.data.post?.title}" created successfully.`,
        {
          duration: 7000,
          action: {
            title: "View",
            onClick() {
              navigate("/blog");
              return true;
            },
          },
        }
      );
      queryClient.invalidateQueries({
        queryKey: ["admin-blogs"],
      });
    },
    onError(error: ApiError) {
      showToast(
        "error",
        error.response?.data.error || "Failed to create blog post"
      );
    },
  });

  // Update blog mutation
  const updateBlogMutation = useMutation({
    mutationKey: ["admin-update-blog"],
    mutationFn: async ({
      uniqueKey,
      payload,
    }: {
      uniqueKey: string;
      payload: IBlogPayload;
    }) => {
      const formData = createBlogFormData(payload);
      const res = await UpdateBlog(uniqueKey, formData as FormData);
      return res;
    },
    onSuccess(data) {
      showToast(
        "success",
        `Blog post "${data.data.post?.title}" updated successfully.`,
        {
          duration: 7000,
          action: {
            title: "View",
            onClick() {
              navigate("/blog");
              return true;
            },
          },
        }
      );
      queryClient.invalidateQueries({
        queryKey: ["admin-blogs"],
      });
    },
    onError(error: ApiError) {
      showToast(
        "error",
        error.response?.data.error || "Failed to update blog post"
      );
    },
  });

  // Delete blog mutation
  const deleteBlogMutation = useMutation({
    mutationKey: ["admin-delete-blog"],
    mutationFn: async (blogUniqeKey: string) => {
      const res = await DeleteBlog(blogUniqeKey);
      return res;
    },
    onSuccess(data) {
      showToast(
        "success",
        data.data.message || "Blog post deleted successfully."
      );
      queryClient.invalidateQueries({
        queryKey: ["admin-blogs"],
      });
    },
    onError(error: ApiError) {
      showToast(
        "error",
        error.response?.data.error || "Failed to delete blog post"
      );
    },
  });

  // Create tag mutation
  const createTagMutation = useMutation({
    mutationKey: ["admin-create-tag"],
    mutationFn: async (payload: { name: string }) => {
      const res = await CreateTag(payload);
      return res;
    },
    onSuccess(data) {
      showToast(
        "success",
        `Tag "${data.data.tag?.name}" created successfully.`
      );
      queryClient.invalidateQueries({
        queryKey: ["admin-tags"],
      });
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Failed to create tag");
    },
  });

  // Delete tag mutation
  const deleteTagMutation = useMutation({
    mutationKey: ["admin-delete-tag"],
    mutationFn: async (tagId: string) => {
      const res = await DeleteTag(tagId);
      return res;
    },
    onSuccess(data) {
      showToast("success", data.data.message || "Tag deleted successfully.");
      queryClient.invalidateQueries({
        queryKey: ["admin-tags"],
      });
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Failed to delete tag");
    },
  });

  // Get blog by UniqueKey query
  const getBlogByUniqueKey = (uniqueKey: string) => {
    return useQuery({
      queryKey: ["admin-blog", uniqueKey],
      queryFn: async () => {
        const res = await GetBlogByUniqueKey(uniqueKey);
        return res.data.blogPost;
      },
      enabled: !!uniqueKey,
      refetchOnWindowFocus: false,
    });
  };

  return {
    blogs,
    tags,
    isBlogsPending,
    isTagsFetching,
    createBlogMutation,
    updateBlogMutation,
    deleteBlogMutation,
    createTagMutation,
    deleteTagMutation,
    getBlogByUniqueKey,
  };
};
