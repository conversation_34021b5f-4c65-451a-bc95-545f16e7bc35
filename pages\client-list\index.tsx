import { useToast } from "@/src/contexts/hooks/toast";
import {
  AddNewClient,
  DeleteClient,
  GetAllClients,
  TClient,
  UpdateClient,
} from "@/src/services/clients.service";
import { truncateText } from "@/lib/helpers";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import AddClientModal from "./components/add-client-modal";
import ClientListControls from "./components/list-controls";
import ClientListTable from "./components/list-table";
import UpdateClientModal from "./components/update-client-modal";
import { filterClients } from "./utils/client-filter";
import { Typography } from "@/components/typography";
import { Card } from "@/components/ui/card";
import CircularLoader from "@/components/ui/circular-loader";

const ClientList = () => {
  const [modals, setModals] = useState({
    addNewClient: false,
    updateClient: false,
  });
  const modalFunctions = {
    openModal: <K extends keyof typeof modals>(modal: K) => {
      setModals((p) => ({
        ...p,
        [modal]: true,
      }));
    },
    closeModal: (modal: keyof typeof modals) => {
      setModals((p) => ({
        ...p,
        [modal]: false,
      }));
    },
  };
  const showToast = useToast();
  // business logic
  // refetch all clients, so that client._id will be updated too;
  const {
    data: fetchedClients = [],
    isFetching: isClientsFetching,
    refetch: refetchClients,
  } = useQuery({
    queryKey: ["clients"],
    queryFn: async () => (await GetAllClients()).data,
    refetchOnWindowFocus: false,
  });
  const [clients, setClients] = useState<TClient[]>([]);
  useEffect(() => {
    if (fetchedClients) setClients(fetchedClients);
  }, [fetchedClients]);
  const [selectedClients, setSelectedClients] = useState<TClient[]>([]);
  const [toEditClient, setToEditClient] = useState<TClient | null>(null);
  const { mutateAsync: addNewClient } = useMutation({
    mutationFn: AddNewClient,
  });
  const { mutateAsync: deleteClient } = useMutation({
    mutationFn: DeleteClient,
  });
  const { mutateAsync: updateClient } = useMutation({
    mutationFn: UpdateClient,
  });

  return (
    <section className="w-full relative">
      <div className="flex flex-col gap-y-10">
        <div className="w-full ">
          <Typography
            variant={"h1"}
            className="font-bold font-Bricolage_Grotesque  "
          >
            Client list
          </Typography>
        </div>
        <div className="w-full h-fit">
          <ClientListControls
            openAddClientModal={() => modalFunctions.openModal("addNewClient")}
            onImportFromCSV={async (newClients) => {
              await Promise.allSettled(
                newClients.map((newClient) =>
                  addNewClient(newClient, {
                    onError: () =>
                      showToast(
                        "error",
                        `Adding client ${truncateText(
                          newClient.clientName,
                          8
                        )} failed`
                      ),
                  })
                )
              );
              refetchClients();
            }}
            onSearch={(searchTerm) => {
              const filteredClients = filterClients(fetchedClients, searchTerm);
              if (searchTerm) {
                setClients(filteredClients);
                if (filteredClients.length === 0) setSelectedClients([]);
              } else setClients(fetchedClients);
            }}
            selectedClients={selectedClients}
            onDeleteSelectedClients={async () => {
              await Promise.allSettled(
                selectedClients.map((client) =>
                  deleteClient(client._id, {
                    onError: () =>
                      showToast(
                        "error",
                        `Deleting client ${truncateText(
                          client.clientName,
                          8
                        )} failed`
                      ),
                  })
                )
              );
              refetchClients();
              setSelectedClients([]);
            }}
          />
          {isClientsFetching ? (
            <div className="w-full py-6">
              <CircularLoader />
            </div>
          ) : clients.length > 0 ? (
            <Card className="rounded-lg !px-0 overflow-x-auto no-scrollbar md:max-w-fit mt-4 ">
              <ClientListTable
                clients={clients}
                selectedClients={selectedClients}
                onSelectAllClients={(clients) => {
                  setSelectedClients(clients);
                }}
                onEditClient={(client) => {
                  modalFunctions.openModal("updateClient");
                  setToEditClient(client);
                }}
                onSelectClient={(client) => {
                  setSelectedClients((p) => {
                    if (p.includes(client))
                      return p.filter((c) => c._id !== client._id);
                    return [...p, client];
                  });
                }}
                isFetching={isClientsFetching}
              />
            </Card>
          ) : (
            <div className="w-full py-6 flex items-center justify-center">
              <Typography variant={"p"} className="font-bold text-paragraph">
                No clients found
              </Typography>
            </div>
          )}
        </div>
        <AddClientModal
          open={modals.addNewClient}
          onClose={() => modalFunctions.closeModal("addNewClient")}
          onSave={(client) => {
            addNewClient(client as TClient, {
              onSuccess: () => {
                refetchClients();
                showToast("success", `Added client ${client.clientEmail}`);
              },
              onError: () =>
                showToast(
                  "error",
                  `Adding client ${truncateText(client.clientName, 8)} failed`
                ),
            });
          }}
        />
        <UpdateClientModal
          open={modals.updateClient}
          onClose={() => modalFunctions.closeModal("updateClient")}
          client={toEditClient}
          setClient={(client) => setToEditClient(client)}
          onSave={(client) => {
            updateClient(
              { clientId: client._id, payload: client as TClient },
              {
                onSuccess: () => refetchClients(),
                onError: () =>
                  showToast(
                    "error",
                    `Updating client ${truncateText(
                      client.clientName,
                      8
                    )} failed`
                  ),
              }
            );
          }}
        />
      </div>
    </section>
  );
};

export default ClientList;
