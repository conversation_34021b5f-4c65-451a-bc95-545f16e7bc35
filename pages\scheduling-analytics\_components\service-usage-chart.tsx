import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { GetMostUsedServices } from "~/services/analytics.service";
import { useQuery } from "@tanstack/react-query";
import { formatDate } from "date-fns";
import { useEffect, useMemo } from "react";
import { Bar, BarChart, CartesianGrid, XAxis } from "recharts";
import CircularLoader from "@/components/ui/circular-loader";
import { useAnalyticsInterval } from "../analytics-interval-context";
import { inlineSwitch } from "@/lib/helpers";

type ServicesDataType = {
  name: string;
  totalClients: number;
  returningClients: number;
  returnRate: `${number}%`;
};

const chartConfig = {
  totalClients: {
    label: "Total Clients",
  },
  returningClients: {
    label: "Returning Clients",
  },
  returnRate: {
    label: "Return Rate",
  },
} satisfies ChartConfig;

export function ServiceUsageChart() {
  const { interval } = useAnalyticsInterval();
  const {
    data: _mostUsedServices,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["appointment-most-used-services"],
    queryFn: async () => (await GetMostUsedServices(interval)).data,
    enabled: !!interval,
  });
  // make the most used services refetch when interval changes
  useEffect(() => (refetch(), void 0), [interval]);

  const mostUsedServices = {
    count: 3,
    service: [
      {
        serviceId: {
          name: "Haircut",
          _id: "1234567890",
        },
        totalClients: 100,
        totalReturningClients: 75,
        returnRate: "75%",
      },
      {
        serviceId: {
          name: "Massage",
          _id: "2345678901",
        },
        totalClients: 150,
        totalReturningClients: 90,
        returnRate: "60%",
      },
      {
        serviceId: {
          name: "Facial",
          _id: "3456789012",
        },
        totalClients: 80,
        totalReturningClients: 40,
        returnRate: "50%",
      },
      {
        serviceId: {
          name: "Manicure",
          _id: "4567890123",
        },
        totalClients: 200,
        totalReturningClients: 160,
        returnRate: "80%",
      },
    ],
  };
  const servicesDataMemo = useMemo(() => {
    const servicesData: ServicesDataType[] = [];
    if (mostUsedServices) {
      mostUsedServices.service.forEach((service) => {
        servicesData.push({
          name: service.serviceId.name,
          totalClients: service.totalClients,
          returningClients: service.totalReturningClients,
          returnRate: service.returnRate as `${number}%`,
        });
      });
    }
    return servicesData;
  }, [mostUsedServices]);

  if (isLoading) {
    return (
      <div className="w-full py-12 flex justify-center ">
        <CircularLoader />
      </div>
    );
  }

  return (
    <Card className="!rounded-3xl ">
      <CardHeader>
        <CardTitle>Service Usage</CardTitle>
        <CardDescription>{formatDate(new Date(), "MMMM, yyy")}</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}  >
          <BarChart accessibilityLayer data={servicesDataMemo}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="name"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <ChartLegend
              className="flex-wrap gap-x-2"
              content={<ChartLegendContent />}
            />
            <Bar
              dataKey="totalClients"
              stackId="a"
              fill="#E91E63"
              className="fill-primary-500"
              radius={[0, 0, 4, 4]}
            />
            <Bar dataKey="returningClients" stackId="a" fill="#FFA07A" />
            <Bar
              dataKey="returnRate"
              stackId="a"
              fill="#87CEEB"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col !items-start gap-2 text-sm">
        <div className="leading-none text-muted-foreground">
          Showing total service usage and returning client trends for{" "}
          {inlineSwitch(
            interval,
            ["today", "today."],
            ["weekly", "this week."],
            ["monthly", "this month."],
            ["yearly", "this year."]
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
