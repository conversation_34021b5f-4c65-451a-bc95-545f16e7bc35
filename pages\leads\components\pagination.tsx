import PaginationComponent from "@/components/ui/pagination";
import React, { MutableRefObject } from "react";
import { useSearchParams } from "react-router-dom";

interface PaginationProps<T> {
  itemsPerPage: number;
  data: Array<T>;
  renderItems: (props: T, index: number) => React.ReactNode;
  totalPages: number;
}

interface ExtraProps {
  listRef: MutableRefObject<HTMLElement | null>;
}

/**
 * @dev the actual pagination is handled on the backend, this is the ui representation for it.
 */
const Pagination = <T extends {}>({
  data,
  totalPages,
  renderItems,
  listRef,
}: PaginationProps<T> & ExtraProps) => {
  const [searchParams, setSearchParams] = useSearchParams()
  const handlePageChange = (value: number) => {
    setSearchParams(prev => {
      prev.set('page', value.toString())
      return prev;
    })
    listRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  return (
    <section className="gap-y-3 flex-col flex ">
      <section className="">{data.map(renderItems)}</section>
      <PaginationComponent
        totalPages={totalPages}
        page={Number(searchParams.get('page') || 1)}
        onPageChange={handlePageChange}
        className="mx-auto"
      />
    </section>
  );
};

export default Pagination;
