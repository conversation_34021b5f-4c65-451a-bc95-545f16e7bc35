import { pick } from "@/lib/helpers";
import { GetUserDetails } from "@/src/services/auth.service";
import { create } from "zustand";

type AuthStateNonNullable = {
	user: User;
	calendarSettings: CalenderSettings;
	userSettings: UserSettings;
};

export type AuthState = {
	[index in keyof AuthStateNonNullable]: AuthStateNonNullable[index] | null;
} & {
	isAuthenticated: boolean;
	token: string | null;
};

const initialState: AuthState = {
	user: null,
	token: null,
	calendarSettings: null,
	userSettings: null,
	isAuthenticated: false,
};

type Initializer = {
	initialState: AuthState;
	/**
	 * @dev this is used to fetch new user details again.
	 */
	refetchUser: () => Promise<void>;
	setUser: (
		payloadFn:
			| AuthStateNonNullable
			| ((p: AuthStateNonNullable) => AuthStateNonNullable),
	) => void;
  /** 
   * @dev change auth state 
   * */
	changeAuthenticationState: (isAuthenticated: boolean) => void;
	logout: () => void;
};

const useAuthStore = create<Initializer>((set, get) => ({
	// state
	initialState,
	// actions
	refetchUser: async () => {
		const userDetails = (await GetUserDetails()).data.data;
		set({
			initialState: {
				...get().initialState,
				user: userDetails.user,
				calendarSettings: userDetails.calendarSettings,
				userSettings: userDetails.userSettings,
				token: localStorage.getItem("PPToken"),
				isAuthenticated: true,
			},
		});
	},
	setUser: (payloadFn) => {
		if (typeof payloadFn === "function")
			set({
				initialState: {
					...get().initialState,
					...payloadFn(get().initialState as any),
				},
			});
		else
			set({
				initialState: {
					...get().initialState,
					...pick(payloadFn, "user", "calendarSettings", "userSettings"),
					isAuthenticated: true,
				},
			});
	},
  changeAuthenticationState(isAuthenticated) {
    set({
      initialState: {
        ...get().initialState,
        isAuthenticated,
        ...(
          !isAuthenticated
          ? { user: null, userSettings: null, calendarSettings: null }
          : {}
        )
      }
    })
  },
	logout: () => {
		set({
			initialState: {
				...get().initialState,
				user: null,
				token: null,
				calendarSettings: null,
				userSettings: null,
				isAuthenticated: false,
			},
		});
	},
}));


export default useAuthStore;
