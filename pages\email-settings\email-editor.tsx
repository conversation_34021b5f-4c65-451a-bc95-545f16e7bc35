import PendingOverlay from '@/components/ui/pending-overlay';
import { useToast } from '@/src/contexts/hooks/toast';
import { UploadImage } from '@/src/services/email-settings.service';
import { useMutation } from '@tanstack/react-query';
import { useEffect, useMemo, useRef } from 'react';
import ReactQuill from 'react-quill'
import 'react-quill/dist/quill.snow.css'
import EmailBanner from './email-banner';
import { usePlanRole } from '@/src/contexts/use-plan-role';

type Props = {
  value: string | undefined,
  defaultValue?: string | undefined
  onChange: (value: string) => void
}

const formats = [
  'header', 'bold', 'italic', 'underline', 'strike',
  'list', 'bullet', 'indent', 'align',
  'link', 'image', 'color', 'size',
  'blockquote', 'code-block'
];


function EmailEditorImpl(props: Props) {
  const quillRef = useRef<ReactQuill>(null);

  const showToast = useToast()

  const compressImage = (file: File, maxWidth = 600, quality = 0.8) => {
    return new Promise<File>((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate dimensions maintaining aspect ratio
        const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
        canvas.width = img.width * ratio;
        canvas.height = img.height * ratio;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
        canvas.toBlob(resolve as any, 'image/jpeg', quality);
      };

      img.src = URL.createObjectURL(file);
    });
  };

  const uploadToServerMutation = useMutation({
    mutationFn: UploadImage
  })

  // Upload to server/CDN (mock implementation)
  const uploadToServer = async (file: File) => {
    // This would be your actual upload logic
    const formData = new FormData();
    formData.append('image', file);
    try {
      // Mock API call - replace with your actual endpoint
      const response = await uploadToServerMutation.mutateAsync(formData)
      const data = response.data;
      return data.imageUrl; // Return the uploaded image URL
    } catch (error) {
      console.error('Upload error:', error);
      throw error;
    }
  };

  // Validate image file
  const validateImage = (file: File) => {
    const validTypes = ['image/jpeg', 'image/png'];
    const maxSize = 2 * 1024 * 1024; // 5MB

    if (!validTypes.includes(file.type)) {
      const message = ('Please select a valid image file (JPG, PNG)');
      showToast('info', message)
      throw new Error(message)
    }

    if (file.size > maxSize) {
      const message = ('Image size must be less than 5MB');
      showToast('info', message)
      throw new Error(message)
    }

    return true;
  };

  const imageHandler = function handleImage() {
    // Create file input
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.style.display = 'none';

    // Add to DOM temporarily
    document.body.appendChild(input);

    input.onchange = async () => {
      const file = input.files?.[0];
      if (!file) return;

      try {
        // Validate file
        validateImage(file);

        // Get Quill instance and current selection
        const quill = quillRef.current?.getEditor();
        if (!quill) return;

        const range = quill.getSelection(true);

        // Show loading placeholder

        // Compress image for email compatibility
        const compressedFile = await compressImage(file, 600, 0.8);
        // Upload image (using mock for demo)
        const imageUrl = await uploadToServer(compressedFile);

        // Replace loading placeholder with actual image
        quill.insertEmbed(range.index, 'image', imageUrl, 'user');

        // Set cursor after image
        quill.setSelection(range, 'user');

      } catch (error) {
        alert(`Image upload failed: ${(error as any).message}`);
        console.error('Image upload error:', error);
      } finally {
        // Clean up
        document.body.removeChild(input);
      }
    };

    // Trigger file selection
    input.click();
  };

  const modules = useMemo(() => ({
    toolbar: {
      container: [
        { 'header': [1, 2, 3, 4, 5, 6, false] },
        'bold', 'italic', 'underline', 'strike',
        { 'list': 'ordered' }, { 'list': 'bullet' },
        { 'indent': '-1' }, { 'indent': '+1' },
        { 'align': [] },
        { 'color': [] },
        'link', 'image',
        'blockquote',
        'clean'
      ],
      handlers: {
        image: imageHandler
      }
    }
  }), [])



  useEffect(() => {
    if (quillRef.current) {
      const editor = quillRef.current.getEditor();
      const toolbar = editor.getModule('toolbar').container;

      // Acuity-style tooltips
      const tooltips = {
        '.ql-bold': 'Bold (Ctrl+B)',
        '.ql-italic': 'Italic (Ctrl+I)',
        '.ql-underline': 'Underline (Ctrl+U)',
        '.ql-strike': 'Strikethrough',
        '.ql-list[value="ordered"]': 'Numbered List',
        '.ql-list[value="bullet"]': 'Bullet Points',
        '.ql-indent[value="-1"]': 'Decrease Indent',
        '.ql-indent[value="+1"]': 'Increase Indent',
        '.ql-align .ql-picker-label': 'Text Alignment',
        '.ql-link': 'Insert Link',
        '.ql-image': 'Insert Image',
        '.ql-color .ql-picker-label': 'Text Color',
        '.ql-size .ql-picker-label': 'Font Size',
        '.ql-blockquote': 'Quote Block',
        '.ql-code-block': 'Code Block',
        '.ql-clean': 'Remove Formatting'
      };

      Object.entries(tooltips).forEach(([selector, tooltip]) => {
        const element = toolbar.querySelector(selector);
        if (element) {
          element.setAttribute('title', tooltip);
        }
      });
    }
  }, []);
  const { planRole: { shortName } } = usePlanRole()

  return (
    <div className="email-editor">
      <PendingOverlay isPending={uploadToServerMutation.isPending} />
      <ReactQuill
        {...props}
        ref={quillRef}
        theme="snow"
        modules={modules}
        formats={formats}
        placeholder="Start typing your email template..."
      />
      {
        shortName === 'premium' ? '' : <EmailBanner />
      }
    </div>
  );
}

function Preview(props: Pick<Props, 'value' | 'defaultValue'>) {
  const { planRole: { shortName } } = usePlanRole()

  return (
    <div className='email-preview'>
      <ReactQuill theme='snow' modules={{ toolbar: false }} {...props} readOnly={true} />
      {
        shortName === 'premium' ? '' : <EmailBanner />
      }
    </div>
  )
}

const EmailEditor = Object.assign(EmailEditorImpl, {
  Preview
})

export default EmailEditor
