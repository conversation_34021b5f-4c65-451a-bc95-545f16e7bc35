import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";

type Props = {
  open: boolean;
  onClose: () => void;
  onDelete: () => void;
  user: ExtendedUser | null;
};

const DeleteUserModal = (props: Props) => {
  return (
    <Modal open={props.open} onClose={props.onClose}>
      <Modal.Body
        className={`min-h-fit max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Delete user
          </Typography>
        </div>
        <Typography className="font-bold text-gray-400">
          Are you sure you want to delete user {props.user?._id}?
        </Typography>
        <div className="w-full flex justify-between items-center mt-6">
          <Button onTap={props.onClose} className="w-full">
            Cancel
          </Button>
          <Button onTap={props.onDelete} className="w-full" variant="ghost">
            Yes, I'm sure
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default DeleteUserModal;
