import DatePicker from "@/components/ui/calendar";
import CircularLoader from "@/components/ui/circular-loader";
import { and, capitalizeFirstWord, noop, pick, sleep } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import { useFormik } from "formik";
import { AnimatePresence, motion } from "framer-motion";
import { Link, useParams } from "react-router-dom";
import * as Yup from "yup";
import ConfirmationScreen from "./components/confirmation-modal";
import CustomerForm from "./components/customer-form";
import SuccessSection from "./components/success-section";
import StripeCheckout from "./components/service-stripe-checkout";
import { useStepper } from "@/components/ui/stepper";
import { useExternalBooking } from "./components/use-booking";
import { ExternalBookingPayload } from "@/src/services/booking.service";
import { ExternalBookingContext } from "./components/use-booking-context";
import { useToast } from "@/src/contexts/hooks/toast";
import { flushSync } from "react-dom";
import TimeSlotZonePicker from "./components/time-slot-zone-picker";
import ServiceReview from "./components/service-review";
import CustomerIntakeForm from "./components/customer-intake-form";
import { validateIntakeFormResponses } from "./components/validate-form";
import { formatDate } from "date-fns";
import { useEffect, useRef } from "react";

type RouteParams = {
  username: string | undefined;
  urlName: string | undefined;
};

const daysOfWeek = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

const ExternalBooking = () => {
  const { username, urlName } = useParams<RouteParams>();
  // First, let's combine the form values interface
  interface BookingFormValues extends ExternalBookingPayload {
    timeZone: string;
  }
  const {
    serviceQuery: { data: service, ...serviceQuery },
  } = useExternalBooking.getService({
    username,
    urlName,
  });
  const bookingFormik = useFormik<BookingFormValues>({
    enableReinitialize: true,
    initialValues: {
      serviceId: service?.service._id || "",
      serviceName: service?.service.name || "",
      userId: service?.user._id || "",
      date: "",
      timeSlot: "",
      timeZone: "Europe/London", // Added from dateTimeFormik
      notes: "",
      price: service?.service.price,
      customerInfo: {
        lastName: "",
        firstName: "",
        email: "",
        phone: "",
      },
      intakeFormResponses: [],
      serviceOptions: [],
    },
    validationSchema: Yup.object({
      customerInfo: Yup.object({
        firstName: Yup.string().required("First Name is required"),
        lastName: Yup.string().required("Last Name is required"),
        email: Yup.string()
          .email("Invalid email address")
          .required("Email is required"),
        phone: Yup.string()
          .required("Phone number is required")
          .min(10, "Phone number must be at least 10 digits"),
      }),
      date: Yup.string().required("Date is required"),
      timeSlot: Yup.string().required("Time slot is required"),
      timeZone: Yup.string().required("Time zone is required"),
    }),
    onSubmit: noop,
  });

  // Update the useExternalBooking call
  const {
    intakeFormQuery,
    workdaysQuery: { data: workDays },
    createBookingMut,
    timeslotsQuery: { data: timeSlots = [], ...timeslotsQuery },
  } = useExternalBooking({
    username,
    urlName,
    serviceData: service,
    dateTimeState: {
      date: bookingFormik.values.date,
      timeZone: bookingFormik.values.timeZone,
    },
  });

  const showToast = useToast();
  const formRef = useRef(bookingFormik);

  // Update the ref whenever formik changes
  useEffect(() => {
    formRef.current = bookingFormik;
  }, [bookingFormik]);
  const validateIntakeForm = async () => {
    const currentFormik = formRef.current;
    const errors = validateIntakeFormResponses(
      currentFormik.values.intakeFormResponses,
      intakeFormQuery.data?.questions || []
    );

    if (errors.length > 0) {
      showToast("error", errors[0].message);
      return false;
    }
    return true;
  };

  // First, add a helper function to determine if payment is required
  const isPaymentRequired = (service?: any) => {
    return (
      Boolean(service?.service.price) &&
      Boolean(service?.user.isStripeConnected)
    );
  };

  const { matchStep, prevStep, nextStep, Stepper } = useStepper(
    [
      { id: "review", title: "Review" },
      {
        id: "customer_form",
        title: "Details",
        shouldGoNext: async () => {
          try {
            const currentFormik = formRef.current;
            await currentFormik.validateField("customerInfo");
            const customerInfoErrors = Object.values(
              currentFormik.errors.customerInfo || {}
            );
            if (customerInfoErrors.length > 0) {
              showToast("error", customerInfoErrors[0] as string);
              return false;
            }
            return true;
          } catch (error) {
            showToast("error", "Please fill in all required fields");
            return false;
          }
        },
      },
      {
        id: "intake_form",
        title: "Details",
        toShow() {
          const { questions, isActive } = intakeFormQuery.data ?? {};
          return and([Boolean(questions), Boolean(isActive)]);
        },
        shouldGoNext: async () => {
          const isValid = await validateIntakeForm();
          return isValid;
        },
      },
      {
        id: "date_picker",
        title: "Select Date",
        shouldGoNext: async () => {
          try {
            const currentFormik = formRef.current;
            await currentFormik.validateField("date");
            await currentFormik.validateField("timeSlot");
            await currentFormik.validateField("timeZone");
            const dateTimeErrors = Object.values(
              pick(currentFormik.errors || {}, "date", "timeSlot", "timeZone")
            ).filter(Boolean);
            if (dateTimeErrors.length > 0) {
              showToast("error", dateTimeErrors[0] as string);
              return false;
            }
            return true;
          } catch (error) {
            showToast("error", "Please fill in all required fields");
            return false;
          }
        },
      },
      { id: "confirmation", title: "Confirmation" },
      {
        id: "payment",
        title: "Complete Payment",
        toShow: () => isPaymentRequired(service),
      },
      {
        id: "success",
        title: "Success 🎉🎉",
        toShow() {
          return Boolean(createBookingMut.data);
        },
      },
    ] as const,
    [intakeFormQuery.data, service, createBookingMut.data] // Remove bookingFormik from dependencies
  );
  // helpful functions
  const onChangeDate = async (date: Date) => {
    bookingFormik.setValues((prev) => ({
      ...prev,
      date: date.toISOString(),
      timeSlot: "",
    }));
    await sleep(800);
    timeslotsQuery.refetch();
  };
  if (serviceQuery.isLoading)
    return (
      <div className="py-20">
        <CircularLoader />
      </div>
    );
  return (
    <ExternalBookingContext.Provider
      value={{
        values: bookingFormik.values,
        setValue: (k, v) => {
          bookingFormik.setFieldValue(k as string, v);
        },
      }}
    >
      <section className="w-screen min-h-screen flex flex-col justify-center items-center py-7 pb-40 px-3 relative ">
        <div className="w-full flex items-center justify-center py-7">
          <Stepper />
        </div>
        <motion.section
          className={cn(
            "w-fit h-fit bg-subtle-gray rounded-[28px] grid grid-cols-1 gap-y-4 p-6 md:px-8 md:py-8 ",
            {
              "!p-0": matchStep(
                "customer_form",
                "intake_form",
                "date_picker",
                "confirmation",
                "success"
              ),
            }
          )}
        >
          <AnimatePresence mode="wait" presenceAffectsLayout>
            {matchStep("review") && (
              <ServiceReview
                bookingType="external"
                service={service}
                billingSettings={{
                  preferredCurrency:
                    service?.user?.billingSettings.preferredCurrency || "usd",
                }}
                // Add this prop to handle free services
                displayPrice={
                  isPaymentRequired(service)
                }
              />
            )}
            {matchStep("customer_form") && (
              <motion.div
                layout
                layoutId="customer_form"
                key={"customer-form"}
                initial={{ opacity: 0, x: -100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0 }}
                className="w-full "
              >
                <CustomerForm
                  billingSettings={{
                    preferredCurrency:
                      service?.user?.billingSettings.preferredCurrency || "usd",
                  }}
                  bookingType="external"
                  onClickNext={() => nextStep()}
                  options={service?.service.serviceOptions || null}
                />
              </motion.div>
            )}
            {matchStep("intake_form") && (
              <motion.div
                layout
                layoutId="intake_form"
                key={"intake_form"}
                initial={{ opacity: 0, x: -100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 100 }}
                className="w-full "
              >
                <CustomerIntakeForm
                  questions={intakeFormQuery.data?.questions || []}
                  onClickNext={() => nextStep()}
                />
              </motion.div>
            )}
            {matchStep("date_picker") && (
              <motion.div
                key={"date_picker"}
                layout
                layoutId="date_picker"
                initial={{ opacity: 0, x: -100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0 }}
                className="flex flex-col gap-y-5"
              >
                <DatePicker
                  className="rounded-[28px] w-full bg-[#EAEBE5]/40 "
                  disableOldDates
                  disabledDays={
                    daysOfWeek
                      .map((day) => day.toLowerCase())
                      .filter((day) => !workDays?.includes(day as any))
                      .map((day) =>
                        capitalizeFirstWord(day.toLowerCase())
                      ) as any
                  }
                  value={
                    bookingFormik.values.date
                      ? new Date(
                        formatDate(bookingFormik.values.date, "yyyy-MM-dd")
                      )
                      : undefined
                  }
                  onChange={onChangeDate}
                />
                <TimeSlotZonePicker
                  dateTime={{
                    date: bookingFormik.values.date,
                    timeSlot: bookingFormik.values.timeSlot,
                    timeZone: bookingFormik.values.timeZone,
                  }}
                  onChangeTimeZone={async (zone) => {
                    flushSync(() => {
                      bookingFormik.setValues((p) => ({
                        ...p,
                        timeZone: zone,
                        timeSlot: "",
                      }));
                    });
                    await sleep(800);
                    timeslotsQuery.refetch();
                  }}
                  onChangeTimeSlot={async (slot) => {
                    flushSync(() => {
                      bookingFormik.setFieldValue("timeSlot", slot);
                    });
                  }}
                  isFetching={timeslotsQuery.isFetching}
                  timeSlots={timeSlots}
                  onClickNext={() => nextStep()}
                />
              </motion.div>
            )}
            {matchStep("payment") && (
              <StripeCheckout
                clientEmail={bookingFormik.values.customerInfo.email}
                serviceId={bookingFormik.values.serviceId}
                serviceOptions={bookingFormik.values.serviceOptions}
                bookingId={service?.service._id!}
                onSuccessFullPayment={() => {
                  // call the second payBusiness service here ohh
                  createBookingMut.mutateAsync(bookingFormik.values);
                }}
              />
            )}
            {matchStep("confirmation") && (
              <ConfirmationScreen
                isBookingPending={createBookingMut.isPending}
                billingSettings={{
                  preferredCurrency:
                    service?.user?.billingSettings?.preferredCurrency || "usd",
                }}
                displayPrice={isPaymentRequired()}
                bookingType="external"
                serviceOptions={
                  bookingFormik.values.serviceOptions?.map(
                    (id) =>
                      service?.service.serviceOptions?.find(
                        (s) => s._id === id
                      )!
                  ) || []
                }
                onCancel={prevStep}
                onConfirm={async () => {
                  if (isPaymentRequired()) return nextStep()
                  const validatedCustomerInfo = await bookingFormik
                    .validateForm()
                    .then((val) => {
                      const errors = Object.values(val.customerInfo || {});
                      if (errors.length === 0) return true;
                      else {
                        showToast("error", errors[0]);
                        return false;
                      }
                    })
                    .catch((err) => {
                      console.error(err);
                      showToast("error", String(err));
                      return false;
                    });
                  const validatedIntakeForm = await validateIntakeForm();
                  if (validatedIntakeForm && validatedCustomerInfo)
                    createBookingMut.mutateAsync(bookingFormik.values, {
                      onSuccess: async () => {
                        await sleep(1000);
                        flushSync(() => {
                          nextStep();
                        })
                      },
                    });
                  else return;
                }}
              />
            )}
            {matchStep("success") && (
              <SuccessSection
                email={bookingFormik.values.customerInfo.email || ""}
              />
            )}
          </AnimatePresence>
        </motion.section>
        {!service?.user?.isPremiumPlan && (
          <Link
            to={`/onboarding`}
            target="_blank"
            className="w-screen mt-20 max-w-fit whitespace-nowrap bg-white/20 backdrop-blur-2xl px-3 rounded-full text-sm py-2 font-Aeonik_Fono hover:opacity-75 transition-opacity duration-200  font-semibold shadow-md "
          >
            Join{" "}
            <span className="relative z-20 after:w-full after:h-full after:absolute after:py-3 after:left-0 after:-z-10 after:-rotate-3 after:inline-block after:bg-green-500 ">
              {username}
            </span>{" "}
            on Puzzle Piece 🧩
          </Link>
        )}
      </section>
    </ExternalBookingContext.Provider>
  );
};

export default ExternalBooking;
