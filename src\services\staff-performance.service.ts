
import { pick } from "@/lib/helpers";
import { FakeData } from "../fake-data";
import { api } from "./api.service";

// Request payload types
export interface GetStaffPerformanceParams {
  staffId: string;
  startDate: string;
  endDate: string;
  periodType: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
}

export interface GetStaffRankingsParams {
  periodType: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  metric: 'efficiency' | 'reliability' | 'customerSatisfaction' | 'totalRevenue';
  startDate: string;
  endDate: string;
}

export interface GetStaffAnalyticsParams {
  startDate: string;
  endDate: string;
  periodType: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
}

export interface SubmitStaffRatingPayload {
  staffId: string;
  bookingId?: string;
  rating: number; // 1-5
  comment?: string;
  customerName?: string;
  customerEmail?: string;
}

export interface GetStaffRatingsParams {
  page?: number;
  limit?: number;
}

// Response types
export interface StaffPerformanceMetrics {
  totalBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  noShowBookings: number;
  totalRevenue: number;
  averageRating: number;
  totalReviews: number;
  totalHours: number;
  efficiency: number;
  customerSatisfaction: number;
  reliability: number;
}

export interface ServiceBreakdown {
  serviceId: string;
  serviceName: string;
  bookings: number;
  revenue: number;
  averageRating: number;
}

export interface StaffPerformance {
  _id: string;
  staffId: string;
  userId: string;
  period: {
    startDate: string;
    endDate: string;
    type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  };
  metrics: StaffPerformanceMetrics;
  serviceBreakdown: ServiceBreakdown[];
  staff: {
    _id: string;
    name: string;
    role: string;
    color: string;
  };
}

export interface StaffRanking {
  _id: string;
  staffId: {
    _id: string;
    name: string;
    role: string;
    color: string;
  };
  metrics: StaffPerformanceMetrics;
  period: {
    startDate: string;
    endDate: string;
    type: string;
  };
}

export interface StaffRating {
  _id: string;
  staffId: string;
  bookingId?: string;
  rating: number;
  comment?: string;
  customerName?: string;
  customerEmail?: string;
  createdAt: string;
}

export interface StaffAnalytics {
  totalStaff: number;
  averageEfficiency: number;
  averageReliability: number;
  averageSatisfaction: number;
  totalRevenue: number;
  totalBookings: number;
  topPerformers: Array<{
    staffId: string;
    name: string;
    role: string;
    color: string;
    efficiency: number;
    reliability: number;
    customerSatisfaction: number;
    totalRevenue: number;
  }>;
  performanceTrends: Array<{
    period: string;
    efficiency: number;
    reliability: number;
    satisfaction: number;
    revenue: number;
  }>;
}

// API Service functions
export const GetStaffPerformance = async (params: GetStaffPerformanceParams) => {
  const response = await api.get<{
    success: true;
    performance: StaffPerformance;
    staff: {
      _id: string;
      name: string;
      role: string;
      color: string;
    };
  }>("/staff-performance/performance", { params });
  return response;
};

export const GetStaffRankings = async (params: GetStaffRankingsParams) => {
  const response = await api.get<{
    success: true;
    rankings: StaffRanking[];
    period: {
      startDate: string;
      endDate: string;
      type: string;
    };
    metric: string;
  }>("/staff-performance/rankings", { params });
  return response;
};

export const GetStaffAnalytics = async (params: GetStaffAnalyticsParams) => {
  // const response = await api.get<{
    // success: true;
    // analytics: StaffAnalytics;
    // period: {
    //   startDate: string;
    //   endDate: string;
    //   type: string;
  //   };
  // }>("/staff-performance/analytics", { params });
  return FakeData.asyncFaked({ period: {
    ...pick(params, 'startDate', 'endDate'),
    type: params.periodType
  },
    analytics: FakeData.staffAnalytics,
    success: true
  })
  // return response;
};

export const SubmitStaffRating = async (payload: SubmitStaffRatingPayload) => {
  const response = await api.post<{
    success: true;
    message: string;
    rating: StaffRating;
  }>("/staff-performance/ratings", payload);
  return response;
};

export const GetStaffRatings = async (staffId: string, params?: GetStaffRatingsParams) => {
  const response = await api.get<{
    success: true;
    ratings: StaffRating[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
    summary: {
      averageRating: number;
      totalRatings: number;
      ratingDistribution: Array<{
        rating: number;
        count: number;
        percentage: number;
      }>;
    };
  }>(`/staff-performance/staff/${staffId}/ratings`, { params });
  return response;
};
