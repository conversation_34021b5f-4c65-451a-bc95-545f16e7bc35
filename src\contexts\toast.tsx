import { Button } from "@/components/buttons";
import { AnimatePresence, motion } from "framer-motion";
import React, { ReactNode, useMemo, useState } from "react";
import { flushSync } from "react-dom";
import {
  ToastContext,
  ToastContextProps,
  ToastOptions,
  ToastType,
} from "./hooks/toast";
import { getRandomString, sleep } from "@/lib/helpers";

const toastStyles = {
  success: "bg-green-700",
  error: "bg-red-700",
  info: "bg-stone-800",
};

type ToastMessageProps = {
  toast: {
    type: ToastType;
    message: string;
    id: string
    options?: ToastOptions;
  };
  index: number;
  messages: Array<{
    type: ToastType;
    message: string;
    id: string
    options?: ToastOptions;
  }>;
  setMessages: React.Dispatch<
    React.SetStateAction<ToastMessageProps["messages"]>
  >;
};

const ToastMessage = ({
  toast,
  index,
  messages,
  setMessages,
}: ToastMessageProps) => {
  const trueIndex = index + 1;
  const msgLength = messages.length;
  const [expanded, setExpanded] = useState(false);
  return (
    <AnimatePresence>
      <motion.div
        key={index}
        variants={{
          hidden: {
            scale: 0.4,
            opacity: 0.4,
            y: 50,
          },
          almost_hidden: {
            y: -15 * (msgLength - trueIndex),
            scale: 1 - (msgLength - trueIndex) / 10,
          },
          shown: {
            scale: [0.4, 0.8, 1],
            opacity: [0, 0.8, 1],
            y: [100, 10, 0],
          },
          exit: {
            scale: [1, 0.5, 0.4],
            opacity: [1, 0.2, 0],
            y: [0, 90, 100],
          },
        }}
        initial={trueIndex === msgLength ? "hidden" : "shown"}
        animate={trueIndex < msgLength ? "almost_hidden" : "shown"}
        exit={"exit"}
        onTap={() => {
          setExpanded((prev) => !prev);
        }}
        transition={{
          type: "keyframes",
          times: [0, 0.8, 1],
        }}
        className="origin-center select-none cursor-pointer absolute bottom-0"
      >
        <motion.div
          whileTap={{
            scale: 0.95,
            transition: {
              type: "keyframes",
            },
          }}
          data-expanded={expanded}
          className={`flex items-center justify-between w-54 backdrop-blur-3xl rounded-[14px] data-[expanded=true]:w-72 shadow-lg p-1.5 pl-0 py-2 ${
            toastStyles[toast.type]
          }`}
        >
          <div className="flex pl-4 flex-col">
            <span
              data-expanded={expanded}
              className={`font-medium text-sm text-white data-[expanded=false]:line-clamp-1`}
            >
              {toast.message}
            </span>
            <span
              data-expanded={expanded}
              className={`text-sm text-white/60 data-[expanded=false]:line-clamp-1 transition-all duration-200`}
            >
              {toast.options?.description}
            </span>
          </div>
          <Button
            onTap={async (e) => {
              e.preventDefault();
              e.stopPropagation();
              const shouldRemove = (
                toast.options?.action?.onClick || (() => true)
              )?.();
              // eslint-disable-next-line @typescript-eslint/no-unused-expressions
              const removeToast = () => setMessages((prev) => prev.filter((msg) => msg.id !== toast.id));
              if (shouldRemove) removeToast();
              else {
                await sleep(2000)
                removeToast()
              }
            }}
            className={`!px-2 !py-1 bg-transparent text-white/60 text-sm font-medium`}
          >
            {toast.options?.action?.title || (
              'Done'
            )}
          </Button>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

const ToastProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [messages, setMessages] = useState<
    Array<{
      type: ToastType;
      message: string;
      id: string
      options?: ToastOptions;
    }>
  >([]);
  const shouldHide = useMemo(() => messages.length === 0, [messages]);
  const showToast: ToastContextProps["showToast"] = (
    type,
    message,
    options
  ) => {
    const timerId = setTimeout(() => {
      setMessages((prevMessages) => [...prevMessages].slice(1));
    }, options?.duration || 6000);
    if (messages.length === 3) {
      flushSync(() => {
        setMessages((prevMessages) => [
          ...prevMessages,
          {
            type,
            message,
            id: getRandomString(),
            options: {
              ...options,
              action: {
                ...options?.action,
                onClick: () => {
                  // clear the timer so we can internally remove the toast
                  clearTimeout(timerId);
                  // toast will only be removed it this function returns true
                  const onClick = options?.action?.onClick;
                  return onClick ? onClick() : true;
                },
              },
            },
          },
        ]);
      });
      setTimeout(() => {
        setMessages((prevMessages) => [...prevMessages.slice(1)]);
      }, 5000);
    } else
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          type,
          message,
          id: getRandomString(),
          options: {
            ...options,
            action: {
              ...options?.action,
              onClick: () => {
                // clear the timer so we can internally remove the toast
                clearTimeout(timerId);
                // toast will only be removed it this function returns true
                const onClick = options?.action?.onClick;
                return onClick ? onClick() : true;
              },
            },
          },
        },
      ]);
  };

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      <section
        className={`fixed z-[10000000] bottom-8 left-1/2 -translate-x-1/2 ${
          shouldHide ? "hidden" : ""
        }`}
      >
        <div className="w-80 min-h-20 flex justify-center relative">
          {messages.map((toast, index) => {
            return (
              <ToastMessage
                setMessages={setMessages}
                key={index}
                toast={toast}
                index={index}
                messages={messages}
              />
            );
          })}
        </div>
      </section>
    </ToastContext.Provider>
  );
};

export default ToastProvider;
