import { useParams, useNavigate } from "react-router-dom";
import { useMutation } from "@tanstack/react-query";
import { Input } from "../../components/inputs";
import { PasswordReset } from "@/src/services/auth.service";
import { useToast } from "@/src/contexts/hooks/toast";
import { useFormik } from "formik";
import { noop } from "@/lib/helpers";
import { object, string } from "yup";
import Logo from '@/assets/logo.png'
import { Typography } from "@/components/typography";
import { Button } from "@/components/buttons";
import { Tick02Icon } from "hugeicons-react";
import CircularLoader from "@/components/ui/circular-loader";

const passwordValidationSchema = string()
  .required("Password is required")
  .matches(
    /[A-Za-z\d@$!%*?&]{8,}/,
    "Must Match 8+ characters from this consisting of lowercase, uppercase and special characters",
  )
  .matches(/(?=.*\d)/, "Must contain at least one number")
  .matches(/^(?=.*[A-Z])/, "Must contain at least one uppercase letter")
  .matches(/^(?=.*[a-z])/, "Must contain at least one lowercase letter")
  .matches(/(?=.*[@$!%*?&])/, "Must contain at least one special character");


export default function ResetPassword() {
  const { token } = useParams<{ token: string }>();
  const formik = useFormik({
    initialValues: { newPassword: '', confirmPassword: '' },
    onSubmit: noop,
    validationSchema: object({
      newPassword: passwordValidationSchema,
      confirmPassword: passwordValidationSchema
    })
  })
  const navigate = useNavigate();
  const showToast = useToast()
  const resetMutation = useMutation({
    mutationFn: PasswordReset.Reset,
    onSuccess: () => {
      showToast('success', 'Password successfully changed', {
        description: 'You can login with your new password now',
        action: {
          title: 'Login',
          onClick() {
            navigate('/signin')
            return true
          },
        }
      })
    },
    onError: (error) => showToast(
      "error",
      (error as unknown as ApiError).response?.data.error ||
      "Something went wrong",
      {
        description: 'Please try again later',
      }
    )
  });

  return (
    <section className="w-screen h-screen flex flex-col items-center justify-center px-4">
      <section className="w-screen h-screen md:max-w-[400px] px-4 overflow-y-auto pt-10 gap-y-5 flex flex-col no-scrollbar ">
        <div className="w-full flex flex-col items-center gap-y-3 justify-center">
          <img src={Logo} className="w-full size-28 object-contain  " />
          <Typography variant={'h3'} className="font-bold text-3xl text-center">
            Puzzle Piece <br /> Solutions
          </Typography>
        </div>
        {resetMutation.data ? (<div className=" flex flex-col items-center gap-y-4 ">
          <Typography variant={"h1"}
            className="text-[28px] font-Bricolage_Grotesque overflow-hidden text-start font-bold "
          >
            Password changed!
          </Typography>
          <div className="flex flex-col gap-y-4 pt-2 px-3">
            <div className="p-8 w-fit h-fit bg-green-500 rounded-full mx-auto">
              <Tick02Icon className="w-8 h-8 text-white " />
            </div>
            <Typography variant="p" className="!mt-0 text-center">
              Your password has been changed, you may login now.
            </Typography>
            <Button
              variant="full"
              className="mt-5"
              onTap={() => navigate("/signin")}
            >
              Login
            </Button>

          </div>
        </div>) : (
          <>
            <Typography variant="h1"
              className="text-[28px]  font-Bricolage_Grotesque overflow-hidden text-center font-bold "
            >
              Reset Password
            </Typography>
            <Input.Password
              value={formik.values.newPassword}
              name="newPassword"
              onChange={(e) => formik.setFieldValue('newPassword', e.currentTarget.value)}
              required
              validationSchema={passwordValidationSchema}
              label="New Password"
            />
            {formik.values.newPassword &&
              <Input.Password
                value={formik.values.confirmPassword}
                name="confirmPassword"
                onChange={(e) => formik.setFieldValue('confirmPassword', e.currentTarget.value)}
                required
                validationSchema={passwordValidationSchema}
                label="Confirm Password"
              />
            }
            <div
              className="w-full max-w-sm flex flex-col gap-4"
            >
              <Button onTap={() => {
                resetMutation.mutateAsync({ token: token!, data: { newPassword: formik.values.newPassword } }, {
                  onSuccess() {
                    showToast("success", "Password Reset!", {
                      description: "You may login now.",
                    });
                  },
                  onError(error) {
                    console.log(error)
                    showToast("error",
                      (error as unknown as ApiError).response?.data.error ||
                      "Something went wrong",
                      {
                        description: "Please try again later.",
                      });
                  }
                })
              }} disabled={resetMutation.isPending || !formik.values.newPassword || (formik.values.confirmPassword !== formik.values.newPassword)}>
                {resetMutation.isPending ? <CircularLoader color="white" /> : "Reset Password"}
              </Button>
            </div>
          </>
        )}
      </section>
    </section>);
}

