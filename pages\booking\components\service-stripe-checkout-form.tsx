import { Button } from "@/components/buttons";
import CircularLoader from "@/components/ui/circular-loader";
import { useToast } from "@/src/contexts/hooks/toast";
import { CompleteServicePayment } from "@/src/services/booking.service";
import {
	PaymentElement,
	useElements,
	useStripe,
} from "@stripe/react-stripe-js";
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";

type Props = {
	bookingId: string;
	onSuccessFullPayment: VoidFunction;
	clientSecret: string;
};

/**
 * @dev confirms a stripe payment and  if successful -> books the service.
 */
const StripeCheckoutForm = ({
	bookingId,
	onSuccessFullPayment,
	clientSecret,
}: Props) => {
	const stripe = useStripe();
	const elements = useElements();
	const showToast = useToast();
	const { mutateAsync } = useMutation({
		mutationFn: async (paymentIntentId: string) => {
			await CompleteServicePayment({
				paymentIntentId: paymentIntentId,
			});
		},
		onSuccess: () => {
			onSuccessFullPayment();
		},
		onError() {},
		retry: 6,
	});

	const [loading, setLoading] = useState(false);
	const approvePayment = async () => {
		if (!stripe || !elements) return;
		setLoading(true);
		const { error, paymentIntent } = await stripe.confirmPayment({
			elements,
			clientSecret: clientSecret as string,
			confirmParams: {
				// this route shows a button which will close the window -> success payment window I mean
				return_url: `${
					import.meta.env.VITE_FRONTEND_URL
				}/booking/external/${bookingId}/success`,
			},
			redirect: "if_required",
		});
		if (error) {
			setLoading(false);
			console.log("error message:", error.message);
			showToast("error", error.message || "Booking service failed");
		} else {
			setLoading(false);
			await mutateAsync(paymentIntent.id as string);
		}
	};

	return (
		<div onSubmit={approvePayment} className="h-full w-full space-y-8">
			<PaymentElement />
			<div className="!pb-6">
				<Button
					onTap={() => {
						elements?.submit();
						approvePayment();
					}}
					disabled={!stripe || !elements}
					className="w-full text-white py-2 flex items-center justify-center "
				>
					{loading ? (
						<CircularLoader color="white" className="mx-auto" />
					) : (
						"Pay"
					)}
				</Button>
			</div>
		</div>
	);
};

export default StripeCheckoutForm;
