import { Button } from "@/components/buttons";
import CircularLoader from "@/components/ui/circular-loader";
import {
	convertPriceToDecimalFormat,
	inlineSwitch,
	noop,
	toSorted,
} from "@/lib/helpers";
import { GetSubscriptionPlans } from "@/src/services/auth.service";
import { useQuery } from "@tanstack/react-query";
import useEmblaCarousel from "embla-carousel-react";
import { motion } from "framer-motion";
import { useEffect, useMemo, useRef, useState } from "react";
import CheckIcon from "~/icons/check-icon";
import { SubscriptionPlan } from "~/interfaces/subscription-plan";

interface PricingPlansProps {
	currentPlanId: string | null;
	onPlanChange: (plan: SubscriptionPlan) => void;
}

const PricingPlans: React.FC<PricingPlansProps> = (props) => {
	const { data: subscriptionPlans = [], isFetching } = useQuery({
		queryKey: ["subscriptionPlans"],
		queryFn: async () => (await GetSubscriptionPlans()).data.plans,
		retry: 2,
		refetchOnWindowFocus: false,
	});
	const plans = useMemo(() => {
		const allPlans = subscriptionPlans! || [];
		const allPlansSorted = toSorted(allPlans, (a, b) => a.price - b.price);
		return (
			allPlansSorted.map((plan, i) => ({
				...plan,
				price: convertPriceToDecimalFormat(plan.price),
				yearlyPrice: convertPriceToDecimalFormat(plan.yearlyPrice || 0),
				action: plan._id === props.currentPlanId ? "Current Plan" : "Upgrade",
				has:
					i === 0
						? // free plan
							"Includes:"
						: // paid plans
							`Everything in ${allPlans[i - 1].name}, plus:`,
			})) || []
		);
	}, [subscriptionPlans]);
	const [emblaRef, emblaApi] = useEmblaCarousel({
		startIndex:
			subscriptionPlans.findIndex((plan) => plan._id === props.currentPlanId) ||
			0,
	});
	emblaApi?.on("settle", (api) => {
		const nodes = api.slideNodes();
		const index = api.selectedScrollSnap();
		const hasPrev = api.canScrollPrev();
		const hasNext = api.canScrollNext();
		const plan = nodes[index];
		if (!plan || innerWidth > 480) return;
		plan.dataset.inview = "true";
		if (hasPrev) {
			// prev node
			const plan = nodes[index - 1];
			if (!plan) return;
			plan.dataset.inview = "false";
		}
		if (hasNext) {
			// prev node
			const plan = nodes[index + 1];
			if (!plan) return;
			plan.dataset.inview = "false";
		}
	});
	const [coordinates, setCoordinates] = useState({
		width: 0 as number | string,
		offsetLeft: 0 as number | string,
	});
	const [period, setPeriod] = useState<"month" | "year">("month");

	const tabsRef = useRef<HTMLDivElement>(null);
	useEffect(() => {
		const timer = setTimeout(() => {
			const monthElement = tabsRef.current?.querySelector<HTMLParagraphElement>(
				"[data-state='month']",
			);
			setCoordinates({
				width: monthElement?.getBoundingClientRect()?.width || 0,
				offsetLeft: monthElement?.offsetLeft || 0,
			});
		}, 1000);
		return () => clearTimeout(timer);
	}, []);
	const { innerWidth } = window;
	if (isFetching)
		return (
			<div className="w-full py-10 flex justify-center">
				<CircularLoader />
			</div>
		);
	return (
		<section className="w-full h-full overflow-y-auto flex flex-col gap-y-5 justify-center pt-0">
			<div
				ref={tabsRef}
				className="w-fit mx-auto grid grid-cols-2 p-1 rounded-full font-semibold border border-[#171717] relative bg-white "
			>
				<p
					data-state="month"
					onClick={(e) => {
						setPeriod("month");
						const { width } = e.currentTarget.getBoundingClientRect();
						setCoordinates({
							width: width,
							offsetLeft: e.currentTarget.offsetLeft,
						});
					}}
					className="px-3 h-8 flex items-center cursor-pointer text-white rounded-full mix-blend-difference relative z-10"
				>
					Monthly
				</p>
				<p
					data-state="year"
					onClick={(e) => {
						setPeriod("year");
						const { width } = e.currentTarget.getBoundingClientRect();
						setCoordinates({
							width: width,
							offsetLeft: e.currentTarget.offsetLeft,
						});
					}}
					className="px-3 h-8 flex items-center cursor-pointer text-white rounded-full mix-blend-difference relative z-10"
				>
					Annually
				</p>
				{/* indicator */}
				<motion.div
					className="absolute z-0 bg-black rounded-full h-8 top-1"
					initial={{
						width: coordinates.width,
						left: coordinates.offsetLeft,
					}}
					animate={{
						width: coordinates.width,
						left: coordinates.offsetLeft,
					}}
				/>
			</div>{" "}
			<section
				className="pt-0 h-fit overflow-x-hidden no-scrollbar select-none"
				ref={emblaRef}
			>
				<section className="w-full flex gap-x-6 px-2 ">
					{plans.map((plan, index) => {
						const { name, description, action, has, features } = plan;
						let { price } = plan;
						return (
							<div
								data-inview={innerWidth <= 768 && index === 1}
								data-iscurrentplan={
									props.currentPlanId === plan._id ? "true" : "false"
								}
								className="min-w-[320px] max-w-[320px] h-[540px] group flex flex-col justify-center"
								key={index}
							>
								<div className="max-w-full h-[80%] border-2 border-gray-200 rounded-md relative transition-[height] duration-500 ease-[ease] group-data-[iscurrentplan=true]:border-primary-500/50 group-hover:border-primary-500/50 group-active:border-primary-500/50 group-data-[inview=true]:h-full group-data-[inview=true]:border-primary-500/50 md:h-full">
									<div className="w-full h-1/2 px-4 pt-4 pb-5 flex flex-col ">
										<h1 className="text-xl font-bold group-hover:text-primary/50 group-data-[inview=true]:text-primary/50 flex ">
											{name}
											{props.currentPlanId === plan._id && (
												<span className="bg-primary-50/20 text-primary-600 text-sm font-normal flex items-center px-2 rounded-3xl ml-auto ">
													Current Plan
												</span>
											)}
										</h1>
										<p className="text-xl font-bold ">
											$
											{inlineSwitch(
												period,
												["month", price],
												["year", plan.yearlyPrice],
												{ default: price },
											)}{" "}
											<span className="text-gray-300 text-base font-medium ">
												/{period}
											</span>{" "}
										</p>
										<p className="font-medium text-gray-800 my-auto">
											{description}
										</p>
										<Button
											disabled={props.currentPlanId === plan._id}
											className="w-fit h-fit mt-auto py-2 px-4 text-sm font-medium border rounded-[8px] text-primary-500/50 bg-transparent group-data-[iscurrentplan=true]:text-white group-data-[iscurrentplan=true]:border-primary-500/50 group-data-[iscurrentplan=true]:bg-primary-500/50 group-hover:border-primary-500/50 group-hover:bg-primary-500/50 group-hover:text-white group-data-[inview=true]:bg-primary-500/50 group-data-[inview=true]:text-white group-data-[inview=true]:border-primary-500/10"
											onTap={
												props.currentPlanId === plan._id
													? noop
													: () => props.onPlanChange(plan)
											}
										>
											{action}
										</Button>
									</div>
									<div className="w-full h-1/2 absolute bottom-0 p-4 flex flex-col gap-y-2 text-sm group-data-[iscurrentplan=true]:bg-primary-500/10 group-hover:bg-primary-500/10 group-data-[inview=true]:bg-primary-500/10 ">
										<p className="font-bold">{has}</p>
										<div className="w-full flex-grow space-y-2">
											{features.map((feature, index) => (
												<div
													className="w-full flex items-start gap-1"
													key={index}
												>
													<div className="w-4 h-4">
														<CheckIcon height={16} width={16} />
													</div>
													<p className="text-gray-500 ">{feature}</p>
												</div>
											))}
										</div>
									</div>
								</div>
							</div>
						);
					})}
				</section>
			</section>
		</section>
	);
};

export default PricingPlans;
