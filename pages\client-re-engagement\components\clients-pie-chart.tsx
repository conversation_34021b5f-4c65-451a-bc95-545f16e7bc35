"use client";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "recharts";
import { PieSectorDataItem } from "recharts/types/polar/Pie";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import React from "react";
import { objectKeys } from "@/lib/helpers";

const chartConfig = {
  visitors: {
    label: "Visitors",
  },
  chrome: {
    label: "Chrome",
    color: "hsl(var(--chart-1))",
  },
  safari: {
    label: "Safari",
    color: "hsl(var(--chart-2))",
  },
  firefox: {
    label: "Firefox",
    color: "hsl(var(--chart-3))",
  },
  edge: {
    label: "Edge",
    color: "hsl(var(--chart-4))",
  },
  other: {
    label: "Other",
    color: "hsl(var(--chart-5))",
  },
  inactive: {
    label: "Inactive",
    color: "hsl(43 74% 66%)",
  },
  atRisk: {
    label: "At Risk",
    color: "hsl(340 82% 52%)",
  }
} satisfies ChartConfig;

type Props = {
  data: {
    [index in 'inactive' | 'atRisk']: {
      value: number
    }
  }
  clientsCount: number
};

const ReEngagementPieChart: React.FC<Props> = (props) => {
  const cData = objectKeys(props.data).map((key) => {
    const value = props.data[key];
    return {
      level: key,
      value: value.value,
      fill: chartConfig[key].color
    }
  })

  return (
    <Card className="flex flex-col mb-5 ">
      <CardHeader className="items-center pb-0">
        <CardTitle className="text-xl">Re Engagement Summary</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[250px]"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent nameKey="level" />}
            />
            <Pie
              data={cData}
              dataKey="value"
              nameKey="level"
              innerRadius={60}
              strokeWidth={10}
              activeIndex={0}
              activeShape={({
                outerRadius = 0,
                ...props
              }: PieSectorDataItem) => (
                <Sector {...props} outerRadius={outerRadius + 10} />
              )}
            />
            <ChartLegend content={<ChartLegendContent nameKey="level" />} className="!mt-0 !pt-0" />
           </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 font-medium leading-none">
          {props.clientsCount} clients need to be re-engaged
        </div>
        <div className="leading-none text-muted-foreground text-xs">
          Clients that have not been active for more than 30 days
        </div>
      </CardFooter>
    </Card>
  );
};

export default ReEngagementPieChart;
