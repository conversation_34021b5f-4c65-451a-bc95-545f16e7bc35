import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { <PERSON><PERSON><PERSON>, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import ButtonTooltip from "@/pages/services/components/button-tooltip";
import { Staff } from "@/src/interfaces/staff";
import { TeamType } from "@/src/interfaces/team";
import { GetStaff } from "@/src/services/staff.service";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { Delete02Icon, Edit02Icon, TaskDaily01Icon, UserGroupIcon } from "hugeicons-react";
import React from "react";
import { useNavigate } from "react-router-dom";

type TeamCardProps = {
  team: TeamType;
  onEdit: (teamId: string) => void;
  onDelete: (teamId: string) => void;
};

const TeamCard = ({ team, onEdit, onDelete }: TeamCardProps) => {
  const navigate = useNavigate();
  const staffQuery = useQuery({
    queryKey: ["staff", team._id],
    queryFn: async () => (await GetStaff(team._id)).data.staff,
    enabled: !!team._id,
  });
  const teamStaff = staffQuery.data ?? []

  return (
    <div className="bg-stone-200 w-full rounded-3xl flex flex-col ">
      <div className="flex flex-1 items-center justify-between px-4 py-4 font-medium transition-all">
        <Typography className="font-semibold text-lg overflow-ellipsis overflow-hidden whitespace-nowrap">{team.name}</Typography>
        <div className="flex items-center gap-x-2">
          <ButtonTooltip content="Edit Team">
            <button
              className="bg-transparent text-gray-500 rounded-lg p-1.5 hover:bg-white"
              onClick={() => onEdit(team._id)}
            >
              <Edit02Icon width={18} height={18} />
            </button>
          </ButtonTooltip>
          <ButtonTooltip content="Delete Team">
            <button
              className="bg-transparent text-gray-500 rounded-lg p-1.5 hover:bg-white"
              onClick={() => onDelete(team._id)}
            >
              <Delete02Icon width={18} height={18} />
            </button>
          </ButtonTooltip>
        </div>
      </div>

      <div
        className="overflow-hidden flex-grow flex flex-col text-sm transition-all"
      >
        <div className={cn("pb-4 pt-0 flex-grow bg-white rounded-3xl p-4 mx-[2px] mb-[2px]")}>
          <Typography variant='pre' className="text-sm font-semibold font-Satoshi line-clamp-3 text-paragraph/60 mb-3">
            {team.description || "No description"}
          </Typography>
          {teamStaff.length ? (
            <div className="flex items-center gap-x-1 mb-3" >
              <div className="flex items-center -space-x-2 ">
                {teamStaff.slice(0, 3).map(staff => (
                  <StaffTooltipPreview staff={staff} key={staff._id} />
                ))
                }
              </div>
              {teamStaff.length > 3 && <Typography variant={'span'}>+{teamStaff.length - 3}</Typography>}
            </div>
          ) :
            <h3 className="text-sm font-semibold text-gray-900 mb-2 ">
              No teams staff
            </h3>

          }
          <Typography className={cn("py-0.5 pl-2.5 pr-3 mb-3 w-fit text-sm !mt-0 rounded-full flex gap-x-1 items-center ",
            {
              "text-green-900 bg-green-100": team.isActive,
              "text-red-900 bg-red-100": !team.isActive
            },
          )}>
            <div className={cn("size-2 mt-0.5 rounded-full",
              {
                "bg-green-600": team.isActive,
                "bg-red-600": !team.isActive
              },
            )} />
            {team.isActive ? 'active' : 'inactive'}
          </Typography>

          <div className="flex items-center gap-x-3 mt-auto">
            <Button
              variant="dormant"
              className="text-sm flex items-center gap-x-2"
              onTap={() => navigate(`/teams/staff?teamId=${team._id}`)}
            >
              <UserGroupIcon size={18} /> Manage Staff
            </Button>
            <Button
              variant="dormant"
              className="text-sm flex items-center gap-x-2"
              onTap={() => navigate(`/teams/${team._id}/rules`)}
            >
              <TaskDaily01Icon size={18} /> Rules
            </Button>
          </div>
        </div>
      </div>
    </div >
  );
};

const StaffTooltipPreview: React.FC<{ staff: Staff }> = (props) => {
  const { staff } = props;
  return (

    <TooltipProvider key={staff._id} delayDuration={300}>
      <Tooltip>
        <TooltipTrigger asChild={true}>
          <motion.div
            whileHover={{ scale: 0.95 }}
            className="size-8 rounded-full flex items-center cursor-pointer shadow-xl justify-center text-white font-semibold"
            style={{ backgroundColor: staff.color }}
          >
            {staff.name.charAt(0).toUpperCase()}
          </motion.div>
        </TooltipTrigger>
        <TooltipContent className="z-[100000000] p-0 !bg-transparent border-none shadow-none" >
          <div className="bg-stone-200 w-full rounded-3xl flex flex-col ">
            <div className="flex flex-1 items-center justify-between px-4 py-4 font-medium transition-all">
              <div className="flex w-full items-start justify-between">
                <div className="flex items-center gap-4">
                  <div
                    className="w-12 h-12 rounded-full flex items-center justify-center text-white font-semibold"
                    style={{ backgroundColor: staff.color }}
                  >
                    {staff.name.charAt(0).toUpperCase()}
                  </div>{" "}
                  <div>
                    <Typography variant="h3" className="font-semibold text-lg overflow-ellipsis overflow-hidden whitespace-nowrap">
                      {staff.name}
                    </Typography>{" "}
                    <Typography className="text-gray-600 text-sm">
                      {staff.role}{" "}
                    </Typography>
                  </div>
                </div>
              </div>

            </div>
            <div
              className="overflow-hidden flex-grow flex flex-col text-sm transition-all"
            >
              <div className={cn("pb-4 pt-0 flex-grow bg-white rounded-3xl p-4 mx-[2px] mb-[2px]")}>
                {staff.email && (
                  <Typography className="text-gray-500 text-sm">
                    {staff.email}{" "}
                  </Typography>
                )}{" "}
                <Typography className={cn("py-0.5 pl-2.5 pr-3 w-fit text-sm !mt-3 rounded-full flex gap-x-1 items-center ",
                  {
                    "text-green-900 bg-green-100": staff.isActive,
                    "text-red-900 bg-red-100": !staff.isActive
                  },
                )}>
                  <div className={cn("size-2 mt-0.5 rounded-full",
                    {
                      "bg-green-600": staff.isActive,
                      "bg-red-600": !staff.isActive
                    },
                  )} />
                  {staff.isActive ? 'active' : 'inactive'}
                </Typography>
                <div className="mt-3 flex font-medium items-center gap-4 text-sm text-gray-600">
                  <span>Capacity: {staff.capacity}</span>
                  {staff.services.length > 0 && (
                    <span>
                      {staff.services.length} service
                      {staff.services.length !== 1 ? "s" : ""}
                    </span>
                  )}{" "}
                </div>
              </div>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>

  )
}

export default TeamCard
