import { useEffect, useRef, useState } from "react";
import { ActionButtonGroup, Button } from "./buttons";
import CheckBox from "./ui/checkbox";
import Toggle from "./ui/toggle-2";
import EditorJS from "@editorjs/editorjs";
import Quote from "@editorjs/quote";
import List from "@editorjs/list";
import Header from "@editorjs/header";
import MarkdownConverter from "@vingeray/editorjs-markdown-converter";
import { Input } from "./inputs";
import { noop } from "@/lib/helpers";
import { useToast } from "@/src/contexts/hooks/toast";
import { formatDate } from "date-fns";
import { Link } from "react-router-dom";
import RadioBox from "./ui/radio-box";
import DatePicker from "./ui/calendar";
import { useStepper } from "./ui/stepper";

let editor: EditorJS;

const Components = () => {
  const [open, setOpen] = useState(false);
  const editorRef = useRef<HTMLDivElement>(null);
  function setupEditor(element: HTMLElement | null) {
    return new EditorJS({
      holder: element as HTMLElement,
      placeholder: "Write something...",
      async onChange(api) {
        const data = await api.saver.save();
        console.log(MarkdownConverter.toMarkdown(data["blocks"]));
      },
      tools: {
        quote: Quote,
        list: List,
        header: Header,
      },
    });
  }
  const showToast = useToast();
  useEffect(() => {
    if (!editorRef.current) return;
    if (!editor) editor = setupEditor(editorRef.current);
    return () => {
      editor.destroy?.();
    };
  }, []);
  const [value, setValue] = useState(null as any);
  const [date, setDate] = useState("");
  const [values, setValues] = useState([] as any);
  const { Stepper } = useStepper([
    { id: "date_picker", title: "Select a Date" },
    {
      id: "date_customer_form",
      title: "Customer Form",
      shouldGoNext: async () => false,
    },
    { id: "confirmation", title: "Confirmation Modal" },
  ]);
  return (
    <section className="w-full h-screen overflow-x-auto p-3 p space-y-10 no-scrollbar relative">
      <Input.Phone
        label="Phone Input"
        name="name"
        value={"<EMAIL>"}
        onChange={noop}
      />
      <Input.TextArea
        label="Text Area Input"
        name="name"
        value={"<EMAIL>"}
        onChange={noop}
      />
      <DatePicker.Time
        name="startTime"
        value={value}
        label=""
        onChange={(value) => setValue(value)}
        className="flex-1"
      />
      <CheckBox checked={open} onChange={(checked) => setOpen(checked)} />
      <Link to={"/leads"}>Go there</Link>
      {value}
      <Stepper />
      {/* <RadioBox */}
      {/*   label="What tuestion ✅" */}
      {/*   onChange={(option) => { */}
      {/*     setValue(option) */}
      {/*   }} */}
      {/*   required */}
      {/*   value={value} */}
      {/*   options={[{ label: 'Me not', value: 'Me not' }, { value: 'No working', label: 'yep' }]} */}
      {/* /> */}
      <RadioBox.Multiple
        label="What question ✅"
        onChange={(vals) => {
          setValues(vals);
        }}
        required
        values={values}
        options={[
          { label: "We not talking", value: "we-not" },
          { value: "No working out", label: "yeppiie" },
        ]}
      />
      <Button type="button" variant="dormant" className="text-sm">
        Cancel
      </Button>
      <DatePicker.FormControl
        label="Min"
        onChange={(_, dateString) => {
          setDate(dateString);
        }}
        min="2020-08-06"
        max="2025-04-08"
        required
        value={date as any}
      />
      <Input.ColorPicker name="backgroundColor" />
      <div
        className="sticky top-0 left-0"
        id="global_save_button_container"
      ></div>
      <Toggle checked={open} onChange={(checked) => setOpen(checked)} />
      <Button
        onTap={() =>
          showToast("success", "Update succesful", {
            description: "",
          })
        }
      >
        Open Modal
      </Button>
      {formatDate(new Date(), "MMMM d, yyyy")}
      <ActionButtonGroup
        cancel={{
          loading: false,
          title: "Back",
        }}
        next={{
          title: "Next",
        }}
      />
      <div className="embla"></div>
    </section>
  );
};

export default Components;
