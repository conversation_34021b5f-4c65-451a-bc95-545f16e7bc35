import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/src/contexts/hooks/toast";
import * as TimeOffService from "@/src/services/time-off.service";
import {
  CreatePTORequest,
  UpdatePTORequest,
  ApprovePTORequest,
  StaffId,
} from "@/src/interfaces/time-off";

export const useTimeOff = (staffId?: StaffId) => {
  const queryClient = useQueryClient();
  const showToast = useToast();

  const timeOffQuery = useQuery({
    queryKey: ["timeOff", staffId],
    queryFn: async () => {
      if (staffId) {
        return (await TimeOffService.GetPTOForStaff(staffId)).data.ptos;
      } else {
        return (await TimeOffService.GetAllPTORequests()).data.ptos;
      }
    },
  });

  const createTimeOffMutation = useMutation({
    mutationFn: ({
      staffId,
      payload,
    }: {
      staffId: StaffId;
      payload: CreatePTORequest;
    }) => TimeOffService.CreatePTORequest(staffId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["timeOff"] });
      showToast("success", "Time-off request created successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to create time-off request",
      );
    },
  });

  const updateTimeOffMutation = useMutation({
    mutationFn: ({
      ptoId,
      payload,
    }: {
      ptoId: string;
      payload: UpdatePTORequest;
    }) => TimeOffService.UpdatePTORequest(ptoId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["timeOff"] });
      showToast("success", "Time-off request updated successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to update time-off request",
      );
    },
  });

  const deleteTimeOffMutation = useMutation({
    mutationFn: TimeOffService.DeletePTORequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["timeOff"] });
      showToast("success", "Time-off request deleted successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to delete time-off request",
      );
    },
  });

  const approveTimeOffMutation = useMutation({
    mutationFn: ({
      ptoId,
      payload,
    }: {
      ptoId: string;
      payload: ApprovePTORequest;
    }) => TimeOffService.ApprovePTORequest(ptoId, payload),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["timeOff"] });
      const message = data.data.pto.isApproved
        ? "Time-off request approved successfully"
        : "Time-off request rejected";
      showToast("success", message);
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to process time-off request",
      );
    },
  });

  return {
    timeOffQuery,
    createTimeOffMutation,
    updateTimeOffMutation,
    deleteTimeOffMutation,
    approveTimeOffMutation,
  };
};
