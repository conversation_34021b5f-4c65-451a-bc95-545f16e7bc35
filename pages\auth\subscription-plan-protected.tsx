import { capitalizeFirstWord } from "@/lib/helpers";
import { useToast } from "@/src/contexts/hooks/toast";
import { usePlanRole } from "@/src/contexts/use-plan-role";
import { useEffect } from "react";
import { Navigate, Outlet } from "react-router-dom";

interface ProtectedRouteProps {
  planName: "free" | "basic" | "premium";
}

const SubscriptionPlanProtectedRoute: React.FC<ProtectedRouteProps> = ({
  planName,
}) => {
  const {
    planRole: { shortName: planRole },
  } = usePlanRole();
  const showToast = useToast();
  useEffect(() => {
    if (!planRole || !planName) return;
    if (planName !== planRole)
      showToast("info", `Plan Upgrade Required`, {
        description: `You need to upgrade to ${capitalizeFirstWord(
          planName
        )} plan to access this feature.`,
        duration: 7000,
      });
  }, [planRole, planName]);
  if (planRole !== planName) {
    return <Navigate to={"/profile/billing"} />;
  }

  return (
    <>
      <Outlet />
    </>
  );
};

export default SubscriptionPlanProtectedRoute;
