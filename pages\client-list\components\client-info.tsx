import { But<PERSON> } from "@/components/buttons";
import CheckBox from "@/components/ui/checkbox";
import { TClient } from "@/src/services/clients.service";
import { formatDate } from "date-fns";
import { motion } from "framer-motion";
import { PencilIcon } from "lucide-react";
import { useMemo } from "react";

interface Props {
  client: TClient;
  index: number;
  selectedClients: TClient[];
  onSelectClient: (client: TClient) => void;
  onEditClient: (client: TClient) => void;
}

const ClientInformation = ({
  client,
  index,
  selectedClients,
  ...props
}: Props) => {
  const checkedMemo = useMemo(
    () => Boolean(selectedClients.find((c) => c._id === client._id)),
    [selectedClients, index]
  );
  return (
    <motion.div
      key={index}
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="w-full grid grid-cols-[64px_repeat(4,160px)_64px] md:grid-cols-[64px_repeat(4,200px)_64px]"
    >
      <div className="flex items-center justify-center ">
        <CheckBox
          checked={checkedMemo}
          onChange={(checked) => {
            if (checked) props.onSelectClient(client);
            else props.onSelectClient(client);
          }}
          className="stroke-gray-300 "
        />
      </div>
      <div className="py-1 whitespace-nowrap line-clamp-1 text-sm flex items-center px-4">
        {client.clientName}
      </div>
      <div className="py-1 whitespace-nowrap line-clamp-1 text-sm flex items-center px-4">
        {client.clientEmail}
      </div>
      <div className="py-1 whitespace-nowrap line-clamp-1 text-sm flex items-center px-4">
        {client.phoneNumber}
      </div>
      <div className="py-1 whitespace-nowrap line-clamp-1 text-sm flex items-center px-4">
        {client.lastAppointmentDate
          ? formatDate(client.lastAppointmentDate, "MMMM, dd yyyy")
          : "--"}
      </div>
      <div className="py-1 whitespace-nowrap line-clamp-1 text-sm flex items-center px-4">
        <Button
          variant="icon"
          className="bg-transparent text-paragraph"
          onTap={() => props.onEditClient(client)}
        >
          <PencilIcon strokeWidth={2} size={18} />
        </Button>
      </div>
    </motion.div>
  );
};

export default ClientInformation;
