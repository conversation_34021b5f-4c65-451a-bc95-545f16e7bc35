@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: <PERSON><PERSON>;
  src: url("../assets/fonts/Satoshi/VariableWght.ttf");
}

@font-face {
  font-family: Aeonik_Fono;
  src: url("../assets/fonts/Aeonik-Fono/AeonikFono-Medium.otf");
}

@font-face {
  font-family: Work_Sans;
  src: url("../assets/fonts/Work_Sans/WorkSans-VariableFont_wght.ttf");
}

@font-face {
  font-family: Bricolage_Grotesque;
  src: url("../assets/fonts/Bricolage_Grotesque/BricolageGrotesque-VariableFont.ttf");
}

:root {
  overflow-x: clip;
}

*:focus {
  outline: none !important;
}

.shadow-full {
  box-shadow: 0 0 3px 2px rgba(0, 0, 0, 0.1);
}

.grid-cols-auto {
  grid-template-columns: .4fr repeat(17, 1fr);
}

/* embla */
.embla__slide {
  flex: 0 0 80%;
  min-width: 0px;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
  width: 0px;
}

.no-scrollbar {
  scrollbar-width: none;
}

.scroll-indicator {
  background: linear-gradient(to bottom, #fff, #000);
}

.shadow-inset {
  --shadow: inset 0px -4px 4px 0 rgb(0 0 0 / 0.05);
  --shadow-colored: inset 0 2px 4px 0 var(--shadow-color);
  box-shadow: var(--ring-offset-shadow, 0 0 #0000),
    var(--ring-shadow, 0 0 #0000), var(--shadow);
}

@layer base {
  :root {
    /* same as bg-primary-500 */
    --chart-1: 340 82% 52%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 12px;
    @apply text-paragraph;
  }

  .dark {
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }

}

@layer utilities {
  .thin-scrollbar {
    scrollbar-width: 0;
    scrollbar-color: #d3d3d3;
    scroll-padding-left: 10px;
  }
  .thin-scrollbar::-webkit-scrollbar {
    width: 3px;
    height: 3px;
    background-color: transparent;
  }
  .thin-scrollbar::webkit-scrollbar-thumb:hover {
    scale: 2;
  }
  .thin-scrollbar::-webkit-scrollbar-thumb {
    background-color: #d3d3d3;
    border-radius: 10px;
  }
}
