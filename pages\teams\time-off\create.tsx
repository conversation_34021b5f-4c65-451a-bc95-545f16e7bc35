import { useNavigate, useSearchParams } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { ActionButtonGroup, Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import Toggle from "@/components/ui/toggle-2";
import DatePicker from "@/components/ui/calendar";
import PendingOverlay from "@/components/ui/pending-overlay";
import { ArrowLeft02Icon } from "hugeicons-react";
import { useStaff } from "../hooks/use-staff";
import { useTimeOff } from "../hooks/use-time-off";
import { useToast } from "~/contexts/hooks/toast";
import { CreatePTORequest, PTOType } from "@/src/interfaces/time-off";
import RecurringPatternForm from "./components/recurring-pattern-form";
import { noop, pickByType } from "@/lib/helpers";
import { useTeam } from "../hooks/use-team";

const PTO_TYPES: { value: PTOType; label: string }[] = [
  { value: "vacation", label: "Vacation" },
  { value: "sick", label: "Sick Leave" },
  { value: "personal", label: "Personal" },
  { value: "meeting", label: "Meeting" },
  { value: "training", label: "Training" },
  { value: "other", label: "Other" },
];

const CreateTimeOff: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const showToast = useToast();
  const selectedTeamId = searchParams.get("teamId") || "";

  const { staffQuery } = useStaff(selectedTeamId);
  const { teamsQuery } = useTeam();
  const { createTimeOffMutation } = useTimeOff();

  const formik = useFormik({
    initialValues: {
      staffId: "",
      startDate: "",
      endDate: "",
      startTime: "",
      endTime: "",
      type: "vacation" as PTOType,
      description: "",
      isRecurring: false,
      recurringPattern: {
        frequency: "weekly" as const,
        interval: 1,
        daysOfWeek: [],
        endDate: "",
      },
    },
    validationSchema: Yup.object({
      staffId: Yup.string().required("Staff member is required"),
      startDate: Yup.string().required("Start date is required"),
      endDate: Yup.string().required("End date is required"),
      type: Yup.string().required("Type is required"),
      description: Yup.string().required("Description is required"),
      recurringPattern: Yup.object().when('isRecurring', {
        is: true,
        then: () => Yup.object({
          frequency: Yup.string().required("Frequency is required"),
          interval: Yup.number().required("Interval is required"),
        }),
        otherwise: (schema) => schema,
      }),
    }),
    validateOnMount: true,
    onSubmit: noop,
  });

  const createTimeOffHandler = async () => {
    formik.validateForm().then((val) => {
      const errors = Object.values(pickByType(val, "string"));
      if (errors.length === 0) {
        const { values } = formik;
        const payload: CreatePTORequest = {
          startDate: values.startDate,
          endDate: values.endDate,
          startTime: values.startTime || undefined,
          endTime: values.endTime || undefined,
          type: values.type,
          description: values.description,
          isRecurring: values.isRecurring,
          recurringPattern: values.isRecurring
            ? values.recurringPattern
            : undefined,
        };

        createTimeOffMutation.mutateAsync(
          {
            staffId: values.staffId,
            payload: {
              ...payload,
              // @ts-expect-error
              staffId: values.staffId
            },
          },
          {
            onSuccess() {
              navigate(`/teams/time-off?teamId=${selectedTeamId}`);
            },
          },
        );
      } else {
        showToast("error", "Invalid Field", {
          description: errors[0],
        });
      }
    });
  };

  const staffOptions =
    staffQuery.data?.map((staff) => ({
      value: staff._id,
      label: staff.name,
    })) || [];

  return (
    <div className="w-full max-w-2xl mx-auto space-y-6 pb-20">
      <div className="flex items-center gap-4">
        <Button
          variant="icon"
          className="p-1.5"
          onTap={() => navigate(`/teams/time-off?teamId=${selectedTeamId}`)}
        >
          <ArrowLeft02Icon size={20} />
        </Button>
        <div>
          <Typography variant="h3" className="font-semibold">
            Create Time-Off Request
          </Typography>
          <Typography variant="p" className="text-gray-600">
            Submit a new time-off request for staff
          </Typography>
        </div>
      </div>

      <div className="space-y-6 bg-white p-6 rounded-3xl border ">
        <div className="gap-y-6 gap-x-3 grid grid-cols-1 md:grid-cols-2  ">
          <SearchableDropdown
            label="Team"
            required
            fullWidth
            value={selectedTeamId}
            options={
              teamsQuery.data?.teams?.map((t) => ({
                label: t.name,
                value: t._id,
              })) || []
            }
            name=""
            className="max-w-60 "
            placeholder="Select a Team"
            onChange={(option) => {
              if (!option) return;
              setSearchParams((p) => {
                p.set("teamId", option.value);
                return p;
              });
            }}
          />
          {selectedTeamId !== "" && (
            <SearchableDropdown
              label="Staff Member"
              name="staffId"
              fullWidth
              required
              value={formik.values.staffId}
              onChange={(option) => {
                if (!option) return;
                formik.setFieldValue("staffId", option.value);
              }}
              options={staffOptions}
              placeholder="Select staff member"
            />
          )}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DatePicker.FormControl
            label="Start Date"
            name="startDate"
            className="w-fit"
            required
            value={formik.values.startDate as any}
            onChange={(value) => formik.setFieldValue("startDate", value)}
          />
          <DatePicker.FormControl
            label="End Date"
            name="endDate"
            className="w-fit"
            required
            value={formik.values.endDate as any}
            onChange={(value) => formik.setFieldValue("endDate", value)}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DatePicker.Time
            label="Start Time (Optional)"
            name="startTime"
            className="w-fit"
            value={formik.values.startTime}
            onChange={(value) => formik.setFieldValue("startTime", value)}
          />
          <DatePicker.Time
            label="End Time (Optional)"
            name="endTime"
            className="w-fit"
            value={formik.values.endTime}
            onChange={(value) => formik.setFieldValue("endTime", value)}
          />
        </div>

        <SearchableDropdown
          label="Type"
          name="type"
          fullWidth
          required
          value={formik.values.type}
          onChange={(option) => {
            if (!option) return;
            formik.setFieldValue("type", option.value);
          }}
          options={PTO_TYPES}
        />

        <Input.TextArea
          label="Description"
          name="description"
          required
          value={formik.values.description}
          onChange={formik.handleChange}
          placeholder="Describe the reason for time off"
        />

        <div className="w-full flex items-center justify-between ">
          <Typography className=" text-paragraph">Recurring Request</Typography>
          <Toggle
            checked={formik.values.isRecurring}
            onChange={(checked) => formik.setFieldValue("isRecurring", checked)}
          />
        </div>

        {formik.values.isRecurring && (
          <RecurringPatternForm
            pattern={formik.values.recurringPattern}
            onChange={(pattern) =>
              formik.setFieldValue("recurringPattern", pattern)
            }
          />
        )}
      </div>

      <ActionButtonGroup
        cancel={{
          title: "Cancel",
          disabled: createTimeOffMutation.isPending,
          onTap: () => navigate(`/teams/time-off?teamId=${selectedTeamId}`),
        }}
        next={{
          loading: createTimeOffMutation.isPending,
          disabled: createTimeOffMutation.isPending || !formik.isValid,
          title: innerWidth <= 480 ? "Save" : "Create Request",
          onTap: createTimeOffHandler,
        }}
      />

      <PendingOverlay isPending={createTimeOffMutation.isPending} />
    </div>
  );
};

export default CreateTimeOff;
