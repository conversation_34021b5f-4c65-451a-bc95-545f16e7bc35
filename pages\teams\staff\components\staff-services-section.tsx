import React, { useMemo, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { SaveButton } from "@/components/buttons";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import CheckBox from "@/components/ui/checkbox";
import { useToast } from "~/contexts/hooks/toast";
import { GetServices } from "@/src/services/services.service";
import { GetStaff, AssignStaffToService } from "@/src/services/staff.service";

interface StaffServicesSectionProps {
  selectedTeamId?: string;
}

const StaffServicesSection: React.FC<StaffServicesSectionProps> = ({
  selectedTeamId,
}) => {
  const showToast = useToast();
  const queryClient = useQueryClient();
  const [pendingChanges, setPendingChanges] = useState<
    Record<string, string[]>
  >({});

  // Fetch services
  const servicesQuery = useQuery({
    queryKey: ["services"],
    queryFn: async () => (await GetServices()).data.Services,
    enabled: true,
  });

  // Fetch staff for selected team
  const staffQuery = useQuery({
    queryKey: ["staff", selectedTeamId],
    queryFn: async () => (await GetStaff(selectedTeamId)).data.staff,
    enabled: !!selectedTeamId,
  });

  // Mutation for assigning staff to service
  const assignStaffMutation = useMutation({
    mutationFn: AssignStaffToService,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["services"] });
      queryClient.invalidateQueries({ queryKey: ["staff"] });
      setPendingChanges({});
    },
    onError: (error: any) => {
      showToast("error", "Failed to assign staff", {
        description: error.response?.data?.error || "Something went wrong",
      });
    },
  });

  const services = useMemo(
    () => servicesQuery.data || [],
    [servicesQuery.data],
  );
  const staff = staffQuery.data || [];

  // Create matrix data with current assignments + pending changes
  const matrixData = useMemo(() => {
    return services.map((service) => ({
      service,
      assignedStaff: pendingChanges[service._id] || service.assignedStaff || [],
    }));
  }, [services, pendingChanges]);

  const handleStaffToggle = (serviceId: string, staffId: string) => {
    setPendingChanges((prev) => {
      const currentAssigned =
        prev[serviceId] ||
        services.find((s) => s._id === serviceId)?.assignedStaff ||
        [];

      const newAssigned = currentAssigned.includes(staffId)
        ? currentAssigned.filter((id) => id !== staffId)
        : [...currentAssigned, staffId];

      return {
        ...prev,
        [serviceId]: newAssigned,
      };
    });
  };

  const handleSaveChanges = async () => {
    const promises = Object.entries(pendingChanges).map(
      ([serviceId, staffIds]) =>
        assignStaffMutation.mutateAsync({
          serviceId,
          staffIds,
        }),
    );

    try {
      await Promise.all(promises).then(() => {
        showToast("info", "Changes saved successfully");
        setPendingChanges({});
      });
    } catch (_error) {
      console.error("Error saving changes:", _error);
    }
  };

  const hasChanges = Object.keys(pendingChanges).length > 0;

  // if (!selectedTeamId) {
  //   return (
  //     <Dialog open={open} onClose={onClose}>
  //       <div className="p-6 text-center">
  //         <Typography className="text-gray-500">
  //           Please select a team first
  //         </Typography>
  //       </div>
  //     </Dialog>
  //   );
  // }

  return (
    <div className="flex flex-col gap-y-3 mt-5">
      <div className="w-full flex flex-col gap-y-2 justify-between items-start">
        <Typography
          variant={"h3"}
          className="font-bold font-Bricolage_Grotesque  "
        >
          Staff-Service Assignments
        </Typography>
        <Typography className="text-sm text-subtext pl-1">
          Assign Staff to Services
        </Typography>
      </div>
      <div className="flex flex-col mt-4 gap-y-5">
        <div className="">
          {servicesQuery.isLoading || staffQuery.isLoading ? (
            <div className="flex justify-center py-10">
              <CircularLoader />
            </div>
          ) : (
            <div className="overflow-x-auto thin-scrollbar pb-3">
              <div className="min-w-fit">
                {/* Header Row */}
                <div
                  className="grid gap-5 mb-4"
                  style={{
                    gridTemplateColumns: `200px repeat(${staff.length}, 120px)`,
                  }}
                >
                  <div className="font-semibold text-gray-600 p-3">
                    Service
                  </div>
                  {staff.map((staffMember) => (
                    <div key={staffMember._id} className="text-center p-2">
                      <div
                        className="w-8 h-8 rounded-full mx-auto mb-1 flex items-center justify-center text-white text-sm font-semibold"
                        style={{ backgroundColor: staffMember.color }}
                      >
                        {staffMember.name.charAt(0).toUpperCase()}
                      </div>
                      <div className="text-xs text-gray-600 truncate">
                        {staffMember.name}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Service Rows */}
                <div className="space-y-2">
                  {matrixData.map(({ service, assignedStaff }) => (
                    <div
                      key={service._id}
                      className="grid gap-2 items-center py-2 px-3 bg-gray-50 rounded-lg"
                      style={{
                        gridTemplateColumns: `200px repeat(${staff.length}, 120px)`,
                      }}
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{
                            backgroundColor: service.color || "#e91e63",
                          }}
                        />
                        <div>
                          <div className="font-medium text-sm">
                            {service.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {service.category?.name}
                          </div>
                        </div>
                      </div>

                      {staff.map((staffMember) => {
                        const isAssigned = assignedStaff.includes(
                          staffMember._id,
                        );

                        return (
                          <div
                            key={staffMember._id}
                            className="flex justify-center"
                          >
                            <CheckBox
                              checked={isAssigned}
                              className="!rounded-lg"
                              onChange={() =>
                                handleStaffToggle(service._id, staffMember._id)
                              }
                            />
                          </div>
                        );
                      })}
                    </div>
                  ))}
                </div>

                {services.length === 0 && (
                  <div className="text-center py-10 text-gray-500">
                    No services found
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        <SaveButton
          onTap={handleSaveChanges}
          disabled={!hasChanges || assignStaffMutation.isPending}
          loading={assignStaffMutation.isPending}
          hasChanges={hasChanges}
          title="Save"
        />
      </div>
    </div>
  );
};

export default StaffServicesSection;
