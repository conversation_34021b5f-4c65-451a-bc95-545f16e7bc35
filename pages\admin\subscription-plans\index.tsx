import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import BasicTooltip from "@/components/ui/basic-tooltip";
import Chip from "@/components/ui/chip";
import PendingOverlay from "@/components/ui/pending-overlay";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import { capitalizeFirstWord, convertPriceToDecimalFormat, omit } from "@/lib/helpers";
import { useToast } from "@/src/contexts/hooks/toast";
import {
  CreateProductPrice,
  DeleteProduct,
  DeleteProductPrice,
  GetAllProductPrices,
  GetAllProducts,
  ProductPayload,
  ProductPricePayload,
  UpdateProductPrice,
} from "@/src/services/admin/subscription.service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { CheckIcon, Tag, X } from "lucide-react";
import { useMemo, useState } from "react";
import PlanTemplate from "./subscription-plan-template";
import { useModalsBuilder } from "@/lib/modals-builder";
import { Pencil } from "@gravity-ui/icons";
import Modal from "@/components/ui/modal";
import Dialog from "@/components/ui/dialog";
import MoreInfo from "@/components/ui/more-info";

export type SubscriptionPlanPayload = ProductPayload & {
  prices: {
    currency: string;
    amount: number;
    interval: "day" | "week" | "month" | "year";
  }[];
};

export type SubscriptionPlan = Pick<
  SubscriptionPlanPayload,
  "active" | "description" | "features" | "name"
> & {
  _id: string;
  stripeProductId: string;
  prices: {
    _id: string;
    currency: string;
    unitAmount: number;
    productId: string;
    interval: "day" | "week" | "month" | "year";
  }[];
};

const AlreadyExistingSubscriptionPlan: React.FC<{
  plan: SubscriptionPlan;
  onEdit: (
    payload: Omit<SubscriptionPlanPayload, "prices"> & { id: string }
  ) => void;
}> = ({ plan, onEdit }) => {
  const [newPrice, setNewPrice] = useState<{
    currency: string;
    amount: number | string;
    interval: "month" | "year";
  }>({
    currency: "usd",
    amount: 0,
    interval: "month",
  });
  const deleteProductMutation = useMutation({ mutationFn: DeleteProduct });
  const deleteProductPriceMutation = useMutation({
    mutationFn: DeleteProductPrice,
  });
  const createProductPriceMutation = useMutation({
    mutationFn: CreateProductPrice,
  });
  const updateProductPriceMutation = useMutation({
    mutationFn: ({
      id,
      payload,
    }: {
      id: string;
      payload: ProductPricePayload;
    }) => UpdateProductPrice(id, payload),
  });
  const queryClient = useQueryClient();
  const showToast = useToast();
  const { modals, modalFunctions } = useModalsBuilder({
    deletePlan: {
      open: false,
    },
  });
  return (
    <div className="min-w-fit md:min-w-0 max-w-[320px] h-[480px] overflow-y-auto no-scrollbar border-2 rounded-lg relative transition-[height] duration-500 ease-[ease] border-primary-500/50 ">
      <PendingOverlay
        isPending={
          deleteProductMutation.isPending ||
          deleteProductPriceMutation.isPending ||
          createProductPriceMutation.isPending
        }
      />
      <div className="w-full pt-4 px-2 flex gap-x-2 justify-end">
        <BasicTooltip content="Edit Plan">
          <div>
            <Button
              onTap={() =>
                onEdit({
                  ...omit(plan, "prices"),
                  id: plan._id,
                })
              }
              variant="icon"
              className="rounded-full !p-1 bg-[#DFE1DB]/60 "
            >
              <Pencil className="text-gray-500 " width={15} height={15} />
            </Button>
          </div>
        </BasicTooltip>
        <BasicTooltip content="Delete Plan">
          <div>
            <Button
              onTap={() => {
                modalFunctions.openModal("deletePlan", {});
              }}
              variant="icon"
              className="rounded-full !p-1 bg-[#DFE1DB]/60 "
            >
              <X className="text-gray-500 " size={15} />
            </Button>
          </div>
        </BasicTooltip>
        <Dialog
          title={`Delete ${plan.name} Plan?`}
          description="Are you sure you want to delete this plan?"
          open={modals.deletePlan.open}
          onClose={() => modalFunctions.closeModal("deletePlan")}
          action={{
            title: "OK",
            onConfirm: () => {
              deleteProductMutation.mutate(plan._id, {
                onSuccess: () => {
                  Promise.all(
                    plan.prices.map((price) =>
                      deleteProductPriceMutation.mutateAsync(price._id)
                    )
                  ).then(() => {
                    queryClient.invalidateQueries({
                      queryKey: ["admin-subscription-plans"],
                    });
                    showToast("success", "Plan deleted successfully", {
                      description:
                        "Product and product prices deleted successfully",
                    });
                  });
                },
              });
            },
          }}
        />
      </div>
      <div className="w-full px-4 pt-4 pb-3 gap-y-2 flex flex-col ">
        <h1 className="text-xl font-bold group-hover:text-primary/50 group-data-[inview=true]:text-primary/50 ">
          {plan.name}
        </h1>
        <p className="font-medium text-gray-800 ">{plan.description}</p>
      </div>
      <div className="w-full pt-2 p-4 flex flex-col gap-y-2 text-sm ">
        <p className="font-bold">Prices:</p>
        <div className="flex flex-wrap gap-y-2 items-center gap-x-4">
          {plan.prices.map((price, index) => (
            <Chip
              onDelete={() => {
                deleteProductPriceMutation.mutateAsync(price._id, {
                  onSuccess: () => {
                    showToast("success", "Price deleted successfully");
                    queryClient.invalidateQueries({
                      queryKey: ["admin-subscription-plans"],
                    });
                  },
                });
              }}
              key={index}
              className="flex items-center gap-x-2"
            >
              <Tag strokeWidth={2} size={18} />{" "}
              <span>
                ${convertPriceToDecimalFormat(price.unitAmount)}/{price.interval}
              </span>
            </Chip>
          ))}
        </div>
        <div className="w-full flex-grow flex flex-col mt-2 gap-y-2">
          <div className="w-full flex items-end gap-x-2">
            <Input.Numeric
              label="Add Price"
              name="price"
              allowFloat={false}
              value={newPrice.amount}
              onChange={(_, value) =>
                setNewPrice((p) => ({
                  ...p,
                  amount: value,
                }))
              }
            />
            <SearchableDropdown
              fullWidth
              options={["month", "year"].map((interval) => ({
                label: `${capitalizeFirstWord(interval)}ly`,
                value: interval,
              }))}
              value={{
                value: newPrice.interval,
                label: capitalizeFirstWord(newPrice.interval),
              }}
              onChange={(option) => {
                setNewPrice((p) => ({
                  ...p,
                  interval: option!.value as "month" | "year",
                }));
              }}
            />
          </div>
          {newPrice.interval === "year" && (
            <MoreInfo.Static

              content="Annual plans must be at least 20% cheaper than paying monthly for a year"
            />
          )}
          <Button
            onTap={() => {
              // you should have only one price for a time interval
              const planPriceToUpdate = plan.prices.find(
                (price) => price.interval === newPrice.interval
              );
              if (planPriceToUpdate) {
                showToast("info", "Price already exists", {
                  description: "Will update the price instead",
                });
                updateProductPriceMutation.mutateAsync(
                  {
                    id: planPriceToUpdate._id,
                    payload: {
                      productId: plan._id,
                      ...newPrice,
                      amount: Number(newPrice.amount),
                    },
                  },
                  {
                    onSuccess() {
                      showToast(
                        "success",
                        `${newPrice.amount} ${newPrice.currency} for ${newPrice.interval} added successfully`
                      );
                      queryClient.invalidateQueries({
                        queryKey: ["admin-subscription-plans"],
                      });
                      setNewPrice((p) => ({
                        ...p,
                        amount: 0,
                        interval: "month",
                      }));
                    },
                  }
                );
              } else {
                // this one now creates the product price and resets state
                createProductPriceMutation.mutateAsync(
                  {
                    productId: plan._id,
                    ...newPrice,
                    amount: Number(newPrice.amount),
                  },
                  {
                    onSuccess() {
                      showToast(
                        "success",
                        `${newPrice.amount} ${newPrice.currency} for ${newPrice.interval} added successfully`
                      );
                      queryClient.invalidateQueries({
                        queryKey: ["admin-subscription-plans"],
                      });
                      setNewPrice((p) => ({
                        ...p,
                        amount: 0,
                        interval: "month",
                      }));
                    },
                  }
                );
              }
            }}
            className="w-full mt-5 py-2 px-4 text-sm font-medium rounded-[8px]  "
          >
            Add Price
          </Button>
        </div>
      </div>
      <div className="w-full mt-auto pt-2 p-4 flex flex-col gap-y-2 text-sm ">
        <p className="font-bold">Includes:</p>
        <div className="w-full flex-grow flex flex-col gap-y-2">
          {plan?.features.map((feature, index) => (
            <div className="w-full flex items-start gap-x-3" key={index}>
              <div className="w-4 h-4 text-green-500 mt-1">
                <CheckIcon height={16} width={16} />
              </div>
              <p className="text-paragraph ">{feature}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default function SubscriptionPlans() {
  const { data: subscriptionPlans = [] } = useQuery({
    queryKey: ["admin-subscription-plans"],
    queryFn: async () => {
      const {
        data: { products },
      } = await GetAllProducts();
      const {
        data: { productPrices },
      } = await GetAllProductPrices();
      const plans = products.map(
        (product): SubscriptionPlan => ({
          ...product,
          prices: productPrices.filter(
            (price) => price.productId === product._id
          ),
        })
      );
      return plans;
    },
  });
  const { modals, modalFunctions } = useModalsBuilder({
    editPlan: {
      open: false,
      payload: null as
        | (Omit<SubscriptionPlanPayload, "prices"> & { id: string })
        | null,
    },
  });
  const plansMemo = useMemo(() => {
    // sort plans based on small monthly plan and small yearly plan to highest monthly plan and highest yearly plan
    return subscriptionPlans.sort((a, b) => {
      const aMonthlyPrice =
        a.prices.find((price) => price.interval === "month")?.unitAmount || 0;
      const bMonthlyPrice =
        b.prices.find((price) => price.interval === "month")?.unitAmount || 0;
      const aYearlyPrice =
        a.prices.find((price) => price.interval === "year")?.unitAmount || 0;
      const bYearlyPrice =
        b.prices.find((price) => price.interval === "year")?.unitAmount || 0;
      if (aMonthlyPrice === 0 && bMonthlyPrice === 0) {
        return aYearlyPrice - bYearlyPrice;
      }
      if (aYearlyPrice === 0 && bYearlyPrice === 0) {
        return aMonthlyPrice - bMonthlyPrice;
      }
      return aMonthlyPrice - bMonthlyPrice;
    });
  }, [subscriptionPlans]);

  return (
    <section className="shadow-none pb-20 w-full max-w-screen-2xl space-y-4 mx-auto ">
      <div className="w-full h-fit flex gap-x-7 gap-y-7 flex-wrap">
        {plansMemo.map((plan, index) => (
          <AlreadyExistingSubscriptionPlan
            plan={plan}
            key={index}
            onEdit={(payload) =>
              modalFunctions.openModal("editPlan", { payload })
            }
          />
        ))}
        <PlanTemplate type="create" />
      </div>
      {modals.editPlan.payload && (
        <Modal
          open={modals.editPlan.open}
          onClose={() => modalFunctions.closeModal("editPlan")}
        >
          <Modal.Body
            className={`min-h-fit min-w-[300px] bg-white h-fit w-full !rounded-lg !p-0 `}
          >
            <PlanTemplate
              type="update"
              plan={{
                ...modals.editPlan.payload,
                onClose: () => modalFunctions.closeModal("editPlan"),
              }}
            />
          </Modal.Body>
        </Modal>
      )}
    </section>
  );
}
