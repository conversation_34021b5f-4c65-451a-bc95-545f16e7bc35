import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import {
  capitalizeFirstWord,
  noop,
  processPaymentMethods,
} from "@/lib/helpers";
import { SubscriptionPlan } from "@/src/interfaces/subscription-plan";
import { GetPaymentMethods } from "@/src/services/stripe.service";
import { useQuery } from "@tanstack/react-query";
import { formatDate } from "date-fns";
import { Check } from "lucide-react";
import { useMemo, useState } from "react";

type Props = {
  open: boolean;
  onClose: () => void;
  onConfirm: (paymentMethod: PaymentMethod) => void;
  plan: SubscriptionPlan;
};

const ConfirmPlanPaymentModal = (props: Props) => {
  const { data: paymentMethods = [] } = useQuery({
    queryKey: ["payment-methods"],
    queryFn: async () => {
      const res = await GetPaymentMethods();
      return res.data.paymentMethods;
    },
    refetchOnWindowFocus: false,
  });
  const filteredPaymentMethods = useMemo(
    () => processPaymentMethods(paymentMethods),
    [paymentMethods]
  );
  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<PaymentMethod | null>(null);
  const formatPaymentMethod = (method: PaymentMethod | null) =>
    method
      ? {
          value: method?._id || "",
          label: `${capitalizeFirstWord(method?.provider || "")} ************${
            method?.last4Digits
          } (expires ${formatDate(
            new Date(Number(method.expiryYear!), Number(method.expiryMonth)),
            "LL/yy"
          )})`,
        }
      : { value: "", label: "Select a payment method" };
  const addPaymentMethodOption = [
    {
      label: "Add a new payment method",
      value: "add-new-payment-method",
    },
  ] as const;
  const paymentMethodOptions = [
    ...(filteredPaymentMethods.map(formatPaymentMethod) || []),
    ...addPaymentMethodOption,
  ];
  return (
    <Modal open={props.open} onClose={props.onClose}>
      <Modal.Body
        className={`min-h-fit max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex flex-col mb-3">
          <Typography variant={"h4"} className="font-bold ">
            Confirm payment for {props.plan.name}
          </Typography>
          <div className="space-y-4 mt-3">
            <div className="flex w-full justify-between items-center">
              <Typography variant={"p"} className="font-Aeonik_Fono ">
                Included Features
              </Typography>
              <Typography
                variant={"p"}
                className="font-Aeonik_Fono text-green-600 "
              >
                ${props.plan.price}
              </Typography>
            </div>
            <ul className="space-y-3">
              {props.plan?.features.map((feature, index) => (
                <li key={index} className="flex items-center gap-2">
                  <Check className="h-5 w-5 text-green-500" />
                  <span className="text-paragraph text-sm">{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <SearchableDropdown
          options={paymentMethodOptions}
          value={
            formatPaymentMethod(selectedPaymentMethod) || {
              value: "",
              label: "",
            }
          }
          fullWidth
          onChange={(option) => {
            if (option?.value === "add-new-payment-method") {
              setSelectedPaymentMethod(null);
              // make a modal for this;
              return noop();
            }
            option?.value &&
              setSelectedPaymentMethod(
                filteredPaymentMethods?.find(
                  (method) => method._id === option.value
                ) || null
              );
          }}
        />
        <div className="w-full flex justify-between items-center mt-6">
          <Button onTap={props.onClose} className="w-full" variant="ghost">
            Cancel
          </Button>
          <Button
            disabled={!selectedPaymentMethod}
            onTap={() => props.onConfirm(selectedPaymentMethod!)}
            className="w-full"
          >
            Confirm
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default ConfirmPlanPaymentModal;
