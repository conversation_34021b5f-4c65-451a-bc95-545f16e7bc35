import { SignupUserPayload } from "@/src/services/auth.service";

export interface FormValues
	extends Omit<NonNullable<SignupUserPayload>, "planId" | "planInterval"> {
	business_name: string;
	industry: string;
	first_name: string;
	last_name: string;
	phone: string;
}

export interface FormData {
	email: string;
}

export interface StepProps {
	values?: FormValues;
	onChange: (
		e:
			| React.ChangeEvent<HTMLInputElement>
			| {
					name: string;
					value: string;
			  },
	) => void;
	onSubmit?: (e: React.FormEvent) => void;
}
