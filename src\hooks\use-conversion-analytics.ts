import { useMutation, useQuery } from "@tanstack/react-query";
import {
  ConversionAnalyticsPayload,
  TrackConversion,
} from "../services/analytics.service";
import { GetUserDetails } from "../services/auth.service";

type ConversionType = ConversionAnalyticsPayload["conversionType"];

/**
 * @dev should be called inside a component
 * @dev this should be in one place -> root component -> App.tsx
 * @dev SHOULD NOT BE IMPLEMENTED YET because we don't have a way to track page url yet.
 */
export const useConversionAnalytics = () => {
  const { data: user } = useQuery({
    queryFn: async () => (await GetUserDetails()).data.data.user,
    queryKey: ["user"],
    refetchOnWindowFocus: false,
  });
  const { mutateAsync } = useMutation({
    mutationFn: TrackConversion,
    mutationKey: ["track-conversion"],
  });
  return {
    trackConversion: (conversionType: ConversionType) => {
      if (!user) return;
      mutateAsync({
        conversionType,
        userId: user?._id,
      });
    },
  };
};
