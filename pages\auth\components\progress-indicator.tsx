import { Check } from "lucide-react";

const ProgressIndicator = ({
  currentStep,
  completedStep,
}: {
  currentStep: number;
  completedStep: 5 | (number & {});
}) => {
  const steps = [
    { number: 1, label: "Select Plan" },
    { number: 2, label: "Create Account" },
    { number: 3, label: "Subscription" },
  ];

  return (
    <section className="mb-8">
      <div className="flex justify-center items-center space-x-4 md:space-x-8">
        {currentStep === completedStep ? (
          <div className="flex items-center">
            <div className="flex flex-col items-center">
              <div
                className={`w-7 h-7 rounded-full flex items-center justify-center border-2 bg-primary border-primary `}
              >
                <Check className="h-5 w-5 text-white" />
              </div>
              <span className={`mt-2 text-xs text-primary font-medium `}>
                Setup Complete!
              </span>
            </div>
          </div>
        ) : (
          steps.map((step, index) => {
            const isCompleted = currentStep > step.number;
            const isCurrent = currentStep === step.number;

            return (
              <div key={step.number} className="flex items-center">
                {index > 0 && (
                  <div className="hidden md:block w-20 h-1 -ml-8 bg-gray-200">
                    <div
                      className="h-full bg-primary transition-all duration-500"
                      style={{ width: isCompleted ? "100%" : "0%" }}
                    />
                  </div>
                )}
                <div className="flex flex-col items-center">
                  <div
                    className={`w-7 h-7 rounded-full flex items-center justify-center border-2 
                    ${
                      isCompleted
                        ? "bg-primary border-primary"
                        : isCurrent
                        ? "border-primary"
                        : "border-gray-300"
                    }`}
                  >
                    {isCompleted ? (
                      <Check className="h-5 w-5 text-white" />
                    ) : (
                      <span
                        className={`${
                          isCurrent ? "text-primary" : "text-gray-400"
                        }`}
                      >
                        {step.number}
                      </span>
                    )}
                  </div>
                  <span
                    className={`mt-2 text-xs ${
                      isCurrent ? "text-primary font-medium" : "text-gray-500"
                    }`}
                  >
                    {step.label}
                  </span>
                </div>
              </div>
            );
          })
        )}
      </div>
    </section>
  );
};

export default ProgressIndicator;
