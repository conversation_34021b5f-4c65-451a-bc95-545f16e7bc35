import React from "react";
import { useNavigate } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { ActionButtonGroup, Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import PendingOverlay from "@/components/ui/pending-overlay";
import { ArrowLeft02Icon } from "hugeicons-react";
import { useMultiLocation } from "./hooks/use-location";
import { CreateLocationPayload } from "@/src/interfaces/multi-location";
import moment from "moment-timezone";
import { useToast } from "@/src/contexts/hooks/toast";
import { noop, pickByType } from "@/lib/helpers";

const timeZones = moment.tz.names();

const CreateLocationPage: React.FC = () => {
  const navigate = useNavigate();
  const { createLocationMutation, geocodeAddressMutation } = useMultiLocation();

  const formik = useFormik<CreateLocationPayload>({
    initialValues: {
      name: "",
      address: {
        street: "",
        city: "",
        state: "",
        zipCode: "",
        country: "",
      },
      timezone: "Europe/London",
      phone: "",
      email: "",
      description: "",
    },
    validationSchema: Yup.object({
      name: Yup.string().required("Location name is required"),
      address: Yup.object({
        street: Yup.string().required("Street address is required"),
        city: Yup.string().required("City is required"),
        state: Yup.string().required("State is required"),
        zipCode: Yup.string().required("ZIP code is required"),
        country: Yup.string().required("Country is required"),
      }),
      timezone: Yup.string().required("Timezone is required"),
      phone: Yup.string(),
      email: Yup.string().email("Invalid email format"),
      description: Yup.string(),
    }),
    onSubmit: noop
  });

  const handleGeocodeAddress = async () => {
    const { street, city, state, country } = formik.values.address;
    const fullAddress = `${street}, ${city}, ${state}, ${country}`;

    if (fullAddress.trim()) {
      geocodeAddressMutation.mutate(fullAddress);
    }
  };

  const showToast = useToast()

  const createLocationHandler = async () => {
    formik.validateForm().then((val) => {
      const errors = Object.values(pickByType(val, "string"));
      if (errors.length === 0) {
        const { values } = formik;
        createLocationMutation.mutateAsync(
          values,
          {
            onSuccess() {
              navigate("/locations");
            },
          },
        );
      } else {
        showToast("error", "Invalid Field", {
          description: errors[0],
        });
      }
    });
  };

  return (
    <section className="w-full h-screen overflow-y-auto p-4">
      <PendingOverlay isPending={createLocationMutation.isPending} />

      <div className="max-w-2xl mx-auto space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="icon"
            onTap={() => navigate("/locations")}
            className="p-1.5"
          >
            <ArrowLeft02Icon className="w-5 h-5" />
          </Button>
          <Typography variant="h3" className="font-semibold">
            Add New Location
          </Typography>
        </div>

        <div className="space-y-6">
          <div className="bg-white rounded-lg p-6 shadow-sm space-y-4">
            <Typography variant="h4" className="font-medium">
              Basic Information
            </Typography>

            <Input.Text
              label="Location Name"
              name="name"
              value={formik.values.name}
              onChange={formik.handleChange}
              required
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input.Phone
                label="Phone"
                name="phone"
                value={formik.values.phone}
                onChange={(value, e) => {
                  formik.setFieldValue(e.currentTarget.name, value);
                }}
              />

              <Input.Email
                label="Email"
                name="email"
                value={formik.values.email}
                onChange={formik.handleChange}
              />
            </div>

            <Input.TextArea
              label="Description"
              name="description"
              value={formik.values.description}
              onChange={formik.handleChange}
            />
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm space-y-4">
            <div className="flex items-center justify-between">
              <Typography variant="h4" className="font-medium">
                Address Information
              </Typography>
              <Button
                type="button"
                variant="dormant"
                onTap={handleGeocodeAddress}
                loading={geocodeAddressMutation.isPending}
                className="text-sm"
              >
                Verify Address
              </Button>
            </div>

            <Input.Text
              label="Street Address"
              name="address.street"
              value={formik.values.address.street}
              onChange={formik.handleChange}
              required
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input.Text
                label="City"
                name="address.city"
                value={formik.values.address.city}
                onChange={formik.handleChange}
                required
              />

              <Input.Text
                label="State"
                name="address.state"
                value={formik.values.address.state}
                onChange={formik.handleChange}
                required
              />

              <Input.Text
                label="ZIP Code"
                name="address.zipCode"
                value={formik.values.address.zipCode}
                onChange={formik.handleChange}
                required
              />
            </div>

            <Input.Text
              label="Country"
              name="address.country"
              value={formik.values.address.country}
              onChange={formik.handleChange}
              required
            />
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm space-y-4">
            <Typography variant="h4" className="font-medium">
              Timezone
            </Typography>

            <SearchableDropdown
              label="Timezone"
              placeholder="Select timezone"
              value={formik.values.timezone}
              options={timeZones.map((tz) => ({
                label: tz,
                value: tz,
              }))}
              onChange={(option) => {
                formik.setFieldValue("timezone", option?.value || "");
              }}
              required
            />
          </div>
        </div>
      </div>
      <ActionButtonGroup
        cancel={{
          title: "Cancel",
          disabled: createLocationMutation.isPending,
          onTap: () => navigate("/locations")
        }}
        next={{
          loading: createLocationMutation.isPending,
          disabled: createLocationMutation.isPending || !formik.isValid,
          title: innerWidth <= 480 ? "Save" : 'Create Location',
          onTap: createLocationHandler
        }}
      />

    </section>
  );
};

export default CreateLocationPage;
