// src/components/Header.tsx
import { Button } from "@/components/buttons";
import Menu from "@/components/ui/menu";
import { useNavigate } from "react-router-dom";
import Avatar from "@/components/ui/avatar";
import useAuthStore from "@/pages/auth/components/auth-store";
import { Login03Icon, Logout03Icon, Settings02Icon } from "hugeicons-react";
import { sleep } from "@/lib/helpers";

export const UserMenu = () => {
	const {
		initialState: { user },
		logout,
	} = useAuthStore();
	const navigate = useNavigate();

  return (
    <div className="w-fit h-fit">
      <Menu transformOrigin="top-left">
        <Menu.Trigger>
          <Button className="!rounded-full !p-0 bg-transparent " variant="icon">
            <Avatar src={user?.user_photo} alt="User Avatar" size={40} />
          </Button>
        </Menu.Trigger>
        <Menu.Content className="h-fit min-w-[240px] !min-h-0 font-Satoshi">
          <Menu.Item
            onClick={() => {
              setTimeout(() => {
                navigate(
                  innerWidth < 1024 ? "/profile" : "/profile/basic-info",
                );
              }, 500);
            }}
          >
            Settings <Settings02Icon width={20} height={20} />
          </Menu.Item>
          {import.meta.env.DEV && (
            <Menu.Item
              onClick={async () => {
                await sleep(500);
                navigate("/signin");
              }}
            >
              Go to Sign-In <Login03Icon width={20} height={20} />
            </Menu.Item>
          )}
          <Menu.Item
            onClick={() => {
              localStorage.clear(); // remove pptoken
              setTimeout(() => {
                logout();
              }, 500);
            }}
          >
            Log Out <Logout03Icon width={20} height={20} />
          </Menu.Item>
        </Menu.Content>
      </Menu>
    </div>
  );
};
