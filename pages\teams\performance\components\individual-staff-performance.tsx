import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Typography } from "@/components/typography";
import { But<PERSON> } from "@/components/buttons";
import CircularLoader from "@/components/ui/circular-loader";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell,
} from "recharts";
import {
  GetStaffPerformance,
  GetStaffPerformanceParams,
} from "@/src/services/staff-performance.service";
import {
  StarIcon,
  ChartBreakoutSquareIcon,
  CheckmarkCircle02Icon,
  DollarCircleIcon,
} from "hugeicons-react";
import StaffRatingsModal from "./staff-ratings-modal";

interface IndividualStaffPerformanceProps {
  staffId: string;
  staffName: string;
  staffColor: string;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  periodType: "daily" | "weekly" | "monthly" | "quarterly" | "yearly";
}

const IndividualStaffPerformance: React.FC<IndividualStaffPerformanceProps> = ({
  staffId,
  staffName,
  staffColor,
  dateRange,
  periodType,
}) => {
  const [showRatingsModal, setShowRatingsModal] = useState(false);

  const performanceQuery = useQuery({
    queryKey: ["staff-performance", staffId, dateRange, periodType],
    queryFn: async () => {
      const params: GetStaffPerformanceParams = {
        staffId,
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
        periodType,
      };
      return (await GetStaffPerformance(params)).data;
    },
    enabled: !!staffId && !!dateRange.startDate && !!dateRange.endDate,
  });

  const performance = performanceQuery.data?.performance;
  const metrics = performance?.metrics;

  if (performanceQuery.isLoading) {
    return (
      <div className="flex justify-center py-8">
        <CircularLoader />
      </div>
    );
  }

  if (!performance || !metrics) {
    return (
      <Card>
        <CardContent className="p-8 text-center text-gray-500">
          No performance data available for this period
        </CardContent>
      </Card>
    );
  }

  const keyMetrics = [
    {
      title: "Total Bookings",
      value: metrics.totalBookings,
      icon: ChartBreakoutSquareIcon,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Total Revenue",
      value: `$${metrics.totalRevenue.toLocaleString()}`,
      icon: DollarCircleIcon,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "Efficiency",
      value: `${(metrics.efficiency * 100).toFixed(1)}%`,
      icon: CheckmarkCircle02Icon,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      title: "Average Rating",
      value: `${metrics.averageRating.toFixed(1)}/5`,
      icon: StarIcon,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
    },
  ];

  const bookingStatusData = [
    { name: "Completed", value: metrics.completedBookings, color: "#10B981" },
    { name: "Cancelled", value: metrics.cancelledBookings, color: "#EF4444" },
    { name: "No Show", value: metrics.noShowBookings, color: "#F59E0B" },
  ];

  const serviceBreakdownData = performance.serviceBreakdown.map((service) => ({
    name:
      service.serviceName.length > 15
        ? service.serviceName.substring(0, 15) + "..."
        : service.serviceName,
    bookings: service.bookings,
    revenue: service.revenue,
    rating: service.averageRating,
  }));

  return (
    <div className="space-y-6">
      {/* Staff Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/*<Avatar
                name={staffName}
                size="lg"
                backgroundColor={staffColor}
              />*/}
              <div>
                <Typography variant="h2" className="font-semibold">
                  {staffName}
                </Typography>
                <Typography className="text-gray-600">
                  Performance Overview •{" "}
                  {periodType.charAt(0).toUpperCase() + periodType.slice(1)}
                </Typography>
              </div>
            </div>
            <Button variant="outline" onTap={() => setShowRatingsModal(true)}>
              View Ratings
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {keyMetrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Typography className="text-sm text-gray-600 mb-1">
                      {metric.title}
                    </Typography>
                    <Typography variant="h3" className="font-bold">
                      {metric.value}
                    </Typography>
                  </div>
                  <div className={`p-3 rounded-full ${metric.bgColor}`}>
                    <Icon className={`w-6 h-6 ${metric.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Booking Status */}
        <Card>
          <CardHeader>
            <CardTitle>Booking Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={bookingStatusData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {bookingStatusData.map((entry, index) => (
                    <Cell key={index} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Service Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Service Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={serviceBreakdownData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="name"
                  tick={{ fontSize: 12 }}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis />
                <Tooltip
                  formatter={(value: number, name: string) => [
                    name === "revenue" ? `$${value.toLocaleString()}` : value,
                    name.charAt(0).toUpperCase() + name.slice(1),
                  ]}
                />
                <Bar dataKey="bookings" fill="#3B82F6" name="Bookings" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Ratings Modal */}
      <StaffRatingsModal
        open={showRatingsModal}
        onClose={() => setShowRatingsModal(false)}
        staffId={staffId}
        staffName={staffName}
        staffColor={staffColor}
      />
    </div>
  );
};

export default IndividualStaffPerformance;
