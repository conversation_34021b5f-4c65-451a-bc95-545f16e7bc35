import { <PERSON><PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Avatar from "@/components/ui/avatar";
import { cn } from "@/lib/utils";
import { useMemo } from "react";
import { Link, Outlet, useLocation, useNavigate } from "react-router-dom";
import useAuthStore from "../auth/components/auth-store";
import AccountSettings from "./account-settings";
import BasicInfoForm from "./basic-info";
import ChangePassword from "./change-password";
import DeleteAccount from "./delete-account";
import NotificationSettings from "./notification-settings";
import Billing from "./billing";
import SessionsSettings from "./session-settings";
import TwoFASettings from "./two-fa-settings";
import CalendarSettings from "./calendar-settings";
import {
  ArrowLeft02Icon,
  ArrowRight01Icon,
  CalendarSetting01Icon,
  CardExchange02Icon,
  CheckmarkBadge01Icon,
  Delete02Icon,
  LockedIcon,
  LockKeyIcon,
  Notification02Icon,
  Timer01Icon,
  UserSquareIcon,
} from "hugeicons-react";

type LinksConfig = {
  userEmail: string;
};

function getSettingsLinks(config: LinksConfig): {
  link: `/profile/${string}` | (string & {});
  icon: JSX.Element;
  title: string;
  inactive?: boolean;
}[] {
  return [
    {
      link: "/profile/basic-info",
      icon: <UserSquareIcon />,
      title: "Basic Info",
    },
    {
      link: `/request-password-reset?email=${config.userEmail}`,
      icon: <LockKeyIcon />,
      title: "Change Password",
    },
    {
      link: "/profile/2fa",
      icon: <LockedIcon />,
      title: "2FA",
      inactive: true,
    },
    {
      link: "/profile/accounts",
      icon: <CheckmarkBadge01Icon />,
      title: "Accounts",
    },
    {
      link: "/profile/billing",
      icon: <CardExchange02Icon />,
      title: "Billing",
    },
    {
      link: "/profile/notifications",
      icon: <Notification02Icon />,
      title: "Notifications",
      inactive: true,
    },
    {
      link: "/profile/sessions",
      icon: <Timer01Icon />,
      title: "Sessions",
      inactive: true,
    },
    {
      link: "/profile/calendar-settings",
      icon: <CalendarSetting01Icon />,
      title: "Calendar Settings",
    },
    {
      link: "/profile/delete-account",
      icon: <Delete02Icon />,
      title: "Delete Account",
      inactive: true,
    },
  ];
}

const ProfilePage = () => {
  const {
    initialState: { user },
  } = useAuthStore();
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const settingsLinks = useMemo(
    () => getSettingsLinks({ userEmail: user?.email || "" }),
    [user?.email],
  );
  const linkTitle = useMemo(() => {
    return (
      settingsLinks.find((link) => link.link === pathname)?.title || "Profile"
    );
  }, [pathname]);

  return (
    <section className="w-full h-fit flex flex-col flex-grow pb-20 lg:!bg-[#DFE1DB]/15 lg:mb-6 lg:py-8 lg:pb-10 lg:px-4 lg:rounded-[32px] ">
      <div className="w-full flex items-center justify-between lg:hidden">
        <Button
          onTap={() => {
            navigate("/profile");
          }}
          variant="icon"
          className={cn(
            "bg-transparent border-2 !border-subtle-gray !rounded-full text-black ",
            {
              "invisible pointer-events-none": pathname === "/profile",
            },
          )}
        >
          <ArrowLeft02Icon />
        </Button>
        <Typography variant={"p"} className="!mt-0 mx-auto text-lg">
          {linkTitle}
        </Typography>
        {/* just here to center align linkTitle, don't remove though */}
        <Button
          variant="icon"
          className="bg-transparent border-2 !border-subtle-gray !rounded-full  pointer-events-none invisible"
        >
          <ArrowLeft02Icon />
        </Button>
      </div>
      {window.innerWidth < 1024 && (
        <div className="w-full py-4">
          <Outlet />
        </div>
      )}
      {window.innerWidth >= 1024 && (
        <div className="hidden md:grid grid-cols-[auto_auto_1fr] gap-x-10">
          <ul className="flex flex-col w-fit pt-0 px-4 gap-y-6">
            {settingsLinks
              .filter((link) => !link.inactive)
              .map(({ link, title }, index) => (
                <li key={index} className="nav-item pt-2">
                  <Link
                    className={cn(" px-2 py-2.5 rounded-full", {
                      "bg-primary-50/40 px-5 ": link === pathname,
                    })}
                    to={link}
                  >
                    <span
                      className={cn("text-base font-bold !mt-0 text-subtext", {
                        "!text-red-500 inline-block !mt-8":
                          link.includes("delete-"),
                        "!text-primary-500 ": link === pathname,
                      })}
                    >
                      {title}
                    </span>
                  </Link>
                </li>
              ))}
          </ul>
          {/* desktop divider */}
          <div className="w-0.5 bg-gray-100" />
          {/* desktop outlet */}
          <div className="flex-grow flex flex-col gap-y-4 pr-4">
            <div className="flex items-center gap-x-4 ">
              <div className="w-fit h-fit ">
                <Avatar
                  src={user?.user_photo}
                  alt="User Avatar"
                  className="!ml-0 !w-12 !h-12"
                />
              </div>
              <div className="h-fit flex flex-col gap-y-">
                <Typography
                  variant={"h4"}
                  className="!text-base font-Bricolage_Grotesque"
                >
                  {user?.last_name} {user?.first_name}
                </Typography>
                <p className="">{user?.business_bio || "Bio"}</p>
              </div>
            </div>
            <Outlet />
          </div>
        </div>
      )}
    </section>
  );
};

/**
 * @dev this page shows first on the mobile view
 */
const ProfileLinksPage = () => {
  const {
    initialState: { user },
  } = useAuthStore();
  const settingsLinks = useMemo(
    () => getSettingsLinks({ userEmail: user?.email || "" }),
    [user?.email],
  );

  return (
    <div className="w-full h-fit mt-3 lg:hidden">
      <div className="w-full flex flex-col items-center gap-y-4 ">
        <Avatar
          src={user?.user_photo}
          alt="User Avatar"
          className="!ml-0 !w-32 !h-32"
        />
        <Typography variant={"h4"} className="!mt-0 font-Bricolage_Grotesque">
          {user?.first_name} {user?.last_name}
        </Typography>
      </div>
      <div className="w-full flex flex-col pt-4 px-2 ">
        {settingsLinks
          .filter((link) => !link.inactive)
          .map(({ link, icon, title }, index) => (
            <Link
              key={index}
              to={link}
              className="w-full cursor-pointer border-b last:border-none"
            >
              <div className="w-full flex gap-x-5 items-center py-4 text-primary-400  ">
                {icon}
                <Typography
                  variant={"p"}
                  className="!mt-0 text-lg text-paragraph "
                >
                  {title}
                </Typography>
                <ArrowRight01Icon className="ml-auto text-paragraph" />
              </div>
            </Link>
          ))}
      </div>
    </div>
  );
};

const Profile = Object.assign(ProfilePage, {
  // do your ui refresh here
  AccountSettings,
  BasicInfoForm,
  ChangePassword,
  DeleteAccount,
  NotificationSettings,
  Billing,
  SessionsSettings,
  TwoFASettings,
  CalendarSettings: CalendarSettings,
  MobileProfileLinks: ProfileLinksPage,
});

export default Profile;
