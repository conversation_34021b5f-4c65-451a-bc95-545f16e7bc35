import { Typography } from "@/components/typography"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { getCurrencySign } from "@/lib/helpers"
import { BookingServiceResponse } from "@/src/services/booking.service"
import { motion } from "framer-motion"
import { Clock01Icon, DollarCircleIcon } from "hugeicons-react"

type Props = {
  service: Replace<
    Replace<
      Pick<BookingServiceResponse, 'Service' | 'userId'>,
      'Service', 'service'
    >, 'userId', 'user'> | undefined;
  bookingType: 'external' | 'internal'
  displayPrice: boolean
  billingSettings: {
    preferredCurrency: string
  }
}

const ServiceReview = (props: Props) => {
  const { service } = props
  return (
    <motion.div
      key={"service-review"}
      initial={{
        opacity: 0,
        x: -100,
      }}
      animate={{
        opacity: 1,
        x: 0,
      }}
      exit={{
        opacity: 0,
        x: -100,
      }}
      className="w-full flex flex-col gap-6 min-w-[256px] md:pl-0"
    >
      <div className="w-full space-y-2">
        <p
          className="text-xl font-Satoshi capitalize font-bold"
        >
          {service?.user?.last_name} {service?.user?.first_name}
        </p>
        <p className="text-subtext font-semibold ">{service?.service.name}</p>
      </div>
      <div className="w-full flex gap-3 text-subtext ">
        <Clock01Icon />
        <p className="text-gray-500 font-semibold">
          {service?.service.duration} minutes
        </p>
      </div>
      {
        <div className="w-full flex gap-3 text-subtext ">
          <DollarCircleIcon width={25} height={25} />
          <p className="text-gray-500 font-semibold">
            {service?.service.price === 0 || !props.displayPrice
              ? "Free"
              : `${getCurrencySign(props.billingSettings?.preferredCurrency)}${service?.service.price}`
            }
          </p>
          {props.bookingType === 'internal' && (
            <span className="text-sm font-medium" >(You will not be charged)</span>
          )}
        </div>
      }
      <section className="w-full bsm:pb-9">
        <Accordion
          type="single"
          defaultValue={innerWidth <= 768 ? undefined : 'more-details'}
          collapsible
          className="w-full"
        >
          <AccordionItem value={`more-details`} className="border-none bg-[#EAEBE5] " >
            <AccordionTrigger className="hover:underline-[none]" >
              <Typography
                className="text-xl font-Satoshi capitalize font-bold"
              >
                More Details
              </Typography>
            </AccordionTrigger>
            <AccordionContent className="flex flex-col gap-y-3 pt-2">
              <div className="flex flex-col gap-y-3 ">
                <Typography className=" py-0.5 bg-white w-fit rounded-md">
                  Description
                </Typography>
                <pre className="px-2 flex items-center justify-between font-Satoshi">
                  {service?.service.description || 'No description'}
                </pre>
              </div>
              <div className="flex flex-col gap-y-3 ">
                <Typography className="py-0.5 bg-white w-fit rounded-md">
                  Service Add-ons
                </Typography>
                <div className="flex-wrap flex items-center gap-[10px] pb-2 ">
                  {service?.service.serviceOptions?.map((option, index) => (
                    <Typography
                      key={index}
                      className="py-0.5 px-3 text-xs !mt-0 text-gray-600 bg-gray-100 rounded-full "
                    >
                      {option.name}{" "}
                      <span className="text-green-500 font-bold">{
                        `${getCurrencySign(props.billingSettings?.preferredCurrency)}${option.price}`
                      }</span>
                    </Typography>
                  ))}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </section>
    </motion.div>

  )
}

export default ServiceReview
