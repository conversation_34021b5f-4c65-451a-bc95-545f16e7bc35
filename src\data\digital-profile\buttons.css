button#wavy-button {
  --s: 0.25em; /* control the wave*/
  width: 100%;
  padding: 0.4em 0.5em;
  background-color: #bf4d28;
  color: #fff;
  --_s: calc(var(--s) * 4) 51% repeat-x;
  --_r: calc(1.345 * var(--s)) at left 50%;
  --_g1: #000 99%, #0000 101%;
  --_g2: #0000 99%, #000 101%;
  --mask: radial-gradient(var(--_r) top calc(var(--s) * 1.9), var(--_g1))
      calc(50% - 2 * var(--s) - var(--_i, 0px)) 0 / var(--_s),
    radial-gradient(var(--_r) top calc(var(--s) * -0.9), var(--_g2))
      calc(50% - var(--_i, 0px)) var(--s) / var(--_s),
    radial-gradient(var(--_r) bottom calc(var(--s) * 1.9), var(--_g1))
      calc(50% - 2 * var(--s) + var(--_i, 0px)) 100% / var(--_s),
    radial-gradient(var(--_r) bottom calc(var(--s) * -0.9), var(--_g2))
      calc(50% + var(--_i, 0px)) calc(100% - var(--s)) / var(--_s);
  -webkit-mask: var(--mask);
  mask: var(--mask);
  clip-path: polygon(
    calc(2 * var(--s) - var(--_i, 0px)) 0,
    calc(100% - var(--_i, 0px)) 0,
    calc(100% - var(--s)) 50%,
    calc(100% - 2 * var(--s) + var(--_i, 0px)) 100%,
    calc(0% + var(--_i, 0px)) 100%,
    var(--s) 50%
  );
  cursor: pointer;
  transition: 0.35s;
}
button#wavy-button.alt {
  clip-path: polygon(
    calc(0% - var(--_i, 0px)) 0,
    calc(100% - 2 * var(--s) - var(--_i, 0px)) 0,
    calc(100% - var(--s)) 50%,
    calc(100% + var(--_i, 0px)) 100%,
    calc(2 * var(--s) + var(--_i, 0px)) 100%,
    var(--s) 50%
  );
}
button#wavy-button:hover {
  --_i: calc(2 * var(--s));
}
button#wavy-button.alt:hover {
  --_i: calc(-2 * var(--s));
}
button#wavy-button:active {
  background-image: linear-gradient(#0004 0 0);
}
button#wavy-button:focus-visible {
  mask: none;
  -webkit-mask: none;
  clip-path: none;
  outline-offset: 0.1em;
  padding-block: 0.2em;
  margin-block: 0.2em;
  transition: 0s;
}

button#wavy-button {
  font-weight: bold;
  padding-block: 12px;
  cursor: pointer;
  border: none;
}

button#background-move {
  width: 98%;
  box-sizing: border-box;
  letter-spacing: 1px;
  display: flex;
  justify-content: center;
  padding: 13px 20px 13px;
  outline: 0;
  border: 1px solid black;
  cursor: pointer;
  position: relative;
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
}

button#background-move:after {
  content: "";
  background-color: #ffe54c;
  width: 100%;
  z-index: -1;
  position: absolute;
  height: 100%;
  top: 7px;
  left: 7px;
  transition: 0.2s;
}

button#background-move:hover:after {
  top: 0px;
  left: 0px;
}

button#perspective {
  background-color: #3dd1e7;
  border: 0 solid #e5e7eb;
  box-sizing: border-box;
  color: #000000;
  display: flex;
  font-size: 1rem;
  font-weight: 700;
  justify-content: center;
  line-height: 1.75rem;
  padding: 0.75rem 1.65rem;
  position: relative;
  text-align: center;
  text-decoration: none #000000 solid;
  text-decoration-thickness: auto;
  width: 100%;
  position: relative;
  cursor: pointer;
  transform: rotate(-2deg);
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
}

button#perspective:focus {
  outline: 0;
}

button#perspective:after {
  content: "";
  position: absolute;
  border: 1px solid #000000;
  bottom: 4px;
  left: 4px;
  width: calc(100% - 1px);
  height: calc(100% - 1px);
}

button#perspective:hover:after {
  bottom: 2px;
  left: 2px;
}

@media (min-width: 768px) {
  button#perspective {
    padding: 0.75rem 3rem;
    font-size: 1.25rem;
  }
}

button#cover {
  position: relative;
  overflow: hidden;
  width: 100%;
  border: 1px solid #18181a;
  color: #18181a;
  display: inline-block;
  font-size: 15px;
  line-height: 15px;
  padding: 18px 18px 17px;
  text-decoration: none;
  cursor: pointer;
  background: #fff;
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
}

button#cover span:first-child {
  position: relative;
  transition: color 600ms cubic-bezier(0.48, 0, 0.12, 1);
  z-index: 10;
}

button#cover span:last-child {
  color: white;
  display: block;
  position: absolute;
  bottom: 0;
  transition: all 500ms cubic-bezier(0.48, 0, 0.12, 1);
  z-index: 100;
  opacity: 0;
  top: 50%;
  left: 50%;
  transform: translateY(225%) translateX(-50%);
  height: 14px;
  line-height: 13px;
}

button#cover:after {
  content: "";
  position: absolute;
  bottom: -50%;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: black;
  transform-origin: bottom center;
  transition: transform 600ms cubic-bezier(0.48, 0, 0.12, 1);
  transform: skewY(9.3deg) scaleY(0);
  z-index: 50;
}

button#cover:hover:after {
  transform-origin: bottom center;
  transform: skewY(12deg) scaleY(2);
}

@media (min-width: 768px) {
  button#cover:hover:after {
    transform-origin: bottom center;
    transform: skewY(9deg) scaleY(2);
  }
}

button#cover:hover span:last-child {
  transform: translateX(-50%) translateY(-60%);
  opacity: 1;
  transition: all 900ms cubic-bezier(0.48, 0, 0.12, 1);
}

/* last button */

button#simple {
  font-weight: 600;
  width: 100%;
  border: solid 2px black;
  outline: 0;
  padding: 1rem 4rem;
  letter-spacing: 0.08rem;
  background-color: white;
  border-radius: 0.35rem;
  position: relative;
  cursor: pointer;
}

button#simple::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: pink;
  z-index: -1;
  border-radius: 0.35rem;
  border: solid 2px black;
  transition: all 0.3s ease-in-out;
}

button#simple::after {
  top: 0.5rem;
  left: 0.5rem;
}

button#simple:hover::after {
  width: calc(100% + 0.5rem);
  height: calc(100% + 0.5rem);
}