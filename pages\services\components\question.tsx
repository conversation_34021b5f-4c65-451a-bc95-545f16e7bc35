import { But<PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import Toggle from "@/components/ui/toggle-2";
import { ArrowReloadHorizontalIcon, ArrowRight01Icon, Delete02Icon, DragDropVerticalIcon, PlusSignIcon } from "hugeicons-react";
import { useFormQuestionMutations } from "./intake-form-context";
import { noop, px, sleep } from "@/lib/helpers";
import { useFormik } from "formik";
import { IntakeFormQuestion, QuestionType } from "@/src/interfaces/intake-form";
import { QUESTION_TYPES_WITH_OPTIONS } from "./form-constants";
import { cn } from "@/lib/utils";
import SelectQuestionModal from "./select-question-modal";
import { useModalsBuilder } from "@/lib/modals-builder";
import { Input } from "@/components/inputs";
import Chip from "@/components/ui/chip";
import { useState } from "react";
import QuestionType<PERSON>xample from "./question-type-example";
import DatePicker from "@/components/ui/calendar";
import { motion } from "framer-motion";

type QuestionProps = {
  question: IntakeFormQuestion;
  index: number
}

function getValidationForType(type: QuestionType) {
  switch (type) {
    case 'number':
    case 'date':
      return [
        { label: 'Min', name: 'min' },
        { label: 'Max', name: 'max' },
      ] as const;
    case 'text':
    case 'email':
    case 'url':
    case 'phone':
    case 'textarea':
      return [
        { label: 'Min. Length', name: 'minLength' },
        { label: 'Max. Length', name: 'maxLength' },
      ] as const;
    default:
      return [];
  }
}

const Question: React.FC<QuestionProps> = (props) => {
  const { question } = props
  const [isOpen, setIsOpen] = useState(props.index === 0);
  const questionFormik = useFormik({
    enableReinitialize: true,
    initialValues: {
      ...question,
      // non optionless questions types should be undefined
      options: QUESTION_TYPES_WITH_OPTIONS.includes(question.type) ? (question.options || []) : undefined
    },
    onSubmit: noop
  })
  const { modals, modalFunctions } = useModalsBuilder({
    updateType: {
      open: false,
      data: null as Nullable<{ order: number }>
    }
  })
  const [newOption, setNewOption] = useState('')
  const { deleteQuestion, updateQuestion } = useFormQuestionMutations()
  return (
    <div className='w-full flex items-start gap-x-3 md:gap-x-6 '>
      <div className="flex flex-col gap-y-3">
        <Button variant='icon' className='bg-transparent p-0 text-paragraph'>
          <DragDropVerticalIcon size={25} strokeWidth={2.7} />
        </Button>
        <Button
          onTap={() => setIsOpen(!isOpen)}
          variant='icon' className='bg-transparent p-0 text-paragraph'>
          <ArrowRight01Icon size={22} className={cn("transition-transform duration-300 -rotate-90 ease-[ease] ",
            { "rotate-90 ": isOpen }
          )} />
        </Button>
      </div>
      <motion.div
        initial={{ height: px(68) }}
        animate={isOpen ? { height: 'auto' } : { height: px(68) }}
        className='flex-grow flex flex-col overflow-hidden gap-y-3'>
        <div className='flex flex-col gap-y-1'>
          <label
            htmlFor={'name'}
            className={`font-medium w-full flex text-sm cursor-pointer text-subtext pl- `}
          >
            Question
            <span
              className={cn("text-red-800 block")}
            >
              *
            </span>
          </label>
          <input
            autoComplete="off"
            name="question"
            className="py-0.5 pb-2 font-medium border-b-2 border-[#f2f2f2]/60"
            value={questionFormik.values.question}
            onChange={questionFormik.handleChange}
          />
        </div>

        <div className='flex flex-col gap-y-1'>
          <label
            htmlFor={'placeholder'}
            className={`font-medium w-full flex text-sm cursor-pointer text-subtext pl- `}
          >
            Placeholder
            <span className="inline-block ml-2 px-1.5 py-0.5 text-sky-600 bg-sky-50 rounded-md text-xs">
              optional
            </span>
          </label>
          <input
            name="placeholder"
            autoComplete="off"
            className="py-0.5 pb-2 font-medium border-b-2 border-[#f2f2f2]/60"
            value={questionFormik.values.placeholder}
            onChange={questionFormik.handleChange}
          />
        </div>
        <QuestionTypeExample
          formControl={questionFormik.values}
        />
        <Accordion
          type="single"
          defaultValue="settings"
          collapsible
          className="w-full space-y-3"
        >

          {
            QUESTION_TYPES_WITH_OPTIONS.includes(questionFormik.values.type) &&
            <AccordionItem value={`settings`}>
              <AccordionTrigger noUnderline className="hover:underline pt-3 pb-1 " >
                <div className="flex flex-grow items-center gap-x-3">
                  <h2 className="text-lg capitalize whitespace-nowrap line-clamp-1 font-semibold mb-2">
                    Options
                  </h2>
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-3.5" >
                <div className="flex flex-col gap-y-3">
                  <div className="flex items-center gap-2 bg-white rounded-3xl flex-wrap">
                    {questionFormik.values.options?.map((option, index) => (
                      <Chip
                        key={index}
                        label={option}
                        onDelete={() =>
                          questionFormik.setFieldValue('options',
                            questionFormik.values?.options?.filter(opt => opt !== option) ?? []
                          )
                        }
                        className="line-clamp-1"
                      />
                    ))}
                  </div>
                  <div
                    className={
                      "bg-[#DFE1DB]/30 shadow-sm pl-2 pr-1.5 py-1.5 w-full max-w-60 flex items-center gap-x-2 rounded-full text-sm font-medium"
                    }
                  >
                    <input
                      placeholder="Add Option"
                      className="!rounded-full w-full text-sm text-paragraph placeholder:text-paragraph/40 bg-transparent outline-none pl-1"
                      value={newOption}
                      onChange={(e) => setNewOption(e.target.value)}
                    />
                    <Button
                      type="button"
                      className="rounded-full !p-1 bg-[#DFE1DB]/60 "
                      variant="icon"
                      onTap={() => {
                        if (newOption.length === 0) return
                        questionFormik.setFieldValue('options',
                          [...(questionFormik.values?.options ?? []), newOption]
                        )
                        setNewOption("");
                      }}
                    >
                      <PlusSignIcon size={16} className="text-gray-500 " />
                    </Button>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          }
          <AccordionItem value={`settings`}>
            <AccordionTrigger noUnderline className="hover:underline pt-3 pb-1 " >
              <div className="flex flex-grow items-center gap-x-3">
                <h2 className="text-lg capitalize whitespace-nowrap line-clamp-1 font-semibold mb-2">
                  Settings
                </h2>
              </div>
            </AccordionTrigger>
            <AccordionContent className="space-y-3.5" >
              {/* all options here */}
              <div className="w-full flex items-start justify-between ">
                <Typography className="text-paragraph">
                  Required
                </Typography>
                <Toggle
                  checked={questionFormik.values.required}
                  onChange={(checked) => {
                    questionFormik.setFieldValue('required', checked)
                  }}
                />
              </div>
              <div className="w-full grid grid-cols-2 gap-2 items-start justify-between ">
                {
                  questionFormik.values && getValidationForType(questionFormik.values.type).map(({ label, name }, index) => {
                    const value = questionFormik.values?.validation?.[name]
                    return (
                      questionFormik.values.type === 'date'
                        ? (
                          <DatePicker.FormControl
                            label={label}
                            onChange={(date) => {
                              questionFormik.setFieldValue('validation', {
                                ...questionFormik.values.validation,
                                [name]: date.getTime(),
                              })
                            }}
                            name={name}
                            key={index}
                            value={value ? new Date(value) : undefined}
                          />
                        ) : (
                          <Input.Numeric
                            key={index}
                            label={label}
                            name={name}
                            defaultValue={questionFormik.values?.validation?.[name]}
                            onChange={({ currentTarget: { name, value } }) => {
                              questionFormik.setFieldValue('validation', {
                                ...questionFormik.values.validation,
                                [name]: value,
                              })
                            }}
                          />
                        )
                    )
                  })
                }
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        <div className='flex items-center gap-x-5 ml-auto mt-3'>
          <Button variant='dormant' onTap={() => modalFunctions.openModal('updateType', {
            data: { order: question.order }
          }
          )}
            className="text-sm whitespace-nowrap flex items-center gap-x-2 p-0 bg-transparent text-primary-400  "
          >
            <ArrowReloadHorizontalIcon size={18} />
          </Button>
          <Button variant='ghost' onTap={() => deleteQuestion(question.PP_Id)}
            className="text-sm whitespace-nowrap flex items-center gap-x-2 p-0 bg-transparent text-primary-400  "
          >
            <Delete02Icon size={18} />
          </Button>
          <Button variant='dormant' onTap={() => {
            updateQuestion(
              question.PP_Id,
              questionFormik.values
            )
          }}
            className="text-sm whitespace-nowrap flex items-center gap-x-2 "
          >Save</Button>
        </div>
      </motion.div>
      {modals.updateType.data && (
        <SelectQuestionModal
          open={modals.updateType.open}
          onClose={modalFunctions.returnClose('updateType')}
          onSelect={async (data) => {
            // do checks here to change options in questionsFormik
            if (QUESTION_TYPES_WITH_OPTIONS.includes(data.type))
              questionFormik.setFieldValue('options',
                questionFormik.values.options ?? (question.options || [])
              )
            questionFormik.setFieldValue('type', data.type)
            await sleep(70)
            modalFunctions.closeModal('updateType')
          }}
          order={modals.updateType.data.order!}
        />)
      }
    </div>
  )
}

export default Question
