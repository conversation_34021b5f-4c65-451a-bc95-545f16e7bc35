import { Check } from "lucide-react";
import { usePayment } from "./payment-context";
import { motion } from "framer-motion";
import { Typography } from "@/components/typography";
import { convertPriceToDecimalFormat } from "@/lib/helpers";

const AnimatedTypography = motion.create(Typography);

/**
 * @dev hardcoded from boss 20% discount is given to user for yearly plan
 */
const SubscriptionPlanSection = () => {
  const { plan } = usePayment();
  const YEARLY_DISCOUNT = 20;

  return (
    <div className="w-full max-w-2xl md:m-auto md:p-6">
      <div className="overflow-hidden">
        {/* Header */}
        <motion.div
          initial={"initial"}
          animate={"shown"}
          className="border-b border-gray-200 pt-4 pb-6 md:p-6"
        >
          <AnimatedTypography
            variant={"h2"}
            className="overflow-hidden text-start font-bold mb-4 "
          >
            <motion.span
              className="inline-block "
              variants={{
                initial: { y: -100 },
                shown: { y: 0 },
              }}
              transition={{
                type: "keyframes",
                delay: 0.4,
              }}
            >
              {plan?.name}
            </motion.span>
          </AnimatedTypography>
          <motion.p className="text-md overflow-hidden mt-2 text-start text-gray-600">
            <motion.span
              className="inline-block"
              variants={{
                initial: { y: 100 },
                shown: { y: 0 },
              }}
              transition={{
                type: "keyframes",
                delay: 0.4,
              }}
            >
              {plan?.description}
            </motion.span>
          </motion.p>
        </motion.div>

        {/* Pricing Section */}
        <div className="space-y-6 px-2 pt-4 pb-6 md:p-6">
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900">
              Billing Details
            </h2>

            {/* Regular Price */}
            <div className="flex items-baseline justify-between">
              <span className="font-semibold text-gray-900">Price</span>
              <div className="text-right">
                <span className="text-2xl font-Aeonik_Fono font-bold text-gray-900">
                  $
                  {plan?.recurring === "year"
                    ? convertPriceToDecimalFormat(
                        (plan?.price || 0) +
                          (plan?.price || 0) * (YEARLY_DISCOUNT / 100)
                      )
                    : convertPriceToDecimalFormat(plan?.price || 0)}
                </span>
                <span className="text-gray-500 ml-1">/{plan?.recurring}</span>
              </div>
            </div>

            {/* show user that they are discounted on yearly plan */}
            {plan?.recurring === "year" && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center justify-between">
                <span className="text-green-700">Yearly Plan Discount</span>
                <span className="font-semibold text-green-700">
                  -{YEARLY_DISCOUNT}%
                </span>
              </div>
            )}

            {/* Total */}
            <div className="pt-4 border-t border-gray-200">
              <div className="flex items-baseline justify-between">
                <span className="font-semibold text-gray-900">Total</span>
                <div className="text-right">
                  <span className="text-2xl font-bold font-Aeonik_Fono text-primary">
                    ${convertPriceToDecimalFormat(plan!.price)}
                  </span>
                  <span className="text-gray-500 ml-1">/{plan?.recurring}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-900">
              Included Features
            </h2>
            <ul className="space-y-3">
              {plan?.features.map((feature, index) => (
                <li key={index} className="flex items-center gap-2">
                  <Check className="h-5 w-5 text-green-500" />
                  <span className="text-gray-700">{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscriptionPlanSection;
