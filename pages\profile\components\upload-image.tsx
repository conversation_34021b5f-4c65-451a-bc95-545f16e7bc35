import useAuthStore from "@/pages/auth/components/auth-store";
import { useToast } from "@/src/contexts/hooks/toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import React, { useEffect, useRef, useState } from "react";
import { UploadUserProfileImage as UploadUserProfileImageService } from "@/src/services/auth.service";
import Avatar from "@/components/ui/avatar";
import { motion } from "framer-motion";
import { Pencil } from "@gravity-ui/icons";

const UploadUserProfileImage = () => {
  const {
    initialState: { user, ...stateRest },
    setUser,
  } = useAuthStore();
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  useEffect(() => {
    setImageUrl(user?.user_photo || null);
  }, []);
  const showToast = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const fileUploadMutation = useMutation({
    mutationFn: UploadUserProfileImageService,
  });
  const queryClient = useQueryClient();
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // set new image url quick
      setImageUrl(URL.createObjectURL(file as Blob));
      let formData = new FormData();
      formData.append("image", file);
      fileUploadMutation.mutateAsync(formData, {
        onSuccess() {
          setUser({
            ...stateRest,
            user: {
              ...user,
              user_photo: URL.createObjectURL(file as Blob),
            },
          } as any);
          setImageUrl(URL.createObjectURL(file as Blob));
          queryClient.invalidateQueries({ queryKey: ["user-photo"] });
          showToast("success", "Image uploaded successfully");
        },
        onError() {
          // revert image url to old one
          setImageUrl(user?.user_photo || null);
          showToast("info", "Image upload reverted");
        },
      });
    }
  };
  return (
    <div className="relative select-none">
      <motion.div
        whileTap={{ scale: 0.95 }}
        onTap={() => fileInputRef.current?.click()}
        className="w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center text-white font-medium cursor-pointer  "
      >
        {
          <Avatar
            src={imageUrl as string | undefined}
            alt="profile image"
            className="!w-full !h-full object-cover"
          />
        }
      </motion.div>
      <motion.button
        whileTap={{ scale: 0.9 }}
        onTap={() => fileInputRef.current?.click()}
        className="absolute bottom-1 right-1 select-none bg-white rounded-full p-1.5 shadow-sm"
      >
        <Pencil width={16} height={16} />
      </motion.button>
      <input
        ref={fileInputRef}
        name="image"
        type="file"
        className="sr-only"
        accept="image/jpeg, image/png"
        onChange={handleImageUpload}
      />
    </div>
  );
};

export default UploadUserProfileImage;
