import ErrorScreen from "@/components/ui/error-screen";
import { GetOauthRedirect } from "@/src/services/settings.service";
import { useQuery } from "@tanstack/react-query";
import Logo from "@/assets/images/Puzzle-Pc-White-bg.png";
import { useLocation, useNavigate } from "react-router-dom";

const OAuthCallback = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const params = new URLSearchParams(location.search);
  const code = params.get("code");
  const state = params.get("state");

  const { isError, refetch } = useQuery({
    queryKey: ['oauth-callback'],
    queryFn: async () => {
      if (code) {
        try {
          const response = await GetOauthRedirect({ code, state })
          if ([200, 201].includes(response?.status)) {
            navigate('/profile/accounts')
            return { connected: true, success: true }
          } else {
            return { connected: false, success: false }
          }
        } catch (error) {
          console.error("Error handling OAuth callback:", error)
          return { connected: false, success: false }
        }
      }
      return { connected: false, success: false }
    },
    enabled: Boolean(code)
  })
  if (isError)
    return (
      <section className="w-full min-h-screen overflow-y-auto flex flex-col justify-center   ">
        <div className="w-full flex justify-center ">
          <img src={Logo} className="w-full max-h-[240px] object-contain  " />
        </div>
        <ErrorScreen onRetry={refetch} title="Something went wrong" message="Click the button below to try again" />
      </section>
    )
  return (
    <div>Redirecting...</div>
  )
};

export default OAuthCallback;
