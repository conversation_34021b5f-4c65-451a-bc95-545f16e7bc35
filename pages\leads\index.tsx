import { Button } from "@/components/buttons";
import Popover from "@/components/ui/popover";
import { pick } from "@/lib/helpers";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ShoppingBasket } from "lucide-react";
import { useEffect, useMemo, useRef, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { useToast } from "~/contexts/hooks/toast";
import { TLead } from "~/interfaces/leads";
import {
  GetBusinessLeads,
  GetPendingCart,
  LeadsCart as TLeadsCart,
  UpsertLeadsCart,
} from "~/services/leads.service";
import { FilterIcon } from "./components/badges";
import { default as LeadsCartPopoverContent } from "./components/leads-cart";
import LeadsFilters from "./components/leads-filters";
import LeadsList from "./components/leads-list";
import LeadsSearch from "./components/leads-search";

interface LeadsParams {
  industry: string;
  locations: string[];
}

const LeadsPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [industry, locations, page] = [
    searchParams.get("industry") || "",
    searchParams.get("locations")?.split(",").filter(Boolean) || [],
    searchParams.get("page") || 1,
  ];
  const [industryAndLocation, setIndustryAndLocation] = useState<LeadsParams>({
    industry,
    locations,
  });
  const showToast = useToast();
  const isValidPage = useMemo(
    () =>
      Boolean(industryAndLocation.industry) &&
      industryAndLocation.locations.length > 0,
    [industryAndLocation]
  );
  const {
    data: leadsData,
    error,
    isLoading,
    isFetching,
    refetch: refetchLeads,
  } = useQuery({
    queryFn: async () =>
      (
        await GetBusinessLeads({
          location: industryAndLocation.locations,
          keyword: industryAndLocation.industry,
          page: Number(page),
        })
      ).data,
    queryKey: [
      "leads",
      industryAndLocation.industry,
      industryAndLocation.locations,
      page,
    ],
    refetchOnWindowFocus: false,
    enabled: isValidPage,
  });
  const [allLeads, setAllLeads] = useState<TLead[]>([]);
  const { data: pendingCart } = useQuery({
    queryFn: GetPendingCart,
    queryKey: ["pending-cart"],
    refetchOnWindowFocus: false,
    refetchOnReconnect: "always",
    enabled: isValidPage,
  });
  const [leadsCart, setLeadsCart] = useState<TLeadsCart["leads"]>(
    pendingCart?.data?.leads || []
  );
  const upsertCartMutation = useMutation({
    mutationFn: async () =>
      UpsertLeadsCart({
        leadIds: leadsCart.map((val) => val._id),
        cartId: pendingCart?.data?._id || null,
      }),
    networkMode: "always",
  });
  const handleAddToCart = async () => {
    await upsertCartMutation.mutateAsync();
  };
  const handleRemoveFromCart = async () => {
    await upsertCartMutation.mutateAsync();
  };
  const [modals, setModals] = useState({
    filter: false,
  });
  const cartIconRef = useRef<HTMLDivElement>(null);
  const openCartPopover = () => {
    cartIconRef.current?.click();
  };
  useEffect(() => {
    if (leadsData?.data) {
      setAllLeads(leadsData.data);
    }
  }, [leadsData]);
  // set search params
  useEffect(() => {
    const { industry, locations } = industryAndLocation;
    setSearchParams({
      industry,
      locations: locations.join(","),
      page: "1",
    });
  }, [industryAndLocation]);
  // refetch on search params change;
  useEffect(() => {
    if (isValidPage) refetchLeads();
  }, [searchParams]);

  return (
    <section className="w-full max-w-7xl mx-auto px-2 pb-6 min-h-screen space-y-4 relative md:px-4 ">
      <section className="w-full flex items-center gap-x-1">
        <LeadsSearch
          industryAndLocation={industryAndLocation}
          setIndustryAndLocation={setIndustryAndLocation}
        />
        <Popover transformOrigin="top-right">
          <Popover.Trigger triggerRef={cartIconRef}>
            <Button
              variant="icon"
              aria-roledescription="Cart Icon"
              className={`ml-2 cursor-pointer bg-stone-200 ${
                !isValidPage && "hidden"
              } `}
            >
              <ShoppingBasket className="stroke-paragraph " />
            </Button>
          </Popover.Trigger>
          <LeadsCartPopoverContent
            onRemoveLead={(leadId) => {
              setLeadsCart((prev) =>
                prev.filter((lead) => lead._id !== leadId)
              );
              handleAddToCart();
            }}
            onLeadsPurchased={() => {
              setAllLeads((prev) => {
                return prev.filter(
                  (lead) =>
                    !leadsCart.some((cartLead) => cartLead._id === lead._id)
                );
              });
              setLeadsCart([]);
            }}
            leadsCart={leadsCart}
            cartId={pendingCart?.data?._id || ""}
          />
        </Popover>
        <Button
          variant="icon"
          onTap={() => {
            setModals({
              ...modals,
              filter: true,
            });
          }}
          className={`ml-2 px-3 py-3.5 cursor-pointer bg-stone-200 ${
            !isValidPage && "hidden"
          } `}
        >
          <FilterIcon />
        </Button>
      </section>
      <section className="flex justify-between gap-x-4 ">
        {isValidPage && (
          <>
            <LeadsList
              paginationProps={
                !leadsData
                  ? { totalBusinesses: 0, totalPages: 0, itemsPerPage: 0 }
                  : {
                      ...pick(leadsData!, "totalBusinesses", "totalPages"),
                      itemsPerPage: leadsData!.limit,
                    }
              }
              onRetry={() => refetchLeads()}
              leadsData={{
                leads: allLeads || [],
                isLoading: isLoading || isFetching,
                error,
              }}
              leadsCart={leadsCart}
              noOfLocations={industryAndLocation.locations.length}
              onSelectLead={(lead) => {
                setLeadsCart((prev) => [...prev, lead]);
                handleAddToCart();
                showToast("info", "Lead added to cart", {
                  description: `You have selected ${lead.name}`,
                  action: {
                    title: "View",
                    onClick: () => {
                      openCartPopover();
                      return true;
                    },
                  },
                });
              }}
              onDeSelectLead={(lead) => {
                setLeadsCart((p) => p.filter((l) => l._id !== lead._id));
                handleRemoveFromCart();
                showToast("info", "Lead removed to cart", {
                  description: `You have removed ${lead.name}`,
                });
              }}
              onSelectAllLeads={(leads) => {
                setLeadsCart(leads);
                openCartPopover();
              }}
            />
            <LeadsFilters
              onClose={() => {
                setModals({
                  ...modals,
                  filter: false,
                });
              }}
              isOpen={modals.filter}
              locations={locations}
              setLocations={(locs) => {
                setIndustryAndLocation((prev) => ({
                  ...prev,
                  locations: locs,
                }));
              }}
            />
          </>
        )}
      </section>
    </section>
  );
};

export default LeadsPage;
