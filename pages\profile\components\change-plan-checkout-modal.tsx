import { But<PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import Modal from "@/components/ui/modal";
import { pick } from "@/lib/helpers";
import { X } from "lucide-react";
import React from "react";

type Props = {
  open: boolean;
  onClose: VoidFunction;
  stripeProps: {
    clientSecret: string;
  };
};

const ChangePlanCheckoutModal: React.FC<Props> = (props) => {
  return (
    <Modal {...pick(props, "onClose", "open")}>
      <Modal.Body
        className={`min-h-fit min-w-80 h-fit w-fit flex flex-col gap-y-5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Complete Payment for plan change
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="w-full h-full flex items-center justify-center">
          {/* {clientSecret ? (
          <Elements stripe={stripe} options={{ clientSecret }}>
            <StripeCheckoutForm />
          </Elements>
        ) : ( */}
          <CircularLoader />
          {/* )} */}
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default ChangePlanCheckoutModal;
