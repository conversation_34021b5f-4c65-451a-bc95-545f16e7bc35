import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { paymentSvgIconMap, processPaymentMethods } from "@/lib/helpers";
import {
	DetachPaymentMethod,
	GetPaymentMethods,
} from "@/src/services/stripe.service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { formatDate } from "date-fns";
import { CreditCard, Trash2 } from "lucide-react";
import { useMemo, useState } from "react";
import AddCardModal from "./add-card-modal";
import CircularLoader from "@/components/ui/circular-loader";
import { useModalsBuilder } from "@/lib/modals-builder";
import { Card } from "@/components/ui/card";
import Dialog from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import PendingOverlay from "@/components/ui/pending-overlay";

const PaymentMethods = () => {
	const { data: paymentMethods = [], isFetching } = useQuery({
		queryKey: ["payment-methods"],
		queryFn: async () => {
			const res = await GetPaymentMethods();
			return res.data.paymentMethods;
		},
		refetchOnWindowFocus: false,
	});
	const detachCardMutation = useMutation({
		mutationFn: DetachPaymentMethod,
	});
	const queryClient = useQueryClient();
	const filteredPaymentMethods = useMemo(
		() => processPaymentMethods(paymentMethods),
		[paymentMethods],
	);
	const { modals, modalFunctions } = useModalsBuilder({
		addCard: {
			open: false,
		},
		detachCard: {
			open: false,
			paymentMethod: null as {
				id: string;
				last4digits: string;
			} | null,
		},
	});
	const [clientSecret, setClientSecret] = useState("");
	return (
		<Card className="space-y-3 px-4 py-4">
			<PendingOverlay isPending={detachCardMutation.isPending} />
			<Typography className="text-paragraph text-xl font-bold mb-3 ">
				Payment methods
			</Typography>
			<Typography className="text-paragraph/70 text-sm font-medium ">
				Payments for digital profile, scheduling and client systems are made
				using the default card.
			</Typography>
			{filteredPaymentMethods.length === 0 ? (
				<div className="border border-gray-200 py-4 rounded-2xl px-5 flex flex-col gap-y-2 items-center">
					<CreditCard
						strokeWidth={1.4}
						className="w-12 h-12 mx-auto text-dark-gray "
					/>
					<Typography className="text-paragraph/70 text-sm font-medium ">
						No payment methods added yet
					</Typography>
				</div>
			) : isFetching ? (
				<div className="w-full py-2">
					<CircularLoader />
				</div>
			) : (
				filteredPaymentMethods?.map((method) => (
					<div
						key={method._id}
						className={cn(
							"flex items-center justify-between p-4 border rounded-xl hover:border-blue-500 transition-colors relative ",
							{
								"pt-6 pb-3": method.type === "default",
							},
						)}
					>
						<div className="flex items-center gap-4">
							<img
								src={
									Array.from(paymentSvgIconMap.entries()).find(
										([cardNames, _]) => cardNames.includes(method.provider!),
									)?.[1]
								}
								className="w-14"
								alt=""
							/>
							<div>
								<p className="font-medium">•••••••••••• {method.last4Digits}</p>
								<p className="text-sm text-gray-600">
									Expires{" "}
									{formatDate(
										new Date(
											Number(method.expiryYear!),
											Number(method.expiryMonth),
										),
										"LL/yy",
									)}
								</p>
							</div>
						</div>
						{method.type === "default" && (
							<div className="absolute text-xs bg-primary-500 px-2 py-1 rounded-lg text-white top-2 right-2">
								default
							</div>
						)}
						<Button
							variant="icon"
							onTap={() =>
								modalFunctions.openModal("detachCard", {
									paymentMethod: {
										id: method._id,
										last4digits: method.last4Digits as string,
									},
								})
							}
							className="p-2 bg-transparent text-gray-500 hover:text-red-500 transition-colors"
						>
							<Trash2 className="w-5 h-5" />
						</Button>
					</div>
				))
			)}
			<div className="flex justify-end mt-4">
				<Button
					onTap={() => {
						modalFunctions.openModal("addCard", {});
					}}
					className="flex items-center gap-2"
				>
					Add Card
				</Button>
			</div>
			{modals.addCard.open && (
				<AddCardModal
					hasPaymentMethods={filteredPaymentMethods.length === 0}
					clientSecret={clientSecret}
					setClientSecret={setClientSecret}
					open={modals.addCard.open}
					onClose={() => {
						modalFunctions.closeModal("addCard");
					}}
					onSuccess={() => {
						queryClient.invalidateQueries({ queryKey: ["payment-methods"] });
					}}
				/>
			)}
			{modals.detachCard.paymentMethod && (
				<Dialog
					open={modals.detachCard.open}
					onClose={() => modalFunctions.closeModal("detachCard")}
					action={{
						title: "Delete",
						onConfirm() {
							detachCardMutation.mutateAsync(
								modals.detachCard.paymentMethod?.id!,
								{
									onSuccess() {
										queryClient.invalidateQueries({
											queryKey: ["payment-methods"],
										});
									},
								},
							);
						},
					}}
					description={`Are you sure you want to delete ****${modals.detachCard.paymentMethod.last4digits}`}
					title="Detach Payment Method?"
				/>
			)}
		</Card>
	);
};

export default PaymentMethods;
