import React from "react";
import { useNavigate } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { <PERSON><PERSON>, SaveButton } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import PendingOverlay from "@/components/ui/pending-overlay";
import { ArrowLeft02Icon } from "hugeicons-react";
import { useTeam } from "./hooks/use-team";
import { CreateTeamPayload } from "@/src/interfaces/team";
import { useToast } from "~/contexts/hooks/toast";
import { noop } from "@/lib/helpers";

const initialValues: CreateTeamPayload = {
  name: "",
  description: "",
};

const CreateTeam: React.FC = () => {
  const navigate = useNavigate();
  const showToast = useToast();
  const { createTeamMutation } = useTeam();

  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: Yup.object({
      name: Yup.string()
        .required("Team name is required")
        .min(2, "Team name must be at least 2 characters")
        .max(100, "Team name must be less than 100 characters"),
      description: Yup.string()
        .required("Team description is required")
        .max(500, "Description must be less than 500 characters"),
    }),
    validateOnMount: true,
    onSubmit: noop,
  });

  const createTeamHandler = async () => {
    formik.validateForm().then((val) => {
      const errors = Object.values(val);
      if (errors.length === 0) {
        const { values } = formik;
        createTeamMutation.mutateAsync(values).then(() => {
          formik.resetForm();
          navigate("/teams");
        });
      } else {
        showToast("error", "Invalid Field", {
          description: errors[0],
        });
      }
    });
  };

  return (
    <section className="w-full h-full max-w-2xl mx-auto flex flex-col gap-y-10 pb-32">
      <div className="w-full flex justify-between items-center">
        <div className="flex items-center gap-x-4">
          <Button
            variant="icon"
            onTap={() => navigate("/teams")}
            className="p-1.5"
          >
            <ArrowLeft02Icon width={20} height={20} />
          </Button>
          <Typography
            variant={"h1"}
            className="font-bold font-Bricolage_Grotesque"
          >
            Create Team
          </Typography>
        </div>
      </div>

      <form
        onSubmit={formik.handleSubmit}
        className="w-full flex flex-col gap-y-6"
      >
        {/* Team Name */}
        <Input.Text
          label="Team Name"
          name="name"
          value={formik.values.name}
          onChange={formik.handleChange}
          required
        />

        {/* Description */}
        <Input.TextArea
          label="Description"
          name="description"
          value={formik.values.description}
          onChange={formik.handleChange}
          required
        />

        {/* Submit Button */}
        <SaveButton
          title="Create Team"
          loading={createTeamMutation.isPending}
          disabled={createTeamMutation.isPending || !formik.isValid}
          onTap={createTeamHandler}
        />
      </form>

      <PendingOverlay isPending={createTeamMutation.isPending} />
    </section>
  );
};

export default CreateTeam;
