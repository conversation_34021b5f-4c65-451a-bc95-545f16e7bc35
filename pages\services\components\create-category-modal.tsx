import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";
import PendingOverlay from "@/components/ui/pending-overlay";
import { capitalizeFirstWord, noop } from "@/lib/helpers";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useFormik } from "formik";
import { X } from "lucide-react";
import * as Yup from "yup";
import { useToast } from "~/contexts/hooks/toast";
import { CreateCategory } from "~/services/services.service";
type Props = {
  open: boolean;
  onClose: VoidFunction;
  /**
   * @dev this is used to show a dynamic message
   * */
  isCategoryCreatedOnServicesPage?: boolean;
};
const CreateCategoryModal = (props: Props) => {
  const queryClient = useQueryClient();
  const showToast = useToast();
  const createCategoryMutation = useMutation({
    mutationFn: CreateCategory,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["categories"] });
      props.onClose();
      showToast(
        "info",
        `${data.data.category.name} category created successfully`,
        {
          description: props.isCategoryCreatedOnServicesPage
            ? "You can select this category in the service creation page"
            : "You can select it in the category dropdown",
        },
      );
    },
    onError: (error: ApiError) => {
      showToast("error", error?.response?.data?.error || error?.message);
    },
  });
  const formik = useFormik({
    initialValues: {
      categoryName: "",
    },
    validationSchema: Yup.object({
      categoryName: Yup.string().required("Category name is required"),
    }),
    onSubmit: (values) => {
      const payload = { name: values?.categoryName };
      createCategoryMutation.mutateAsync(payload); // This is where you pass the payload
      formik.resetForm();
    },
  });

  return (
    <Modal open={props.open} onClose={props.onClose}>
      <Modal.Body
        className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Create Category
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="w-full grid my-3">
          <Input.Text
            name="categoryName"
            label="Category Name"
            required
            value={formik.values.categoryName}
            onChange={({ target: { name, value } }) => {
              formik.setFieldValue(name, capitalizeFirstWord(value));
            }}
          />
        </div>
        <div className="w-full ">
          <Button
            className="w-full"
            disabled={createCategoryMutation.isPending}
            onTap={() => formik.handleSubmit()}
          >
            Create
          </Button>
        </div>
        <PendingOverlay isPending={createCategoryMutation.isPending} />
      </Modal.Body>
    </Modal>
  );
};

type EditCModalProps = Pick<Props, 'open' | 'onClose'> & {
  onSave: (name: string) => void
  name: string
}

export const EditCategoryModal = (props: EditCModalProps) => {

  const formik = useFormik({
    enableReinitialize: true, 
    initialValues: {
      categoryName: props.name || '',
    },
    validationSchema: Yup.object({
      categoryName: Yup.string().required("Category name is required"),
    }),
    onSubmit: noop
  })

  return (
    <Modal open={props.open} onClose={props.onClose} >
      <Modal.Body
        className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Edit Category
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="w-full grid my-3">
          <Input.Text
            name="categoryName"
            label="Category Name"
            required
            value={formik.values.categoryName}
            onChange={({ target: { name, value } }) => {
              formik.setFieldValue(name, capitalizeFirstWord(value));
            }}
          />
        </div>
        <div className="w-full ">
          <Button
            className="w-full"
            onTap={() => props.onSave(formik.values.categoryName)}
          >
            Save Changes
          </Button>
        </div>
      </Modal.Body>
    </Modal >

  )
}

export default CreateCategoryModal;
