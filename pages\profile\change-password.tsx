import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import CircularLoader from "@/components/ui/circular-loader";
import { noop } from "@/lib/helpers";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { object, ref, string } from "yup";
import { useToast } from "~/contexts/hooks/toast";
import { UpdateUserPassword } from "~/services/auth.service";

const fields = [
	{
		label: "Current Password",
		name: "currentPassword",
		type: "password",
	},
	{
		label: "New Password",
		name: "newPassword",
		type: "password",
	},
	{
		label: "Confirm New Password",
		name: "confirmPassword",
		type: "password",
	},
] as const;

const ChangePassword = () => {
	const showToast = useToast();
	const passwordValidationSchema = string()
		.required("Password is required")
		.matches(
			/[A-Za-z\d@$!%*?&]{8,}/,
			"Must Match 8+ characters from this consisting of lowercase, uppercase and special characters",
		)
		.matches(/(?=.*\d)/, "Must contain at least one number")
		.matches(/^(?=.*[A-Z])/, "Must contain at least one uppercase letter")
		.matches(/^(?=.*[a-z])/, "Must contain at least one lowercase letter")
		.matches(/(?=.*[@$!%*?&])/, "Must contain at least one special character");
	const changePasswordValidationSchema = object({
		currentPassword: string().required("Current Password is required"),
		newPassword: passwordValidationSchema,
		confirmPassword: passwordValidationSchema.oneOf(
			[ref("newPassword")],
			"Passwords must match",
		),
	});
	const changePasswordMutation = useMutation({
		mutationFn: UpdateUserPassword,
		onSuccess: () => showToast("success", "Password changed successfully"),
		onError: (error: ApiError) => {
			showToast("error", "Failed to change password", {
				description: error.response?.data.error || "Unknown error occured",
			});
		},
	});
	const handleChangePassword = () => {
		formik.validateForm().then((val) => {
			const errors = Object.values(val);
			if (errors.length === 0) {
				changePasswordMutation.mutateAsync({
					...formik.values,
				});
			} else
				errors.forEach((error, i) =>
					setTimeout(() => {
						showToast("error", "Invalid Password", {
							description: error,
						});
					}, i * 500),
				);
		});
	};
	const formik = useFormik({
		validationSchema: changePasswordValidationSchema,
		initialValues: {
			currentPassword: "",
			newPassword: "",
			confirmPassword: "",
		},
		onSubmit: noop,
	});
	return (
		<section className="">
			<form className="w-full h-fit">
				<div className="w-full grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
					{fields.map((field, index) => {
						return (
							<Input.Password
								key={index}
								label={field.label}
								name={field.name}
								validationSchema={
									["confirmPassword", "newPassword"].includes(field.name)
										? passwordValidationSchema
										: undefined
								}
								onChange={({ currentTarget: { name, value } }) => {
									formik.setFieldValue(name, value);
								}}
							/>
						);
					})}
				</div>
				<div className="mt-6 flex justify-end">
					<Button
						className="px-7"
						disabled={changePasswordMutation.isPending}
						type="button"
						onTap={handleChangePassword}
					>
						{changePasswordMutation.isPending ? (
							<CircularLoader color="white" />
						) : (
							"Save"
						)}
					</Button>
				</div>
			</form>
		</section>
	);
};

export default ChangePassword;
