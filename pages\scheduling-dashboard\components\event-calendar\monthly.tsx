import { Typography } from "@/components/typography";
import { cn } from "@/lib/utils";
import { CalendarEvent } from "~/interfaces/booking";
import { formatDate } from "date-fns";
import { motion } from "framer-motion";
import { memo, useEffect } from "react";
import MarkerTooltip from "./marker-tooltip";
import { type Months } from "../../use-calendar";
import { getSuffix } from "@/lib/helpers";
import EventGroup from "./event-group";

const allColors = [
  "#87CEEB", // Sky Blue
  "#ADD8E6", // Light Blue
  "#B0E0E6", // Powder Blue
  "#FFB347",
  "#FFC0CB",
];

export const monthWeeks = [
  [1, 7],
  [8, 14],
  [15, 21],
  [22, 27],
  [28, 31],
];

const weekBlockHeight = 80;

function calculateTopPosition(startDate: string) {
  // Parse the event start time
  const eventDate = new Date(startDate).getDate();

  const eventDayIndex = monthWeeks.findIndex((week) => {
    return eventDate >= week[0] && eventDate <= week[1];
  });
  // Calculate position: each week block is 80px (h-20)
  // Add proportional minutes (80px per hour, so divide minutes by 60 and multiply by 80)
  const topPosition = eventDayIndex * weekBlockHeight;
  return topPosition;
}

function pickRandomColor() {
  const randomIndex = Math.floor(Math.random() * allColors.length);
  return allColors[randomIndex];
}

/**
 * Checks if a given date falls within a month range
 * @param date The date to check
 * @param month A tuple [startDate, endDate] representing first day and last day of the month
 * @returns Boolean indicating whether the date is within the month range
 */
function isDayWithinMonth(date: Date, month: [Date, Date]): boolean {
  // Create copies to avoid modifying original dates
  const checkDate = new Date(date);
  const monthStart = new Date(month[0]);
  const monthEnd = new Date(month[1]);

  // Set all dates to midnight to compare only the date part
  checkDate.setHours(0, 0, 0, 0);
  monthStart.setHours(0, 0, 0, 0);
  monthEnd.setHours(0, 0, 0, 0);

  // Check if the date is greater than or equal to the start of the month
  // and less than or equal to the end of the month
  return (
    checkDate.getTime() >= monthStart.getTime() &&
    checkDate.getTime() <= monthEnd.getTime()
  );
}

/**
 * Groups events by week based on the monthWeeks ranges
 * @param events Array of calendar events to group
 * @returns Array of event arrays, where each inner array contains events from the same week
 */
function getEventGroups(events: CalendarEvent[]): CalendarEvent[][] {
  // Create an array to hold events for each week
  const weekGroups: CalendarEvent[][] = Array(monthWeeks.length)
    .fill(null)
    .map(() => []);

  // Sort events by start date
  const sortedEvents = [...events].sort(
    (a, b) => new Date(a.start).getTime() - new Date(b.start).getTime()
  );

  // Iterate through each event and assign to appropriate week group
  for (const event of sortedEvents) {
    const eventDate = new Date(event.start).getDate();

    // Find which week this event belongs to
    const weekIndex = monthWeeks.findIndex(
      (week) => eventDate >= week[0] && eventDate <= week[1]
    );

    // If we found a matching week, add the event to that week's group
    if (weekIndex !== -1) {
      weekGroups[weekIndex].push(event);
    }
  }

  // Filter out empty week groups
  return weekGroups.filter((group) => group.length > 0);
}

/**
 * @dev do not use directly, memoize it
 */
const EventCard = ({
  event: { start, end, ...rest },
  ...props
}: {
  event: CalendarEvent;
  onClick?: (event: CalendarEvent) => void;
}) => {
  const topPosition = calculateTopPosition(start);
  return (
    <div
      style={{
        top: `${topPosition}px`,
      }}
      className="w-full absolute px-1 left-0"
    >
      <motion.div
        whileTap={{ scale: 0.9 }}
        whileHover={{ scale: 1.05 }}
        onClick={() => props.onClick?.({ start, end, ...rest })}
        className="w-full rounded-xl py-2 px-2.5 cursor-pointer flex flex-col gap-y-1 "
        style={{
          minHeight: `fit-content`,
          background: pickRandomColor(),
        }}
      >
        <p className="text-[#171717]/80 font-medium text-xs line-clamp-1">
          {rest.extendedProps?.customerInfo?.firstName}{" "}
          {rest.extendedProps?.customerInfo?.lastName}
        </p>
        <p className="text-[#171717]/70 text-xs font-semibold line-clamp-1">
          {rest.title}
        </p>
        <p className=" text-xs font-semibold">
          {formatDate(new Date(start), "dd MMM h:mm a")}
        </p>
      </motion.div>
    </div>
  );
};

const MemoizedEventCard = memo(EventCard);

type Props = {
  events: CalendarEvent[];
  onEventClick: (event: CalendarEvent) => void;
  selectedDate: Date;
  setSelectedDate: React.Dispatch<React.SetStateAction<Date>>;
  months: Months;
};

const MonthlyCalendar = ({
  months,
  selectedDate,
  setSelectedDate,
  events,
  ...props
}: Props) => {
  const today = Object.freeze(new Date());

  useEffect(() => {
    // when ever weeks change, we check the first set it as selectedDate month
    const [month, fullYear] = [
      months[0][0].getMonth(),
      months[0][0].getFullYear(),
    ];
    if (month !== selectedDate.getMonth()) {
      selectedDate.setMonth(month);
      selectedDate.setFullYear(fullYear);
      setSelectedDate(new Date(selectedDate));
    }
  }, [months]);

  return (
    <section className="w-full h-fit space-y-10 select-none ">
      <section className="w-full flex gap-x-2 pb-10 md:gap-x-0">
        <div className="whitespace-nowrap h-full pt-28 flex flex-col gap-y-10 md:w-20 ">
          {monthWeeks.map(([firstDay, lastDay], index) => (
            <Typography
              key={index}
              variant={"p"}
              className="text-[#AFAFAF] font-bold text-center !min-h-10 flex flex-col items-center justify-center text-sm !mt-0 md:text-sm "
            >
              {`${firstDay}${getSuffix(firstDay)}`} -
              {` ${lastDay}${getSuffix(lastDay)}`}
            </Typography>
          ))}
        </div>
        <div className="w-full h-full flex flex-col gap-y-4 items-start flex-grow overflow-x-auto no-scrollbar">
          <div className="min-w-full h-fit flex items-center ">
            {months.map(([firstDayOfMonth, lastDayOfMonth], index) => (
              <MarkerTooltip
                content={formatDate(firstDayOfMonth, "MMM, yyyy")}
                key={index}
              >
                <div
                  data-iscurrent={isDayWithinMonth(today, [
                    firstDayOfMonth,
                    lastDayOfMonth,
                  ])}
                  className="w-44 h-24 gap-y-0 text-[#AFAFAF] flex flex-col items-center justify-center data-[iscurrent=true]: cursor-pointer"
                >
                  <Typography variant={"h1"} className="text-inherit">
                    {formatDate(firstDayOfMonth, "MMM")}
                  </Typography>
                  <Typography
                    variant={"p"}
                    className="text-inherit !font-bold !mt-0 "
                  >
                    {formatDate(firstDayOfMonth, "yyyy").toLowerCase()}
                  </Typography>
                  <div className="w-0.5 h-6 bg-[#AFAFAF] " />
                </div>
              </MarkerTooltip>
            ))}
          </div>
          {/* fixed width here */}
          <div className="w-[77rem] relative ">
            <div className="w-full flex-grow flex flex-col gap-y-10 ">
              {monthWeeks.map((_, index) => (
                <div
                  key={index}
                  className="w-full h-10 flex flex-col justify-center items-center"
                >
                  <div className="w-full h-0.5 bg-subtle-gray " />
                </div>
              ))}
            </div>
            {/* events container */}
            <div className="min-w-full h-full flex-grow absolute left-0 top-0 z-10 flex pt-5 ">
              {months.map((month, index) => {
                const matchingEvents = events.filter((event) =>
                  isDayWithinMonth(new Date(event.start), month)
                );
                const groupedEvents = getEventGroups(matchingEvents);
                return (
                  <div key={index} className={cn("h-full relative w-44 ")}>
                    {groupedEvents.map((eventGroup, eventGroupIndex) => {
                      if (eventGroup.length === 1)
                        return (
                          <MemoizedEventCard
                            onClick={() => {
                              props.onEventClick(eventGroup[0]);
                            }}
                            key={eventGroupIndex}
                            event={eventGroup[0]}
                          />
                        );
                      else if (eventGroup.length > 1)
                        return (
                          <EventGroup
                            key={eventGroupIndex}
                            events={eventGroup}
                            topPosition={calculateTopPosition(
                              eventGroup[0].start
                            )}
                            onClickEvent={props.onEventClick}
                          />
                        );
                      else return null;
                    })}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </section>
    </section>
  );
};

export default MonthlyCalendar;
