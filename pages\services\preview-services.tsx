import { Typography } from "@/components/typography"
import { usePreviewServices } from "./components/use-preview-services"
import Toggle from "@/components/ui/toggle-2"
import { useFormik } from "formik"
import { inlineSwitch, noop, pick } from "@/lib/helpers"
import SearchableDropdown from "@/components/ui/searchable-dropdown"
import PendingOverlay from "@/components/ui/pending-overlay"
import { useRef, useState } from "react"
import { Button, SaveButton } from "@/components/buttons"
import { Edit02Icon, Image01Icon } from "hugeicons-react"
import MoreInfo from "@/components/ui/more-info"
import RichTextEditor from "./components/preview-rich-text-editor"
import Tabs from "@/components/ui/tabs"
import CircularLoader from "@/components/ui/circular-loader"
import { IBookingSettings } from "@/src/services/booking-settings.service"
import { Input } from "@/components/inputs"
import UploadUserProfileImage from "../profile/components/upload-image"

const previewTypeOptions = [
  { label: 'Image', value: 'image' },
  { label: 'Logo With Description', value: 'html' }
];

const PreviewServices = () => {
  const { previewData, toggleBookingSettingsStatus, uploadBookingImageMutation, updateBookingSettingsHTMLMutation, ...previewRest } = usePreviewServices()
  const previewFormik = useFormik({
    enableReinitialize: true,
    initialValues: {
      enabled: Boolean(previewData.data?.isActive),
      type: previewData.data?.bookingInfo.isImage ? 'image' : 'html'
    },
    onSubmit: noop
  })
  const htmlFormik = useFormik({
    enableReinitialize: true,
    initialValues: {
      data: previewData?.data?.bookingInfo.isImage ? undefined : previewData.data?.bookingInfo.data,
      isImage: false
    },
    onSubmit: noop
  })
  const themeFormik = useFormik({
    enableReinitialize: true,
    initialValues: previewData.data?.themeSettings || ({} as IBookingSettings['themeSettings']),
    onSubmit: noop,
  })
  const [saveButtonHasChanges, setSaveButtonHasChanges] = useState(false)
  if (previewData.isLoading)
    return (
      <div className="flex w-full justify-center py-10">
        <CircularLoader />
      </div>
    )
  return (
    <section className="w-full max-w-2xl px-2 flex flex-col gap-y-5 pb-32 ">
      <div className="w-full flex flex-col gap-y-2 justify-between items-start">
        <Typography
          variant={"h1"}
          className="font-bold font-Bricolage_Grotesque  "
        >
          Preview Services
        </Typography>
        <Typography className="text-sm text-subtext pl-1">
          Here, you can customize how you want your services preview to be.
        </Typography>
      </div>
      <div className="flex flex-col">
        <div className="w-full flex items-start justify-between ">
          <Typography className=" text-paragraph">
            Toggle Preview
          </Typography>
          <Toggle
            checked={previewFormik.values.enabled}
            onChange={(checked) => {
              previewFormik.setFieldValue('enabled', checked)
              toggleBookingSettingsStatus.mutateAsync(checked)
            }}
          />
        </div>
        <MoreInfo.Static content="You can enable previews to streamline your clients' booking flows, share some information before they proceed to book an appointment" className="text-sm text-subtext">
        </MoreInfo.Static>
      </div>
      {
        previewFormik.values.enabled && (
          <>
            <div className="w-full flex flex-col gap-y-2  ">
              <Typography className=" text-paragraph">Select Preview Type</Typography>
              <SearchableDropdown
                fullWidth
                noDelete
                value={previewTypeOptions.find(type => type.value === previewFormik.values.type)!}
                options={previewTypeOptions}
                name="preview_type"
                onChange={(option) => {
                  if (!option) return;
                  previewFormik.setFieldValue("type", option.value);
                }}
              />
              <Typography className="text-sm text-subtext pl-1">
                {
                  inlineSwitch(previewFormik.values.type,
                    ['image', "Show your clients a preview image before they proceed to select services"],
                    { default: "Show your clients your logo and a custom description before they can select services, you can include your privacy policy here" }
                  )}
              </Typography>
            </div>
            <div className="flex w-full ">
              {
                previewFormik.values.type === 'image'
                  ? (
                    <ImageUploadPreview
                      src={previewData.data?.bookingInfo.isImage ? previewData.data?.bookingInfo.data : undefined}
                      onChange={(file) => {
                        const typedFormData = new FormData() as TypedFormData<{ image: File }>;
                        typedFormData.set('image', file)
                        uploadBookingImageMutation.mutateAsync(typedFormData)
                        // do the mutation here 
                      }} />
                  )
                  : (
                    <LogoWithDescription
                      value={htmlFormik.values.data}
                      onChange={(value) => {
                        htmlFormik.setFieldValue('data', value)
                        console.log('yepp, mounted', value)
                        setSaveButtonHasChanges(true)
                      }}
                      saveButtonProps={{
                        loading: updateBookingSettingsHTMLMutation.isPending,
                        disabled: updateBookingSettingsHTMLMutation.isPending,
                        hasChanges: saveButtonHasChanges,
                        onTap: () => {
                          updateBookingSettingsHTMLMutation
                            .mutateAsync({
                              ...(pick(htmlFormik.values, 'isImage')),
                              data: htmlFormik.values.data || ''
                            }, {
                              onSuccess() {
                                setSaveButtonHasChanges(false)
                              }
                            })
                        }
                      }}
                    />
                  )
              }
            </div>
          </>
        )
      }
      <ThemeSettings formik={themeFormik} />
      <SaveButton
        title="Save Changes"
        onTap={() => {
          if (htmlFormik.values.data) {
            updateBookingSettingsHTMLMutation.mutateAsync(htmlFormik.values as any)
          }
          previewRest.updateBookingThemeSettingsMutation.mutateAsync(themeFormik.values)
        }}

        loading={uploadBookingImageMutation.isPending || previewRest.updateBookingThemeSettingsMutation.isPending || updateBookingSettingsHTMLMutation.isPending}

        disabled={uploadBookingImageMutation.isPending || previewRest.updateBookingThemeSettingsMutation.isPending || updateBookingSettingsHTMLMutation.isPending}
      ></SaveButton>
      <div className="h-44" />
      <PendingOverlay
        isPending={uploadBookingImageMutation.isPending || previewRest.updateBookingThemeSettingsMutation.isPending || updateBookingSettingsHTMLMutation.isPending}
      />
    </section>
  )
}

type ThemeSettingsProps = {
  formik: ReturnType<typeof useFormik<IBookingSettings['themeSettings']>>
}

const ThemeSettings = ({ formik }: ThemeSettingsProps) => {
  return (
    <div className="flex flex-col gap-y-3 mt-5">
      <div className="w-full flex flex-col gap-y-2 justify-between items-start">
        <Typography
          variant={"h1"}
          className="font-bold font-Bricolage_Grotesque  "
        >
          Theme Settings
        </Typography>
        <Typography className="text-sm text-subtext pl-1">
          Customize the theme of your Services preview here.
        </Typography>
      </div>
      <div className="flex flex-col mt-4 gap-y-5" >
        <div className="w-full flex items-start justify-between ">
          <div className="flex flex-col gap-y-1">
            <Typography className=" text-paragraph">
              Background Color
            </Typography>
            <Typography className="text-xs text-subtext">
              Background color for the Preview page
            </Typography>
          </div>
          <Input.ColorPicker name="backgroundColor" value={formik.values.backgroundColor} onChange={formik.handleChange} />
        </div>

        <div className="w-full flex items-start justify-between ">
          <div className="flex flex-col gap-y-1">
            <Typography className=" text-paragraph">
              Primary Color
            </Typography>
            <Typography className="text-xs text-subtext">
              Color of sub-headings on Preview Page
            </Typography>
          </div>
          <Input.ColorPicker name="primaryColor" value={formik.values.primaryColor} onChange={formik.handleChange} />
        </div>

        <div className="w-full flex items-start justify-between ">
          <div className="flex flex-col gap-y-1">
            <Typography className="text-paragraph">
              Text Color
            </Typography>
            <Typography className="text-xs text-subtext">
              Color of text on the Preview page
            </Typography>
          </div>
          <Input.ColorPicker name="textColor" value={formik.values.textColor} onChange={formik.handleChange} />
        </div>

        <div className="w-full flex flex-col mt-3 gap-y-1 justify-between items-start">
          <Typography
            variant={"h3"}
            className="font-bold font-Bricolage_Grotesque  "
          >
            Button Settings
          </Typography>
          <Typography className="text-sm text-subtext">
            Customize the theme for buttons
          </Typography>
          <button
            style={{
              color: formik.values.buttonTextColor,
              backgroundColor: formik.values.buttonColor,
              borderRadius: formik.values.borderRadius
            }}
            className="mx-auto mt-3 px-4 py-2 text-sm font-medium transition duration-300 ">
            Example Button
          </button>
        </div>
        <div className="w-full flex items-start justify-between ">
          <div className="flex flex-col gap-y-1">
            <Typography className=" text-paragraph">
              Button Background Color
            </Typography>
            <Typography className="text-xs text-subtext">
              Button Background Color on the Preview page
            </Typography>
          </div>
          <Input.ColorPicker name="buttonColor" value={formik.values.buttonColor} onChange={formik.handleChange} />
        </div>
        <div className="w-full flex items-start justify-between ">
          <div className="flex flex-col gap-y-1">
            <Typography className=" text-paragraph">
              Button Text Color
            </Typography>
            <Typography className="text-xs text-subtext">
              Button Text Color on the Preview page
            </Typography>
          </div>
          <Input.ColorPicker name="buttonTextColor" value={formik.values.buttonTextColor} onChange={formik.handleChange} />
        </div>        <div className="w-full flex items-start justify-between ">
          <div className="flex flex-col gap-y-1">
            <Typography className=" text-paragraph">
              Button Corner Rounding
            </Typography>
          </div>
          <Input.Numeric name="borderRadius" value={formik.values.borderRadius}
            label=""
            className="max-w-20"
            onChange={({ currentTarget: { value } }) => {
              formik.setFieldValue('borderRadius', parseInt(value || '0'))
            }} />
        </div>
      </div>
    </div>
  )
}

type ImageUploadPreviewProps = {
  src: string | null | undefined;
  onChange(file: File): void
}
const ImageUploadPreview = (props: ImageUploadPreviewProps) => {
  const inputRef = useRef<HTMLInputElement>(null)
  const attemptReplacePicture = () => {
    inputRef.current?.click()
  }
  return (
    <div className="w-full" >
      <div className="cursor-pointer w-full active:scale-[0.95] ">
        {!props.src ? (
          <div
            onClick={() => inputRef.current?.click()}
            className="w-full border border-dashed border-gray-400 rounded-3xl flex items-center justify-center aspect-[5/3] active:scale-[0.95] "
          >
            <Image01Icon className="text-gray-300 size-20 " />
          </div>
        ) : (
          <div className="relative w-full group rounded-3xl aspect-[5/3] ">
            <img
              className="w-full h-full  object-cover rounded-3xl "
              src={
                props.src
              }
            />
            {innerWidth <= 1024 ? (
              <div className="w-fit absolute right-2 bottom-2 flex items-center justify-center gap-x-2">
                <Button
                  variant="icon"
                  className="rounded-full"
                  onTap={attemptReplacePicture}
                >
                  <Edit02Icon size={18} />
                </Button>
              </div>
            ) : (
              <div className="w-full aspect-[5/3] absolute z-20 top-0 bg-white/20 backdrop-blur-lg rounded-3xl hidden items-center justify-center gap-x-3 group-hover:flex ">
                <Button onTap={attemptReplacePicture}>Replace</Button>
              </div>
            )}
          </div>
        )}
      </div>
      <input
        type="file"
        name="picture"
        className="hidden"
        accept="image/jpeg, image/png"
        ref={inputRef}
        onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
          const files = event.target.files;
          if (files && files.length > 0) {
            props.onChange(files[0] || null)
          }
        }}
      />
      {props.src ? (
        <MoreInfo.Static
          className="text-sm hidden xl:flex"
          content="Hover on the image to see more options"
        />
      ) : null}
    </div>
  )
}

type LogoWithDescriptionProps = {
  value?: string;
  onChange(value: string): void;
  saveButtonProps: {
    loading: boolean;
    disabled: boolean;
    hasChanges: boolean;
    onTap: VoidFunction
  }
}

const LogoWithDescription = (props: LogoWithDescriptionProps) => {
  return (
    <div className="w-full " >
      <div className="w-full py-2 flex items-center justify-center">
        <UploadUserProfileImage />
      </div>
      <Tabs
        defaultValue="edit"
        className="w-full"
      >
        <Tabs.List className="grid w-full max-w-[320px] mx-auto mt-2 mb-10 grid-cols-2 md:flex-grow ">
          <Tabs.Trigger
            value="edit"
            className=" px-0 flex items-center justify-center gap-x-2"
          >
            Editor
          </Tabs.Trigger>
          <Tabs.Trigger
            value="preview"
            className="px-0 flex items-center justify-center gap-x-2"
          >
            Preview
          </Tabs.Trigger>
        </Tabs.List>
        <Tabs.Content
          className="mt-2 focus:outline-none active:outline-none w-full"
          value={"edit"}
        >
          <RichTextEditor
            value={props.value}
            onChange={(value) => {
              props.onChange(value)
            }}
          />
        </Tabs.Content>
        <Tabs.Content
          className="mt-2 focus:outline-none active:outline-none w-full"
          value={"preview"}
        >
          <RichTextEditor.Preview defaultValue={props.value} value={props.value} />
        </Tabs.Content>
      </Tabs>
    </div>
  )
}

export default PreviewServices
