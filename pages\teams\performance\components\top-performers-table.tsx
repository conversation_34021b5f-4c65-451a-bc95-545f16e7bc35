import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Typography } from "@/components/typography";
import {
  StarIcon,
  ChartBreakoutSquareIcon,
  CheckmarkCircle02Icon,
  DollarCircleIcon,
} from "hugeicons-react";
import { StaffAnalytics } from "@/src/services/staff-performance.service";
import { useFormattedPrice } from "@/lib/use-formatted-price";

interface TopPerformersTableProps {
  topPerformers: StaffAnalytics["topPerformers"];
  metric:
    | "efficiency"
    | "reliability"
    | "customerSatisfaction"
    | "totalRevenue";
  onStaffClick: (staffId: string) => void;
}

export const formatMetricValue = (
  performer: StaffAnalytics["topPerformers"][0],
  metricType: string,
) => {
  switch (metricType) {
    case "efficiency":
      return `${(performer.efficiency * 100).toFixed(1)}%`;
    case "reliability":
      return `${(performer.reliability * 100).toFixed(1)}%`;
    case "customerSatisfaction":
      return `${performer.customerSatisfaction.toFixed(1)}/5`;
    case "totalRevenue":
      return `$${performer.totalRevenue.toLocaleString()}`;
    default:
      return "-";
  }
};
const TopPerformersTable: React.FC<TopPerformersTableProps> = ({
  topPerformers,
  metric,
  onStaffClick,
}) => {
  const getMetricIcon = (metricType: string) => {
    switch (metricType) {
      case "efficiency":
        return <ChartBreakoutSquareIcon className="w-4 h-4 text-blue-600" />;
      case "reliability":
        return <CheckmarkCircle02Icon className="w-4 h-4 text-green-600" />;
      case "customerSatisfaction":
        return <StarIcon className="w-4 h-4 text-yellow-600" />;
      case "totalRevenue":
        return <DollarCircleIcon className="w-4 h-4 text-purple-600" />;
      default:
        return null;
    }
  };

  const getMetricTitle = () => {
    switch (metric) {
      case "efficiency":
        return "Efficiency";
      case "reliability":
        return "Reliability";
      case "customerSatisfaction":
        return "Customer Satisfaction";
      case "totalRevenue":
        return "Total Revenue";
      default:
        return "Metric";
    }
  };

  const { formatPrice } = useFormattedPrice();

  if (topPerformers.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Top Performers</CardTitle>
        </CardHeader>
        <CardContent className="p-6 text-center text-gray-500">
          No performance data available
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getMetricIcon(metric)}
          Top Performers - {getMetricTitle()}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto thin-scrollbar pb-3">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3 px-2">Rank</th>
                <th className="text-left py-3 px-2">Staff Member</th>
                <th className="text-left py-3 px-2">Role</th>
                <th className="text-left py-3 px-2">Efficiency</th>
                <th className="text-left py-3 px-2">Reliability</th>
                <th className="text-left py-3 px-2">Satisfaction</th>
                <th className="text-left py-3 px-2">Revenue</th>
              </tr>
            </thead>
            <tbody>
              {topPerformers.map((performer, index) => (
                <tr
                  key={performer.staffId}
                  className={`border-b last:border-b-[0] hover:bg-gray-50 cursor-pointer`}
                  onClick={() => onStaffClick?.(performer.staffId)}
                >
                  <td className="py-3 px-2">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-sm font-semibold">
                      {index + 1}
                    </div>
                  </td>
                  <td className="py-3 px-2">
                    <div className="flex items-center gap-3">
                      {/*<Avatar
                        name={performer.name}
                        backgroundColor={performer.color}
                      />*/}
                      <Typography className="font-medium">
                        {performer.name}
                      </Typography>
                    </div>
                  </td>
                  <td className="py-3 px-2">
                    <Typography className="text-gray-600 capitalize">
                      {performer.role}
                    </Typography>
                  </td>
                  <td className="py-3 px-2">
                    <Typography
                      className={
                        metric === "efficiency"
                          ? "font-semibold text-blue-600"
                          : ""
                      }
                    >
                      {(performer.efficiency * 100).toFixed(1)}%
                    </Typography>
                  </td>
                  <td className="py-3 px-2">
                    <Typography
                      className={
                        metric === "reliability"
                          ? "font-semibold text-green-600"
                          : ""
                      }
                    >
                      {(performer.reliability * 100).toFixed(1)}%
                    </Typography>
                  </td>
                  <td className="py-3 px-2">
                    <div className="flex items-center gap-1">
                      <StarIcon className="w-4 h-4 text-yellow-500" />
                      <Typography
                        className={
                          metric === "customerSatisfaction"
                            ? "font-semibold text-yellow-600"
                            : ""
                        }
                      >
                        {performer.customerSatisfaction.toFixed(1)}
                      </Typography>
                    </div>
                  </td>
                  <td className="py-3 px-2">
                    <Typography
                      className={
                        metric === "totalRevenue"
                          ? "font-semibold text-purple-600"
                          : ""
                      }
                    >
                      {formatPrice(performer.totalRevenue)}
                    </Typography>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

export default TopPerformersTable;
