import React from "react";

type StatusBarProps = React.SVGProps<SVGSVGElement> & {
  foreground?: /*dark*/ "#030712" | /*light*/ "#f3f4f6";
  bPercentage?: /*dark*/ "#e2e8f0" | /*light*/ "transparent";
};

const StatusBar: React.FC<StatusBarProps> = ({
  className,
  foreground = "#030712",
  bPercentage = "#e2e8f0",
  ...rest
}) => {
  return (
    <svg
      {...rest}
      className={className + " h-fit"}
      width="430"
      height="67"
      viewBox="0 0 430 67"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M58.7031 33.6718C58.7031 34.7604 58.6165 35.7345 58.4434 36.5942C58.2702 37.4477 57.9919 38.1744 57.6084 38.7744C57.2311 39.3743 56.7301 39.832 56.1055 40.1474C55.4808 40.4628 54.7139 40.6205 53.8047 40.6205C52.6729 40.6205 51.7451 40.3453 51.0215 39.7949C50.2979 39.2382 49.7629 38.4435 49.4165 37.4106C49.0763 36.3715 48.9062 35.1253 48.9062 33.6718C48.9062 32.206 49.0609 30.9536 49.3701 29.9145C49.6855 28.8692 50.202 28.0683 50.9194 27.5117C51.6369 26.955 52.5986 26.6767 53.8047 26.6767C54.9365 26.6767 55.8612 26.955 56.5786 27.5117C57.3022 28.0621 57.8372 28.86 58.1836 29.9052C58.5299 30.9443 58.7031 32.1998 58.7031 33.6718ZM52.5522 33.6718C52.5522 34.5563 52.5832 35.2984 52.645 35.8984C52.7131 36.4983 52.8368 36.9498 53.0161 37.2529C53.2017 37.5497 53.4645 37.6982 53.8047 37.6982C54.1449 37.6982 54.4046 37.5497 54.584 37.2529C54.7633 36.9498 54.887 36.5014 54.9551 35.9077C55.0231 35.3077 55.0571 34.5624 55.0571 33.6718C55.0571 32.775 55.0231 32.0266 54.9551 31.4267C54.887 30.8268 54.7633 30.3753 54.584 30.0722C54.4046 29.763 54.1449 29.6083 53.8047 29.6083C53.4645 29.6083 53.2017 29.763 53.0161 30.0722C52.8368 30.3753 52.7131 30.8268 52.645 31.4267C52.5832 32.0266 52.5522 32.775 52.5522 33.6718ZM69.8545 32.8183C69.8545 33.7893 69.7679 34.68 69.5947 35.4902C69.4277 36.3004 69.1649 37.024 68.8062 37.6611C68.4536 38.2919 67.999 38.83 67.4424 39.2753C66.8857 39.7145 66.2209 40.0484 65.4478 40.2773C64.6746 40.5061 63.784 40.6205 62.7759 40.6205C62.4976 40.6205 62.1729 40.6144 61.8018 40.602C61.4368 40.5896 61.1276 40.5649 60.874 40.5278V37.624C61.14 37.6673 61.4059 37.7044 61.6719 37.7353C61.9378 37.76 62.2471 37.7724 62.5996 37.7724C63.6201 37.7724 64.4087 37.6271 64.9653 37.3364C65.5282 37.0457 65.924 36.6437 66.1528 36.1303C66.3817 35.6108 66.5146 35.0139 66.5518 34.3398H66.4404C66.2796 34.6428 66.0848 34.915 65.856 35.1562C65.6271 35.3912 65.3395 35.5768 64.9932 35.7128C64.6468 35.8489 64.217 35.9169 63.7036 35.9169C62.9429 35.9169 62.2811 35.7438 61.7183 35.3974C61.1616 35.051 60.7287 34.5501 60.4194 33.8945C60.1102 33.2327 59.9556 32.4286 59.9556 31.4824C59.9556 30.4742 60.1473 29.6145 60.5308 28.9033C60.9204 28.1858 61.474 27.6354 62.1914 27.2519C62.915 26.8684 63.7747 26.6767 64.7705 26.6767C65.4756 26.6767 66.1343 26.7973 66.7466 27.0385C67.3651 27.2797 67.9062 27.6508 68.3701 28.1518C68.834 28.6466 69.1958 29.2805 69.4556 30.0536C69.7215 30.8268 69.8545 31.7483 69.8545 32.8183ZM64.8633 29.6083C64.4365 29.6083 64.1025 29.7537 63.8613 30.0444C63.6263 30.3351 63.5088 30.802 63.5088 31.4453C63.5088 31.9524 63.6201 32.3544 63.8428 32.6513C64.0654 32.9482 64.3932 33.0966 64.8262 33.0966C65.0983 33.0966 65.3364 33.0255 65.5405 32.8832C65.7508 32.7348 65.9147 32.5493 66.0322 32.3266C66.1497 32.0978 66.2085 31.8627 66.2085 31.6215C66.2085 31.3803 66.1807 31.1422 66.125 30.9072C66.0755 30.666 65.9951 30.4495 65.8838 30.2578C65.7786 30.0598 65.6395 29.9021 65.4663 29.7846C65.2993 29.6671 65.0983 29.6083 64.8633 29.6083ZM71.3018 38.895C71.3018 38.2394 71.4935 37.7786 71.877 37.5126C72.2666 37.2467 72.7336 37.1137 73.2778 37.1137C73.7912 37.1137 74.2365 37.2467 74.6138 37.5126C74.991 37.7786 75.1797 38.2394 75.1797 38.895C75.1797 39.5196 74.991 39.9711 74.6138 40.2495C74.2365 40.5278 73.7912 40.6669 73.2778 40.6669C72.7336 40.6669 72.2666 40.5278 71.877 40.2495C71.4935 39.9711 71.3018 39.5196 71.3018 38.895ZM71.3018 31.5659C71.3018 30.9164 71.4935 30.4588 71.877 30.1928C72.2666 29.9269 72.7336 29.7939 73.2778 29.7939C73.7912 29.7939 74.2365 29.9269 74.6138 30.1928C74.991 30.4588 75.1797 30.9164 75.1797 31.5659C75.1797 32.2029 74.991 32.6606 74.6138 32.9389C74.2365 33.211 73.7912 33.3471 73.2778 33.3471C72.7336 33.3471 72.2666 33.211 71.877 32.9389C71.4935 32.6606 71.3018 32.2029 71.3018 31.5659ZM86.8413 37.8095H85.3477V40.435H81.7295V37.8095H76.3208V35.1191L81.9614 26.8715H85.3477V35.0727H86.8413V37.8095ZM81.7295 35.0727V33.4955C81.7295 33.3533 81.7326 33.1739 81.7388 32.9575C81.745 32.741 81.7511 32.5245 81.7573 32.308C81.7697 32.0854 81.779 31.8998 81.7852 31.7514C81.7975 31.5968 81.8068 31.5133 81.813 31.5009H81.7109C81.5934 31.7792 81.4852 32.0112 81.3862 32.1967C81.2873 32.3761 81.1574 32.5895 80.9966 32.8369L79.4937 35.0727H81.7295ZM95.8682 40.435H92.1479V33.7275C92.1479 33.6038 92.1479 33.378 92.1479 33.0502C92.1479 32.7162 92.151 32.3482 92.1572 31.9462C92.1696 31.5442 92.1851 31.1824 92.2036 30.8608C92.0428 31.0525 91.8913 31.2164 91.749 31.3525C91.613 31.4885 91.48 31.6153 91.3501 31.7328L89.8101 33.0038L87.9082 30.666L92.5654 26.8715H95.8682V40.435Z"
        fill={foreground}
      />
      <g clipPath="url(#clip0_312_871)">
        <rect
          x="141.5"
          y="13"
          width="147"
          height="40.8721"
          rx="20.4361"
          fill="black"
        />
        <path
          d="M266.91 41.0003C271.555 41.0003 275.32 37.4185 275.32 33.0002C275.32 28.5818 271.555 25 266.91 25C262.265 25 258.5 28.5818 258.5 33.0002C258.5 37.4185 262.265 41.0003 266.91 41.0003Z"
          fill="#161616"
        />
        <path
          d="M266.913 37.9339C269.778 37.9339 272.1 35.7246 272.1 32.9993C272.1 30.274 269.778 28.0647 266.913 28.0647C264.048 28.0647 261.726 30.274 261.726 32.9993C261.726 35.7246 264.048 37.9339 266.913 37.9339Z"
          fill="#0A0D13"
        />
        <path
          d="M266.912 36.7592C269.095 36.7592 270.865 35.0759 270.865 32.9994C270.865 30.923 269.095 29.2397 266.912 29.2397C264.73 29.2397 262.96 30.923 262.96 32.9994C262.96 35.0759 264.73 36.7592 266.912 36.7592Z"
          fill="#091427"
        />
        <g filter="url(#filter0_f_312_871)">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M264.192 30.4844C263.723 30.5313 263.057 31.2118 263.008 32.5494C262.958 33.8869 263.575 34.6143 263.92 34.6143C264.266 34.6143 265.696 32.6667 264.192 30.4844Z"
            fill="#235A91"
            fillOpacity="0.556075"
          />
        </g>
        <g filter="url(#filter1_f_312_871)">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M267.556 30.0162C266.816 30.2326 266.674 31.3686 266.703 32.0178C266.731 32.667 267.698 34.3711 268.864 33.9924C270.029 33.6137 270.228 32.2072 269.745 31.2604C269.262 30.3137 268.38 29.6375 267.556 30.0162Z"
            fill="#235A91"
            fillOpacity="0.556075"
          />
        </g>
      </g>
      <g clipPath="url(#clip1_312_871)">
        <g clipPath="url(#clip2_312_871)">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M335.064 26.5779C334.512 26.5779 334.064 27.0256 334.064 27.5778V33.8064C334.064 34.3587 334.512 34.8064 335.064 34.8064H336.893C337.446 34.8064 337.893 34.3587 337.893 33.8064V27.5779C337.893 27.0256 337.446 26.5779 336.893 26.5779H335.064ZM328.721 28.0636C328.169 28.0636 327.721 28.5113 327.721 29.0636V33.8065C327.721 34.3588 328.169 34.8065 328.721 34.8065H330.551C331.103 34.8065 331.551 34.3588 331.551 33.8065V29.0636C331.551 28.5113 331.103 28.0636 330.551 28.0636H328.721ZM321.379 30.5493C321.379 29.997 321.827 29.5493 322.379 29.5493H324.208C324.761 29.5493 325.208 29.997 325.208 30.5493V33.8064C325.208 34.3587 324.761 34.8064 324.208 34.8064H322.379C321.827 34.8064 321.379 34.3587 321.379 33.8064V30.5493ZM316.036 31.035C315.484 31.035 315.036 31.4827 315.036 32.035V33.8064C315.036 34.3587 315.484 34.8064 316.036 34.8064H317.866C318.418 34.8064 318.866 34.3587 318.866 33.8064V32.035C318.866 31.4827 318.418 31.035 317.866 31.035H316.036ZM334.064 37.635C334.064 37.0828 334.512 36.635 335.064 36.635H336.893C337.446 36.635 337.893 37.0828 337.893 37.635V39.2922C337.893 39.8445 337.446 40.2922 336.893 40.2922H335.064C334.512 40.2922 334.064 39.8445 334.064 39.2922V37.635ZM328.721 36.635C328.169 36.635 327.721 37.0828 327.721 37.635V39.2922C327.721 39.8445 328.169 40.2922 328.721 40.2922H330.551C331.103 40.2922 331.551 39.8445 331.551 39.2922V37.635C331.551 37.0828 331.103 36.635 330.551 36.635H328.721ZM321.379 37.635C321.379 37.0828 321.827 36.635 322.379 36.635H324.208C324.761 36.635 325.208 37.0828 325.208 37.635V39.2922C325.208 39.8445 324.761 40.2922 324.208 40.2922H322.379C321.827 40.2922 321.379 39.8445 321.379 39.2922V37.635ZM316.036 36.635C315.484 36.635 315.036 37.0828 315.036 37.635V39.2922C315.036 39.8445 315.484 40.2922 316.036 40.2922H317.866C318.418 40.2922 318.866 39.8445 318.866 39.2922V37.635C318.866 37.0828 318.418 36.635 317.866 36.635H316.036Z"
            fill={foreground}
          />
        </g>
        <path
          d="M353.333 36.095C354.153 36.095 354.951 36.3255 355.653 36.7649L355.938 36.9429C356.154 37.0779 356.192 37.3938 356.015 37.5824L353.584 40.1728C353.435 40.3319 353.193 40.3319 353.043 40.1728L350.629 37.5991C350.452 37.4113 350.489 37.097 350.703 36.961L350.985 36.7822C351.694 36.3316 352.501 36.095 353.333 36.095Z"
          fill={foreground}
        />
        <path
          d="M353.333 31.3366C355.374 31.3366 357.334 32.0603 358.931 33.4007L359.156 33.5901C359.336 33.7411 359.352 34.0257 359.19 34.1983L357.739 35.7447C357.605 35.8878 357.392 35.9042 357.24 35.7833L357.063 35.6428C355.985 34.7863 354.685 34.3268 353.333 34.3268C351.972 34.3268 350.664 34.792 349.582 35.6584L349.405 35.8001C349.253 35.922 349.04 35.906 348.905 35.7625L347.455 34.2166C347.293 34.0444 347.308 33.7605 347.488 33.6092L347.712 33.4197C349.312 32.0674 351.281 31.3366 353.333 31.3366Z"
          fill={foreground}
        />
        <path
          d="M353.332 26.5779C356.572 26.5779 359.67 27.7992 362.122 30.0379L362.332 30.229C362.501 30.3834 362.511 30.6586 362.353 30.8265L360.907 32.368C360.767 32.5173 360.543 32.5278 360.391 32.3921L360.212 32.232C358.281 30.5066 355.861 29.5681 353.332 29.5681C350.795 29.5681 348.367 30.5131 346.434 32.2493L346.254 32.4101C346.103 32.5464 345.878 32.5362 345.738 32.3866L344.292 30.8454C344.134 30.6778 344.144 30.403 344.313 30.2484L344.521 30.0573C346.976 27.8065 350.083 26.5779 353.332 26.5779Z"
          fill={foreground}
        />
        <g clipPath="url(#clip3_312_871)">
          <ellipse
            cx="395.607"
            cy="33.435"
            rx="2.85714"
            ry="3.42858"
            fill={foreground}
          />
          <rect
            x="368.75"
            y="25.435"
            width="27.4287"
            height="16"
            rx="4"
            fill={foreground}
          />
          <path
            d="M378.274 37.435H376.316V33.9047C376.316 33.8396 376.316 33.7208 376.316 33.5483C376.316 33.3725 376.318 33.1788 376.321 32.9672C376.328 32.7556 376.336 32.5652 376.346 32.3959C376.261 32.4968 376.181 32.5831 376.106 32.6547C376.035 32.7263 375.965 32.7931 375.896 32.8549L375.086 33.5239L374.085 32.2934L376.536 30.2963H378.274V37.435ZM385.042 33.8754C385.042 34.4483 384.996 34.961 384.905 35.4135C384.814 35.8627 384.668 36.2452 384.466 36.561C384.267 36.8767 384.004 37.1176 383.675 37.2836C383.346 37.4496 382.942 37.5327 382.464 37.5327C381.868 37.5327 381.38 37.3878 380.999 37.0981C380.618 36.8051 380.337 36.3868 380.154 35.8432C379.975 35.2963 379.886 34.6404 379.886 33.8754C379.886 33.1039 379.967 32.4448 380.13 31.8979C380.296 31.3478 380.568 30.9262 380.945 30.6332C381.323 30.3403 381.829 30.1938 382.464 30.1938C383.06 30.1938 383.546 30.3403 383.924 30.6332C384.305 30.923 384.586 31.3429 384.769 31.893C384.951 32.4399 385.042 33.1007 385.042 33.8754ZM381.805 33.8754C381.805 34.3409 381.821 34.7315 381.854 35.0473C381.889 35.3631 381.954 35.6007 382.049 35.7602C382.146 35.9164 382.285 35.9946 382.464 35.9946C382.643 35.9946 382.78 35.9164 382.874 35.7602C382.968 35.6007 383.034 35.3647 383.069 35.0522C383.105 34.7364 383.123 34.3442 383.123 33.8754C383.123 33.4034 383.105 33.0095 383.069 32.6938C383.034 32.378 382.968 32.1404 382.874 31.9809C382.78 31.8181 382.643 31.7368 382.464 31.7368C382.285 31.7368 382.146 31.8181 382.049 31.9809C381.954 32.1404 381.889 32.378 381.854 32.6938C381.821 33.0095 381.805 33.4034 381.805 33.8754ZM390.901 33.8754C390.901 34.4483 390.856 34.961 390.765 35.4135C390.674 35.8627 390.527 36.2452 390.325 36.561C390.127 36.8767 389.863 37.1176 389.534 37.2836C389.205 37.4496 388.802 37.5327 388.323 37.5327C387.728 37.5327 387.239 37.3878 386.858 37.0981C386.478 36.8051 386.196 36.3868 386.014 35.8432C385.835 35.2963 385.745 34.6404 385.745 33.8754C385.745 33.1039 385.826 32.4448 385.989 31.8979C386.155 31.3478 386.427 30.9262 386.805 30.6332C387.182 30.3403 387.688 30.1938 388.323 30.1938C388.919 30.1938 389.406 30.3403 389.783 30.6332C390.164 30.923 390.446 31.3429 390.628 31.893C390.81 32.4399 390.901 33.1007 390.901 33.8754ZM387.664 33.8754C387.664 34.3409 387.68 34.7315 387.713 35.0473C387.749 35.3631 387.814 35.6007 387.908 35.7602C388.006 35.9164 388.144 35.9946 388.323 35.9946C388.502 35.9946 388.639 35.9164 388.733 35.7602C388.828 35.6007 388.893 35.3647 388.929 35.0522C388.965 34.7364 388.982 34.3442 388.982 33.8754C388.982 33.4034 388.965 33.0095 388.929 32.6938C388.893 32.378 388.828 32.1404 388.733 31.9809C388.639 31.8181 388.502 31.7368 388.323 31.7368C388.144 31.7368 388.006 31.8181 387.908 31.9809C387.814 32.1404 387.749 32.378 387.713 32.6938C387.68 33.0095 387.664 33.4034 387.664 33.8754Z"
            fill={bPercentage}
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_f_312_871"
          x="262.802"
          y="30.2816"
          width="2.26801"
          height="4.53562"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="bPercentageImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="bPercentageImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="0.101426"
            result="effect1_foregroundBlur_312_871"
          />
        </filter>
        <filter
          id="filter1_f_312_871"
          x="266.496"
          y="29.7063"
          width="3.69672"
          height="4.54318"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="bPercentageImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="bPercentageImageFix"
            result="shape"
          />
          <feGaussianBlur
            stdDeviation="0.101426"
            result="effect1_foregroundBlur_312_871"
          />
        </filter>
        <clipPath id="clip0_312_871">
          <rect
            width="147"
            height="40.87"
            fill="white"
            transform="translate(141.5 13)"
          />
        </clipPath>
        <clipPath id="clip1_312_871">
          <rect
            width="83.4286"
            height="34"
            fill="white"
            transform="translate(315.036 16.435)"
          />
        </clipPath>
        <clipPath id="clip2_312_871">
          <rect
            width="22.8571"
            height="16"
            fill="white"
            transform="translate(315.036 25.435)"
          />
        </clipPath>
        <clipPath id="clip3_312_871">
          <rect
            width="29.7143"
            height="16"
            fill="white"
            transform="translate(368.75 25.435)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default StatusBar;
