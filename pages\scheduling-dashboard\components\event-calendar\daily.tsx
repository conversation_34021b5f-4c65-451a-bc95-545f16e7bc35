import { Typography } from "@/components/typography";
import { CalendarEvent } from "~/interfaces/booking";
import { formatDate } from "date-fns";
import { motion } from "framer-motion";
import { memo } from "react";
import { Calendar, ChevronLeft, ChevronRight } from "@gravity-ui/icons";
import { Button } from "@/components/buttons";

const allColors = [
  "#87CEEB", // Sky Blue
  "#ADD8E6", // Light Blue
  "#B0E0E6", // Powder Blue
  "#FFB347",
  "#FFC0CB",
];

export const businessHours = [
  "8 am",
  "9 am",
  "10 am",
  "11 am",
  "12 pm",
  "1 pm",
  "2 pm",
  "3 pm",
  "4 pm",
  "5 pm",
  "6 pm",
];

const hourBlockHeight = 80;

function calculateTopPosition(startTime: string) {
  // Parse the event start time
  const eventHour = new Date(startTime).getHours();
  const eventMinutes = new Date(startTime).getMinutes();

  // Find index of the hour in businessHours array
  const hourIndex = businessHours.findIndex((hour) => {
    const businessHour = parseInt(hour.split(" ")[0]);
    return businessHour === (eventHour > 12 ? eventHour - 12 : eventHour);
  });

  // Calculate position: each hour block is 80px (h-20)
  // Add proportional minutes (80px per hour, so divide minutes by 60 and multiply by 80)
  const topPosition =
    hourIndex * hourBlockHeight + (eventMinutes / 60) * hourBlockHeight;
  return topPosition;
}

function calculateEventHeight(startTime: string, endTime: string) {
  const start = new Date(startTime);
  const end = new Date(endTime);

  // Get difference in milliseconds and convert to hours
  const diffInHours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);

  // Convert hours to pixels (80px per hour)
  return diffInHours * hourBlockHeight;
}

function pickRandomColor() {
  const randomIndex = Math.floor(Math.random() * allColors.length);
  return allColors[randomIndex];
}

function areDatesEqualWithoutHours(date: Date, checkDate: Date): boolean {
  date.setHours(0, 0, 0, 0);
  checkDate.setHours(0, 0, 0, 0);
  return checkDate.getTime() === date.getTime();
}

/**
 * @dev do not use directly, memoize it
 */
const EventCard = ({
  event: { start, end, ...rest },
  ...props
}: {
  event: CalendarEvent;
  onClick?: (event: CalendarEvent) => void;
}) => {
  const topPosition = calculateTopPosition(start);
  const minHeight = calculateEventHeight(start, end);

  return (
    <div
      style={{
        top: `${topPosition}px`,
      }}
      className="w-full absolute px-1 left-0 md:w-1/2 md:first:left-0 md:left-auto md:right-0"
    >
      <motion.div
        whileTap={{ scale: 0.9 }}
        whileHover={{ scale: 1.05 }}
        onClick={() => props.onClick?.({ start, end, ...rest })}
        className="w-full rounded-xl py-2 px-2.5 cursor-pointer flex flex-col gap-y-1 "
        style={{
          minHeight: `${minHeight}px`,
          background: pickRandomColor(),
        }}
      >
        <div className="w-full flex items-center justify-between gap-x-1">
          <p className="text-gray-600 font-medium text-xs line-clamp-1 w-1/2">
            {rest.extendedProps?.customerInfo?.firstName}{" "}
            {rest.extendedProps?.customerInfo?.lastName}
          </p>
          <p className="text-gray-600 font-medium text-end text-xs line-clamp-1 w-1/2">
            {rest.title}
          </p>
        </div>
        <div className="w-full flex items-center justify-between gap-x-1">
          <p className="text-gray-600 font-medium text-xs line-clamp-1 w-1/2 ">
            {rest.extendedProps?.service.name}
          </p>
          <p className="w-[60%] text-end text-xs font-semibold">
            {new Date(start).toLocaleTimeString([], {
              hour: "numeric",
              minute: "2-digit",
            })}{" "}
            -{" "}
            {new Date(end).toLocaleTimeString([], {
              hour: "numeric",
              minute: "2-digit",
            })}
          </p>
        </div>
      </motion.div>
    </div>
  );
};

const MemoizedEventCard = memo(EventCard);

type Props = {
  events: CalendarEvent[];
  onEventClick: (event: CalendarEvent) => void;
  selectedDay: Date;
  dateModifierFunctions: {
    prev(): void;
    next(): void;
    /**
     * @dev this intent should open the calendar modal to pick date manually
     */
    changeDateIntent(): void;
  };
};

const DailyCalendar = ({
  selectedDay,
  dateModifierFunctions,
  events,
  ...props
}: Props) => {
  return (
    <section className="w-full h-fit space-y-10 select-none ">
      <section className="w-full flex gap-x-2 pb-10 md:gap-x-0">
        {/* we give padding here to that business hours can show inline with the calendar */}
        <div className="whitespace-nowrap h-full pt-[42px] flex flex-col gap-y-10 md:w-20 md:pt-24 ">
          {businessHours.map((hour) => (
            <Typography
              key={hour}
              variant={"p"}
              className="text-[#AFAFAF] font-bold text-center !min-h-10 flex flex-col items-center justify-center text-sm !mt-0 md:text-base "
            >
              {hour}
            </Typography>
          ))}
        </div>
        <div className="w-full h-full flex flex-col gap-y-4 items-start flex-grow overflow-x-auto no-scrollbar">
          <div className="min-w-full h-fit flex items-center justify-center gap-x-2 md:gap-x-5 md:pb-8 ">
            <Button
              onTap={dateModifierFunctions.changeDateIntent}
              variant="icon"
              className="p-0 bg-transparent mr-auto"
            >
              <Calendar className="text-[#171717] size-5 md:size-[28px] " />
            </Button>
            <Button
              onTap={dateModifierFunctions.prev}
              variant="icon"
              className="p-0 bg-transparent md:px-2 md:pl-4 md:rounded-l-full md:py-2 md:bg-gray-200 "
            >
              <ChevronLeft className="text-[#171717] size-5 md:size-[28px] " />
            </Button>
            <Typography
              variant={"h3"}
              className="text-inherit font-bold text-xl md:text-5xl"
            >
              {formatDate(selectedDay, "EE, dd MMM yyyy")}
            </Typography>
            <Button
              onTap={dateModifierFunctions.next}
              variant="icon"
              className="p-0 bg-transparent md:px-2 md:pr-4 md:rounded-r-full md:py-2 md:bg-gray-200 mr-auto"
            >
              <ChevronRight className="text-[#171717] size-5 md:size-[28px] " />
            </Button>
          </div>
          {/* no fixed width here unlike weekly and monthly calendars */}
          <div className="w-full relative ">
            <div className="w-full flex-grow flex flex-col gap-y-10 ">
              {businessHours.map((hour) => (
                <div
                  key={hour}
                  className="w-full h-10 flex flex-col justify-center items-center"
                >
                  <div className="w-full h-0.5 bg-subtle-gray " />
                </div>
              ))}
            </div>
            {/* events container */}
            <div className="min-w-full h-full flex-grow absolute left-0 top-0 z-10 flex mt-5 ">
              {/* use selectedDay to render events */}
              {events
                .filter((event) =>
                  areDatesEqualWithoutHours(selectedDay, new Date(event.start))
                )
                .map((event) => (
                  <MemoizedEventCard
                    onClick={() => {
                      props.onEventClick(event);
                    }}
                    key={event.id}
                    event={event}
                  />
                ))}
            </div>
          </div>
        </div>
      </section>
    </section>
  );
};

export default DailyCalendar;
