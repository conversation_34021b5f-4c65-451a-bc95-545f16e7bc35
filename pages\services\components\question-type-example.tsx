import { Input } from "@/components/inputs"
import DatePicker from "@/components/ui/calendar"
import RadioBox from "@/components/ui/radio-box"
import SearchableDropdown from "@/components/ui/searchable-dropdown"
import { hyphenSeparatedLowercase, inlineSwitch, pick } from "@/lib/helpers"
import { FormControl } from "@/src/interfaces/intake-form"
import { useCallback, useState } from "react"

type Props = {
  formControl: FormControl
}

const FIELD_TYPE_MAP = {
  text: Input.Text,
  email: Input.Email,
  textarea: Input.TextArea,
  number: Input.Numeric,
  phone: Input.Phone,
  url: Input.Text
};

const TYPES_WITH_MIN_MAX_VALIDATION = [
  'number'
]

const QuestionTypeExample = (props: Props) => {
  const { type, question, ...formControl } = props.formControl
  const [optionValue, setOptionValue] = useState('')
  const [optionValues, setOptionValues] = useState([] as any)
  const [date, setDate] = useState('')
  const getValue = useCallback(() => {
    const value = formControl.options?.find(opt => opt === optionValue);
    if (value) return { label: value, value: value }
    else return null
  }, [optionValue, formControl.options])
  const render = useCallback(() => {
    if (type in FIELD_TYPE_MAP) {
      const InputType = FIELD_TYPE_MAP[type as keyof typeof FIELD_TYPE_MAP]
      const validation: Record<string, any> = TYPES_WITH_MIN_MAX_VALIDATION
        .includes(type as any)
        ? pick(formControl.validation || {}, 'min', 'max')
        : pick(formControl.validation || {}, 'maxLength', 'minLength')
      return (
        <InputType
          {...validation}
          name=""
          required={formControl.required}
          label={question}
          placeholder={formControl.placeholder}
        />
      )
    }
    return inlineSwitch(type,
      [
        'select',
        <SearchableDropdown
          fullWidth
          label={question}
          value={(() => {
            const value = formControl.options?.find(opt => opt === optionValue);
            if (value) return { label: value, value: value }
            else return null
          })()}
          required={formControl.required}
          placeholder={formControl.placeholder}
          options={formControl.options?.map(opt => ({ label: opt, value: opt })) || []}
          name={hyphenSeparatedLowercase(question)}
          onChange={(option) => {
            setOptionValue(option?.value ?? '')
          }}
        />
      ],
      [
        'radio',
        <RadioBox
          label={question}
          onChange={(option) => {
            setOptionValue(option?.value ?? '')
          }}
          required={formControl.required}
          value={getValue()}
          options={formControl.options?.map(opt => ({ label: opt, value: opt })) || []}
        />
      ],
      [
        'checkbox',
        <RadioBox.Multiple
          label={question}
          onChange={(vals) => {
            setOptionValues(vals)
          }}
          required={formControl.required}
          values={optionValues}
          options={formControl.options?.map(opt => ({ label: opt, value: opt })) || []}
        />
      ],
      [
        'date',
        <DatePicker.FormControl
          label={question}
          onChange={(_, valueAsString) => {
            setDate(valueAsString)
          }}
          {...pick(formControl.validation || {}, 'min', 'max')}
          required={formControl.required}
          value={date as any}
        />
      ]
    )
  }, [props.formControl, optionValue, optionValues, date])
  return (
    <div className="flex flex-col py-2 " >
      {/* a very large inline switch 🤣🤣🤣 */}
      {render()}
    </div>
  )
}

export default QuestionTypeExample
