import clothoidize from "@/lib/clothoidize";
import { getDims, px, round } from "@/lib/helpers";
import {
    Dispatch,
    MutableRefObject,
    SetStateAction,
    useEffect,
    useState,
} from "react";

export type BasePhoneConfig<T = string> = {
  width: T;
  height: T;
  virtualHomeButtonWidth: T;
  clothoidRadius: string;
  deviceBarRatios: {
    top: T;
    bottom: T;
  };
  safeAreaInset: T;
};

export const basePhoneConfig: BasePhoneConfig = {
  width: "",
  height: "",
  virtualHomeButtonWidth: "",
  clothoidRadius: "",
  deviceBarRatios: {
    top: "",
    bottom: "",
  },
  safeAreaInset: "",
};

export interface IphoneConfig extends BasePhoneConfig {}

export const iphoneConfig: IphoneConfig = {
  ...basePhoneConfig,
  ...{},
};

export const useDeviceConfig = <E extends HTMLElement>(
  wrapperRef: MutableRefObject<E | null>,
  configRatios: BaseIphoneConfigRatios,
  fn?: (target: E) => void
) => {
  const [siphoneConfig, setIphoneConfig] =
    useState<BasePhoneConfig>(iphoneConfig);
  setupResizeEffect(
    siphoneConfig,
    setIphoneConfig,
    wrapperRef,
    configRatios,
    fn
  );
  return {
    iphoneConfig: siphoneConfig,
  };
};

type BaseIphoneConfigRatios<T = number> = {
  deviceWidthRatio: T;
  deviceHeightRatio: T;
  virtualHomeButtonRatio: T;
  /**
   * @deprecated use clothoidRadiusRatio instead
   */
  borderRadiusRatio?: T;
  clothoidRadiusRatio: T;
  deviceBarRatios: readonly [T, T];
  safeAreaInsetRatio: T;
};

const setupResizeEffect = <E extends HTMLElement>(
  iphoneConfig: IphoneConfig,
  setIphoneConfig: Dispatch<SetStateAction<IphoneConfig>>,
  wrapperRef: MutableRefObject<E | null>,
  configRatios: BaseIphoneConfigRatios,
  fn?: (target: E) => void
) => {
  const {
    deviceWidthRatio,
    deviceHeightRatio,
    virtualHomeButtonRatio,
    clothoidRadiusRatio,
    deviceBarRatios,
    safeAreaInsetRatio,
  } = configRatios;
  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      const [{ target }] = entries;
      const { width, height } = getDims(getComputedStyle(target!));
      setIphoneConfig({
        width: px(width - round(width * deviceWidthRatio)),
        height: px(height - round(height * deviceHeightRatio)),
        virtualHomeButtonWidth: px(round(width * virtualHomeButtonRatio)),
        clothoidRadius: clothoidize({
          radius: round(width * clothoidRadiusRatio),
          format: "minify",
          precise: 15,
          unit: "px",
        }),
        deviceBarRatios: {
          top: px(round(height * deviceBarRatios[0])),
          bottom: px(round(height * deviceBarRatios[1])),
        },
        safeAreaInset: px(round(height * safeAreaInsetRatio)),
      });
      fn?.(wrapperRef.current!);
    });
    observer.observe(wrapperRef.current!);
    return () => observer.disconnect();
  }, [iphoneConfig]);
};
