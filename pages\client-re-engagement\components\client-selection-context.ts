import { ClientToReEngage } from "@/src/services/analytics.service";
import { createContext, useContext } from "react";

type ClientSelectionContextType = {
  selectedClients: ClientToReEngage[];
  addClient(client: ClientToReEngage): void;
  removeClient(client: ClientToReEngage): void;
};

const ClientSelectionContext = createContext<ClientSelectionContextType | null>(null)

const useClientSelection = () => {
  const context = useContext(ClientSelectionContext)
  if (!context) {
    throw new Error('useClientSelection must be used within a ClientSelectionProvider')
  }
  return context
}

export {
  ClientSelectionContext,
  useClientSelection
}