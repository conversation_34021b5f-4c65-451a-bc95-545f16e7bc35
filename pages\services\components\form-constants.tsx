
import { QuestionType } from '@/src/interfaces/intake-form';
import { Calendar03Icon, Link01Icon, MailEdit01Icon, SmartPhone01Icon, TextIcon } from 'hugeicons-react';
import { HashIcon } from 'lucide-react';
import { ReactNode } from 'react';

export const QUESTION_TYPES: { value: QuestionType; label: string; icon: ReactNode; description: string }[] = [
  { 
    value: 'text', 
    label: 'Text Input', 
    icon: <TextIcon size={18} />,
    description: 'Single line text input'
  },
  { 
    value: 'textarea', 
    label: 'Text Area',
    icon: <TextIcon size={18} />,
    description: 'Multi-line text input'
  },
  { 
    value: 'email', 
    label: 'Email', 
    icon: <MailEdit01Icon size={18} />,
    description: 'Email address with validation'
  },
  { 
    value: 'phone', 
    label: 'Phone', 
    icon: <SmartPhone01Icon size={18} />,
    description: 'Phone number input'
  },
  { 
    value: 'number', 
    label: 'Number', 
    icon: <HashIcon size={18} />,
    description: 'Numeric input'
  },
  { 
    value: 'date', 
    label: 'Date', 
    icon: <Calendar03Icon size={18} />,
    description: 'Date picker'
  },
  { 
    value: 'url', 
    label: 'URL', 
    icon: <Link01Icon size={18} />,
    description: 'Website URL input'
  },
  { 
    value: 'select', 
    label: 'Dropdown', 
    icon: '▼️',
    description: 'Single choice from options'
  },
  { 
    value: 'radio', 
    label: 'Radio Buttons',
    icon: '⚪️',
    description: 'Single choice with radio buttons'
  },
  { 
    value: 'checkbox', 
    label: 'Checkboxes',
    icon: '✔️',
    description: 'Multiple choice checkboxes'
  },
];

export const QUESTION_TYPES_WITH_OPTIONS = ['select', 'checkbox', 'radio']
