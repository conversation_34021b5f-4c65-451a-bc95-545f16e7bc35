import { Typography } from "@/components/typography";
import ClientDistributionMetrics from "./client-distribution-metrics";
import ServicePerformanceMetrics from "./service-performance-metrics";
import FinancialMetrics from "./financial-metrics";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { AnalyticsIntervalProvider } from "./analytics-interval-context";
import { objectKeys } from "@/lib/helpers";

const SchedulingAnalytics = () => {
  const filters = {
    today: "Today",
    weekly: "Weekly",
    monthly: "Monthly",
    yearly: "Yearly",
  } as const;
  const [activeFilter, setActiveFilter] =
    useState<keyof typeof filters>("today");
  return (
    <AnalyticsIntervalProvider
      value={{
        interval: activeFilter,
      }}
    >
      <section className="w-full space-y-8 pb-6">
        <div className="w-full flex flex-col items-start gap-y-0 justify-between md:flex-row md:items-center ">
          <Typography
            variant={"h1"}
            className="font-bold font-Bricolage_Grotesque  "
          >
            Service Analytics
          </Typography>
          {/* this has fixed height on mobile screen so sibling element doesn't keep moving unnecessarily when we run the button click animation */}
          <section className="w-full flex items-center justify-between gap-x-4 stroke-peach fill-none h-[48px] md:h-fit md:w-fit md:justify-normal  ">
            {objectKeys(filters).map((filter, i) => (
              <button
                key={i}
                onClick={() => {
                  setActiveFilter(filter);
                }}
                className={cn(
                  `cursor-pointer text-sm font-medium lg:block transition-all duration-200 ease-[ease] bg-transparent p-0`,
                  {
                    "text-paragraph ": filter !== activeFilter,
                    " text-white shadow-sm shadow-primary-100 bg-primary-500 rounded-full py-1 px-4":
                      filter === activeFilter,
                  }
                )}
              >
                {filters[filter]}
              </button>
            ))}
          </section>
        </div>
        <div className="space-y-6">
          <Typography variant={"h4"} className="!font-bold !font-Satoshi text-xl ">
            Service Performance Metrics
          </Typography>
          <ServicePerformanceMetrics />
        </div>
        <div className="space-y-6">
          <Typography variant={"h4"} className="!font-bold !font-Satoshi text-xl ">
            Financial Metrics
          </Typography>
          <FinancialMetrics />
        </div>
        <div className="space-y-6">
          <Typography variant={"h4"} className="!font-bold !font-Satoshi text-xl ">
            Client Distribution Metrics
          </Typography>
          <ClientDistributionMetrics />
        </div>
      </section>
    </AnalyticsIntervalProvider>
  );
};

Object.assign(
  {},
  {
    ServicePerformanceMetrics,
    FinancialMetrics,
    ClientDistributionMetrics,
  }
);

export default SchedulingAnalytics;
