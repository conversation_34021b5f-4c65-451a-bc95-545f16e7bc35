export class HiddenTextRenderer {
	private static asterisks(input: string, hasSpace?: boolean) {
		return Array.from(input)
			.map((_) => (hasSpace ? "* " : "*"))
			.join("");
	}
	private static removeWhiteSpace(input: string) {
		return input.replace(/ /g, "");
	}
	/**
	 * @description - takes in an input, and returns the first
	 */
	static toNumberHashed(input: string, upUntil: number = 3) {
		input = HiddenTextRenderer.removeWhiteSpace(input);
		const firstSection = input.slice(0, upUntil);
		return `${firstSection}${HiddenTextRenderer.asterisks(input.slice(upUntil))}`;
	}
	static toNameHashed(input: string, upUntil = 2) {
		input = HiddenTextRenderer.removeWhiteSpace(input);
		const firstSection = input.slice(0, upUntil);
		return `${firstSection}${HiddenTextRenderer.asterisks(input.slice(upUntil))}`;
	}
	static toPostCodeHashed(input: string, upUntil = 4) {
		input = HiddenTextRenderer.removeWhiteSpace(input);
		const firstSection = input.slice(0, upUntil);
		return `${firstSection}${HiddenTextRenderer.asterisks(input.slice(upUntil))}`;
	}
	static toEmailHashed(input: string, upUntil = 2) {
		input = HiddenTextRenderer.removeWhiteSpace(input);
		const firstSection = input.slice(0, upUntil);
		const atSignIndex = input.lastIndexOf("@");
		const asteriskedSection = HiddenTextRenderer.asterisks(
			input.slice(upUntil, atSignIndex),
		);
		const asteriskedDomain = input.slice(
			atSignIndex + 1,
			input.lastIndexOf("."),
		);
		const domainType = input.slice(input.lastIndexOf("."));
		return `${firstSection}${asteriskedSection}@${HiddenTextRenderer.asterisks(asteriskedDomain)}${domainType}`;
	}
	/**
	 * @dev blocks words that could give away lead name
	 */
	static toDescriptionHashed(input: string, blockedWords: string[]) {
		let formattedInput = input;
		blockedWords.forEach((word) => {
			const regex = new RegExp(word, "gi");
			formattedInput = formattedInput.replace(
				regex,
				HiddenTextRenderer.asterisks(word),
			);
		});
		return formattedInput;
	}
}

export const name = { jdald: 339302 };

