import { api } from "./api.service";
import {
  TeamType,
  CreateTeamPayload,
  UpdateTeamPayload,
  TeamId,
} from "../interfaces/team";
import { PlanRole } from "../contexts/use-plan-role";

export const GetTeams = async () => {
  const response = await api.get<{
    success: true;
    teams: TeamType[],
    limits: {
      currentCount: number,
      maxAllowed: number,
      plan: PlanRole['fullName']
    }

  }>("/teams");
  return response;
};

export const GetTeam = async (teamId: TeamId) => {
  const response = await api.get<{
    success: true;
    team: TeamType;
  }>(`/teams/${teamId}`);
  return response;
};

export const CreateTeam = async (payload: CreateTeamPayload) => {
  const response = await api.post<{
    success: true;
    team: TeamType;
  }>("/teams", payload);
  return response;
};

export const UpdateTeam = async (
  teamId: TeamId,
  payload: UpdateTeamPayload
) => {
  const response = await api.put<{
    success: true;
    team: TeamType;
  }>(`/teams/${teamId}`, payload);
  return response;
};

export const DeleteTeam = async (teamId: TeamId) => {
  const response = await api.delete<{
    success: true;
    message: string;
  }>(`/teams/${teamId}`);
  return response;
};
