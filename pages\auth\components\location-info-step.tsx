import { Input } from "@/components/inputs";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import { motion } from "framer-motion";
import { StepProps } from "../types";
import { Country, State } from "country-state-city";
import { useState } from "react";

const fields = [
	{
		label: "Postcode",
		name: "postcode",
		type: "text",
	},
] as const;

const FieldTypeMap = {
	text: Input.Text,
	number: Input.Numeric,
};

const LocationInfoStep: React.FC<MakeRequired<StepProps, "values">> = ({
	onChange,
	values,
}) => {
	const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
	return (
		<motion.div
			initial={{ x: 300, opacity: 0 }}
			animate={{ x: 0, opacity: 1 }}
			exit={{ x: -300, opacity: 0 }}
			transition={{ type: "spring", stiffness: 300, damping: 30 }}
			className="w-full flex flex-col gap-y-4"
		>
			{fields.map((field, index) => {
				const InputType = FieldTypeMap[field.type];
				return (
					<InputType
						key={index}
						label={field.label}
						name={field.name}
						value={values[field.name]}
						required
						onChange={onChange}
					/>
				);
			})}
			<Input.Phone
				label="Phone Number"
				name="phone"
				required
				value={values.phone}
				onChange={(value, e) => {
					onChange({
						value: value,
						name: e.currentTarget.name,
					});
				}}
			/>

			<SearchableDropdown
				options={Country.getAllCountries().map((option) => ({
					label: `${option.name}`,
					value: option.name,
					data: option,
				}))}
				className="rounded-3xl my-1"
				inputProps={{
					className: "py-3.5",
				}}
				fullWidth
				placeholder="Select a Country"
				value={{ label: values.country, value: values.country }}
				onChange={(option) => {
					onChange({
						target: { name: "country", value: option?.value || "" },
					} as any);
					setSelectedCountry(option?.data?.isoCode || null);
				}}
			/>
			{values.country && selectedCountry && (
				<SearchableDropdown
					options={State.getStatesOfCountry(selectedCountry).map((option) => ({
						label: option.name,
						value: option.name,
					}))}
					className="rounded-3xl my-1"
					inputProps={{
						className: "py-3.5",
					}}
					fullWidth
					placeholder="Select a Town"
					value={{ label: values.town, value: values.town }}
					onChange={(value) => {
						onChange({
							target: { name: "town", value: value?.label || "" },
						} as any);
					}}
				/>
			)}
			{/* Address Fields in Flex Container */}
			<div className="flex flex-col gap-4">
				<Input.Text
					name="address"
					className="flex-1 "
					onChange={onChange}
					label="Address"
					required
					value={values["address"]}
				/>
				<Input.Text
					name="address_2"
					className="flex-1 "
					onChange={onChange}
					label="Address 2"
					value={values["address_2"]}
				/>
			</div>
		</motion.div>
	);
};

export default LocationInfoStep;
