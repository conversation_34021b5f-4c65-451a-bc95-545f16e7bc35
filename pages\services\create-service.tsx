import {
  CreateService,
  CreateServicePayload,
  GetCategory,
  GetServices,
  VerifyStripeAccount,
} from "~/services/services.service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useFormik } from "formik";
import React, { useMemo, useRef, useState } from "react";
import * as Yup from "yup";
import { useToast } from "~/contexts/hooks/toast";
import CreateCategoryModal from "./components/create-category-modal";
import { Typography } from "@/components/typography";
import { Button, SaveButton } from "@/components/buttons";
import { PlusIcon } from "lucide-react";
import PendingOverlay from "@/components/ui/pending-overlay";
import { Input } from "@/components/inputs";
import Toggle from "@/components/ui/toggle-2";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import { capitalizeFirstWord, inlineSwitch, noop, omit } from "@/lib/helpers";
import Dialog from "@/components/ui/dialog";
import { useNavigate } from "react-router-dom";
import { useModalsBuilder } from "@/lib/modals-builder";
import { Picture } from "@gravity-ui/icons";
import MoreInfo from "@/components/ui/more-info";
import { cn } from "@/lib/utils";
import {
  CreateServiceOptionModal,
} from "./components/service-options-modals";
import { usePlanRole } from "@/src/contexts/use-plan-role";
import useAuthStore from "../auth/components/auth-store";
import { useServiceAddons } from "./components/use-service-add-on";
import { useFormattedPrice } from "@/lib/use-formatted-price";
import { ArrowLeft02Icon, Delete02Icon, Edit02Icon } from "hugeicons-react";

type Props = {};

const initialValues = {
  name: "",
  description: "",
  messageAfterScheduling: "",
  duration: 30,
  price: 0,
  category: {
    name: "",
    _id: "",
  },
  color: "",
  picture: null as File | null,
  access: "public",
  isGroupEvent: false,
  blockExtraTimeBefore: 0,
  blockExtraTimeAfter: 0,
  serviceOptions: null as CreateServicePayload["serviceOptions"],
} as const;

const CreateServicePage: React.FC<Props> = (_props) => {
  const showToast = useToast();
  const queryClient = useQueryClient();
  const [hasPrice, setHasPrice] = useState(false);
  const [hasExtraTime, setHasExtraTime] = useState(false);
  const { serviceAddons } = useServiceAddons()
  const createServiceMutation = useMutation({
    mutationFn: CreateService,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["services"] });
      modalFunctions.openModal("serviceCreated", {});
      formik.resetForm();
    },
    onError: (error: ApiError) => {
      showToast("error", error?.response?.data?.error || error?.message);
    },
  });
  const { data: categories = [] } = useQuery({
    queryFn: async () => (await GetCategory()).data.categories,
    queryKey: ["categories"],
  });
  const [hasStripeConnected, setHasStripeConnected] = useState(false);
  const verifyStripeAccountMutation = useMutation({
    mutationFn: VerifyStripeAccount,
    onSuccess: () => {
      showToast("success", "Stripe account verified", {
        description: "You can now create services with prices",
      });
      setHasStripeConnected(true)
    },
    onError: (_error: ApiError) => {
      showToast("error", "Stripe not connected", {
        description: "Connect Stripe in the billing settings page",
        duration: 7500
      });
      setHasStripeConnected(false)
    },
  });

  const { initialState: { userSettings } } = useAuthStore()
  const currency = useMemo(() => (userSettings?.billingSettings?.preferredCurrency || 'usd').toUpperCase(), [userSettings?.billingSettings])
  const formik = useFormik({
    initialValues: initialValues,
    validationSchema: Yup.object({
      name: Yup.string().required("Service name is required"),
      duration: Yup.number().min(0).required("Duration is required"),
      price: Yup.number().when([], {
        is: () => hasPrice && hasStripeConnected,
        then: (schema) => schema.min(2, `Price must be at least 2 ${currency} `),
        otherwise: (schema) => schema.notRequired().nullable(),
      }),
      category: Yup.object({
        name: Yup.string().required("Category name is required"),
        _id: Yup.string().required("Category id is required"),
      }).required("Category is required"),
    }),
    validateOnMount: true,
    onSubmit: noop,
  });
  // if user selects it should have a price, we verify if they have a stripe account connected, if they don't, we show a toast, if they do, alright, we continue
  const handlePriceToggle = async (shouldHavePrice: boolean) => {
    if (shouldHavePrice) verifyStripeAccountMutation.mutateAsync();
  };
  const createServiceHandler = async () => {
    if (formik.values.price > 0)
      if (hasStripeConnected === false)
        showToast('info', "Service will be created without price", {
          description: "Connect Stripe in billing settings page to enable price",
          duration: 7500
        })
    formik.validateForm().then((val) => {
      const errors = Object.values(val);
      if (errors.length === 0) {
        const { values } = formik;
        createServiceMutation.mutateAsync({
          ...omit(values, "category"),
          category: values.category._id,
        });
      } else
        errors.forEach((error, i) =>
          setTimeout(() => {
            showToast("error", "Invalid Field", {
              description: typeof error === "object" ? error.name : error,
            });
          }, i * 500),
        );
    });
  };
  const { modals, modalFunctions } = useModalsBuilder({
    createCategory: {
      open: false,
    },
    serviceCreated: {
      open: false,
    },
    createServiceOption: {
      open: false,
    },
    editServiceOption: {
      open: false,
      data: null as {
        name: string;
        description?: string;
        price: number;
        optionIndex: number;
      } | null,
    },
  });
  const { data: services = [] } = useQuery({
    queryKey: ["services"],
    queryFn: async () => (await GetServices()).data.Services,
  });
  const {
    planRole: { shortName: planShortName },
  } = usePlanRole();
  const canCreateServiceWithOptionsMemo = useMemo(() => {
    if (planShortName === "none" || planShortName === "free") {
      // check if two services in the array have options
      const servicesWithOptions = services.filter((service) => {
        return service.serviceOptions?.length;
      });
      const servicesWithOptionsCount = servicesWithOptions.length;
      return servicesWithOptionsCount < 2;
    } else return true;
  }, [planShortName, services]);
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);
  const { formatPrice } = useFormattedPrice()
  function attemptDeletePicture() {
    formik.setFieldValue("picture", null);
  }
  function attemptReplacePicture() {
    inputRef.current?.click();
  }
  return (
    <section className="w-full h-full max-w-2xl mx-auto flex flex-col gap-y-10 pb-32">
      <div className="w-full flex items-center gap-x-4">
        <Button
          variant="icon"
          onTap={() => navigate("/scheduling/services")}
          className="p-1.5"
        >
          <ArrowLeft02Icon width={20} height={20} />
        </Button>
        <Typography
          variant={"h1"}
          className="font-bold font-Bricolage_Grotesque  "
        >
          Create Service
        </Typography>
      </div>
      <form
        onSubmit={formik.handleSubmit}
        className="w-full flex flex-col gap-y-6"
      >
        {/* Name */}
        <Input.Text
          label="Name"
          name="name"
          value={formik.values.name}
          onChange={formik.handleChange}
          required
        />
        {/* Description */}
        <div className="flex flex-col gap-y-6">
          <Input.TextArea
            label="Description"
            name="description"
            value={formik.values.description}
            onChange={formik.handleChange}
          />
          <Input.TextArea
            label="Scheduling Message"
            name="messageAfterScheduling"
            value={formik.values.messageAfterScheduling}
            onChange={formik.handleChange}
          />
        </div>
        {/* Duration */}
        <Input.Numeric
          label="Duration (minutes)"
          name="duration"
          required
          defaultValue={formik.values.duration}
          onChange={formik.handleChange}
        />
        <div className="space-y-3">
          <div className="w-full flex items-center justify-between ">
            <Typography className=" text-paragraph">
              Block Extra time
            </Typography>
            <Toggle
              checked={hasExtraTime}
              onChange={(checked) => setHasExtraTime(checked)}
            />
          </div>
          {hasExtraTime && (
            <div className="w-full flex flex-col gap-y-3">
              <Input.Numeric
                label="Block time before appointment (minutes)"
                name="blockExtraTimeBefore"
                onChange={({ currentTarget: { name, value } }) => {
                  formik.setFieldValue(name, value ? Number(value) : 0)
                }}
              />
              <Input.Numeric
                label="Block time after appointment (minutes)"
                name="blockExtraTimeAfter"
                onChange={({ currentTarget: { name, value } }) => {
                  formik.setFieldValue(name, value ? Number(value) : 0)
                }}
              />
            </div>
          )}
        </div>
        {/* Price */}
        <div className="space-y-3">
          <div className="w-full flex items-center justify-between ">
            <Typography className=" text-paragraph">Enable Price</Typography>
            <Toggle
              checked={hasPrice}
              onChange={(checked) => {
                handlePriceToggle(checked);
                setHasPrice(checked);
                if (!checked) formik.setFieldValue("price", 0);
              }}
              disabled={verifyStripeAccountMutation.isPending}
            />
          </div>
          {hasPrice && (
            <Input.Numeric
              label={`Price (${inlineSwitch(userSettings?.billingSettings?.preferredCurrency, ['usd', 'USD'], ['gbp', 'GBP'], { default: 'USD' })})`}
              name="price"
              disabled={hasStripeConnected === false}
              defaultValue={formik.values.price}
              onChange={(e) => {
                const { value } = e.target;
                formik.setFieldValue("price", parseInt(value || '0'));
              }}
            />
          )}
        </div>
        {/* Category */}
        <div className="w-full flex flex-col gap-y-2  ">
          <Typography className=" text-paragraph">Category</Typography>
          <div className="w-full grid grid-cols-[1fr_auto] gap-x-2 ">
            <SearchableDropdown
              fullWidth
              value={{
                value: formik.values.category._id,
                label: formik.values.category.name,
              }}
              options={
                categories?.map((category) => ({
                  label: category?.name,
                  value: category?._id,
                })) || []
              }
              name="category"
              onChange={(option) => {
                if (!option) return;
                formik.setFieldValue("category", {
                  name: option.label,
                  _id: option.value,
                });
              }}
            />
            <Button
              type="button"
              variant="icon"
              className="px-4"
              onTap={() => modalFunctions.openModal("createCategory", {})}
            >
              <PlusIcon />
            </Button>
          </div>
        </div>
        <div className="w-full flex flex-col gap-y-1  ">
          <div className="w-full flex items-center gap-x-2 mb-2">
            <Typography className=" text-paragraph">
              Service Add-ons{" "}
              <span className="px-2 py-0.5 text-sky-600 bg-sky-50 rounded-md text-sm">
                optional
              </span>
            </Typography>
            <MoreInfo
              content="Service Add-ons (Add additional services or variations that
                customers can choose alongside the main service. Each option can
                have its own price and description)"
            />
          </div>
          {serviceAddons?.map((addon, index) => {
            const addonIsInPayload = formik.values.serviceOptions?.includes(addon._id)
            return (
              <div
                key={index}
                className="flex items-start gap-x-2 group mb-2 last:mb-0"
              >
                <div className="w-full px-3 py-2 rounded-xl bg-stone-100">
                  <div className="flex items-center justify-between text-paragraph font-Bricolage_Grotesque">
                    <Typography className="">{addon.name}</Typography>
                    <Typography className={cn("text-paragraph ")}>
                      {formatPrice(addon.price)}
                    </Typography>
                  </div>
                  <div className="w-full flex justify-between items-center text-gray-400 font-normal text-xs gap-x-2">
                    <Typography className="font-normal flex-grow line-clamp-1">
                      {addon.description ?? "No description"}
                    </Typography>
                    <Button
                      onTap={() => {
                        if (!addonIsInPayload) {
                          // do check for blocking of more services 
                          if (canCreateServiceWithOptionsMemo)
                            formik.setFieldValue('serviceOptions', [...(formik.values.serviceOptions || []), addon._id])
                          else {
                            showToast("info", "Upgrade plan to add options", {
                              description:
                                "You can add options to 2 services on the free plan.",
                              action: {
                                title: "Upgrade",
                                onClick() {
                                  navigate("/profile/billing");
                                  return true;
                                },
                              },
                              duration: 7000,
                            });
                          }
                        } else {
                          formik.setFieldValue('serviceOptions',
                            formik.values.serviceOptions?.filter(v => v !== addon._id) || []
                          )
                        }
                      }
                      }
                      className={cn("px-6", {
                        "bg-green-400": !addonIsInPayload,
                        "bg-red-600": addonIsInPayload
                      })}
                    >
                      {addonIsInPayload
                        ? "Remove"
                        : "Attach"}{" "}
                    </Button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        <div className="w-full flex items-start justify-between ">
          <Typography className=" text-paragraph">
            Color
          </Typography>
          <Input.ColorPicker name="color" value={formik.values.color} onChange={formik.handleChange} />
        </div>
        <div className="w-full flex flex-col gap-y-2 ">
          <Typography className=" text-paragraph">Picture</Typography>
          <div className="cursor-pointer w-full active:scale-[0.95] ">
            {!formik.values.picture ? (
              <div
                onClick={() => inputRef.current?.click()}
                className="w-full border border-dashed border-gray-400 rounded-3xl flex items-center justify-center aspect-[5/3] active:scale-[0.95] "
              >
                <Picture className="text-gray-300 size-20 " />
              </div>
            ) : (
              <div className="relative w-full group rounded-3xl aspect-[5/3] ">
                <img
                  className="w-full h-full object-cover rounded-3xl "
                  src={
                    formik.values.picture
                      ? URL.createObjectURL(formik.values.picture)
                      : ""
                  }
                />
                {innerWidth <= 480 ? (
                  <div className="w-fit absolute right-2 bottom-2 flex items-center justify-center gap-x-2">
                    <Button
                      variant="icon"
                      className="rounded-full"
                      onTap={attemptDeletePicture}
                    >
                      <Delete02Icon size={18} />
                    </Button>
                    <Button
                      variant="icon"
                      className="rounded-full"
                      onTap={attemptReplacePicture}
                    >
                      <Edit02Icon size={18} />
                    </Button>
                  </div>
                ) : (
                  <div className="w-full aspect-[5/3] absolute z-20 top-0 bg-white/20 backdrop-blur-lg rounded-3xl hidden items-center justify-center gap-x-3 group-hover:flex ">
                    <Button onTap={attemptDeletePicture}>Delete</Button>
                    <Button onTap={attemptReplacePicture}>Replace</Button>
                  </div>
                )}
              </div>
            )}
          </div>
          <input
            type="file"
            name="picture"
            className="hidden"
            accept="image/jpeg, image/png"
            ref={inputRef}
            onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
              const files = event.target.files;
              if (files && files.length > 0) {
                formik.setFieldValue("picture", files[0] || null);
              }
            }}
          />
          {formik.values.picture ? (
            <MoreInfo.Static
              className="text-sm hidden md:flex"
              content="Hover on the image to see more options"
            />
          ) : null}
        </div>
        <div className="w-full flex flex-col gap-y-2  ">
          <Typography className=" text-paragraph">Access</Typography>
          <SearchableDropdown
            fullWidth
            value={{
              value: formik.values.access,
              label: capitalizeFirstWord(formik.values.access),
            }}
            options={[
              { label: "Private", value: "private" },
              { label: "Public", value: "public" },
            ]}
            name="access"
            onChange={(option) => {
              if (!option) return;
              formik.setFieldValue("access", option.value);
            }}
          />
        </div>
        <div className="w-full flex items-center justify-between">
          <Typography className=" text-paragraph">Group Event</Typography>
          <Toggle
            name="isGroupEvent"
            checked={formik.values.isGroupEvent}
            onChange={(checked) =>
              formik.setFieldValue("isGroupEvent", checked)
            }
          />
        </div>
        {/* Submit Button */}
        <SaveButton
          title="Create Service"
          loading={createServiceMutation.isPending}
          disabled={createServiceMutation.isPending || !formik.isValid}
          onTap={createServiceHandler}
        >
        </SaveButton>
      </form>
      <CreateCategoryModal
        open={modals.createCategory.open}
        onClose={() => modalFunctions.closeModal("createCategory")}
      />
      <CreateServiceOptionModal
        open={modals.createServiceOption.open}
        onClose={() => modalFunctions.closeModal("createServiceOption")}
        onSave={(option) => {
          formik.setFieldValue("serviceOptions", [
            ...(formik.values.serviceOptions || []),
            option,
          ]);
          modalFunctions.closeModal("createServiceOption");
        }}
        hasStripeConnected={hasStripeConnected === false}
      />
      <Dialog
        title="Service created"
        description="Your service has been created successfully, return to the services page"
        open={modals.serviceCreated.open}
        onClose={() => modalFunctions.closeModal("serviceCreated")}
        action={{
          title: "OK",
          onConfirm: () => {
            modalFunctions.closeModal("serviceCreated");
            navigate("/scheduling/services");
          },
        }}
      />
      <PendingOverlay isPending={createServiceMutation.isPending} />
    </section>
  );
};

export default CreateServicePage;
