name: Staging Deployment

on:
  push:
    branches:
      - main

env:
  VITE_BACKEND_URL: ${{ secrets.VITE_BACKEND_URL }}
  VITE_FRONTEND_URL: ${{ secrets.VITE_FRONTEND_URL }}
  VITE_STRIPE_PUBLIC_KEY: ${{ secrets.VITE_STRIPE_PUBLIC_KEY }}
  VITE_STRIPE_CLIENT_ID: ${{ secrets.VITE_STRIPE_CLIENT_ID }}
  VITE_NODE_ENV: production
  SERVER_PORT: 5173
  APP_NAME: puzzle-piece-frontend
  DEPLOY_PATH: /var/www/html/app.puzzlepiecesolutions.com

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'

      - name: Clear npm cache
        run: npm cache clean --force

      - name: Install dependencies
        run: |
          npm install --legacy-peer-deps

      - name: Build application
        run: npm run build
        env:
          VITE_NODE_ENV: ${{ env.VITE_NODE_ENV }}
          VITE_STRIPE_PUBLIC_KEY: ${{ env.VITE_STRIPE_PUBLIC_KEY }}
          VITE_STRIPE_CLIENT_ID: ${{ env.VITE_STRIPE_CLIENT_ID }}
          VITE_BACKEND_URL: ${{ env.VITE_BACKEND_URL }}
          VITE_FRONTEND_URL: ${{ env.VITE_FRONTEND_URL }}

      - name: Verify build output
        run: |
          if [ ! -d "dist" ]; then
            echo "❌ Build failed - dist directory not found"
            exit 1
          fi
          echo "✅ Build successful - dist directory exists"
          ls -la dist/

      - name: Deploy build folder to server
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "dist/*"
          target: "${{ env.DEPLOY_PATH }}"
          strip_components: 1

      - name: Setup and start static file server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USERNAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd ${{ env.DEPLOY_PATH }}
            
            # Install PM2 globally if not installed
            if ! command -v pm2 &> /dev/null; then
              sudo npm install -g pm2
            fi
            
            # Stop existing PM2 process if running
            pm2 stop ${{ env.APP_NAME }} || true
            pm2 delete ${{ env.APP_NAME }} || true
            
            # Install Node.js 20 if not already installed
            if ! command -v node &> /dev/null || [[ $(node -v | cut -d'.' -f1 | cut -d'v' -f2) -lt 20 ]]; then
              curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
              sudo apt-get install -y nodejs
            fi
            
            # Install serve globally if not installed
            if ! command -v serve &> /dev/null; then
              sudo npm install -g serve
            fi
            
            # Set correct permissions
            sudo chown -R $USER:$USER ${{ env.DEPLOY_PATH }}
            sudo chmod -R 755 ${{ env.DEPLOY_PATH }}
            
            # Start with PM2 directly (no config file needed)
            VITE_NODE_ENV=${{ env.VITE_NODE_ENV }} pm2 start serve \
              --name "${{ env.APP_NAME }}" \
              -- -s . -l ${{ env.SERVER_PORT }}
            
            # Save PM2 configuration
            pm2 save
            
            # Setup PM2 to start on boot
            sudo env PATH=$PATH:/usr/bin pm2 startup systemd -u $USER --hp $HOME || true
            
            # Wait for server to start
            sleep 10
            
            # Check if server is running
            if curl -f http://localhost:${{ env.SERVER_PORT }} > /dev/null 2>&1; then
              echo "✅ Static file server started successfully on port ${{ env.SERVER_PORT }}"
              echo "Serving built files from: ${{ env.DEPLOY_PATH }}"
              pm2 status
              echo "Files in deployment directory:"
              ls -la ${{ env.DEPLOY_PATH }}/
            else
              echo "❌ Failed to start static file server"
              pm2 logs ${{ env.APP_NAME }} --lines 20
              exit 1
            fi
            
            # Verify nginx is still working
            sudo nginx -t && echo "✅ Nginx configuration is valid"
