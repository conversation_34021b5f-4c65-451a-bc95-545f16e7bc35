import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/src/contexts/hooks/toast";
import * as SchedulingRulesService from "@/src/services/scheduling-rules.service";
import {
  ServiceId,
  CreateSchedulingRulesPayload,
  UpdateSchedulingRulesPayload,
} from "@/src/interfaces/scheduling-rules";

export const useSchedulingRules = (serviceId?: ServiceId) => {
  const queryClient = useQueryClient();
  const showToast = useToast();

  const schedulingRulesQuery = useQuery({
    queryKey: ["schedulingRules", serviceId],
    queryFn: async () => {
      if (!serviceId) throw new Error("Service ID is required");
      return (await SchedulingRulesService.GetSchedulingRules(serviceId)).data;
    },
    enabled: !!serviceId,
    refetchOnWindowFocus: false,
  });

  const allSchedulingRulesQuery = useQuery({
    queryKey: ["schedulingRules"],
    queryFn: async () =>
      (await SchedulingRulesService.GetAllSchedulingRules()).data
        .schedulingRules,
    refetchOnWindowFocus: false,
  });

  const createSchedulingRulesMutation = useMutation({
    mutationFn: ({
      serviceId,
      payload,
    }: {
      serviceId: ServiceId;
      payload: CreateSchedulingRulesPayload;
    }) => SchedulingRulesService.CreateSchedulingRules(serviceId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["schedulingRules"] });
      showToast("success", "Scheduling rules created successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to create scheduling rules",
      );
    },
  });

  const updateSchedulingRulesMutation = useMutation({
    mutationFn: ({
      serviceId,
      payload,
    }: {
      serviceId: ServiceId;
      payload: UpdateSchedulingRulesPayload;
    }) => SchedulingRulesService.UpdateSchedulingRules(serviceId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["schedulingRules"] });
      showToast("success", "Scheduling rules updated successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to update scheduling rules",
      );
    },
  });

  return {
    schedulingRulesQuery,
    allSchedulingRulesQuery,
    createSchedulingRulesMutation,
    updateSchedulingRulesMutation,
  };
};

export const usePublicSchedulingPolicy = (
  username?: string,
  serviceName?: string,
) => {
  const publicPolicyQuery = useQuery({
    queryKey: ["publicSchedulingPolicy", username, serviceName],
    queryFn: async () => {
      if (!username || !serviceName)
        throw new Error("Username and service name are required");
      return (
        await SchedulingRulesService.GetPublicSchedulingPolicy(
          username,
          serviceName,
        )
      ).data.policy;
    },
    enabled: !!username && !!serviceName,
  });

  return {
    publicPolicyQuery,
  };
};
