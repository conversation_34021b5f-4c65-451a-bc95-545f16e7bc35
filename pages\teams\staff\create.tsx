import React from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Button, SaveButton } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import PendingOverlay from "@/components/ui/pending-overlay";
import { ArrowLeft02Icon } from "hugeicons-react";
import { useStaff } from "../hooks/use-staff";
import { useTeam } from "../hooks/use-team";
import { CreateStaffPayload } from "@/src/interfaces/staff";
import { useToast } from "~/contexts/hooks/toast";
import { noop } from "@/lib/helpers";
import SearchableDropdown from "@/components/ui/searchable-dropdown";

const initialValues: CreateStaffPayload = {
  teamId: "",
  name: "",
  email: "",
  phone: "",
  role: "",
  color: "#3B82F6",
  buffer: 15,
  travelTime: 30,
  capacity: 8,
};

const teamIdQueryParam = 'teamId'


const CreateStaff: React.FC = () => {
  const [sParams] = useSearchParams()
  const selectedTeamId = sParams.get(teamIdQueryParam) || ''
  const navigate = useNavigate();
  const showToast = useToast();
  const { teamsQuery } = useTeam();
  const { createStaffMutation } = useStaff();

  const formik = useFormik({
    initialValues: {
      ...initialValues,
      teamId: selectedTeamId
    },
    validationSchema: Yup.object({
      teamId: Yup.string().required("Team is required"),
      name: Yup.string()
        .required("Name is required")
        .min(2, "Name must be at least 2 characters")
        .max(100, "Name must be less than 100 characters"),
      email: Yup.string()
        .email("Invalid email format")
        .max(255, "Email must be less than 255 characters"),
      phone: Yup.string()
        .max(20, "Phone must be less than 20 characters"),
      role: Yup.string()
        .required("Role is required")
        .max(100, "Role must be less than 100 characters"),
      color: Yup.string()
        .matches(/^#[0-9A-F]{6}$/i, "Invalid color format"),
      buffer: Yup.number()
        .min(0, "Buffer must be 0 or greater")
        .max(120, "Buffer must be 120 minutes or less"),
      travelTime: Yup.number()
        .min(0, "Travel time must be 0 or greater")
        .max(240, "Travel time must be 240 minutes or less"),
      capacity: Yup.number()
        .required()
        .min(1, "Capacity must be at least 1")
        .max(50, "Capacity must be 50 or less"),
    }),
    validateOnMount: true,
    onSubmit: noop,
  });

  const createStaffHandler = async () => {
    formik.validateForm().then((val) => {
      const errors = Object.values(val);
      if (errors.length === 0) {
        const { values } = formik;
        createStaffMutation.mutateAsync(values).then(() => {
          navigate(`/teams/staff?${teamIdQueryParam}=${formik.values.teamId}`);
          formik.resetForm();
        });
      } else {
        showToast("error", "Invalid Field", {
          description: errors[0],
        });
      }
    });
  };

  return (
    <section className="w-full h-full max-w-2xl mx-auto flex flex-col gap-y-10 pb-32">
      <div className="w-full flex justify-between items-center">
        <div className="flex items-start gap-x-4">
          <Button
            variant="icon"
            onTap={() =>
              navigate(`/teams/staff?${teamIdQueryParam}=${selectedTeamId}`)
            }
            className="p-1.5"
          >
            <ArrowLeft02Icon width={20} height={20} />
          </Button>
          <Typography
            variant={"h1"}
            className="font-bold font-Bricolage_Grotesque"
          >
            Add Staff Member
          </Typography>
        </div>
      </div>

      <div
        className="w-full flex flex-col gap-y-6"
      >
        {/* Team Selection */}
        <div className="w-full flex flex-col gap-y-2">
          <SearchableDropdown
            fullWidth
            label="Team"
            required
            value={formik.values.teamId}
            options={teamsQuery.data?.teams?.map(t => ({
              label: t.name,
              value: t._id
            })) || []}
            name=""
            className="max-w-60 "
            placeholder="Select a Team"
            onChange={(option) => {
              if (!option) return;
              formik.setFieldValue('teamId', option.value)
            }}
          />
        </div>

        <div className="grid grid-cols-2 gap-x-5 gap-y-4 md:grid-cols-2">
          <Input.Text
            label="Full Name"
            name="name"
            value={formik.values.name}
            onChange={formik.handleChange}
            required
          />

          <Input.Email
            label="Email Address"
            name="email"
            value={formik.values.email}
            onChange={formik.handleChange}
          />

          <Input.Phone
            label="Phone Number"
            name="phone"
            value={formik.values.phone}
            onChange={(value, e) => {
              formik.setFieldValue(e.currentTarget.name, value);
            }}
          />

          <Input.Text
            label="Role/Position"
            name="role"
            value={formik.values.role}
            onChange={formik.handleChange}
            required
          />
        </div>

        <div className="w-full flex items-start justify-between ">
          <div className="flex flex-col gap-y-1">
            <Typography className=" text-paragraph">
              Display Color
            </Typography>
          </div>
          {/* Display Settings */}
          <Input.ColorPicker
            name="color"
            value={formik.values.color}
            onChange={formik.handleChange}
          />
        </div>

        {/* Schedule Settings */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input.Numeric
            label="Buffer Time (minutes)"
            name="buffer"
            value={formik.values.buffer}
            onChange={formik.handleChange}
          />

          <Input.Numeric
            label="Travel Time (minutes)"
            name="travelTime"
            value={formik.values.travelTime}
            onChange={formik.handleChange}
          />

          <Input.Numeric
            label="Daily Capacity"
            name="capacity"
            value={formik.values.capacity}
            onChange={formik.handleChange}
          />
        </div>

        {/* Submit Button */}
        <SaveButton
          title="Create Staff Member"
          loading={createStaffMutation.isPending}
          disabled={createStaffMutation.isPending || !formik.isValid}
          onTap={createStaffHandler}
        />
      </div>

      <PendingOverlay isPending={createStaffMutation.isPending} />
    </section >
  );
};

export default CreateStaff;
