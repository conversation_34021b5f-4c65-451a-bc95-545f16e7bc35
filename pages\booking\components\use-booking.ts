import { useToast } from "@/src/contexts/hooks/toast";
import { BookingServiceResponse, CreateExternalBooking, CreateInternalBooking, GetAvailableTimeslot, GetAvailableWorkDays, GetExternalBookingService, GetInternalBookingService } from "@/src/services/booking.service";
import { GetPublicIntakeForm } from "@/src/services/intake-form.service";
import { useMutation, useQuery } from "@tanstack/react-query";

const useExternalBookingImpl = (payload: {
  username: Nullable<string | undefined>,
  urlName: Nullable<string | undefined>,
  serviceData: {
    user: BookingServiceResponse['userId'],
    service: BookingServiceResponse['Service']
  } | undefined
  dateTimeState: { date: string, timeZone: string }
}) => {
  const { username, urlName, dateTimeState, serviceData } = payload;
  const showToast = useToast()
  const intakeFormQuery = useQuery({
    queryFn: async () => {
      const form = (await GetPublicIntakeForm({ username: username!, serviceUrlName: urlName! })).data.intakeForm
      return form;
    },
    queryKey: ['intake-form', username, urlName],
  });
  const timeslotsQuery = useQuery({
    queryKey: ["timeslots", serviceData?.user._id, dateTimeState.date, dateTimeState.timeZone],
    queryFn: async () => {
      const data = (
        await GetAvailableTimeslot({
          userId: serviceData?.user._id ?? "",
          payload: {
            timezone: dateTimeState.timeZone,
            date: dateTimeState.date,
          },
        })
      ).data;
      return data.availableTimeSlots;
    },
    // when timezone and date are set, fetch time slots
    enabled:
      Boolean(dateTimeState.date) && Boolean(dateTimeState.timeZone),
  });
  const workdaysQuery = useQuery({
    queryKey: ["workdays", serviceData?.user._id],
    queryFn: async () =>
      (await GetAvailableWorkDays(serviceData?.user._id || "")).data.workdays,
    enabled: Boolean(serviceData),
  });
  const createBookingMut = useMutation({
    mutationFn: CreateExternalBooking,
    onSuccess: () => {
      showToast("success", "Booking Successful");
    },
    onError: (error: ApiError) =>
      showToast("error", error.response?.data.error || "Booking failed", {
        description: error.response?.data.details || "Booking failed"
      }),
  });

  return {
    intakeFormQuery,
    workdaysQuery,
    timeslotsQuery,
    createBookingMut
  }
}

export const useExternalBooking = Object.assign(useExternalBookingImpl, {
  /** 
   * @dev we have to fetch services separately 😭😭, so that we can pass it to bookingFormik and other places
   * */
  getService: (payload: {
    username: Nullable<string | undefined>,
    urlName: Nullable<string | undefined>,
  }) => {
    const { username, urlName } = payload
    const serviceQuery = useQuery({
      queryFn: async () => {
        const service = (await GetExternalBookingService({ username: username!, urlName: urlName! })).data;
        return { service: service.Service, user: service.userId, success: true };
      },
      queryKey: ["external-booking", username, urlName],
      enabled: Boolean(username) && Boolean(urlName),
      retry: 2,
      refetchOnWindowFocus: false
    });
    return {
      serviceQuery
    }
  }
})

const useInternalBookingImpl = (payload: {
  username: Nullable<string | undefined>,
  urlName: Nullable<string | undefined>,
  serviceData: {
    user: BookingServiceResponse['userId'],
    service: BookingServiceResponse['Service']
  } | undefined
  dateTimeState: { date: string, timeZone: string }
}) => {
  const { serviceData, dateTimeState } = payload;
  const showToast = useToast()
  const timeslotsQuery = useQuery({
    queryKey: ["timeslots", 'internal-booking', serviceData?.user._id, dateTimeState.date, dateTimeState.timeZone],
    queryFn: async () => {
      const data = (
        await GetAvailableTimeslot({
          userId: serviceData?.user._id ?? "",
          payload: {
            timezone: dateTimeState.timeZone,
            date: dateTimeState.date,
          },
        })
      ).data;
      return data.availableTimeSlots;
    },
    // when timezone and date are set, fetch time slots
    enabled:
      Boolean(dateTimeState.date) && Boolean(dateTimeState.timeZone),
  });
  const workdaysQuery = useQuery({
    queryKey: ["workdays", 'internal-booking', serviceData?.user._id],
    queryFn: async () =>
      (await GetAvailableWorkDays(serviceData?.user._id || "")).data.workdays,
    enabled: Boolean(serviceData),
  });
  const createBookingMut = useMutation({
    mutationFn: CreateInternalBooking,
    onSuccess: () => {
      showToast("success", "Booking Successful");
    },
    onError: (error: ApiError) =>
      showToast("error", error.response?.data.error || "Booking failed"),
  });

  return {
    workdaysQuery,
    timeslotsQuery,
    createBookingMut
  }
}


export const useInternalBooking = Object.assign(useInternalBookingImpl, {
  /** 
   * @dev we have to fetch services separately 😭😭, so that we can pass it to bookingFormik and other places
   * */
  getService: (payload: {
    username: Nullable<string | undefined>,
    urlName: Nullable<string | undefined>,
  }) => {
    const { username, urlName } = payload
    const serviceQuery = useQuery({
      queryFn: async () => {
        const service = (await GetInternalBookingService({ username: username!, urlName: urlName! })).data;
        return { service: service.Service, user: service.userId, success: true };
      },
      queryKey: ["internal-booking", username, urlName],
      enabled: Boolean(username) && Boolean(urlName),
      retry: 2,
      refetchOnWindowFocus: false
    });
    return {
      serviceQuery
    }
  }
})

