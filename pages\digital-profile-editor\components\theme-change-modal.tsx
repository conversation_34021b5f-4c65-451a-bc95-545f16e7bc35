import { But<PERSON> } from "@/components/buttons";
import Modal from "@/components/ui/modal";
import { motion } from "framer-motion";
import { X } from "lucide-react";
import { useState } from "react";
import { Typography } from "@/components/typography";
import { ITheme } from "../types";
import Tabs from "@/components/ui/tabs";
import { ThemeCollection } from "../utils/theme-collection";
import { omit, truncateText } from "@/lib/helpers";
import { useDigitalProfileContext } from "../hooks/use-digital-profile";
import { cn } from "@/lib/utils";

type Props = {
  open: boolean;
  onClose: VoidFunction
  onSave(theme: ITheme): void
};

/**
 * @dev has access to digital profile context
 * */
export default function ThemeChangeModal({ open, onClose, onSave }: Props) {
  const { profile: { theme } } = useDigitalProfileContext()
  const [selectedTheme, setSelectedTheme] = useState(
    theme
  );

  return (
    <Modal open={open} onClose={onClose}>
      <Modal.Body
        className={`min-h-fit min-w-80 h-screen w-screen flex flex-col gap-y-6 items-center rounded-none bg-white max-[768px]:border-none md:h-fit md:w-fit md:justify-center md:items-center md:rounded-[32px] md:max-w-[480px] md:min-h-[540px] md:px-6 md:py-6`}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h3"} className="font-bold ">
            Select Theme
          </Typography>
          <Button
            variant="icon"
            onTap={onClose}
            className="p-1 !rounded-full"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Form */}
        <div className="space-y-2 w-full overflow-y-auto thin-scrollbar pr-2 max-h-[80%] md:max-h-[400px] ">
          <Tabs
            defaultValue="curated"
            className="w-full"
          >
            <Tabs.List className="grid w-fit max-w-[320px] mt-2 mb-10 grid-cols-2 md:flex-grow ">
              <Tabs.Trigger
                value="curated"
                className="px-5 flex items-center justify-center gap-x-2"
              >
                Curated
              </Tabs.Trigger>
              <Tabs.Trigger
                value="customizable"
                className=" px-5 flex items-center justify-center gap-x-2"
              >
                Customizable
              </Tabs.Trigger>
            </Tabs.List>
            <Tabs.Content
              className="mt-2 focus:outline-none active:outline-none w-full"
              value={"curated"}
            >
              <div className="space-y-2">
                <div className="w-full grid grid-cols-2 gap-3 justify-between md:grid-cols-3 ">
                  {ThemeCollection.getAllThemes().map((theme, index) => {
                    const KEYS_TO_OMIT = ['hoverEffect', 'gradient', 'background'] as const
                    const themeButtonStyleObj = omit(theme.buttonStyles[0].style, ...KEYS_TO_OMIT)
                    const { gradient, background } = theme.buttonStyles[0].style
                    return (
                      <div
                        key={index}
                        className="flex flex-col gap-y-3 items-center">
                        <motion.div
                          onTap={() => {
                            setSelectedTheme(
                              theme
                            );
                            // this will break
                            onSave(theme)
                          }}
                          whileHover={{ scale: 0.8 }}
                          whileTap={{ scale: 0.9, rotate: "25deg" }}
                          className={cn("w-full border relative border-gray-100 h-[180px] cursor-pointer rounded-[24px] pl-4 pt-5 overflow-hidden ", {
                            "border-primary-500": selectedTheme?.name === theme.name
                          })}
                          style={{
                            background: theme.backgroundValue,
                          }}
                        >
                          <Typography variant={'h2'} className="font-bold text-5xl " style={{
                            color: theme.colors.text
                          }} >
                            Aa
                          </Typography>
                          <button
                            style={{
                              ...themeButtonStyleObj,
                              background: gradient ?? background
                            }}
                            className="absolute w-[200%] h-10 left-4 bottom-4 "
                          />
                        </motion.div>
                        <Typography>{truncateText(theme.name, innerWidth <= 768 ? 20 : 30)}</Typography>
                      </div>
                    )
                  })}
                </div>
              </div>
            </Tabs.Content>
            <Tabs.Content
              className="mt-2 focus:outline-none active:outline-none w-full text-center"
              value={"customizable"}
            >
              <Typography>No customizable themes yet</Typography>
            </Tabs.Content>
          </Tabs>
        </div>

        <div className="w-full mt-auto">
          <Button
            onTap={() => {
              onClose();
            }}
            className="w-full py-3 !rounded-full font-medium "
          >
            Save
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
}
