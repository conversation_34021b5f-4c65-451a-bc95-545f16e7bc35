import CircularLoader from "@/components/ui/circular-loader";
import { inlineSwitch, pick } from "@/lib/helpers";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ReactNode, useEffect, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import {
  CheckSubscriptionStatus,
  GetUserDetails,
  PaymentDetails,
} from "~/services/auth.service";
import useAuthStore from "./components/auth-store";
import { PlanRoleProvider } from "@/src/contexts/use-plan-role";
import { GetCurrentUserSubscriptionPlan } from "@/src/services/payment.service";
import { useToast } from "@/src/contexts/hooks/toast";
interface ProtectedRouteProps {
  children: ReactNode;
}

const AuthProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const {
    initialState: { isAuthenticated, user },
    setUser,
    changeAuthenticationState
  } = useAuthStore();
  const location = useLocation();
  const showToast = useToast()
  const { error, isFetching: isAuthenticatedFetching } = useQuery({
    queryKey: ["user-details-login"],
    queryFn: async () => {
      const response = await GetUserDetails();
      if ([200, 201].includes(response?.status)) {
        setUser(
          {
            ...pick(response.data.data, "calendarSettings", "user", "userSettings"),
          }
        );
        return response?.data;
      } else {
        changeAuthenticationState(false)
        showToast('error', 'User not found')
        return null
      }
    },
    retry: false,
    refetchOnWindowFocus: false,
  });
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetails | null>(
    null
  );
  const { data: userCurrentPlan } = useQuery({
    queryKey: ["user-current-plan"],
    queryFn: async () => (await GetCurrentUserSubscriptionPlan()).data.plan,
    refetchOnWindowFocus: false,
  });
  const paymentDetailsMutation = useMutation({
    mutationFn: CheckSubscriptionStatus,
    onSuccess() { },
    onError(error: ApiError<PaymentDetails>) {
      if (error.response?.status === 402) {
        setPaymentDetails(error.response.data);
      }
    },
    retry(_, error) {
      // we dont need a 200 here, we need to see a 402 here.
      // if status is 402, don't retry again
      if (error.response?.status === 402) return false;
      return true;
    },
  });
  useEffect(() => {
    if (isAuthenticated && user) {
      if (user.role === "admin") {
        return undefined;
      } else if (user.role === "user") paymentDetailsMutation.mutateAsync();
    }
  }, [isAuthenticated, user?.role]);
  if (paymentDetailsMutation.isPending || isAuthenticatedFetching)
    return (
      <div className="flex items-center justify-center h-screen w-full">
        <CircularLoader />
      </div>
    );
  if (paymentDetails) {
    return (
      <Navigate
        to={`/payment?redirect=${location.pathname}`}
        state={paymentDetails}
        replace
      />
    );
  }
  if (!isAuthenticated || error) {
    localStorage.clear();
    return (
      <Navigate
        to={`/signin?redirect=${location.pathname}`}
        state={{ from: location }}
        replace
      />
    );
  }

  return (
    <PlanRoleProvider
      value={{
        planRole: inlineSwitch(user?.role,
          ['admin', { fullName: 'Free', id: '', shortName: 'free' }],
          ['user', ({
            fullName: 'Premium', // (userCurrentPlan?.name as 'Free') || 'Free',
            id: userCurrentPlan?._id as string,
            shortName: 'premium', //(userCurrentPlan?.name.toLowerCase() as 'free') || 'free'
          })],
          { default: { fullName: 'Free', id: '', shortName: 'free' } }
        ),
        isPremium() {
          return inlineSwitch(user?.role,
            ['admin', false],
            ['user', userCurrentPlan?.name ? (userCurrentPlan?.name.toLowerCase() === 'premium') : false],
            { default: false }
          )
        },
      }}
    >
      {children}
    </PlanRoleProvider>
  );
};

export default AuthProtectedRoute;
