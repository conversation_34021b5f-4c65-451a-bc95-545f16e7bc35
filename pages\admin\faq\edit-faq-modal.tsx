import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";
import { isUnwantedInArray } from "@/lib/helpers";
import { AdminNS } from "@/src/services/admin.service";
import { X } from "lucide-react";

type Props = {
  open: boolean;
  onClose: () => void;
  onSubmit: () => void;
  onChange: (name: keyof AdminNS.CreateFaqPayload, value: string) => void;
  values: AdminNS.CreateFaqPayload;
};

const fields = [
  {
    label: "Category",
    name: "category",
    type: "text",
  },
  {
    label: "Question",
    name: "question",
    type: "text",
  },
] as const;

const FieldTypeMap = {
  text: Input.Text,
};

const EditFaqModal = (props: Props) => {
  return (
    <Modal open={props.open} onClose={props.onClose}>
      <Modal.Body
        className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Edit faq
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="w-full grid gap-y-3">
          {fields.map((field) => {
            const InputType = FieldTypeMap[field.type];
            return (
              <InputType
                {...field}
                required
                onChange={({ target: { value } }) => {
                  props.onChange(field.name, value);
                }}
                value={props.values?.[field.name]}
                className="w-full"
                key={field.label}
              />
            );
          })}
          <Input.TextArea
            label="Answer"
            name="answer"
            required
            onChange={({ target: { value } }) => {
              props.onChange("answer", value);
            }}
            value={props.values?.["answer"]}
            className="w-full"
          />
        </div>
        <div className="w-full flex justify-between items-center mt-6">
          <Button
            disabled={isUnwantedInArray(Object.values(props.values || {}))}
            onTap={props.onSubmit}
            className="w-full"
          >
            Edit Faq
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default EditFaqModal;
