import CheckBox from "@/components/ui/checkbox";
import { ClientToReEngage } from "@/src/services/analytics.service";
import { formatDate } from "date-fns";
import { motion } from "framer-motion";
import { useClientSelection } from "./client-selection-context";
import { useMemo } from "react";

interface Props {
  client: ClientToReEngage;
  index: number;
  onClick: (client: ClientToReEngage) => void;
}

const ClientToEngageInformation = ({ client, index, ...props }: Props) => {
  const { selectedClients, addClient, removeClient } = useClientSelection();
  const checkedMemo = useMemo(() => {
    return selectedClients.some((c) => c.client._id === client.client._id);
  }, [selectedClients]);
  return (
    <motion.div
      key={index}
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      whileTap={{ backgroundColor: "#f9f9f9" }}
      className="w-full grid grid-cols-[auto_80px] cursor-pointer origin-top-left md:grid-cols-[auto_80px]"
    >
      {/* onClick interferes with the checkbox, so we're using a div to wrap up all elements before the check box */}
      <div
        onClick={() => props.onClick(client)}
        className="grid cursor-pointer grid-cols-[repeat(5,minmax(120px,160px))] md:grid-cols-[repeat(5,200px)]"
      >
        <div className="flex items-center line-clamp-1 px-4 py-2.5 border-b text-sm">
          {client.client.name}
        </div>
        <div className="flex items-center gap-x-1 line-clamp-1 px-4 py-2.5 border-b text-sm whitespace-nowrap">
          <span className="font-bold whitespace-nowrap">
            {client.daysSinceLastAppointment} days
          </span>{" "}
          &bull;
          <span>
            {client.lastAppointment
              ? formatDate(client.lastAppointment, "MMM, dd yyyy")
              : "--"}
          </span>
        </div>
        <div className="flex items-center line-clamp-1 px-4 py-2.5 border-b text-sm font-bold whitespace-nowrap">
          {client.averageReturnInterval} days
        </div>
        <div className="flex items-center line-clamp-1 px-4 py-2.5 border-b text-sm">
          {client.client.email}
        </div>
        <div className="flex items-center line-clamp-1 px-4 py-2.5 border-b text-sm">
          {client.client.phone}
        </div>
      </div>
      <div className="flex items-center justify-center line-clamp-1 px-4 py-2.5 border-b text-sm cursor-default">
        <CheckBox
          checked={checkedMemo}
          onChange={(checked) => {
            if (checked) addClient(client);
            else removeClient(client);
          }}
        />
      </div>
    </motion.div>
  );
};

export default ClientToEngageInformation;
