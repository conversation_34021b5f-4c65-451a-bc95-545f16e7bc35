import React from "react";

interface TimePickerProps {
  icon: React.ReactNode;
  time: string;
  setTime: (newTime: string) => void;
}

const TimePicker: React.FC<TimePickerProps> = ({ icon, time, setTime }) => {
  return (
    <div className="flex flex-col mb-4">
      {icon}
      <input
        name="startTime"
        type="time"
        value={time}
        onChange={({ target: { value } }) => setTime(value!)}
        className="flex-1"
      />
    </div>
  );
};

export default TimePicker;
