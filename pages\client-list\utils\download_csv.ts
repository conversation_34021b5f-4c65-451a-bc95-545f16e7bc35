import { TClient } from "~/services/clients.service";
import { arrayToCsv } from "@/lib/helpers";
import { formatDate } from "date-fns";

export const downloadAsCSV =
  (
    downloadElRef: React.RefObject<HTMLAnchorElement>,
    selectedClients: TClient[]
  ) =>
  () => {
    if (!selectedClients) return;
    const selectedClients2DArray = selectedClients.map(
      ({ clientName, clientEmail, phoneNumber, lastAppointmentDate }) => {
        return [
          clientName,
          clientEmail,
          phoneNumber,
          lastAppointmentDate
            ? formatDate(lastAppointmentDate, "L/dd/yyy")
            : "--",
        ];
      }
    );
    selectedClients2DArray.unshift([
      "Full Name",
      "Email",
      "Phone Number",
      "Appointment Date",
    ]);
    const csvContent = arrayToCsv(selectedClients2DArray);
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    downloadElRef.current!.href = url;
    downloadElRef.current!.setAttribute("download", "clients.csv");
    downloadElRef.current?.click();
  };
