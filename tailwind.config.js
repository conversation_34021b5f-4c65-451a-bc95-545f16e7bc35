import AnimateConfig from 'tailwindcss-animate'

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "index.html",
    "./src/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
  ],
  darkMode: ["class"],
  theme: {
    extend: {
      fontFamily: {
        Satoshi: ["Satoshi", "sans-serif"],
        Aeonik_Fono: ["Aeonik_Fono", "sans-serif"],
        Bricolage_Grotesque: ["Bricolage_Grotesque", "sans-serif"],
        Work_Sans: ["Work_Sans", "sans-serif"],
      },
      screens: {
        amd: "840px",
        bsm: "480px",
        bxl: "1200px",
      },
      colors: {
        primary: {
          50: "#F9C5D7",
          100: "#F8B3CA",
          200: "#F48DB0",
          300: "#F06897",
          400: "#ED437D",
          500: "#E91E63",
          600: "#BC124C",
          700: "#890D38",
          800: "#560823",
          900: "#23030E",
          950: "#0A0104",
          default: "#E91E63",
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        chart: {
          1: "hsl(173 58% 39%)",
          2: `hsl(12 76% 61%)`,
          3: "hsl(197 37% 24%)",
          4: "hsl(43 74% 66%)",
          5: "hsl(27 87% 67%)",
        },
        sidebar_bg: "#F2F2F2",
        sidebar_bg_focused: "#272A2F",
        sidebar_item_shadow: "#16191E",
        sidebar_text: "#5B5F62",
        sidebar_text_focused: "#D3D7DA",
        sidebar_trails: "#262A2D",
        "subtle-gray": "#F9F9F9",
        "dark-gray": "#9DA0B6",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
      },
      textColor: {
        paragraph: "#2F2F2F",
        subtext: "#A5A8AE",
      },
      borderColor: {
        paragraph: "#2F2F2F",
        subtext: "#A5A8AE",
      },
      fill: {
        paragraph: "#2F2F2F",
        subtext: "#A5A8AE",
      },
      stroke: {
        paragraph: "#2F2F2F",
        subtext: "#A5A8AE",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [AnimateConfig],
};
