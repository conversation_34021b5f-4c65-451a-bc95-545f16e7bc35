import React from "react";
import { FormikProps } from "formik";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import Toggle from "@/components/ui/toggle-2";
import { CreateSchedulingRulesPayload } from "@/src/interfaces/scheduling-rules";

interface ReschedulingSettingsProps {
  formik: FormikProps<CreateSchedulingRulesPayload>;
}

const ReschedulingSettings: React.FC<ReschedulingSettingsProps> = ({ formik }) => {
  return (
    <div className="bg-white rounded-2xl border p-6 space-y-4">
      <div className="space-y-1">
        <Typography variant="h4" className="font-semibold">
          Rescheduling Policy
        </Typography>
        <Typography className="text-sm text-gray-600">
          Control when and how clients can reschedule appointments
        </Typography>
      </div>

      <div className="space-y-3">
        <Toggle
          label="Allow rescheduling"
          checked={formik.values.allowRescheduling}
          onChange={(checked) => formik.setFieldValue("allowRescheduling", checked)}
        />
        <Typography className="text-xs text-gray-500 -mt-1">
          When disabled, clients cannot reschedule and must cancel/rebook instead
        </Typography>
      </div>

      {formik.values.allowRescheduling && (
        <div className="space-y-2">
          <Input.Numeric
            label="Rescheduling cutoff (hours before appointment)"
            name="rescheduleCutoff"
            value={formik.values.rescheduleCutoff}
            onChange={formik.handleChange}
            placeholder="2"
            min={0}
          />
          <Typography className="text-xs text-gray-500">
            Clients can reschedule up to this many hours before their appointment
          </Typography>

          <div className="bg-green-50 border border-green-200 rounded-lg p-3 mt-3">
            <Typography className="text-green-800 text-sm">
              <strong>Tip:</strong> Set a shorter cutoff than cancellation to encourage rescheduling over canceling.
            </Typography>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReschedulingSettings;
