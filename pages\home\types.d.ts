export type MenuItem = {
  title: string;
  icon: React.ReactNode;
  className?: string;
  link?: string;

  /**
   * for users that are not in the premium plan we send them to the /profile/billing and ask them to upgrade their plan
   */
  planTag?: () => (
    ({
      tag: 'Premium' | (string & {}),
      link: '/profile/billing' | (string & {})
    }) | null)
  onClick?: () => void;
  toShow?: () => boolean;
  /**
   * @dev if a menu item, at level 1 has the `planTag` property, we dont show the chevron and the sub items;
   */
  subItems?: MenuItem[];
}

export interface ProfileMenuItem extends Pick<MenuItem, 'icon' | 'link' | 'title'> {
  subItems: MenuItem[]
}
