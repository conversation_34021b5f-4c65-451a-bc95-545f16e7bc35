import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { <PERSON><PERSON>, SaveButton } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import PendingOverlay from "@/components/ui/pending-overlay";
import CircularLoader from "@/components/ui/circular-loader";
import Toggle from "@/components/ui/toggle-2";
import { ArrowLeft02Icon } from "hugeicons-react";
import { useTeam } from "./hooks/use-team";
import { useToast } from "~/contexts/hooks/toast";
import { noop } from "@/lib/helpers";
import { useQuery } from "@tanstack/react-query";
import * as TeamService from "@/src/services/team.service";

const EditTeam: React.FC = () => {
  const navigate = useNavigate();
  const { teamId } = useParams<{ teamId: string }>();
  const showToast = useToast();
  const { updateTeamMutation } = useTeam();

  const teamQuery = useQuery({
    queryKey: ["team", teamId],
    queryFn: async () => (await TeamService.GetTeam(teamId!)).data.team,
    enabled: !!teamId,
  });

  const team = teamQuery.data;

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      name: team?.name || "",
      description: team?.description || "",
      isActive: team?.isActive || true,
    },
    validationSchema: Yup.object({
      name: Yup.string()
        .required("Team name is required")
        .min(2, "Team name must be at least 2 characters")
        .max(100, "Team name must be less than 100 characters"),
      description: Yup.string()
        .required("Team description is required")
        .max(500, "Description must be less than 500 characters"),
      isActive: Yup.boolean(),
    }),
    validateOnMount: true,
    onSubmit: noop,
  });

  const updateTeamHandler = async () => {
    if (!teamId) return;

    formik.validateForm().then((val) => {
      const errors = Object.values(val);
      if (errors.length === 0) {
        const { values } = formik;
        const payloadData = {
          teamId,
          payload: values,
        };
        updateTeamMutation.mutateAsync(payloadData).then(() => {
          navigate("/teams");
        });
      } else {
        showToast("error", "Invalid Field", {
          description: errors[0]
        });
      }
    });
  };

  if (teamQuery.isLoading) {
    return (
      <div className="flex w-full justify-center py-10">
        <CircularLoader />
      </div>
    );
  }

  if (teamQuery.isError || !team) {
    return (
      <section className="w-full h-full max-w-2xl mx-auto flex flex-col gap-y-10 pb-32">
        <div className="w-full flex justify-between items-center">
          <div className="flex items-center gap-x-4">
            <Button
              variant="icon"
              onTap={() => navigate("/teams")}
              className="p-1.5"
            >
              <ArrowLeft02Icon width={20} height={20} />
            </Button>
            <Typography
              variant={"h1"}
              className="font-bold font-Bricolage_Grotesque"
            >
              Edit Team
            </Typography>
          </div>
        </div>
        <div className="text-center py-10">
          <Typography className="text-red-500">
            Failed to load team data. Please try again.
          </Typography>
        </div>
      </section>
    );
  }

  return (
    <section className="w-full h-full max-w-2xl mx-auto flex flex-col gap-y-10 pb-32">
      <div className="w-full flex justify-between items-center">
        <div className="flex items-start gap-x-4">
          <Button
            variant="icon"
            onTap={() => navigate("/teams")}
            className="p-1.5 mt-2"
          >
            <ArrowLeft02Icon width={20} height={20} />
          </Button>
          <Typography
            variant={"h1"}
            className="font-bold font-Bricolage_Grotesque"
          >
            Edit Team: {team.name}
          </Typography>
        </div>
      </div>

      <form
        onSubmit={formik.handleSubmit}
        className="w-full flex flex-col gap-y-6"
      >
        {/* Team Name */}
        <Input.Text
          label="Team Name"
          name="name"
          value={formik.values.name}
          onChange={formik.handleChange}
          required
        />

        {/* Description */}
        <Input.TextArea
          label="Description"
          name="description"
          required
          value={formik.values.description}
          onChange={formik.handleChange}
        />

        {/* Active Status */}
        <div className="w-full flex items-center justify-between">
          <Typography className="text-paragraph">Team is Active</Typography>
          <Toggle
            checked={formik.values.isActive}
            onChange={(checked) =>
              formik.setFieldValue("isActive", checked)
            }
          />
        </div>

        {/* Submit Button */}
        <SaveButton
          title="Edit Team"
          loading={updateTeamMutation.isPending}
          disabled={updateTeamMutation.isPending || teamQuery.isLoading || !formik.isValid}
          onTap={updateTeamHandler}
        />
      </form>

      <PendingOverlay isPending={updateTeamMutation.isPending} />
    </section>
  );
};

export default EditTeam;
