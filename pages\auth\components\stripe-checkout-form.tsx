import { Button } from "@/components/buttons";
import { useToast } from "@/src/contexts/hooks/toast";
import {
  PaymentElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { useState } from "react";
import CircularLoader from "@/components/ui/circular-loader";

/**
 * @dev this reads from PaymentContext
 * @note this component handles it's error and failure states with a toast.
 */
const StripeCheckoutForm = () => {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);
  const showToast = useToast();
  const handleSubmit = async () => {
    if (!stripe || !elements) return;
    setLoading(true);
    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: `http://localhost:5173/onboarding/success`,
      },
    });

    if (error)
      showToast("error", error.message || "Payment failed!", {
        description: "Fill in your details and try again.",
      });
    else showToast("success", "Payment successful!");
    setLoading(false);
  };

  return (
    <div
      onSubmit={handleSubmit}
      className="h-full w-full flex flex-col gap-y-8"
    >
      <PaymentElement />
      <div className="!pb-6">
        <Button
          onTap={handleSubmit}
          disabled={!stripe || !elements}
          className="w-full text-white py-2 flex items-center justify-center "
        >
          {loading ? (
            <CircularLoader color="white" className="mx-auto" />
          ) : (
            "Subscribe"
          )}
        </Button>
      </div>
    </div>
  );
};

export default StripeCheckoutForm;
