import { api } from "./api.service";

type ServiceId = string;

export interface BaseEmailStage {
  _id: string;
  attachedServices: Array<ServiceId>;
  name: string;
  emailSubject: string;
  emailTemplate: string;
}

export interface ReminderEmailStage extends BaseEmailStage {
  hoursBeforeAppointment: number;
}

export interface InitialConfirmationEmailStage extends BaseEmailStage { }

export interface FollowUpEmailStage extends BaseEmailStage { }

// this will be for a mongodb model / collection
export interface EmailSettings {
  initialConfirmation: InitialConfirmationEmailStage[];
  followUps: FollowUpEmailStage[];
  reminders: ReminderEmailStage[];
}

function prependApiUrl<T extends `/${string}`>(str: T): `/email-settings${T}` {
  return `/email-settings${str}`;
}

export const GetEmailSettings = async () => {
  try {
    const response = await api.get<{ emailSettings: EmailSettings }>(
      prependApiUrl("/"),
    );
    return response;
    //const res = await FakeData.asyncFaked({ emailSettings: FakeData.emailSettings })
    //return res.data
  } catch (error) {
    console.error("email-settings error:", error);
    throw error;
  }
};

export const UploadImage = async (formData: FormData) => {
  try {
    const response = await api.post<{ imageUrl: string }>(
      prependApiUrl("/image-upload"),
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      }
    );
    return response;
  } catch (error) {
    console.error("email-settings error:", error);
    throw error;
  }
};


export interface InitialConfirmationStagePayload
  extends Omit<InitialConfirmationEmailStage, "_id"> { }

export const InitialConfirmationStage = {
  Create: async (payload: InitialConfirmationStagePayload) => {
    try {
      const response = await api.post<EmailSettings>(
        prependApiUrl("/initial-confirmation"),
        payload,
      );
      return response;
    } catch (error) {
      console.error("email-settings ini-c error:", error);
      throw error;
    }
  },
  Update: async (payload: {
    payload: InitialConfirmationStagePayload;
    id: string;
  }) => {
    try {
      const response = await api.put<EmailSettings>(
        prependApiUrl(`/initial-confirmation/${payload.id}`),
        payload.payload,
      );
      return response;
    } catch (error) {
      console.error("email-settings ini-c update error:", error);
      throw error;
    }
  },
  Delete: async (id: string) => {
    try {
      const response = await api.delete(
        prependApiUrl(`/initial-confirmation/${id}`),
      );
      return response;
    } catch (error) {
      console.error("email-settings ini-c delete error:", error);
      throw error;
    }
  },
};

export interface FollowUpStagePayload extends Omit<FollowUpEmailStage, "_id"> { }

export const FollowUpStage = {
  Create: async (payload: FollowUpStagePayload) => {
    try {
      const response = await api.post<EmailSettings>(
        prependApiUrl("/follow-up"),
        payload,
      );
      return response;
    } catch (error) {
      console.error("email-settings error:", error);
      throw error;
    }
  },
  Update: async (payload: { id: string; payload: FollowUpStagePayload }) => {
    try {
      const response = await api.put<EmailSettings>(
        prependApiUrl(`/follow-up/${payload.id}`),
        payload.payload,
      );
      return response;
    } catch (error) {
      console.error("email-settings follow-up update error:", error);
      throw error;
    }
  },
  Delete: async (id: string) => {
    try {
      const response = await api.delete(prependApiUrl(`/follow-up/${id}`));
      return response;
    } catch (error) {
      console.error("email-settings follow-up delete error:", error);
      throw error;
    }
  },
};

export interface ReminderStagePayload extends Omit<ReminderEmailStage, "_id"> { }

export const ReminderStage = {
  Create: async (payload: ReminderStagePayload) => {
    try {
      const response = await api.post<EmailSettings>(
        prependApiUrl("/reminder"),
        payload,
      );
      return response;
    } catch (error) {
      console.error("email-settings reminder error:", error);
      throw error;
    }
  },
  Update: async (payload: { id: string; payload: ReminderStagePayload }) => {
    try {
      const response = await api.put<EmailSettings>(
        prependApiUrl(`/reminder/${payload.id}`),
        payload.payload,
      );
      return response;
    } catch (error) {
      console.error("email-settings reminder update error:", error);
      throw error;
    }
  },
  Delete: async (id: string) => {
    try {
      const response = await api.delete(prependApiUrl(`/reminder/${id}`));
      return response;
    } catch (error) {
      console.error("email-settings reminder delete error:", error);
      throw error;
    }
  },
};
