import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import BasicTooltip from "@/components/ui/basic-tooltip";
import { Card } from "@/components/ui/card";
import { arrayToCsv } from "@/lib/helpers";
import { Plus } from "@gravity-ui/icons";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { motion } from "framer-motion";
import { FileDown } from "lucide-react";
import { useRef } from "react";
import { useNavigate } from "react-router-dom";
import { GetPurchasedLeads, PurchasedCartLead } from "~/services/leads.service";

const tableColumns = [
  "Industry",
  "No of Leads Purchased",
  "Credits Spent",
  "Date & Time of Purchase",
];

const ViewLeadPurchases = () => {
  const { data: purchases, isFetching } = useQuery({
    queryFn: GetPurchasedLeads,
    queryKey: ["purchases"],
    refetchOnWindowFocus: false,
    enabled: true,
  });
  const getPurchasedLeadsRows = (purchase: PurchasedCartLead) => {
    const rows = [
      purchase.leads
        .reduce((acc, curr) => {
          const industry = curr.industry;
          return acc
            ? acc.includes(industry)
              ? acc
              : `${acc}, ${industry}`
            : industry;
        }, "")
        .toString(),
      purchase.leads.length.toString(),
      purchase.leads
        .reduce((acc, curr) => acc + curr.requiredCredit, 0)
        .toString(),
      format(purchase.purchaseDate, "dd MMMM yyyy, hh:mm a"),
    ];
    return rows;
  };
  const navigate = useNavigate();
  const downloadElRef = useRef<HTMLAnchorElement>(null);
  const downloadAsCSV = (index: number) => {
    if (!purchases?.data) return;
    const leadsPurchased2DArray = purchases?.data[index].leads.map((lead) => {
      const row = [
        lead.industry,
        lead.name,
        lead.address,
        lead.telephone_numbers.join(" | "),
        lead.email_addresses.join(" | "),
        lead.town,
        lead.postcode,
        lead.requiredCredit.toString(),
      ];
      return row;
    });
    leadsPurchased2DArray.unshift([
      "Industry",
      "Name",
      "Address",
      "Telephone Numbers",
      "Email Addresses",
      "Town",
      "Postcode",
      "Required Credits",
    ]);
    const csvContent = arrayToCsv(leadsPurchased2DArray);

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    downloadElRef.current!.href = url;
    downloadElRef.current!.setAttribute("download", "purchased_leads.csv");
    downloadElRef.current?.click();
  };

  return (
    <section className="w-full">
      <div className="flex justify-between mb-4">
        <Typography
          variant={"h1"}
          className="font-bold font-Bricolage_Grotesque  "
        >
          Purchase History
        </Typography>
        <a ref={downloadElRef} className="hidden"></a>
      </div>
      <Card className="rounded-lg !px-0 overflow-x-auto no-scrollbar md:max-w-fit mt-4 text-black">
        <div className="w-fit flex flex-col ">
          <div className="flex-grow rounded-t-lg bg-subtle-gray grid grid-cols-[64px_repeat(3,210px)_240px] md:grid-cols-[64px_repeat(4,200px)]">
            <div className="text-center text-sm"></div>
            {tableColumns.map((key, index) => (
              <div key={index} className="px-1 pl-4 pb-2.5 text-sm ">
                <div className="w-full pt-2.5 rounded-lg whitespace-nowrap ">
                  {key}
                </div>
              </div>
            ))}
          </div>
          {purchases?.data.map((purchase, index) => (
            <motion.div
              key={index}
              initial={{ y: -20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              className="w-full grid grid-cols-[64px_repeat(3,210px)_240px] md:grid-cols-[64px_repeat(4,200px)]"
            >
              <div className="flex items-center justify-center ">
                <BasicTooltip content="Download CSV">
                  <button
                    onClick={() => downloadAsCSV(index)}
                    className="bg-transparent !p-0 text-gray-500 "
                  >
                    <FileDown />
                  </button>
                </BasicTooltip>
              </div>
              {getPurchasedLeadsRows(purchase).map((row, index) => (
                <div
                  key={index}
                  className="py-3 whitespace-nowrap line-clamp-1 text-sm flex items-center px-4 !text-black"
                >
                  {row}
                </div>
              ))}
            </motion.div>
          ))}
        </div>
      </Card>
      {!isFetching && purchases?.data.length === 0 && (
        <div className="flex mt-4 flex-col items-center justify-center text-center">
          <div className="mb-6">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Plus className="w-8 h-8 text-gray-400" />
            </div>
          </div>

          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No purchases found
          </h3>

          <p className="text-gray-500 mb-6 max-w-sm">
            Get started by purchasing a lead with your credits
          </p>

          <Button
            onTap={() => navigate("/leads")}
            className="font-medium py-3 px-6 flex items-center gap-2"
          >
            Purchase Lead
          </Button>
        </div>
      )}
    </section>
  );
};

export default ViewLeadPurchases;
