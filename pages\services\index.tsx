import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion";
import CircularLoader from "@/components/ui/circular-loader";
import Dialog from "@/components/ui/dialog";
import Menu from "@/components/ui/menu";
import PendingOverlay from "@/components/ui/pending-overlay";
import { pick } from "@/lib/helpers";
import { useModalsBuilder } from "@/lib/modals-builder";
import { useFormattedPrice } from "@/lib/use-formatted-price";
import { cn } from "@/lib/utils";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Briefcase01Icon, Delete02Icon, Edit02Icon, FolderAddIcon, Link04Icon, MoreVerticalIcon, PlusSignCircleIcon, PlusSignIcon, Settings01Icon, SmartPhone01Icon } from "hugeicons-react";
import React, { useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useToast } from "~/contexts/hooks/toast";
import {
    DeleteCategory,
    GetCategories,
    GetServices,
    ServiceAddon,
    UpdateCategory,
    VerifyStripeAccount
} from "~/services/services.service";
import useAuthStore from "../auth/components/auth-store";
import ButtonTooltip from "./components/button-tooltip";
import CreateCategoryModal, { EditCategoryModal } from "./components/create-category-modal";
import Service from "./components/service";
import { CreateServiceOptionModal, EditServiceOptionModal } from "./components/service-options-modals";
import { useServiceAddons } from "./components/use-service-add-on";
import CreateService from "./create-service";
import EditServicePage from "./edit-service";
import PreviewServices from "./preview-services";

const Services: React.FC = () => {
  // Group services by category
  const { data: services = [], isLoading: isFetching } = useQuery({
    queryKey: ["services"],
    queryFn: async () => (await GetServices()).data.Services,
  });
  const { data: categories = [] } = useQuery({
    queryFn: async () => (await GetCategories()).data.categories,
    queryKey: ["categories"],
  });
  const mutations = {
    deleteCategory: useMutation({
      mutationFn: DeleteCategory,
      onSuccess() {
        showToast('success', 'Category deleted successfully')
      },
      onError(error: ApiError) {
        showToast('error', (error.response?.data.error || 'Something went wrong'))
      }
    }),
    updateCategory: useMutation({
      mutationFn: UpdateCategory,
      onSuccess() {
        showToast('success', 'Category updated successfully')
      },
      onError(error: ApiError) {
        showToast('error', (error.response?.data.error || 'Something went wrong'))
      }
    })
  }
  const groupedServicesMemo = useMemo(() => {
    // show all categories inside here
    const categoryMap = categories.map(category => ({
      ...pick(category, 'name', '_id'),
      services: services.filter(service => service.category._id === category._id)
    }))
    return categoryMap
  }, [services, categories]);
  const navigate = useNavigate();
  const showToast = useToast();
  const { initialState: { user } } = useAuthStore()
  const { modals, modalFunctions } = useModalsBuilder({
    category: { open: false },
    editCategory: { open: false, id: null as string | null, name: '' },
    deleteCategory: {
      open: false,
      props: null as {
        action: {
          title?: string;
          onConfirm: () => void;
        };
        title: string;
        description?: string;
      } | null
    },
    serviceAddon: { open: false }
  })
  const menuFunctions = {
    createService: () => {
      navigate("/scheduling/services/create");
    },
    createCategory: () => {
      modalFunctions.openModal("category", {});
    },
    viewPreview: () => {
      navigate(`/services/${user?.username}`);
    },
    editPreview: () => {
      navigate("/scheduling/services/preview");
    },
    copyServicesLink: async () => {
      const servicesLink = import.meta.env.VITE_FRONTEND_URL + "/services/" + user?.username;
      navigator.clipboard
        .writeText(servicesLink)
        .then(() => {
          showToast('info', 'Link copied to clipboard')
        })
        .catch((err) => {
          if (err.name === "NotAllowedError") {
            showToast(
              "error",
              "Please allow clipboard access in your browser settings.",
            );
          } else {
            showToast("error", "Failed to copy to clipboard");
          }
        })
    }
  };
  const queryClient = useQueryClient()
  if (isFetching) return (
    <div className="flex w-full justify-center py-10">
      <CircularLoader />
    </div>
  )
  return (
    <section className="w-full px-2 flex flex-col gap-y-5 ">
      <div className="flex items-center justify-between">
        <Typography
          variant={"h1"}
          className="font-bold font-Bricolage_Grotesque  "
        >
          Services
        </Typography>
        <Menu transformOrigin="top-right">
          <Menu.Trigger className="cursor-pointer">
            <MoreVerticalIcon size={28} strokeWidth={3} />
          </Menu.Trigger>
          <Menu.Content className="h-fit focus:outline-none min-w-fit !min-h-0 right-0 whitespace-nowrap">
            <Menu.Item
              className="flex items-center gap-x-4 pr-5"
              onClick={menuFunctions.createService}
            >
              Create Service <PlusSignCircleIcon width={20} height={20} />
            </Menu.Item>
            <Menu.Item
              className="flex items-center gap-x-4 pr-5"
              onClick={menuFunctions.createCategory}
            >
              Create Category
              <FolderAddIcon width={20} height={20} />
            </Menu.Item>
            <Menu.Item
              className="flex items-center gap-x-4 pr-5 text-sky-500"
              onClick={menuFunctions.copyServicesLink}
            >
              Copy Services Link
            </Menu.Item>
          </Menu.Content>
        </Menu>
      </div>
      <div className="overflow-x-auto -mr-4 pr-4 thin-scrollbar py-2 flex items-center gap-x-2 ">
        <Button
          className="text-sm whitespace-nowrap flex items-center gap-x-2 pl-2"
          onTap={menuFunctions.createService}>
          <PlusSignIcon size={18} /> New Service
        </Button>
        <Button
          variant="dormant"
          className="text-sm whitespace-nowrap flex items-center gap-x-2 pl-2"
          onClick={menuFunctions.copyServicesLink}>
          <Link04Icon size={18} /> Copy Link
        </Button>
        <Button
          variant="dormant"
          className="text-sm whitespace-nowrap flex items-center gap-x-2 pl-2"
          onTap={menuFunctions.createCategory}>
          <FolderAddIcon size={18} /> New Category
        </Button>
        <Button
          variant="dormant"
          className="text-sm whitespace-nowrap flex items-center gap-x-2 pl-2"
          onTap={menuFunctions.viewPreview}>
          <SmartPhone01Icon size={18} /> Preview
        </Button>
        <Button
          variant="dormant"
          className="text-sm whitespace-nowrap flex items-center gap-x-2 pl-2"
          onTap={menuFunctions.editPreview}>
          <Settings01Icon size={18} /> Edit Preview
        </Button>
      </div>
      <div className="w-full flex flex-col gap-y-10 ">
        {Object.keys(groupedServicesMemo).length === 0 && !isFetching && (
          <div className="flex flex-col items-center justify-center text-center">
            <div className="mb-6">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Briefcase01Icon className="w-8 h-8 text-gray-400" />
              </div>
            </div>

            <h3 className="text-lg font-semibold text-gray-900 ">
              No services found
            </h3>

            <p className="text-gray-500 mb-6 text-sm max-w-sm">
              Get started by creating your first service to begin managing your
              offerings.
            </p>

            <Button
              onTap={menuFunctions.createService}
              className="font-medium text-sm flex items-center gap-2"
            >
              <PlusSignIcon className="w-5 h-5" />
              Create Service
            </Button>
          </div>
        )}
        <div
          className="w-full grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-6"
        >
          {groupedServicesMemo.map((category, index) => (
            <Accordion
              type="single"
              defaultValue="item-0"
              collapsible
              key={index}
            >
              <AccordionItem value={`item-0`} className="h-fit" >
                <AccordionTrigger noUnderline className="hover:underline" >
                  <div className="flex flex-grow items-center gap-x-3">
                    <h2 className="text-xl capitalize whitespace-nowrap line-clamp-1 font-semibold mb-2">
                      {category.name}
                    </h2>
                    <div className="w-fit flex items-center ml-auto gap-x-3 pr-2">
                      <ButtonTooltip content="Edit Category">
                        <button
                          className="bg-transparent text-paragraph active:scale-[.95]  "
                          onClick={(e) => {
                            e.preventDefault()
                            e.stopPropagation()
                            modalFunctions.openModal('editCategory', { id: category._id, name: category.name })
                          }}
                        >
                          <Edit02Icon width={18} height={18} />
                        </button>
                      </ButtonTooltip>
                      <ButtonTooltip content="Delete Category">
                        <button
                          className="bg-transparent text-paragraph active:scale-[.95]  "
                          onClick={(e) => {
                            e.preventDefault()
                            e.stopPropagation()
                            if (category.services.length > 0)
                              return showToast('info', 'Move the services to delete category', {
                                description: 'Move or delete the services to delete the category'
                              })
                            modalFunctions.openModal('deleteCategory', {
                              props: {
                                title: 'Delete '.concat(category.name).concat('?'),
                                action: {
                                  title: 'Delete',
                                  onConfirm() {
                                    mutations.deleteCategory.mutate(category._id, {
                                      onSuccess: () => {
                                        queryClient.invalidateQueries({
                                          queryKey: ["categories"],
                                        });
                                      },
                                    })
                                  },
                                }
                              }
                            })
                          }}
                        >
                          <Delete02Icon width={18} height={18} />
                        </button>
                      </ButtonTooltip>
                    </div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="flex flex-col gap-y-3" >
                  {category.services.length === 0 && (
                    <Typography className="text-sm text-paragraph">
                      No Services for this category
                    </Typography>
                  )}
                  {category.services?.map((service, serviceIndex) => (
                    <Service
                      onEdit={(urlName) => navigate(`/scheduling/services/edit/${urlName}`)}
                      index={serviceIndex}
                      service={service}
                      key={serviceIndex}
                    />
                  ))}
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          ))}
        </div>
      </div>
      <ServiceAddons />
      <CreateCategoryModal
        isCategoryCreatedOnServicesPage={true}
        open={modals.category.open}
        onClose={() => modalFunctions.closeModal("category")}
      />
      {modals.editCategory.id && <EditCategoryModal
        open={modals.editCategory.open}
        onClose={() => modalFunctions.closeModal('editCategory')}
        onSave={(name) => {
          mutations.updateCategory.mutateAsync({ id: modals.editCategory.id!, data: { name } }, {
            onSuccess() {
              queryClient.invalidateQueries({ queryKey: ['categories'] })
            }
          })
          modalFunctions.closeModal('editCategory')
        }}
        name={modals.editCategory.name}
      />
      }
      {modals.deleteCategory.props &&
        <Dialog
          open={modals.deleteCategory.open}
          onClose={() => modalFunctions.closeModal('deleteCategory')}
          {...modals.deleteCategory.props}
        />
      }
      <PendingOverlay isPending={mutations.updateCategory.isPending || mutations.deleteCategory.isPending} />
    </section>
  );
};

export const ServiceAddons = () => {
  const addons = useServiceAddons()
  const { formatPrice } = useFormattedPrice()
  const { modals, modalFunctions } = useModalsBuilder({
    createAddon: { open: false },
    editAddon: {
      open: false,
      props: null as Omit<ServiceAddon, 'isActive'> | null
    },
    deleteAddon: {
      open: false,
      props: null as {
        action: {
          title?: string;
          onConfirm: () => void;
        };
        title: string;
        description?: string;
      } | null
    },
  })
  const [hasStripeConnected, setHasStripeConnected] = useState(false)
  const { data: _isStripeConnected } = useQuery({
    queryKey: ['is-stripe-connected'],
    queryFn: async () => {
      try {
        const data = (await VerifyStripeAccount()).data
        if (data.success) setHasStripeConnected(true)
      } catch (err) {
        setHasStripeConnected(false)
        console.error(err)
      }
    }
  })
  return (
    <section id="add-ons" className="w-full max-w-2xl px-2 pb-64 flex flex-col gap-y-5 ">
      <div className="w-full flex flex-col gap-y-2 justify-between items-start">
        <Typography
          variant={"h1"}
          className="font-bold font-Bricolage_Grotesque  "
        >
          Add-ons
        </Typography>
        <Typography className="text-sm text-subtext pl-1">
          Add additional services that customers can choose alongside the main service.
        </Typography>
      </div>

      <div className="overflow-x-auto -mr-4 pr-4 thin-scrollbar py-2 flex items-center gap-x-2 ">
        <Button
          variant="dormant"
          className="text-sm whitespace-nowrap flex items-center gap-x-2 pl-2"
          onTap={() => {
            modalFunctions.openModal("createAddon", {});
          }}>
          <PlusSignIcon size={18} /> New Add-on
        </Button>
      </div>
      <div className="flex flex-col gap-y-2">
        {addons.serviceAddons?.map((addon, index) => {
          return (
            <div
              key={index}
              className="flex items-start gap-x-2 group mb-2 last:mb-0"
            >
              <div className="space-y-1 -mt-0.5 flex-grow">
                <div className="px-3 py-2 rounded-xl bg-stone-100 ">
                  <div className="flex items-center justify-between text-paragraph font-Bricolage_Grotesque">
                    <Typography className="">{addon.name}</Typography>
                    <Typography className={cn("font-normal text-green-500")}>
                      + {formatPrice(addon.price)}
                    </Typography>
                  </div>
                  <div className="flex items-center justify-between text-gray-400 font-normal text-xs">
                    <Typography className="font-normal flex-grow line-clamp-1">
                      {addon.description}
                    </Typography>
                    <div className="w-fit flex items-center ml-auto gap-x-3">
                      <ButtonTooltip content="Edit Option">
                        <button
                          className="bg-transparent !text-subtext"
                          onClick={() =>
                            modalFunctions.openModal("editAddon", {
                              props: addon
                            })
                          }
                        >
                          <Edit02Icon width={18} height={18} />
                        </button>
                      </ButtonTooltip>
                      <ButtonTooltip content="Delete Option">
                        <button
                          className="bg-transparent !text-subtext"
                          onClick={() => {
                            modalFunctions.openModal('deleteAddon', {
                              props: {
                                title: 'Delete '.concat(addon.name).concat('?'),
                                action: {
                                  title: 'Delete',
                                  onConfirm() {
                                    addons.delete.mutateAsync(addon._id)
                                  },
                                }
                              }
                            })
                          }}
                        >
                          <Delete02Icon width={18} height={18} />
                        </button>
                      </ButtonTooltip>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
        {modals.editAddon.props && <EditServiceOptionModal
          open={modals.editAddon.open}
          onClose={() => modalFunctions.closeModal('editAddon')}
          onSave={(payload) => {
            addons.update.mutateAsync({
              id: modals.editAddon.props?._id!,
              data: { ...(payload as Required<typeof payload>), isActive: true }
            })
            modalFunctions.closeModal('editAddon')
          }}
          option={modals.editAddon.props}
          hasStripeConnected={hasStripeConnected}
        />
        }
        <CreateServiceOptionModal
          open={modals.createAddon.open}
          onClose={() => modalFunctions.closeModal('createAddon')}
          onSave={(payload) => {
            addons.create.mutateAsync({
              ...(payload as Required<typeof payload>), isActive: true
            })
            modalFunctions.closeModal('createAddon')
          }}
          hasStripeConnected={hasStripeConnected}
        />
        {modals.deleteAddon.props &&
          <Dialog
            open={modals.deleteAddon.open}
            onClose={() => modalFunctions.closeModal('deleteAddon')}
            {...modals.deleteAddon.props}
          />
        }
        <PendingOverlay isPending={addons.create.isPending || addons.delete.isPending || addons.update.isPending} />
      </div>
    </section >
  )
}

export default Object.assign(Services, {
  CreateService: CreateService,
  EditService: EditServicePage,
  PreviewServices: PreviewServices
});
