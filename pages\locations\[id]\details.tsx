import React from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import { useLocationDetails } from "../hooks/use-location";
import { useStaff } from "@/pages/teams/hooks/use-staff";
import { useModalsBuilder } from "@/lib/modals-builder";
import StaffAssignmentModal from "../components/staff-assignment-modal";
import {
  ArrowLeft02Icon,
  Edit02Icon,
  Location01Icon,
  SmartPhone01Icon,
  Mail01Icon,
  Clock01Icon,
  UserGroupIcon,
  ServiceIcon,
} from "hugeicons-react";
import { and } from "@/lib/helpers";

const LocationDetailsPage: React.FC = () => {
  const { id: locationId } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const { locationQuery, staffByLocationQuery } = useLocationDetails(locationId);
  const { staffQuery } = useStaff();

  const { modals, modalFunctions } = useModalsBuilder({
    staffAssignment: {
      open: false,
    },
  });

  if (locationQuery.isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <CircularLoader />
      </div>
    );
  }

  if (!locationQuery.data) {
    return (
      <div className="text-center py-20">
        <Typography className="text-red-500">Location not found</Typography>
        <Button onTap={() => locationQuery.refetch()} className="mt-4">
          Try again
        </Button>
      </div>
    );
  }

  const location = locationQuery.data;
  const assignedStaff = staffByLocationQuery.data || [];
  const allStaff = staffQuery.data || [];

  return (
    <section className="w-full h-screen overflow-y-auto no-scrollbar p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="icon"
            onTap={() => navigate("/locations")}
            className="p-1.5"
          >
            <ArrowLeft02Icon className="w-5 h-5" />
          </Button>
          <Typography variant="h2" className="font-semibold flex-1">
            {location.name}
          </Typography>
          <Button
            variant="dormant"
            onTap={() => navigate(`/locations/edit/${location._id}`)}
            className="flex items-center gap-2"
          >
            <Edit02Icon className="w-4 h-4" />
            Edit Location
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Location Information */}
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <Typography variant="h4" className="font-medium mb-4">
                Location Information
              </Typography>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Location01Icon className="w-5 h-5 text-gray-500 mt-0.5" />
                  <div>
                    <Typography className="font-medium">Address</Typography>
                    <Typography variant="p" className="text-gray-600">
                      {location.address.street}<br />
                      {location.address.city}, {location.address.state} {location.address.zipCode}<br />
                      {location.address.country}
                    </Typography>
                  </div>
                </div>

                {location.phone && (
                  <div className="flex items-center gap-3">
                    <SmartPhone01Icon className="w-5 h-5 text-gray-500" />
                    <div>
                      <Typography className="font-medium">Phone</Typography>
                      <Typography variant="p" className="text-gray-600">
                        {location.phone}
                      </Typography>
                    </div>
                  </div>
                )}

                {location.email && (
                  <div className="flex items-center gap-3">
                    <Mail01Icon className="w-5 h-5 text-gray-500" />
                    <div>
                      <Typography className="font-medium">Email</Typography>
                      <Typography variant="p" className="text-gray-600">
                        {location.email}
                      </Typography>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3">
                  <Clock01Icon className="w-5 h-5 text-gray-500" />
                  <div>
                    <Typography className="font-medium">Timezone</Typography>
                    <Typography variant="p" className="text-gray-600">
                      {location.timezone}
                    </Typography>
                  </div>
                </div>

                {location.description && (
                  <div>
                    <Typography className="font-medium mb-2">Description</Typography>
                    <Typography variant="p" className="text-gray-600">
                      {location.description}
                    </Typography>
                  </div>
                )}
              </div>
            </div>

            {/* Assigned Staff */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <Typography variant="h4" className="font-medium flex items-center gap-2">
                  <UserGroupIcon className="w-5 h-5" />
                  Assigned Staff ({assignedStaff.length})
                </Typography>
                <Button
                  variant="dormant"
                  onTap={() => modalFunctions.openModal("staffAssignment", {})}
                  className="text-sm"
                >
                  Manage Staff
                </Button>
              </div>

              {staffByLocationQuery.isLoading ? (
                <CircularLoader />
              ) : assignedStaff.length > 0 ? (
                <div className="space-y-3">
                  {assignedStaff.map((staff: any) => (
                    <div key={staff._id} className="flex items-center gap-3 p-3 rounded-lg bg-gray-50">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: staff.color }}
                      />
                      <div className="flex-1">
                        <Typography className="font-medium">{staff.name}</Typography>
                        <Typography variant="p" className="text-sm text-gray-600">
                          {staff.role}
                        </Typography>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <Typography variant="p" className="text-gray-500 text-center py-4">
                  No staff assigned to this location
                </Typography>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <Typography variant="h4" className="font-medium mb-4">
                Quick Actions
              </Typography>

              <div className="space-y-3">
                <Button
                  variant="dormant"
                  onTap={() => navigate(`/locations/${location._id}/staff`)}
                  className="w-full"
                >
                  <UserGroupIcon className="w-4 h-4" />
                  Manage Staff
                </Button>

                <Button
                  variant="dormant"
                  onTap={() => navigate(`/locations/${location._id}/services`)}
                  className="w-full "
                >
                  <ServiceIcon className="w-4 h-4" />
                  Manage Services
                </Button>

                <Button
                  variant="dormant"
                  onTap={() => navigate(`/locations/${location._id}/analytics`)}
                  className="w-full "
                >
                  Analytics
                </Button>
              </div>
            </div>

            {/* Status */}
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <Typography variant="h4" className="font-medium mb-4">
                Status
              </Typography>

              <div className="flex items-center gap-2">
                <div
                  className={`w-3 h-3 rounded-full ${
                    location.isActive ? "bg-green-500" : "bg-gray-400"
                  }`}
                />
                <Typography variant="p" className="text-sm">
                  {location.isActive ? "Active" : "Inactive"}
                </Typography>
              </div>
            </div>
          </div>
        </div>
      </div>

      {and([modals.staffAssignment.open]) && (
        <StaffAssignmentModal
          open={modals.staffAssignment.open}
          onClose={modalFunctions.returnClose("staffAssignment")}
          location={location}
          availableStaff={allStaff}
          assignedStaffIds={assignedStaff.map((staff: any) => staff._id)}
        />
      )}
    </section>
  );
};

export default LocationDetailsPage;
