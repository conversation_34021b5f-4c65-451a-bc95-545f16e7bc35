<svg width="226" height="167" viewBox="0 0 226 167" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_353_4479)">
<rect x="21.3229" y="21.3229" width="182.562" height="124.354" rx="14.5521" fill="white" stroke="#D9D9D9" stroke-width="2.64583"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M113.927 110.789C107.622 116.23 99.4425 119.515 90.5049 119.515C70.5624 119.515 54.3958 103.161 54.3958 82.9865C54.3958 62.8125 70.5624 46.4583 90.5049 46.4583C99.4425 46.4583 107.622 49.7431 113.927 55.1844C120.232 49.7431 128.412 46.4583 137.349 46.4583C157.292 46.4583 173.458 62.8126 173.458 82.9866C173.458 103.161 157.292 119.515 137.349 119.515C128.412 119.515 120.232 116.23 113.927 110.789Z" fill="#ED0006"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M113.927 110.789C121.691 104.089 126.614 94.1193 126.614 82.9865C126.614 71.8538 121.691 61.8843 113.927 55.1843C120.232 49.743 128.412 46.4583 137.349 46.4583C157.292 46.4583 173.458 62.8125 173.458 82.9865C173.458 103.161 157.292 119.515 137.349 119.515C128.412 119.515 120.232 116.23 113.927 110.789Z" fill="#F9A000"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M113.927 110.789C121.691 104.089 126.614 94.1193 126.614 82.9866C126.614 71.854 121.691 61.8845 113.927 55.1846C106.163 61.8845 101.24 71.854 101.24 82.9866C101.24 94.1193 106.163 104.089 113.927 110.789Z" fill="#FF5E00"/>
</g>
<defs>
<filter id="filter0_d_353_4479" x="0" y="0" width="225.208" height="167" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_353_4479"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_353_4479" result="shape"/>
</filter>
</defs>
</svg>
