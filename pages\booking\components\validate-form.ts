import { IntakeFormQuestion, IntakeFormResponse } from "@/src/interfaces/intake-form";

export interface ValidationError {
  questionId: string;
  question: string;
  message: string;
}

export const validateIntakeFormResponses = (
  responses: IntakeFormResponse[],
  questions: IntakeFormQuestion[]
): ValidationError[] => {
  const errors: ValidationError[] = [];

  questions.forEach(question => {
    const response = responses.find(r => r.questionId === question.PP_Id);
    const answer = response?.answer;

    // Required validation
    if (question.required) {
      if (!answer ||
        (typeof answer === 'string' && answer.trim() === '') ||
        (Array.isArray(answer) && answer.length === 0)) {
        errors.push({
          questionId: question.PP_Id,
          question: question.question,
          message: `${question.question} is required`
        });
        return; // Skip other validations if required field is empty
      }
    }

    // Skip other validations if no answer provided for optional fields
    if (!answer) return;

    const validation = question.validation;
    if (!validation) return;

    // String length validations
    if (typeof answer === 'string') {
      if (validation.minLength && answer.length < validation.minLength) {
        errors.push({
          questionId: question.PP_Id,
          question: question.question,
          message: `${question.question} must be at least ${validation.minLength} characters`
        });
      }

      if (validation.maxLength && answer.length > validation.maxLength) {
        errors.push({
          questionId: question.PP_Id,
          question: question.question,
          message: `${question.question} must not exceed ${validation.maxLength} characters`
        });
      }
    }

    // Numeric validations
    if (question.type === 'number' && typeof answer === 'number') {
      if (validation.min !== undefined && answer < validation.min) {
        errors.push({
          questionId: question.PP_Id,
          question: question.question,
          message: `${question.question} must be at least ${validation.min}`
        });
      }

      if (validation.max !== undefined && answer > validation.max) {
        errors.push({
          questionId: question.PP_Id,
          question: question.question,
          message: `${question.question} must not exceed ${validation.max}`
        });
      }
    }

    // Date validations
    if (question.type === 'date' && (answer instanceof Date || typeof answer === 'string')) {
      const dateValue = answer instanceof Date ? answer : new Date(answer);

      if (validation.min) {
        const minDate = new Date(validation.min);
        if (dateValue < minDate) {
          errors.push({
            questionId: question.PP_Id,
            question: question.question,
            message: `${question.question} must be after ${minDate.toLocaleDateString()}`
          });
        }
      }

      if (validation.max) {
        const maxDate = new Date(validation.max);
        if (dateValue > maxDate) {
          errors.push({
            questionId: question.PP_Id,
            question: question.question,
            message: `${question.question} must be before ${maxDate.toLocaleDateString()}`
          });
        }
      }
    }
  });

  return errors;
};

export const hasIntakeFormErrors = (
  responses: IntakeFormResponse[],
  questions: IntakeFormQuestion[]
): boolean => {
  return validateIntakeFormResponses(responses, questions).length > 0;
};
