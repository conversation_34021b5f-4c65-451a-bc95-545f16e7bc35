:root {
   --brand-primary: rgb(47, 112, 193);
  --brand-secondary: rgb(116, 97, 195);
  --brand-alternative: rgb(19, 120, 134);
  --background-site: rgb(249, 249, 249);
  --background-code: rgb(244, 244, 244);
  --text-body: rgb(54, 49, 61);
  --text-comment: rgb(99, 94, 105);
  --text-high-contrast: rgb(49, 49, 49);
  --text-medium-contrast: rgb(99, 94, 105);
  --text-low-contrast: rgb(116, 109, 118);
  --detail-high-contrast: rgb(192, 192, 192);
  --detail-medium-contrast: rgb(234, 234, 234);
  --detail-low-contrast: rgb(240, 240, 242);
  --admonition-note: rgb(46, 109, 188);
  --admonition-warning: rgb(255, 196, 9);
  --admonition-danger: rgb(220, 38, 38);
  --brand-primary-rgb-value: 47, 112, 193;
  --brand-secondary-rgb-value: 116, 97, 195;
  --brand-alternative-rgb-value: 19, 120, 134;
  --background-site-rgb-value: 249, 249, 249;
  --background-code-rgb-value: 244, 244, 244;
  --text-body-rgb-value: 54, 49, 61;
  --text-comment-rgb-value: 99, 94, 105;
  --text-high-contrast-rgb-value: 49, 49, 49;
  --text-medium-contrast-rgb-value: 99, 94, 105;
  --text-low-contrast-rgb-value: 116, 109, 118;
  --detail-high-contrast-rgb-value: 192, 192, 192;
  --detail-medium-contrast-rgb-value: 234, 234, 234;
  --detail-low-contrast-rgb-value: 240, 240, 242;
  --admonition-note-rgb-value: 46, 109, 188;
  --admonition-warning-rgb-value: 255, 196, 9;
  --admonition-danger-rgb-value: 220, 38, 38;
}

.embla {
  position: relative;
  display: flex;
  width: 100%;
  height: 12rem;
  max-width: 30rem;
  margin-left: auto;
  margin-right: auto;
}
.embla:before,
.embla:after {
  position: absolute;
  left: 0;
  right: 0;
  content: '';
  display: block;
  height: 32px;
  z-index: 1;
  pointer-events: none;
}
/* currently we dont need this oneeeeee */
.embla:before {
  /* top: -0.5px; */
  /* background: blue; */
  /* border-bottom: 0.5px solid rgba(var(--text-high-contrast-rgb-value), 0.3); */
  /* background: linear-gradient( */
  /*   to top, */
  /*   rgba(var(--background-site-rgb-value), 0.65) 0%, */
  /*   rgba(var(--background-site-rgb-value), 1) 100% */
  /* ); */
}
/* blurred scene for selected values */
.embla:after {
  /* i dont what what this means though 🙆🙆 teehe */
  bottom: calc(50% - 32px / 2);
  background: rgba(0, 0, 0, .15);
  backdrop-filter: 2px;
  border-radius: 8px;
  /* background: linear-gradient( */
  /*   to bottom, */
  /*   rgba(var(--background-site-rgb-value), 0.65) 0%, */
  /*   rgba(var(--background-site-rgb-value), 1) 100% */
  /* ); */
}
.embla__ios-picker {
  height: 100%;
  display: flex;
  align-items: center;
  min-width: 50%;
  justify-content: center;
  gap: 12px;
  line-height: 1;
  position: relative;
  z-index: 20;
  font-size: 1rem;
}
.embla__ios-picker__scene {
  min-width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  touch-action: pan-x;
}
.embla__ios-picker__viewport {
  height: 32px;
  width: 100%;
  perspective: 1000px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -khtml-user-select: none;
  -webkit-tap-highlight-color: transparent;
}
.embla__ios-picker__viewport--perspective-left {
  perspective-origin: calc(50% + 20px) 50%;
  transform: translateX(10px);
}
.embla__ios-picker__viewport--perspective-right {
  perspective-origin: calc(50% - 90px) 50%;
  transform: translateX(-27px);
}
.embla__ios-picker__container {
  height: 100%;
  width: 100%;
  transform-style: preserve-3d;
  will-change: transform;
}
.embla__ios-picker__slide {
  width: 100%;
  height: 100%;
  font-size: 14px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  backface-visibility: hidden;
  opacity: 0;
}
.embla__ios-picker__label {
  color: #2f2f2f;
  font-weight: 600;
  transform: translateX(-55px);
  pointer-events: none;
}

