import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { ActionButtonGroup, Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import Toggle from "@/components/ui/toggle-2";
import PendingOverlay from "@/components/ui/pending-overlay";
import CircularLoader from "@/components/ui/circular-loader";
import { ArrowLeft02Icon } from "hugeicons-react";
import { useLocationDetails, useMultiLocation } from "./hooks/use-location";
import { UpdateLocationPayload } from "@/src/interfaces/multi-location";
import moment from "moment-timezone";
import { noop, pickByType } from "@/lib/helpers";
import { useToast } from "@/src/contexts/hooks/toast";

const timeZones = moment.tz.names();

const EditLocationPage: React.FC = () => {
  const { id: locationId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { locationQuery } = useLocationDetails(locationId);
  const { updateLocationMutation, geocodeAddressMutation } = useMultiLocation();

  const formik = useFormik<UpdateLocationPayload>({
    enableReinitialize: true,
    initialValues: {
      name: locationQuery.data?.name || "",
      address: {
        street: locationQuery.data?.address.street || "",
        city: locationQuery.data?.address.city || "",
        state: locationQuery.data?.address.state || "",
        zipCode: locationQuery.data?.address.zipCode || "",
        country: locationQuery.data?.address.country || "",
      },
      timezone: locationQuery.data?.timezone || "Europe/London",
      phone: locationQuery.data?.phone || "",
      email: locationQuery.data?.email || "",
      description: locationQuery.data?.description || "",
      isActive: locationQuery.data?.isActive ?? true,
    },
    validationSchema: Yup.object({
      name: Yup.string().required("Location name is required"),
      address: Yup.object({
        street: Yup.string().required("Street address is required"),
        city: Yup.string().required("City is required"),
        state: Yup.string().required("State is required"),
        zipCode: Yup.string().required("ZIP code is required"),
        country: Yup.string().required("Country is required"),
      }),
      timezone: Yup.string().required("Timezone is required"),
      phone: Yup.string(),
      email: Yup.string().email("Invalid email format"),
      description: Yup.string(),
      isActive: Yup.boolean(),
    }),
    onSubmit: noop,
  });

  const handleGeocodeAddress = async () => {
    const { street, city, state, country } = formik.values.address;
    const fullAddress = `${street}, ${city}, ${state}, ${country}`;

    if (fullAddress.trim()) {
      geocodeAddressMutation.mutate(fullAddress);
    }
  };

  const showToast = useToast();

  const updateLocationHandler = async () => {
    formik.validateForm().then((val) => {
      const errors = Object.values(pickByType(val, "string"));
      if (errors.length === 0) {
        const { values } = formik;
        if (!locationId) return;
        updateLocationMutation.mutateAsync(
          {
            locationId,
            payload: values,
          },
          {
            onSuccess() {
              navigate("/locations");
            },
          },
        );
      } else {
        showToast("error", "Invalid Field", {
          description: errors[0],
        });
      }
    });
  };

  if (locationQuery.isLoading) {
    return (
      <div className="flex w-full justify-center py-10">
        <CircularLoader />
      </div>
    );
  }

  if (!locationQuery.data) {
    return (
      <div className="text-center py-20">
        <Typography className="text-red-500">Location not found</Typography>
        <Button onTap={() => locationQuery.refetch()} className="mt-4">
          Try again
        </Button>
      </div>
    );
  }

  return (
    <section className="w-full h-screen overflow-y-auto no-scrollbar p-4">
      <PendingOverlay isPending={updateLocationMutation.isPending} />

      <div className="max-w-2xl mx-auto space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="icon"
            onTap={() => navigate("/locations")}
            className="p-1.5"
          >
            <ArrowLeft02Icon className="w-5 h-5" />
          </Button>
          <Typography variant="h3" className="font-semibold">
            Edit Location
          </Typography>
        </div>

        <div className="space-y-6">
          <div className="bg-white rounded-lg p-6 shadow-sm space-y-4">
            <div className="flex items-center justify-between">
              <Typography variant="h4" className="font-medium">
                Basic Information
              </Typography>
              <div className="flex items-center gap-2">
                <Typography variant="p" className="text-sm">
                  Active
                </Typography>
                <Toggle
                  checked={Boolean(formik.values.isActive)}
                  onChange={(checked) =>
                    formik.setFieldValue("isActive", checked)
                  }
                />
              </div>
            </div>

            <Input.Text
              label="Location Name"
              name="name"
              value={formik.values.name}
              onChange={formik.handleChange}
              required
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input.Phone
                label="Phone"
                name="phone"
                value={formik.values.phone}
                onChange={(value, e) => {
                  formik.setFieldValue(e.currentTarget.name, value);
                }}
              />

              <Input.Email
                label="Email"
                name="email"
                value={formik.values.email}
                onChange={formik.handleChange}
              />
            </div>

            <Input.TextArea
              label="Description"
              name="description"
              value={formik.values.description}
              onChange={formik.handleChange}
            />
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm space-y-4">
            <div className="flex items-center justify-between">
              <Typography variant="h4" className="font-medium">
                Address Information
              </Typography>
              <Button
                variant="dormant"
                onTap={handleGeocodeAddress}
                loading={geocodeAddressMutation.isPending}
                className="text-sm"
              >
                Verify Address
              </Button>
            </div>

            <Input.Text
              label="Street Address"
              name="address.street"
              value={formik.values.address.street}
              onChange={formik.handleChange}
              required
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input.Text
                label="City"
                name="address.city"
                value={formik.values.address.city}
                onChange={formik.handleChange}
                required
              />

              <Input.Text
                label="State"
                name="address.state"
                value={formik.values.address.state}
                onChange={formik.handleChange}
                required
              />

              <Input.Text
                label="ZIP Code"
                name="address.zipCode"
                value={formik.values.address.zipCode}
                onChange={formik.handleChange}
                required
              />
            </div>

            <Input.Text
              label="Country"
              name="address.country"
              value={formik.values.address.country}
              onChange={formik.handleChange}
              required
            />
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm space-y-4">
            <Typography variant="h4" className="font-medium">
              Timezone
            </Typography>

            <SearchableDropdown
              label="Timezone"
              placeholder="Select timezone"
              value={formik.values.timezone}
              options={timeZones.map((tz) => ({
                label: tz,
                value: tz,
              }))}
              onChange={(option) => {
                formik.setFieldValue("timezone", option?.value || "");
              }}
              required
            />
          </div>

          <ActionButtonGroup
            cancel={{
              title: "Cancel",
              disabled: updateLocationMutation.isPending,
              onTap: () => navigate("/locations")
            }}
            next={{
              loading: updateLocationMutation.isPending,
              disabled: updateLocationMutation.isPending,
              title: innerWidth <= 480 ? "Save" : 'Update Location',
              onTap: updateLocationHandler
            }}
          />
        </div>
      </div>
    </section>
  );
};

export default EditLocationPage;
