import { useToast } from "@/src/contexts/hooks/toast";
import { GetBookingSettings, ToggleBookingSettingsStatus, UpdateBookingSettingsHTML, UpdateBookingThemeSettings, UploadBookingSettingsImage } from "@/src/services/booking-settings.service";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export const usePreviewServices = () => {
  const queryclient = useQueryClient();
  const showToast = useToast();
  const previewData = useQuery({
    queryKey: ["booking-settings"],
    queryFn: async () => {
      const res = await GetBookingSettings();
      return res.data.bookingSettings
    },
  })
  const updateBookingSettingsHTMLMutation = useMutation({
    mutationFn: UpdateBookingSettingsHTML,
    onSuccess() {
      queryclient.invalidateQueries({
        queryKey: ["booking-settings"],
      });
      showToast("info", "Preview settings updated");
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  const toggleBookingSettingsStatus = useMutation({
    mutationFn: ToggleBookingSettingsStatus,
    onSuccess() {
      queryclient.invalidateQueries({
        queryKey: ["booking-settings"],
      });
      showToast("info", "Preview settings updated successfully");
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  const uploadBookingImageMutation = useMutation({
    mutationFn: UploadBookingSettingsImage,
    onSuccess() {
      queryclient.invalidateQueries({
        queryKey: ["booking-settings"],
      });
      showToast("info", "Preview Image updated successfully");
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });

  const updateBookingThemeSettingsMutation = useMutation({
    mutationFn: UpdateBookingThemeSettings,
    onSuccess() {
      queryclient.invalidateQueries({
        queryKey: ["booking-settings"],
      });
      showToast("info", "Preview theme settings updated");
    },
    onError(error: ApiError) {
      showToast("error", error.response?.data.error || "Something went wrong");
    },
  });
  return {
    previewData,
    updateBookingSettingsHTMLMutation,
    uploadBookingImageMutation,
    toggleBookingSettingsStatus,
    updateBookingThemeSettingsMutation
  }
}

