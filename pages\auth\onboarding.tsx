import SignupLottie from "@/assets/lottie-animations/signup.json";
import { useState } from "react";
import { SubscriptionPlan } from "~/interfaces/subscription-plan";
import { AuthContextProvider } from "./components/auth-context";
import MultiStepForm from "./components/multi-step-form";
import PricingPlans from "./components/pricing-plans";
import Lottie from "lottie-react";

const Onboarding = () => {
  const [plan, setPlan] = useState<
    | (SubscriptionPlan & {
        selectedInterval: "week" | "month" | "year";
      })
    | null
  >(null);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  return (
    <AuthContextProvider
      value={{
        clientSecret,
        setClientSecret,
        plan: plan,
        setPlan,
      }}
    >
      <section className="w-screen h-screen px-0 flex items-center ">
        {plan ? (
          <>
            <MultiStepForm />
            {innerWidth >= 768 && (
              <div className="flex-1 h-full md:flex md:place-content-center ">
                <Lottie
                  animationData={SignupLottie}
                  loop={true}
                  className="aspect-square"
                />
              </div>
            )}
          </>
        ) : (
          // this will set subscriptionPlans inside with context
          <PricingPlans />
        )}
      </section>
    </AuthContextProvider>
  );
};

export default Onboarding;
