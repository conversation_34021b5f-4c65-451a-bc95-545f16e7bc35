import { Typography } from "@/components/typography"
import { DigitalProfileContextType, socialMediaMatcher, useDigitalProfileContext } from "../hooks/use-digital-profile"
import { motion } from "framer-motion"
import { PencilIcon, X } from "lucide-react"
import { Xmark } from "@gravity-ui/icons"
import { Button } from "@/components/buttons"
import { PlusSignIcon } from "hugeicons-react"
import Modal, { BaseModalProps } from "@/components/ui/modal"
import { noop, pick } from "@/lib/helpers"
import { useModalsBuilder } from "@/lib/modals-builder"
import { useFormik } from "formik"
import { ISocialMedia } from "../types"
import { Input } from "@/components/inputs"
import { useMemo } from "react"

const SocialMediaIcon = (props: ISocialMedia) => {
  const Icon = useMemo(() => {
    if (props.type === 'custom')
      return () => props.icon ? <span className="text-[36px] ">{props.icon}</span> : null
    else {
      const socialMediaIcon = socialMediaMatcher.find(([regExp]) => regExp.test(props.url))
      if (socialMediaIcon) {
        const Icon = socialMediaIcon[1].icon;
        return () => <Icon className="size-[22px] " />
      } else return () => null
    }
  }, [props.url])
  return <Icon />
}

/** 
 * @dev no props, every thing even functions are passed through context
 * */
const SocialMediaSettings = () => {
  const { profile, socialsSettings } = useDigitalProfileContext()
  const { modals, modalFunctions } = useModalsBuilder({
    /** 
     * @dev create or update 
     * */
    upsert: {
      open: false,
      data: null as { index: number; socialMedia: ISocialMedia } | null
    },
  })
  return (
    <div className="flex flex-col gap-y-3 mt-5">
      <div className="w-full flex flex-col gap-y-2 justify-between items-start">
        <Typography
          variant={"h2"}
          className="font-bold font-Bricolage_Grotesque  "
        >
          Social Icons 
        </Typography>
        <Typography className="text-sm text-subtext pl-1">
          Add social icon links here
        </Typography>
      </div>
      <div className="flex items-center flex-wrap gap-x-5" >
        {
          profile.socialMedia?.map((link, idx) => (
            <div key={idx} className="relative rounded-full flex items-center justify-center size-[36px]">
              <SocialMediaIcon {...link} />
              <motion.button
                whileTap={{ scale: 0.9 }}
                onTap={() => socialsSettings.delete(idx, link.url)}
                className="absolute -top-2 -right-3 bg-stone-100 rounded-full p-1.5 shadow-sm"
              >
                <Xmark height={10} width={10} />
              </motion.button>
              <motion.button
                whileTap={{ scale: 0.9 }}
                onTap={() => modalFunctions.openModal('upsert', {
                  data: { index: idx, socialMedia: link }
                })}
                className="absolute -bottom-2 -right-3 bg-stone-100 rounded-full p-1.5 shadow-sm"
              >
                <PencilIcon size={10} />
              </motion.button>
            </div>
          ))
        }
        <Button
          onTap={() => modalFunctions.openModal('upsert', { data: null })}
          variant="dormant" className="size-[36px] rounded-full flex items-center justify-center !p-0 " >
          <PlusSignIcon />
        </Button>
      </div>
      <SocialsModals.Upsert
        open={modals.upsert.open}
        onClose={modalFunctions.returnClose('upsert')}
        data={modals.upsert.data?.socialMedia}
        onSave={(payload) => {
          modalFunctions.closeModal('upsert')
          if (modals.upsert.data)
            socialsSettings.update(modals.upsert.data.index, payload)
          else socialsSettings.add(payload)
        }}
      />
    </div>
  )
}

type SocialsModalProps = BaseModalProps & {
  data: ISocialMedia | undefined 
  onSave: DigitalProfileContextType['socialsSettings']['add']
}

const SocialsModals = {
  Upsert: (props: SocialsModalProps) => {
    const formik = useFormik({
      enableReinitialize: true,
      initialValues: {
        // @ts-ignore
        icon: '',
        ...(
          props.data ||
          {
            type: '' as ISocialMedia['type'] | 'custom',
            url: '',
          } 
        )
      },
      onSubmit: noop
    })
    const iconMemo = useMemo(() => {
      const socialMediaIcon = socialMediaMatcher.find(([regExp]) => regExp.test(formik.values.url))
      if (socialMediaIcon) {
        const Icon = socialMediaIcon[1].icon;
        return { type: socialMediaIcon[1].type, Icon: () => <Icon className="size-[22px] " /> }
      } else return {
        type: 'custom',
        Icon: () => <span className="text-[22px] " >{formik.values.icon}</span>
      }
    }, [formik.values.url, formik.values.icon])
    return (
      <Modal {...pick(props, 'onClose', 'open')} >
        <Modal.Body
          className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] !p-6 bg-white `}
        >
          <div className="w-full flex justify-between items-center">
            <Typography variant={"h4"} className="font-bold ">
              Add Social Media
            </Typography>
            <Button
              variant="icon"
              onTap={props.onClose}
              className="p-1 !rounded-full "
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
          <div className="w-full flex gap-x-2 justify-between items-center ">
            <div className="w-full flex items-center gap-x-2">
              <Input.Text
                name="url"
                label="URL"
                required
                className="flex-grow"
                placeholder="https://wa.me..."
                value={formik.values.url}
                onChange={({ currentTarget: { value } }) => {
                  const socialMediaIcon = socialMediaMatcher.find(([regExp]) => regExp.test(value))
                  if (socialMediaIcon)
                    formik.setValues(p => ({ ...p, type: socialMediaIcon[1].type, icon: '', url: value }))
                  else formik.setValues(p => ({ ...p, type: 'custom', url: value }))
                  formik.handleChange
                }}
              />
            </div>
            <div className="size-[22px] ">
              <iconMemo.Icon />
            </div>
          </div>
          {formik.values.type === 'custom' &&
            <Input.Text
              name="icon"
              maxLength={3}
              label="Icon (can be an emoji 😀)"
              className="[&>div]:!max-w-20 mr-auto"
              value={formik.values.icon}
              onChange={formik.handleChange}
            />
          }
          <Button
            className="w-full"
            disabled={!formik.values.url}
            onTap={() => props.onSave({
              ...pick(formik.values, 'type', 'url'),
              ...(formik.values.type === 'custom' ? { icon: formik.values.icon } : {})
            } as any)}
          >
            Add
          </Button>
        </Modal.Body>
      </Modal>
    )
  }
}

export default SocialMediaSettings
