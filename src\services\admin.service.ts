import { pick } from "@/lib/helpers";
import { AxiosResponse } from "axios";
import { api } from "./api.service";

export namespace AdminNS {
  export type GetUsersResponse = {
    users: ExtendedUser[];
  };

  export type EditUserPayload = Partial<
    Pick<User, "first_name" | "last_name" | "username" | "email"> & {
      password: string;
    }
  >;

  export type ToggleUserStatusPayload = {
    isActive: boolean;
    userId: string;
  };

  export type UserLog = {
    type: string;
    description: string;
    ip: string;
    user_agent: string;
    timestamp: Date | string;
    userId: string;
  }

  export type GetUserLogsResponse = {
    logs: Array<UserLog>;
  };
  /**
   * @dev the logCount when shown on the table should show the full
   */
  export type UserWithLogs = {
    _id: string;
    name: string;
    email: string;
    logCount: number;
  };
  /**
   * @dev backend code doesn't infer a type for the elements of the logs array, so we log to console and see
   */
  export type GetAllUsersWithLogsResponse = {
    logs: UserWithLogs[];
  };

  export interface Faq {
    _id: string
    question: string;
    answer: string;
    category?: string;
    createdAt: Date;
    updatedAt: Date;
  }

  export type GetAllFaqsResponse = {
    faqs: Faq[];
  };

  export type CreateFaqPayload = Pick<Faq, "answer" | "question" | "category">;

  export type EditFaqPayload = CreateFaqPayload;

  export interface MaintenanceMode {
    isActive: boolean;
    message: string;
    updatedAt: Date;
  }

  export interface SupportTicket {
    _id: string
    userId: {
      _id: string;
      first_name: string;
      last_name: string
    };
    title: string;
    description: string;
    status: "open" | "in-progress" | "resolved" | "closed";
    priority: "low" | "medium" | "high";
    ip: string;
    createdAt: Date;
    updatedAt: Date;
  }

  export type CreateSupportTicketPayload = Pick<
    SupportTicket,
    "title" | "description" | "priority" | "ip"
  > & {
    userId: string
  };
}

export class AdminUserServices {
  /**
   * @dev could respond with 404 if no users
   */
  static async GetUsers(): Promise<AxiosResponse<AdminNS.GetUsersResponse>> {
    try {
      const res = api.get("/admin/user");
      return res;
    } catch (error) {
      throw error;
    }
  }
  /**
   * @dev some props are picked on the backend
   */
  static async EditUser(payload: AdminNS.EditUserPayload): Promise<
    AxiosResponse<{
      message: string;
    }>
  > {
    try {
      const res = api.post(`/admin/user/edit`, payload);
      return res;
    } catch (error) {
      throw error;
    }
  }
  static async DeleteUser(userId: string): Promise<
    AxiosResponse<{
      message: string;
    }>
  > {
    try {
      const res = api.delete(`/admin/user/${userId}`);
      return res;
    } catch (error) {
      throw error;
    }
  }
  /**
   * @uiimplementation use a toggle component inside the table row.
   */
  static async ToggleUserStatus(
    payload: AdminNS.ToggleUserStatusPayload
  ): Promise<
    AxiosResponse<{
      message: string;
    }>
  > {
    try {
      const res = api.patch(
        `/admin/toggle-status/${payload.userId}`,
        pick(payload, "isActive")
      );
      return res;
    } catch (error) {
      throw error;
    }
  }
}

export class AdminUserLogsServices {
  static async GetUserLogs(
    userId: string
  ): Promise<AxiosResponse<AdminNS.GetUserLogsResponse>> {
    try {
      const res = api.get(`/admin/logs/${userId}`);
      return res;
    } catch (error) {
      throw error;
    }
  }

  /**
   * @dev backend code doesn't infer a type for the elements of the logs array, so we log to console and see
   */
  static async GetAllUsersWithLogs(): Promise<
    AxiosResponse<AdminNS.GetAllUsersWithLogsResponse>
  > {
    try {
      const res = api.get("/admin/logs/users");
      return res;
    } catch (error) {
      throw error;
    }
  }
}

export class AdminFaqServices {
  static async GetAllFaqs(): Promise<
    AxiosResponse<AdminNS.GetAllFaqsResponse>
  > {
    try {
      const res = api.get("/admin/faq");
      return res;
    } catch (error) {
      throw error;
    }
  }

  static async GetFaqById(faqId: string): Promise<
    AxiosResponse<{
      faq: AdminNS.Faq;
    }>
  > {
    try {
      const res = api.get(`/admin/faq/${faqId}`);
      return res;
    } catch (error) {
      throw error;
    }
  }

  static async CreateFaq(payload: AdminNS.CreateFaqPayload): Promise<
    AxiosResponse<{
      newFaq: AdminNS.Faq;
    }>
  > {
    try {
      const res = api.post("/admin/faq", payload);
      return res;
    } catch (error) {
      throw error;
    }
  }

  static async EditFaq(
    faqId: string,
    payload: AdminNS.EditFaqPayload
  ): Promise<
    AxiosResponse<{
      updateFaq: AdminNS.Faq;
    }>
  > {
    try {
      const res = api.put(`/admin/faq/${faqId}`, payload);
      return res;
    } catch (error) {
      throw error;
    }
  }

  static async DeleteFaq(faqId: string): Promise<
    AxiosResponse<{
      message: string;
    }>
  > {
    try {
      const res = api.delete(`/admin/faq/${faqId}`);
      return res;
    } catch (error) {
      throw error;
    }
  }
}

export class AdminMaintenanceModeServices {
  static async SetMaintenanceMode(payload: {
    isActive: boolean;
    message: string;
  }): Promise<
    AxiosResponse<{
      message: string;
    }>
  > {
    try {
      const res = api.put("/maintenance", payload);
      return res;
    } catch (error) {
      throw error;
    }
  }

  static async GetMaintenanceMode(): Promise<
    AxiosResponse<{
      maintenance: AdminNS.MaintenanceMode;
    }>
  > {
    try {
      const res = api.get("/maintenance");
      return res;
    } catch (error) {
      throw error;
    }
  }
}

export class AdminSupportTicketServices {
  static async CreateSupportTicket(
    payload: AdminNS.CreateSupportTicketPayload
  ): Promise<AxiosResponse<any>> {
    try {
      const res = api.post("/support-tickets", payload);
      return res;
    } catch (error) {
      throw error;
    }
  }

  static async GetSupportTickets(): Promise<
    AxiosResponse<{
      tickets: AdminNS.SupportTicket[];
    }>
  > {
    try {
      const res = api.get("/admin/tickets");
      return res;
    } catch (error) {
      throw error;
    }
  }

  static async GetSupportTicket(ticketId: string): Promise<
    AxiosResponse<{
      ticket: AdminNS.SupportTicket;
    }>
  > {
    try {
      const res = api.get(`/admin/tickets/${ticketId}`);
      return res;
    } catch (error) {
      throw error;
    }
  }

  static async DeleteSupportTicket(ticketId: string): Promise<
    AxiosResponse<{
      ticket: AdminNS.SupportTicket;
      message: string;
    }>
  > {
    try {
      const res = api.delete(`/admin/tickets/${ticketId}`);
      return res;
    } catch (error) {
      throw error;
    }
  }
}
