import { SubscriptionPlan } from "~/interfaces/subscription-plan";
import { createContext, useContext } from "react";

export type PaymentContextType = {
  // add more things here
  clientSecret: string | null;
  plan: SubscriptionPlan | null;
};

const PaymentContext = createContext<PaymentContextType | null>(null);

const usePayment = () => {
  const authContext = useContext(PaymentContext);
  if (!authContext) {
    throw new Error("usePayment must be used within a PaymentContextProvider");
  }
  return authContext;
};

const PaymentContextProvider = ({
  children,
  value,
}: {
  children: React.ReactNode;
  value: PaymentContextType;
}) => {
  return (
    <PaymentContext.Provider value={value}>{children}</PaymentContext.Provider>
  );
};

export { PaymentContextProvider, usePayment };
