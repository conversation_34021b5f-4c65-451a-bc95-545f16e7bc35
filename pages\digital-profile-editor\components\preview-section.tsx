import { Button } from "@/components/buttons"
import { motion } from "framer-motion"
import { EyeIcon } from "hugeicons-react"
import DevicePreview from "./device-preview"
import LayoutPreview from "./layout-preview"
import { useState } from "react"
import { cn } from "@/lib/utils"
import { and } from "@/lib/helpers"
import { useDigitalProfileContext } from "../hooks/use-digital-profile"
import { usePlanRole } from "@/src/contexts/use-plan-role"
import useAuthStore from "@/pages/auth/components/auth-store"

const PreviewSection = () => {
  const isLargeScreen = window.innerWidth >= 1200;
  const [previewShown, setPreviewShown] = useState(isLargeScreen);
  const { profile } = useDigitalProfileContext()
  const { isPremium } = usePlanRole();
  const { initialState:
    { user }
  } = useAuthStore()
  return (
    <>
      <Button
        variant="dormant"
        onTap={() => setPreviewShown(!previewShown)}
        className="fixed bottom-4 !left-1/2 !-translate-x-1/2 z-[600000] p-2 !rounded-full size-12 flex items-center justify-center bxl:hidden "
      >
        <EyeIcon size={20} />
      </Button>
      <motion.section
        transition={{
          type: "none",
        }}
        variants={{
          hidden: {
            opacity: 0,
            y: 60,
          },
          visible: {
            opacity: 1,
            y: 0,
          },
        }}
        onClick={(e) => {
          if (and([e.target === e.currentTarget, !isLargeScreen])) setPreviewShown(!previewShown)
        }}
        initial={"hidden"}
        animate={previewShown ? "visible" : "hidden"}
        className={cn("flex-grow bg-white/25 backdrop-blur-[2px] h-screen flex items-center justify-center w-full fixed top-0 left-0 bxl:bg-transparent bxl:z-[900] bxl:relative bxl:max-h-[86.6vh] bxl:w-auto ",
          { "z-[500000]": previewShown },
          { "-z-[20]": !previewShown }
        )}
      >
        <DevicePreview>
          <LayoutPreview
            userDetails={{
              isPremium,
              username: user?.username || '',
              isOnPublicPage: false
            }}
            profile={profile} />
        </DevicePreview>
      </motion.section>

    </>
  )
}

export default PreviewSection
