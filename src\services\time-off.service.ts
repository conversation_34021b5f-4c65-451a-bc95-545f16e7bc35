import { api } from "./api.service";
import * as PTOTypes from "../interfaces/time-off";


export const GetPTOForStaff = async (
  staffId: PTOTypes.StaffId,
  params?: {
    startDate?: string;
    endDate?: string;
    type?: PTOTypes.PTOType;
    isApproved?: boolean;
  }
) => {
  const response = await api.get<{
    success: true;
    ptos: PTOTypes.PTORequest[];
  }>(`/staff/${staffId}/pto`, { params });
  return response;
};

export const ApprovePTORequest = async (ptoId: string, payload: PTOTypes.ApprovePTORequest) => {
  const response = await api.patch<{
    success: true;
    message: string;
    pto: PTOTypes.PTORequest;
  }>(`/pto/${ptoId}/approve`, payload);
  return response;
};

export const GetAllPTORequests = async (params?: {
  startDate?: string;
  endDate?: string;
  type?: PTOTypes.PTOType;
  isApproved?: boolean;
  staffId?: string;
}) => {
  const response = await api.get<{
    success: true;
    ptos: PTOTypes.PTORequest[];
  }>("/pto", { params });
  return response;
};

// Staff Facing apis
export const CreatePTORequest = async (staffId: PTOTypes.StaffId, payload: PTOTypes.CreatePTORequest) => {
  const response = await api.post<{
    success: true;
    message: string;
    pto: PTOTypes.PTORequest;
  }>(`/staff/${staffId}/pto`, payload);
  return response;
};


export const UpdatePTORequest = async (ptoId: string, payload: PTOTypes.UpdatePTORequest) => {
  const response = await api.put<{
    success: true;
    message: string;
    pto: PTOTypes.PTORequest;
  }>(`/pto/${ptoId}`, payload);
  return response;
};

export const DeletePTORequest = async (ptoId: string) => {
  const response = await api.delete<{
    success: true;
    message: string;
  }>(`/pto/${ptoId}`);
  return response;
};
