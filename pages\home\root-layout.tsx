import { useEffect, useState } from "react";
import { Outlet } from "react-router-dom";
import { Navbar } from "./navbar";
import DashboardSidebar from "./sidebar";

export default function RootLayout() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(
    window.innerWidth >= 768 ? true : false
  );

  const toggleSidebar = () => {
    if (window.innerWidth >= 768) return;
    setIsSidebarOpen(!isSidebarOpen);
  };

  useEffect(() => {
    const observer = new ResizeObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.contentRect.width >= 768) setIsSidebarOpen(true);
        else setIsSidebarOpen(false);
      });
    });
    observer.observe(document.body);
    return () => observer.disconnect();
  }, []);

  return (
    <section className="flex h-full">
      <DashboardSidebar
        zIndexClass="z-[500000]"
        isSidebarOpen={isSidebarOpen}
        toggleSidebar={toggleSidebar}
      />
      <main className="w-full flex flex-col overflow-y-auto no-scrollbar md:w-[calc(100%-266px)] md:h-screen md:pb-3 lg:md:w-[calc(100%-320px)] md:ml-auto relative">
        <div className="sticky top-0 left-0 z-[1000000000000] " id="global_save_button_container" ></div>
        <Navbar toggleSidebar={toggleSidebar} />
        <div className="w-full mt-[30px] flex-grow px-3">
          <Outlet />
        </div>
      </main>
    </section>
  );
}
