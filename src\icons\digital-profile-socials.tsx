
const SocialMediaIcons = {
  Instagram: (props: React.JSX.IntrinsicElements["svg"]) => {
    return (
      <svg
        fill="#383D38"
        {...props}
        width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
        <path d="M47.9837 0H16.0163C7.18471 0 0 7.50385 0 16.7277V47.2723C0 56.4962 7.18471 64 16.0163 64H47.9837C56.8153 64 64 56.4962 64 47.2723V16.7277C64 7.50385 56.8153 0 47.9837 0ZM5.65004 16.7277C5.65004 10.7587 10.3011 5.90096 16.0163 5.90096H47.9837C53.6989 5.90096 58.35 10.7587 58.35 16.7277V47.2723C58.35 53.2413 53.6989 58.099 47.9837 58.099H16.0163C10.3011 58.099 5.65004 53.2413 5.65004 47.2723V16.7277Z" fill="inherit" />
        <path d="M31.9999 47.5558C40.2126 47.5558 46.8978 40.5775 46.8978 31.9962C46.8978 23.415 40.2162 16.4366 31.9999 16.4366C23.7837 16.4366 17.1021 23.415 17.1021 31.9962C17.1021 40.5775 23.7837 47.5558 31.9999 47.5558ZM31.9999 22.3414C37.0998 22.3414 41.2477 26.6736 41.2477 32C41.2477 37.3264 37.0998 41.6586 31.9999 41.6586C26.9 41.6586 22.7521 37.3264 22.7521 32C22.7521 26.6736 26.9 22.3414 31.9999 22.3414Z" fill="inherit" />
        <path d="M48.277 18.9504C50.4885 18.9504 52.291 17.0717 52.291 14.7582C52.291 12.4446 50.4921 10.5658 48.277 10.5658C46.0618 10.5658 44.2629 12.4446 44.2629 14.7582C44.2629 17.0717 46.0618 18.9504 48.277 18.9504Z" fill="inherit" />
      </svg>
    );
  },
  Twitter: (props: React.JSX.IntrinsicElements["svg"]) => {
    return (
      <svg
        fill="#383D38"
        {...props}
        width="64" height="60" viewBox="0 0 64 60" xmlns="http://www.w3.org/2000/svg">
        <path d="M0.153931 0L24.8634 33.0459L0 59.9144H5.59739L27.3674 36.3918L44.9554 59.9144H64L37.9014 25.0097L61.0455 0H55.4481L35.4012 21.6639L19.2023 0H0.157643H0.153931ZM8.3829 4.12331H17.13L55.7635 55.7912H47.0164L8.3829 4.12331Z" fill="inherit" />
      </svg>
    );
  },
  LinkedIn: (props: React.JSX.IntrinsicElements["svg"]) => {
    return (
      <svg
        fill="#383D38"
        {...props}
        width="64" height="60" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
        <path d="M2.2441 12.6659C0.745345 11.2554 0 9.50952 0 7.43223C0 5.35494 0.749331 3.5322 2.2441 2.1177C3.74286 0.707245 5.67212 0 8.03585 0C10.3996 0 12.2531 0.707245 13.7479 2.1177C15.2467 3.52816 15.992 5.3024 15.992 7.43223C15.992 9.56206 15.2427 11.2554 13.7479 12.6659C12.2491 14.0763 10.3478 14.7836 8.03585 14.7836C5.72394 14.7836 3.74286 14.0763 2.2441 12.6659ZM14.7324 20.7568V64H1.25557V20.7568H14.7324Z" fill="inherit" />
        <path d="M59.5955 25.0285C62.5332 28.2617 64.0001 32.6992 64.0001 38.3491V63.2362H51.2009V40.1031C51.2009 37.2539 50.4715 35.0392 49.0166 33.463C47.5617 31.8869 45.6005 31.0947 43.1451 31.0947C40.6897 31.0947 38.7285 31.8828 37.2736 33.463C35.8187 35.0392 35.0892 37.2539 35.0892 40.1031V63.2362H22.2144V20.6356H35.0892V26.2855C36.3927 24.4022 38.1506 22.9149 40.3589 21.8197C42.5672 20.7244 45.0504 20.1788 47.8128 20.1788C52.7316 20.1788 56.6617 21.7954 59.5955 25.0285Z" fill="inherit" />
      </svg>
    );
  },
  Youtube: (props: React.JSX.IntrinsicElements["svg"]) => {
    return (
      <svg
        fill="#383D38"
        {...props}
        width="64" height="45" viewBox="0 0 64 45" xmlns="http://www.w3.org/2000/svg">
        <path d="M63.4546 12.0633C63.0614 8.23691 62.2156 4.00702 59.1022 1.78412C56.6909 0.0603072 53.5213 -0.0034204 50.5624 0.000119253C44.3076 0.0036589 38.0494 0.010752 31.7946 0.0142917C25.7785 0.021371 19.7624 0.0249279 13.7463 0.0320072C11.2331 0.0355469 8.79018 -0.162656 6.45604 0.934635C4.45185 1.87618 2.88289 3.66719 1.93871 5.66001C0.629484 8.43156 0.355719 11.5713 0.197771 14.6366C-0.0935575 20.2186 -0.0619677 25.8148 0.28552 31.3933C0.541748 35.4639 1.19108 39.9627 4.31145 42.5573C7.07731 44.8545 10.9804 44.9678 14.5676 44.9714C25.954 44.982 37.3439 44.9926 48.7337 44.9997C50.1939 45.0033 51.7172 44.9749 53.2054 44.8121C56.1328 44.4935 58.9232 43.6476 60.8045 41.4601C62.7034 39.2549 63.1913 36.186 63.4791 33.2799C64.1811 26.2289 64.1741 19.1107 63.4546 12.0633ZM25.2099 32.3844V12.6155L42.1842 22.4981L25.2099 32.3844Z" fill="inherit" />
      </svg>
    );
  },
  Tiktok: (props: React.JSX.IntrinsicElements["svg"]) => {
    return (
      <svg
        fill="#383D38"
        {...props}
        width="55" height="64" viewBox="0 0 55 64" xmlns="http://www.w3.org/2000/svg">
        <path d="M55 15.4966V26.5275C53.0878 26.3392 50.6031 25.9013 47.8545 24.8862C44.2655 23.56 41.5941 21.7468 39.8442 20.3429V42.6381L39.7996 42.5684C39.828 43.0105 39.8442 43.4607 39.8442 43.9151C39.8442 54.9869 30.9083 64 19.9221 64C8.93593 64 0 54.9869 0 43.9151C0 32.8432 8.93593 23.8261 19.9221 23.8261C20.998 23.8261 22.0536 23.912 23.0848 24.0798V34.9512C22.0942 34.5951 21.0305 34.4027 19.9221 34.4027C14.7213 34.4027 10.4868 38.6677 10.4868 43.9151C10.4868 49.1624 14.7213 53.4275 19.9221 53.4275C25.1229 53.4275 29.3574 49.1583 29.3574 43.9151C29.3574 43.7186 29.3534 43.5221 29.3412 43.3257V0H40.2746C40.3152 0.93323 40.3517 1.87469 40.3923 2.80792C40.4654 4.64573 41.115 6.4099 42.2477 7.85476C43.5753 9.55341 45.5363 11.5262 48.2889 13.1021C50.867 14.5715 53.2867 15.206 55 15.5048V15.4966Z" fill="inherit" />
      </svg>
    );
  },
  Facebook: (props: React.JSX.IntrinsicElements["svg"]) => {
    return (
      <svg
        fill="#383D38"
        {...props}
        width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
        <path d="M63.9789 32.0011C63.9789 48.1631 52.0022 61.5246 36.4435 63.6928C34.9887 63.8946 33.5003 64 31.9895 64C30.2455 64 28.533 63.861 26.8652 63.5919C11.6338 61.1389 0 47.9277 0 32.0011C0 14.3279 14.3236 0 31.9917 0C49.6598 0 63.9834 14.3279 63.9834 32.0011H63.9789Z" fill="inherit" />
        <path d="M36.4434 25.696V32.6671H45.0645L43.6994 42.0575H36.4434V63.6928C34.9886 63.8946 33.5002 64 31.9894 64C30.2454 64 28.5329 63.861 26.8652 63.5919V42.0575H18.9143V32.6671H26.8652V24.1376C26.8652 18.846 31.1533 14.5543 36.4456 14.5543V14.5588C36.4613 14.5588 36.4748 14.5543 36.4905 14.5543H45.0667V22.6757H39.4628C37.7973 22.6757 36.4456 24.0278 36.4456 25.6937L36.4434 25.696Z" fill="white" />
      </svg>
    );
  },
  Pinterest: (props: React.JSX.IntrinsicElements["svg"]) => {
    return (
      <svg
        fill="#383D38"
        {...props}
        width="49" height="64" viewBox="0 0 49 64" xmlns="http://www.w3.org/2000/svg">
        <path fillRule="evenodd" clipRule="evenodd" d="M20.4882 41.7486C20.4359 41.9322 20.3867 42.0879 20.3466 42.2436C18.0224 51.4527 17.7638 53.4981 15.3749 57.7758C14.2359 59.8088 12.9522 61.7359 11.533 63.579C11.3729 63.7876 11.2221 64.0554 10.9019 63.99C10.551 63.9153 10.5233 63.5976 10.4863 63.3112C10.1046 60.5124 9.89218 57.7073 9.98453 54.8805C10.1046 51.2006 10.5541 49.9365 15.2456 29.9865C15.3134 29.6814 15.2364 29.4293 15.1379 29.1553C14.0142 26.0918 13.7926 22.9817 14.7746 19.831C16.8987 13.0254 24.5272 12.5055 25.8601 18.1187C26.6821 21.59 24.5087 26.1355 22.8402 32.8539C21.4579 38.3956 27.9104 42.337 33.4239 38.2897C38.5095 34.56 40.4828 25.6186 40.1073 19.28C39.3684 6.64312 25.6662 3.91275 16.9757 7.98182C7.01075 12.6424 4.74501 25.1329 9.24571 30.8427C9.81523 31.5681 10.8896 32.0133 10.7018 32.7481C10.4124 33.8906 10.1569 35.0426 9.84292 36.1789C9.60895 37.0257 8.27599 37.3339 7.42942 36.9852C5.77013 36.3065 4.38789 35.2387 3.26733 33.8315C-0.553032 29.0464 -1.64588 19.5819 3.40587 11.5683C9.00251 2.69235 19.4139 -0.900407 28.9201 0.189245C40.2735 1.49371 47.4494 9.3392 48.7916 18.2401C49.4042 22.2936 48.964 32.2904 43.3366 39.3576C36.8626 47.477 26.3681 48.0157 21.5257 43.0313C21.1532 42.6483 20.8546 42.2031 20.4882 41.7454V41.7486Z" fill="inherit" />
      </svg>
    );
  },
  Snapchat: (props: React.JSX.IntrinsicElements["svg"]) => {
    return (
      <svg
        fill="#383D38"
        {...props}
        width="66" height="65" viewBox="0 0 66 65" xmlns="http://www.w3.org/2000/svg">
        <path d="M50.2278 21.9839C50.22 22.7297 50.1416 23.4754 50.071 24.2212C50.0475 24.4672 50.0396 24.717 50.0357 24.963C50.0279 25.3886 50.369 25.7752 50.7963 25.7478C51.0002 25.7361 51.208 25.6815 51.404 25.6151C52.0587 25.3848 52.7056 25.1387 53.3564 24.8928C53.9445 24.6663 54.5444 24.4633 55.152 24.2993C55.8146 24.1197 56.4772 23.9869 57.1515 24.0962C58.5237 24.3188 59.4176 25.7908 58.9157 27.0832C58.7746 27.4463 58.559 27.7587 58.3002 28.0476C57.9435 28.442 57.5396 28.7777 57.1005 29.0744C56.583 29.4219 56.0616 29.7617 55.5402 30.1053C54.6698 30.6753 53.7994 31.2414 52.933 31.8193C52.5723 32.0575 52.2234 32.3112 51.8823 32.5728C51.6941 32.7173 51.5177 32.8852 51.3569 33.0609C51.0511 33.4006 50.8825 33.834 50.91 34.287C50.9374 34.7672 51.0198 35.2357 51.1531 35.6964C51.5647 37.1333 52.196 38.4686 52.9605 39.7454C55.0109 43.154 57.916 45.8168 61.3739 47.9916C62.3227 48.589 63.3381 49.0732 64.3378 49.5847C64.479 49.6589 64.6162 49.7331 64.7534 49.8151C64.8906 49.8971 65.0122 50.0063 65.1024 50.1391C65.2435 50.3382 65.2631 50.5491 65.1415 50.7677C65.0396 50.9552 64.8906 51.0996 64.7299 51.2323C64.5495 51.3807 64.3535 51.5057 64.1496 51.6189C62.9813 52.2787 61.7268 52.7707 60.4291 53.1143C59.8018 53.2822 59.1745 53.458 58.5511 53.6415C58.1669 53.7547 57.8062 53.9226 57.4495 54.1061C56.8496 54.4145 56.5085 54.9104 56.3831 55.5546C56.3125 55.9138 56.2694 56.2809 56.2027 56.6401C56.038 57.5498 55.7088 57.8817 54.8149 58.0379C54.5012 58.0926 54.1797 58.1237 53.8622 58.1433C53.0624 58.1862 52.2626 58.2096 51.4628 58.2526C51.4393 58.2526 51.4157 58.2526 51.3961 58.2565C48.2009 58.4439 45.1116 59.463 42.3711 61.1107C42.226 61.1966 42.081 61.2864 41.932 61.3723C40.8225 62.0399 39.6934 62.6764 38.4819 63.1449C37.1293 63.6681 35.7336 63.9805 34.287 64.1093C33.0677 64.2186 31.8523 64.2031 30.6409 64.0664C28.8256 63.8634 27.1045 63.3363 25.4775 62.5085C24.7208 62.122 23.9916 61.6925 23.2702 61.2513C23.231 61.2279 23.1918 61.2044 23.1487 61.177C20.271 59.4317 16.9974 58.3736 13.6297 58.237C13.6061 58.237 13.5826 58.237 13.5591 58.237C12.8534 58.2096 12.1516 58.1823 11.4459 58.1393C11.0343 58.1159 10.6226 58.0613 10.2188 57.9519C9.70522 57.8114 9.33276 57.3624 9.23475 56.8431C9.17986 56.5581 9.13674 56.2731 9.08577 55.9881C9.07401 55.9256 9.07398 55.8591 9.06222 55.7967C8.94461 54.9611 8.52903 54.3481 7.741 53.9928C7.25878 53.7742 6.76483 53.5984 6.25516 53.4579C5.37696 53.2158 4.49089 53.0011 3.63229 52.6926C2.87563 52.4232 2.14643 52.103 1.44465 51.7204C1.23295 51.6072 1.02905 51.4783 0.833028 51.3456C0.69973 51.2558 0.574296 51.1543 0.464521 51.041C0.111673 50.674 0.146942 50.1938 0.546837 49.8814C0.778148 49.6979 1.04865 49.5769 1.31132 49.4481C3.14221 48.5539 4.87904 47.5075 6.4943 46.2659C8.93287 44.3956 10.999 42.1818 12.5633 39.515C13.12 38.5623 13.5826 37.5511 13.9629 36.5125C14.2138 35.8253 14.4177 35.1382 14.4687 34.4158C14.5039 33.8848 14.3236 33.3576 13.959 32.9711C13.6963 32.6939 13.3905 32.4714 13.0808 32.2488C12.6181 31.9209 12.1516 31.5968 11.6812 31.2883C10.6697 30.6246 9.65424 29.9725 8.64275 29.3127C8.19973 29.0237 7.77236 28.7114 7.38815 28.3483C7.10979 28.0828 6.85888 27.7977 6.65893 27.4658C6.24336 26.7708 6.212 26.0563 6.52172 25.3223C6.70599 24.885 7.05103 24.514 7.48228 24.3149C8.09781 24.0338 8.74077 23.9986 9.38766 24.104C10.0855 24.2173 10.7559 24.432 11.4185 24.6702C12.0967 24.9123 12.7632 25.1778 13.4376 25.4277C13.7512 25.5449 14.0687 25.662 14.402 25.7127C14.4804 25.7245 14.5588 25.7478 14.6373 25.7439C15.0254 25.7322 15.3468 25.6073 15.3429 25.088C15.3429 24.8381 15.3351 24.5843 15.3234 24.3344C15.2645 23.085 15.19 21.8355 15.1587 20.5861C15.0881 17.6109 15.0998 14.624 15.88 11.7347C16.3583 9.96597 17.1856 8.32219 18.3029 6.86191C20.62 3.82814 23.678 1.9032 27.3358 0.876321C29.3353 0.314076 31.3779 0.130565 33.448 0.193037C35.4043 0.251604 37.3528 0.579634 39.2033 1.22387C41.3988 1.98915 43.3943 3.09796 45.0919 4.67146C47.0208 6.45971 48.5067 8.54867 49.3144 11.0788C49.679 12.2228 49.9103 13.3941 50.0514 14.5811C50.3455 17.037 50.2631 19.5085 50.2396 21.9761L50.2278 21.9839Z" fill="inherit" />
      </svg>

    );
  },
  Whatsapp: (props: React.JSX.IntrinsicElements["svg"]) => {
    return (
      <svg
        fill="#383D38"
        {...props}
        width="67" height="64" viewBox="0 0 67 64" xmlns="http://www.w3.org/2000/svg">
        <path d="M43.2162 46.8621C30.22 46.8621 19.6466 36.282 19.6431 23.282C19.6466 19.9866 22.3291 17.3069 25.6164 17.3069C25.9544 17.3069 26.2888 17.3354 26.609 17.3923C27.3134 17.5097 27.9823 17.7482 28.5977 18.1076C28.6867 18.161 28.7472 18.2464 28.7614 18.3461L30.1346 27.0044C30.1524 27.1076 30.1204 27.2073 30.0528 27.282C29.295 28.1218 28.3273 28.7269 27.2494 29.0294L26.73 29.1753L26.9256 29.677C28.6973 34.1895 32.3048 37.7945 36.8195 39.5738L37.3212 39.7731L37.467 39.2536C37.7694 38.1753 38.3742 37.2073 39.2138 36.4493C39.2743 36.3923 39.3561 36.3639 39.438 36.3639C39.4558 36.3639 39.4736 36.3639 39.4949 36.3674L48.1507 37.7411C48.2539 37.7589 48.3393 37.8158 48.3926 37.9048C48.7484 38.5205 48.9868 39.193 49.1077 39.8976C49.1647 40.2108 49.1896 40.5418 49.1896 40.887C49.1896 44.1788 46.5106 46.8585 43.2162 46.8621Z" fill="inherit" />
        <path d="M66.2626 29.1537C65.5617 21.232 61.9329 13.8833 56.045 8.46334C50.1214 3.01138 42.4368 0.0078125 34.4001 0.0078125C16.7611 0.0078125 2.40947 14.3637 2.40947 32.0078C2.40947 37.9295 4.04241 43.6982 7.13403 48.7231L0.239258 63.9901L22.3146 61.6377C26.1534 63.2107 30.2162 64.0078 34.3965 64.0078C35.4958 64.0078 36.6236 63.9509 37.755 63.8334C38.7511 63.7267 39.7579 63.5702 40.747 63.3709C55.5184 60.3851 66.3017 47.2712 66.3871 32.1786V32.0078C66.3871 31.047 66.3444 30.0861 66.259 29.1573L66.2626 29.1537ZM23.1649 54.9366L10.9515 56.2392L14.5981 48.1573L13.8687 47.1787C13.8154 47.1075 13.762 47.0363 13.7015 46.9544C10.5352 42.5808 8.86306 37.4135 8.86306 32.0114C8.86306 17.926 20.3188 6.46686 34.4001 6.46686C47.5919 6.46686 58.7737 16.7623 59.8517 29.9046C59.9086 30.6093 59.9406 31.3174 59.9406 32.0149C59.9406 32.2142 59.937 32.4099 59.9335 32.6199C59.6631 44.4028 51.4342 54.41 39.9216 56.958C39.0428 57.1538 38.1427 57.3032 37.2462 57.3993C36.3141 57.5061 35.3571 57.5595 34.4072 57.5595C31.0238 57.5595 27.7365 56.9047 24.6307 55.6093C24.2856 55.4705 23.9476 55.321 23.631 55.168L23.1685 54.9438L23.1649 54.9366Z" fill="inherit" />
      </svg>
    );
  },
} as const;

export default SocialMediaIcons
