import { TClient } from "@/src/services/clients.service";
import { useMemo } from "react";
import ClientInformation from "./client-info";
import CheckBox from "@/components/ui/checkbox";

interface Props {
  clients: TClient[];
  selectedClients: TClient[];
  isFetching: boolean;
  onSelectClient: (clientId: TClient) => void;
  onEditClient: (clientId: TClient) => void;
  onSelectAllClients: (clientIds: TClient[]) => void;
}

const tableColumns = [
  "Full Name",
  "Email",
  "Phone",
  "Appointmet Date",
] as const;

const ClientListTable = (props: Props) => {
  const { clients, selectedClients } = props;
  const allCheckedMemo = useMemo(() => {
    return selectedClients.length === clients.length;
  }, [selectedClients, clients]);
  return (
    <div className="w-fit flex flex-col ">
      <div className="flex-grow rounded-t-lg bg-subtle-gray grid grid-cols-[64px_repeat(4,160px)_64px] md:grid-cols-[64px_repeat(4,200px)_64px]">
        <div className="flex items-center justify-center ">
          <CheckBox
            checked={allCheckedMemo}
            onChange={(checked) => {
              if (checked) props.onSelectAllClients(clients);
              else props.onSelectAllClients([]);
            }}
          />
        </div>
        {tableColumns.map((key, index) => (
          <div key={index} className="px-1 pl-4 pb-2.5 text-sm ">
            <div className="w-full pt-2.5 rounded-lg whitespace-nowrap ">
              {key}
            </div>
          </div>
        ))}
        <div className="text-center text-sm"></div>
      </div>
      {clients?.map((client, index) => (
        <ClientInformation
          key={index}
          client={client}
          index={index}
          selectedClients={selectedClients}
          onSelectClient={props.onSelectClient}
          onEditClient={props.onEditClient}
        />
      ))}
    </div>
  );
};

export default ClientListTable;
