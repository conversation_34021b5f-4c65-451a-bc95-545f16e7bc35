import { api } from "../api.service";
import { AxiosResponse } from "axios";

interface PromoCodePayload {
  code: string;
  codeType: "subscription" | "credit_purchase";
  discountType: "fixed" | "percentage";
  discountAmount: number;
  maxUsage: number;
}

interface Discount {
  type: "fixed" | "percentage";
  amount: number;
}

interface PromoCode {
  code: string;
  type: "subscription" | "credit_purchase";
  discount: Discount;
  description: string;
  expiringDate: Date;
  isActive: boolean;
  maxUsage: number;
  createdAt: Date;
  updatedAt: Date;
}

export const CreatePromoCode = async (
  payload: PromoCodePayload
): Promise<
  AxiosResponse<{
    promoCode: PromoCode;
  }>
> => {
  try {
    const response = await api.post(`/admin/promo-code`, payload);
    return response;
  } catch (error) {
    console.error("create-promocode-error", error);
    throw error;
  }
};

export const GetAllPromoCodes = async (): Promise<
  AxiosResponse<PromoCode[]>
> => {
  try {
    const response = await api.get(`/admin/promo-code`);
    return response;
  } catch (error) {
    console.error("get-promocode-error", error);
    throw error;
  }
};

export const GetPromoCode = async (
  id: string
): Promise<AxiosResponse<PromoCode>> => {
  try {
    const response = await api.get(`/admin/promo-code/${id}`);
    return response;
  } catch (error) {
    console.error("get-promocode-error", error);
    throw error;
  }
};

export const UpdatePromoCode = async (
  id: string,
  payload: PromoCode
): Promise<
  AxiosResponse<{
    message: string;
  }>
> => {
  try {
    const response = await api.put(`/admin/promo-code/${id}`, payload);
    return response;
  } catch (error) {
    console.error("update-promocode-error", error);
    throw error;
  }
};

export const DeletePromoCode = async (
  Id: string
): Promise<
  AxiosResponse<{
    message: string;
  }>
> => {
  try {
    const response = await api.delete(`/admin/promo-code/${Id}`);
    return response;
  } catch (error) {
    console.error("delete-promocode-error", error);
    throw error;
  }
};
