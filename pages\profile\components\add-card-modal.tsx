import { X } from "lucide-react";
import Modal from "@/components/ui/modal";
import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import { useToast } from "@/src/contexts/hooks/toast";
import { useMutation } from "@tanstack/react-query";
import {
  AddPaymentMethod,
  CreateSetupIntent,
} from "@/src/services/stripe.service";
import PendingOverlay from "@/components/ui/pending-overlay";
import { FC, useEffect, useState } from "react";
import {
  CardElement,
  Elements,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import ErrorScreen from "@/components/ui/error-screen";
import Toggle from "@/components/ui/toggle-2";
import MoreInfo from "@/components/ui/more-info";

type Props = {
  open: boolean;
  onClose: () => void;
  clientSecret: string;
  /** 
   * @dev if there is no payment method at all, this card must be default
   * */
  hasPaymentMethods: boolean
  setClientSecret: (clientSecret: string) => void;
  onSuccess: () => void;
};

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

export default function AddCardModal({ open, onClose, ...props }: Props) {
  const { data: setupIntentData, ...createSetupIntentMutation } = useMutation({
    mutationFn: CreateSetupIntent,
  });
  useEffect(() => {
    if (props.clientSecret) return;
    else
      createSetupIntentMutation.mutateAsync(undefined, {
        onSuccess: (data) => {
          console.log(data.data.clientSecret)
          props.setClientSecret(data.data.clientSecret);
        },
      });
  }, []);

  return (
    <Modal open={open} onClose={onClose}>
      <Modal.Body
        className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Add New Card
          </Typography>
          <Button
            variant="icon"
            onTap={onClose}
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="pt-2 w-full flex items-center justify-center ">
          {createSetupIntentMutation.isPending ? (
            <CircularLoader />
          ) : props.clientSecret ? (
            <Elements
              stripe={stripePromise}
              options={{ clientSecret: props.clientSecret }}
            >
              <StripeAddCardForm
                hasPaymentMethods={props.hasPaymentMethods}
                clientSecret={props.clientSecret}
                onSuccess={() => {
                  onClose();
                  props.onSuccess();
                }}
              />
            </Elements>
          ) : (
            <ErrorScreen
              onRetry={() => createSetupIntentMutation.mutateAsync()}
            />
          )}
        </div>
      </Modal.Body>
    </Modal>
  );
}

type AddCardProps = {
  onSuccess: () => void;
  clientSecret: string;
  /** 
   * @dev if there is no payment method attached at all, this card will be created as default by force
   *
   */
  hasPaymentMethods: boolean
};

const StripeAddCardForm: FC<AddCardProps> = (props) => {
  const stripe = useStripe();
  const elements = useElements();
  const [loading, setLoading] = useState(false);
  const [methodType, setMethodType] = useState<"default" | "others">(props.hasPaymentMethods ? 'default' : 'others');
  const showToast = useToast();
  const addPaymentMethodMutation = useMutation({
    mutationFn: AddPaymentMethod,
    onSuccess() {
      showToast("success", "Payment method added successfully");
      props.onSuccess();
    },
  });
  const handleSubmit = async () => {
    if (!stripe || !elements) return;
    setLoading(true);
    const result = await stripe.confirmCardSetup(props.clientSecret, {
      payment_method: {
        card: elements.getElement(CardElement)!,
      },
    });
    if (result.error) {
      showToast("error", result.error.message || "Failed to add card");
    } else {
      // Card was successfully set up
      // on success, this closes the modal
      await addPaymentMethodMutation.mutateAsync({
        paymentId: result.setupIntent.payment_method as string,
        type: methodType,
      });
    }
    setLoading(false);
  };
  return (
    <div onSubmit={handleSubmit} className="w-full flex flex-col">
      <div className="w-full flex items-center justify-between pb-3" >
        <Typography className="text-base !mt-0">
          Add a card that will be used to pay for subscriptions and lead credits.
        </Typography>
      </div>
      <CardElement
        className="font-Aeonik_Fono my-2"
        options={{
          style: {
            base: {
              fontSize: "16px",
              fontFamily: "inherit",
              color: "#424770",
              "::placeholder": {
                color: "#aab7c4",
              },
            },
            invalid: {
              color: "#9e2146",
            },
          },
          disableLink: true,
          hidePostalCode: true,
        }}
      />
      {props.hasPaymentMethods ?
        (<MoreInfo.Static className="text-sm pt-3 pb-1" content="This will be used as the default payment method" />) :
        (<div className="w-full flex items-center justify-between pt-4 mb-3 ">
          <Typography className="text-paragraph text-sm">
            Set as default card
          </Typography>
          <Toggle
            checked={methodType === "default"}
            onChange={(checked) => setMethodType(checked ? "default" : "others")}
            name="isDefaultCard"
          />
        </div>)}
      <div className="mt-3" >
        <Button
          onTap={handleSubmit}
          disabled={!stripe || !elements}
          className="w-full text-white py-2 flex items-center justify-center "
        >
          Add Card
        </Button>
      </div>
      <PendingOverlay isPending={loading} />
    </div>
  );
};
