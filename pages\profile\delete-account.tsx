import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";

/**
 * @note this component takes care of it's own state, mutations to database.
 */
const DeleteAccount = () => {
  return (
    <section>
      <div className="pb-3 ">
        <Typography className="text-gray-500 pb-5 rounded-xl ">
          Once you delete your account, there is no going back. Please be
          certain.
        </Typography>
      </div>
      <div className="flex justify-end gap-x-3">
        <Button>Deactivate</Button>
        <Button>Delete Account</Button>
      </div>
    </section>
  );
};

export default DeleteAccount;
