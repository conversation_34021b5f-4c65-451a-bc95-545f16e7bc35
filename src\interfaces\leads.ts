export type Lead = {
  name: string;
  location: string;
  timeAgo: string;
  description: string;
  credits: number;
  isPaymentVerified: boolean;
  hasHighHiringIntent: boolean;
};

/** 
 * api.get('/api/leads?keyword=Accounts&locations=London,Manchester')
*/ 
export type TLead = {
  _id: string;
  name: string;
  industry: string;
  field: string[]
  description: string;
  town: string;
  business_functions: string[];
  // number of credits, you will calculate
  requiredCredit: 10 | (number & {});
};

/**
 * api.get('/api/leads/:id')
 */
export type TLeadDetails = TLead & {
  // show first three of post code
  // 384***
  postcode: string;
  // first 3 letters of email before the @ and after use the first two letters of the domain(@gmail.com -> @gm***.com), feel free to leave the tld (.com)
  // ***thebrand@gm***.com
  email_addresses: string[];
  // first 4 digits of telephone number should be asterisks `0810******` ( strip of space )
  telephone_numbers: string[];
  url_maps: {
    _id: string;
    website: string;
  }[]
  address: string;
  address2: string;
  location: {
    type: "Point";
    coordinates: [number, number];
  };
};
