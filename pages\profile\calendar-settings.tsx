import { SaveButton } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Toggle from "@/components/ui/toggle-2";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Clock12Icon, Clock1Icon } from "lucide-react";
import React, { useMemo, useState } from "react";
import { useToast } from "~/contexts/hooks/toast";
import AdvanceBookingLimitSelect from "./components/advance-booking-limit";
import TimePicker from "./components/time-picker";
import TimeSlotSelector from "./components/time-slot-selector";
import TimezoneSelector from "./components/time-zone-selector";
import { GetCalendarSettings, UpdateCalendarSettings } from "@/src/services/calendar-settings.service";
import { useFormik } from "formik";
import { noop } from "@/lib/helpers";
import { array, number, object, string } from "yup";
import PendingOverlay from "@/components/ui/pending-overlay";
import CircularLoader from "@/components/ui/circular-loader";

interface Workday {
  day: string;
  startTime: string; // HH:MM format
  closingTime: string; // HH:MM format
}

export interface CalendarPayload {
  workdays?: Workday[] | undefined;
  advanceBookingLimit?: number;
  timezone?: string;
  customTimeslotDuration?: number;
  timeslots?: string;
}

const daysOfWeek = [
  "sunday",
  "monday",
  "tuesday",
  "wednesday",
  "thursday",
  "friday",
  "saturday",
];
const CalendarSettings: React.FC = () => {
  const { data: calendarSettings, isFetching } = useQuery({
    queryKey: ['calendar-settings-setting'],
    queryFn: async () => {
      const { calendarSettings } = (await GetCalendarSettings()).data;
      console.log(calendarSettings, 'settings')
      return calendarSettings
    }
  })
  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      workdays: calendarSettings?.workdays || [],
      advanceBookingLimit: calendarSettings?.advanceBookingLimit || null,
      timezone: calendarSettings?.timezone || 'Europe/London',
      timeslots: calendarSettings?.timeslots || '',
      customTimeslotDuration: calendarSettings?.customTimeslotDuration || null,
    },
    validationSchema: object({
      workdays: array().of(
        object({ day: string().required(), startTime: string().required(), closingTime: string().required() })
      ),
      advanceBookingLimit: number().required('Advance booking Limit is required'),
      timezone: string().required('Timesone is required'),
      timeslots: string().required('Timeslot is required'),
      customTimeslotDuration: number().nullable()
    }),
    onSubmit: noop
  })
  const [sameTime, setSameTime] = useState(false);
  const queryClient = useQueryClient();
  const showToast = useToast();
  const updateCalendarSettingsMutation = useMutation({
    mutationFn: UpdateCalendarSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["userDetails"] })
      queryClient.invalidateQueries({ queryKey: ['calendar-settings'] })
      showToast("success", "Calendar Settings updated successfully");
    },
    onError: (error: ApiError) =>
      showToast("error", error.response?.data?.error || error?.message),
  });

  const setStartTime = (day: string, newStartTime: string) => {
    const { workdays = [] } = formik.values
    const updatedWorkdays = workdays.map(workday => {
      return { ...workday, startTime: (workday.day === (day as any)) ? newStartTime : workday.startTime }
    })
    formik.setFieldValue('workdays', updatedWorkdays)
  };

  const setEndTime = (day: string, newClosingTime: string) => {
    const { workdays = [] } = formik.values
    const updatedWorkdays = workdays.map(workday => {
      return { ...workday, closingTime: (workday.day === (day as any)) ? newClosingTime : workday.closingTime }
    })
    formik.setFieldValue('workdays', updatedWorkdays)
  };

  const weekendDays = ["saturday", "sunday"];
  const hasWeekendAvailability = useMemo(() => {
    return (
      formik.values.workdays.some((workday) =>
        weekendDays.includes(workday.day)
      ) || false
    );
  }, [formik.values.workdays]);
  const setWeekendAvailability = (checked: boolean) => {
    // remove weekend days from workdays
    const existingWorkdaysWithoutWeekend = formik.values.workdays
      .filter(weekendDay => !weekendDays.includes(weekendDay.day))
    if (checked) {
      const weekendWorkDays = weekendDays.map((weekendDay) => ({
        day: weekendDay,
        startTime: "10:00",
        closingTime: "15:00",
      }));
      formik.setFieldValue('workdays', [
        ...existingWorkdaysWithoutWeekend,
        ...weekendWorkDays
      ])
      showToast("info", "Weekend availability added with default time");
    } else {
      formik.setFieldValue('workdays', [
        ...existingWorkdaysWithoutWeekend,
      ])
      showToast("info", "Weekend availability removed");
    }
  };

  const handleSameTimeToggle = (checked: boolean) => {
    setSameTime(checked);
    if (checked) {
      const { workdays } = formik.values
      const firstEnabledDay = workdays[0];
      if (firstEnabledDay) {
        const updatedWorkdays = workdays.map((workday) => ({
          ...workday,
          startTime: firstEnabledDay.startTime,
          closingTime: firstEnabledDay.closingTime,
        }));
        formik.setFieldValue('workdays', updatedWorkdays)
        showToast("success", "All enabled days set to the same time");
      }
    }
  };

  const handleSubmit = () => {
    formik.validateForm().then((val) => {
      const errors = Object.values(val);
      if (errors.length === 0) {
        const { values } = formik;
        updateCalendarSettingsMutation.mutateAsync({
          ...values,
          advanceBookingLimit: values.advanceBookingLimit || 0,
          customTimeslotDuration: values.customTimeslotDuration || undefined
        });
      } else
        errors.forEach((error, i) =>
          setTimeout(() => {
            showToast("error", "Invalid Field", {
              description: error as any,
            });
          }, i * 500),
        );
    });
  };
  const daysOfWeekMemo = useMemo(() => daysOfWeek, [formik.values.workdays])
  if (isFetching)
    return <div className="py-20">
      <CircularLoader />
    </div>
  return (
    <section className="flex flex-col gap-y-8 pt-4 px-1 md:max-h-[540px] md:overflow-y-auto no-scrollbar ">
      <AdvanceBookingLimitSelect
        advanceBookingLimit={formik.values.advanceBookingLimit}
        setAdvanceBookingLimit={(newAdvanceBookingLimit) => {
          formik.setFieldValue('advanceBookingLimit', newAdvanceBookingLimit)
        }}
      />
      <TimeSlotSelector
        timeSlotsProps={{
          timeslots: formik.values.timeslots!,
          customTimeslotDuration: formik.values.customTimeslotDuration || undefined
        }}
        setTimeslot={({ timeslots, customTimeslotDuration }) => {
          if (timeslots === 'custom') {
            formik.setFieldValue('timeslots', 'custom')
            formik.setFieldValue('customTimeslotDuration', customTimeslotDuration)
          } else {
            formik.setFieldValue('timeslots', timeslots)
            formik.setFieldValue('customTimeslotDuration', customTimeslotDuration)
          }
        }}
      />
      <TimezoneSelector
        timezone={formik.values.timezone}
        setTimezone={(newValue) => formik.setFieldValue('timezone', newValue)}
      />
      <div className="w-full flex items-center justify-between ">
        <Typography className=" text-paragraph">
          Weekend Availability
        </Typography>
        <Toggle
          checked={hasWeekendAvailability}
          onChange={setWeekendAvailability}
        />
      </div>
      <div className="w-full flex items-center justify-between ">
        <Typography className=" text-paragraph">
          Apply same time to days
        </Typography>
        <Toggle checked={sameTime} onChange={handleSameTimeToggle} />
      </div>
      <Typography className="text-gray-500 border-b-2 border-gray-500/20 pb-3">
        Work days
      </Typography>
      <div className="flex flex-col gap-y-6">
        {daysOfWeekMemo.map((day, index) => {
          const workDay =
            formik.values.workdays &&
            formik.values.workdays.find((workday) => workday.day === day);
          return (
            <div key={index} className="flex justify-between items-start gap-4">
              <div className="w-fit flex flex-col gap-y-3 ">
                <p className="capitalize font-medium">{day}</p>
                {!workDay ? (
                  <span className="text-gray-500 text-sm">Unavailable</span>
                ) : (
                  <div className="flex items-center gap-4 w-fit">
                    <TimePicker
                      icon={<Clock1Icon className="text-green-500" />}
                      time={workDay?.startTime}
                      setTime={(newStartTime) => {
                        setStartTime(day, newStartTime);
                        setSameTime(false);
                      }}
                    />
                    <span>-</span>
                    <TimePicker
                      icon={<Clock12Icon className="text-red-500" />}
                      time={workDay?.closingTime}
                      setTime={(newClosingTime) => {
                        setEndTime(day, newClosingTime);
                        setSameTime(sameTime);
                      }}
                    />
                  </div>
                )}
              </div>
              <Toggle
                checked={!!workDay}
                onChange={() => {
                  const { workdays: existingWorkdays = [] } = formik.values
                  if (workDay)
                    formik.setFieldValue('workdays', existingWorkdays.filter(
                      (workday) => workday.day !== day
                    )
                    );
                  else
                    formik.setFieldValue('workdays', [
                      ...existingWorkdays,
                      { day, startTime: '09:00', closingTime: '17:00' }
                    ])
                }}
              />
            </div>
          );
        })}
      </div>
      <SaveButton
        disabled={updateCalendarSettingsMutation.isPending}
        loading={updateCalendarSettingsMutation.isPending}
        onTap={() => {
          handleSubmit();
        }}
      >
      </SaveButton>
      <PendingOverlay isPending={updateCalendarSettingsMutation.isPending} />
    </section>
  );
};

export default CalendarSettings;
