import { useMemo } from "react";
import { useDigitalProfileContext } from "../hooks/use-digital-profile";
import { usePlanRole } from "@/src/contexts/use-plan-role";
import { Button } from "@/components/buttons";
import { useToast } from "@/src/contexts/hooks/toast";
import { useNavigate } from "react-router-dom";
import { PlusSignIcon } from "hugeicons-react";
import LinkCard from "./link-card";
import { Reorder } from "framer-motion";

/** 
 * @dev this is the editor body to add new link, edit links;
 * @dev no props, every thing even functions are passed through context
 * */
const LinkEditor = () => {
  const { profile, linkSettings } = useDigitalProfileContext()
  const {
    planRole: { shortName: planShortName },
  } = usePlanRole();
  const canCreateMoreThan3LinksMemo = useMemo(() => {
    if (planShortName === "none" || planShortName === "free")
      return (profile.websiteLinks?.length || 0) < 3
    return true;
  }, [
    planShortName,
    profile.websiteLinks
  ]);
  const showToast = useToast()
  const navigate = useNavigate()
  return (
    <section className="" >
      <Button
        onTap={() => {
          const index = (profile.websiteLinks?.length || 0) + 1
          if (canCreateMoreThan3LinksMemo)
            linkSettings.add({
              title: `New Link ${index}`,
              url: "https://deadlink.com",
              buttonStyle: profile.theme?.buttonStyles[0].name!,
              type: 'link',
              icon: null,
              order: index,
              description: 'No description',
              isActive: true,
            });
          else {
            showToast("info", "Upgrade plan to create more links", {
              action: {
                title: "Upgrade",
                onClick() {
                  navigate("/profile/billing");
                  return true;
                },
              },
              duration: 7000
            });
          }
        }}
        className="w-full !rounded-full py-3 px-4 mb-3 flex items-center justify-center gap-2 font-medium"
      >
        <PlusSignIcon size={20} className="stroke-white" />
        Add Link
      </Button>
      <Reorder.Group
        as='section'
        axis='y'
        values={profile.websiteLinks || []}
        className='w-full grid gap-y-4'
        onReorder={(newOrder) => linkSettings.reorder(newOrder.map(v => v._id))}
      >
        {profile.websiteLinks?.map((link, index) => (
          <Reorder.Item
            as='div'
            dragElastic={0.1}
            key={link._id}
            value={link}
            layoutId={`item-${link._id}`} // Helps with complex animations
            layout
          >
            <LinkCard
              key={index}
              link={link}
            />
          </Reorder.Item>
        ))}
      </Reorder.Group>
    </section>
  )
}

export default LinkEditor
