export interface SubscriptionPlan {
  _id: string;
  name: string;
  /**
   * @dev this comes as the unitAmount in the database
   */
  price: number;
  description: string;
  recurring: "week" | "month" | "year";
  discount?: {
    id: string;
    name: string;
    coupon_code?: string;
  };
  /**
   * @deprecated this has no need
   * If user1 selects yearly price, it will show up in the `SubscriptionPlan.price` field
   */
  yearlyPrice?: number;
  features: string[];
}
