import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { ArrowRight02Icon } from "hugeicons-react";
import React from "react";
import { useNavigate } from "react-router-dom";

const WavyLines = (props: React.JSX.IntrinsicElements["svg"]) => {
  return (
    <svg
      width="1440"
      height="780"
      fill="none"
      stroke="white"
      {...props}
      viewBox="0 0 1440 780"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M53.8776 777.576C74.9221 684.2 166.421 490.819 364.062 464.298C611.112 431.148 789.713 612.305 1066.16 555.479C1287.32 510.017 1472.98 167.625 1538.17 2.11119M-9.22097 451.838C27.6487 522.689 151.724 654.364 353.069 614.259C604.75 564.128 940.08 97.0631 1262.15 134.758C1519.8 164.914 1738.81 206.601 1816.1 223.675M-4.58385 635.541C8.98734 523.213 96.1381 288.193 336.172 246.743C636.214 194.929 860.731 399.473 1184.82 336.279C1444.09 285.723 1614.96 133.043 1668 63.0229"
        stroke="inherit"
        strokeOpacity="0.8"
        strokeWidth="3"
        strokeLinecap="round"
        width={"inherit"}
        height={"inherit"}
      />
    </svg>
  );
};

export default function NotFound() {
  const navigate = useNavigate();
  return (
    <section className="w-fit h-fit relative px-4 flex flex-col justify-center gap-y-12 md:flex-row md:items-center md:mt-10">
      <div className="fixed -z-10 -top-20 left-0 ">
        <WavyLines
          width="1440"
          height="780"
          className="md:!w-screen md:scale-[1.1] stroke-gray-100 "
        />
      </div>

      <div className="w-full relative z-[100] pt-20 flex flex-col gap-y-2 md:w-fit md:gap-y-8 md:-translate-y-[25%] ">
        <Typography variant={"h1"} className=" text-8xl mb-3">
          404
        </Typography>
        <Typography variant={"h2"} className=" text-2xl">
          Page Not Found
        </Typography>
        <Typography variant={"span"} className=" !mt-0 ">
          You may not have permission to access this page.{" "}
          <br className="hidden md:inline" /> Please check the URL or go back to
          the previous page.
        </Typography>
        <Button
          onTap={() => navigate("/")}
          className="text-white w-fit flex gap-x-1 mt-4 items-center"
        >
          Go Home <ArrowRight02Icon  />
        </Button>
      </div>
    </section>
  );
}
