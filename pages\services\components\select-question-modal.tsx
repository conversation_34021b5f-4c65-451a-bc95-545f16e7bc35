import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";
import { or, pick, sleep } from "@/lib/helpers";
import { AddQuestionRequest } from "@/src/interfaces/intake-form";
import { X } from "lucide-react";
import { FC, useEffect, useMemo, useRef, useState } from "react";
import { QUESTION_TYPES, QUESTION_TYPES_WITH_OPTIONS } from "./form-constants";

type Props = {
  open: boolean;
  order: number;
  onClose(): void;
  onSelect(data: AddQuestionRequest): void;
};

const SelectQuestionModal: FC<Props> = (props) => {
  const [searchTerm, setSearchTerm] = useState('')
  const filteredTypes = useMemo(() => {
    if (!searchTerm) return QUESTION_TYPES;
    const lowerCasedSearchTerm = searchTerm.toLowerCase()
    const filteredTypes = QUESTION_TYPES.filter(type =>
      or<boolean>([
        type.description.toLowerCase().includes(lowerCasedSearchTerm),
        type.label.toLowerCase().includes(lowerCasedSearchTerm),
        type.value.toLowerCase().includes(lowerCasedSearchTerm)
      ])
    )
    return filteredTypes;
  }, [QUESTION_TYPES, searchTerm])
  const inputRef = useRef<HTMLInputElement>(null)
  async function focusOnInput() {
    if (!props.open) return
    await sleep(100)
    inputRef.current
      ?.focus?.()
  }
  useEffect(() => {
    focusOnInput()
  }, [props.open])
  return (
    <Modal {...pick(props, 'open', 'onClose')} >
      <Modal.Body
        className={`min-h-fit max-h-[400px] min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Select Question Type
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        {/* search for question type */}
        <div className="w-full" >
          <Input.Text
            label=""
            inputRef={inputRef}
            name="search_question_types"
            placeholder="Search..."
            onChange={({ currentTarget: { value } }) => {
              setSearchTerm(value || '')
            }}
            className="w-full"
          />
        </div>
        <div className="w-full grid grid-cols-1 max-h-56 mt-5 pr-1 overflow-y-auto thin-scrollbar gap-4">
          {filteredTypes.map((type) => (
            <div
              className="w-full"
              key={type.value}
            >
              <Button
                // no hover for this 
                whileHover={undefined}
                whileTap={undefined}
                variant="dormant"
                className="w-full h-auto p-2 flex flex-col rounded-2xl items-start text-left"
                onTap={() => props.onSelect({ 
                  type: type.value, 
                  order: props.order, 
                  required: false, 
                  question: 'New Question ✅',
                  options: QUESTION_TYPES_WITH_OPTIONS.includes(type.value) ? ['Default Option'] : undefined 
                })}
              >
                <div className="flex items-center gap-3">
                  <span className="text-[18px] px-2">{type.icon}</span>
                  <div>
                    <Typography className="font-medium">
                      {type.label}
                    </Typography>
                    <Typography className="text-sm text-gray-500">
                      {type.description}
                    </Typography>
                  </div>
                </div>
              </Button>
            </div>
          ))}
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default SelectQuestionModal;
