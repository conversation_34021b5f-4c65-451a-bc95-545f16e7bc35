import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { Palette, PencilIcon } from "lucide-react";
import { useRef } from "react";
import { useToast } from "@/src/contexts/hooks/toast";
import ButtonTooltip from "@/pages/services/components/button-tooltip";
import { Copy } from "@gravity-ui/icons";
import useAuthStore from "@/pages/auth/components/auth-store";
import DisplayNameModal from "./display-name-modal";
import ThemeChangeModal from "./theme-change-modal";
import { useDigitalProfileContext } from "../hooks/use-digital-profile";
import { useFormik } from "formik";
import { and, noop, pick } from "@/lib/helpers";
import { useModalsBuilder } from "@/lib/modals-builder";
import { Button } from "@/components/buttons";

const Avatar = (props: React.JSX.IntrinsicElements["img"]) => {
  return (
    <img
      src={props.src}
      alt={props.alt}
      className={cn("w-44 h-44 rounded-full object-cover", props.className)}
    />
  );
};

/** 
 * @dev no props, every thing even functions are passed through context
 * */
const ProfileHeader = () => {
  const {
    profile,
    imageSettings: { uploadProfileImage },
    basicDetailsSettings: { update: updateDetails },
    themeSettings: { update: updateTheme }
  } = useDigitalProfileContext();
  const formik = useFormik({
    initialValues: pick(profile, 'hasImage', 'imageUrl', 'coverImage'),
    enableReinitialize: true,
    onSubmit: noop
  })
  const showToast = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      formik.setFieldValue('imageUrl', URL.createObjectURL(file))
      uploadProfileImage(file)
    }
  };
  const { initialState: { user } } = useAuthStore()
  const { modals, modalFunctions } = useModalsBuilder({
    themeChange: { open: false },
    details: { open: false }
  })

  return (
    <div className="flex items-center justify-between pb-4 bg-transparent">
      <div className="flex items-center space-x-4">
        <div className="relative">
          <motion.div
            whileTap={{ scale: 0.95 }}
            onTap={() => fileInputRef.current?.click()}
            className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-white font-medium  ">
            {and([formik.values.imageUrl, formik.values.hasImage]) ? (
              <Avatar
                src={formik.values.imageUrl as string | undefined}
                alt="profile image"
                className="w-full h-full object-cover"
              />
            ) : (
              <span className="text-gray-600">
                {profile.details.title.charAt(0)}
              </span>
            )}
          </motion.div>
          <motion.button
            whileTap={{ scale: 0.9 }}
            onClick={() => fileInputRef.current?.click()}
            className="absolute -bottom-1 -right-1 bg-white rounded-full p-1.5 shadow-sm"
          >
            <PencilIcon size={14} />
          </motion.button>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/jpeg, image/png"
            onChange={handleImageUpload}
            className="sr-only"
          />
        </div>

        {/* Profile Info */}
        <div className="space-y-1">
          <h2
            className="font-medium  hover:underline hover:font-bold"
            onClick={() => modalFunctions.openModal('details', {})}
          >
            {profile.details.title}
          </h2>
          <p
            className="text-sm text-subtext hover:underline hover:font-bold max-w-[320px] line-clamp-1 "
            onClick={() => modalFunctions.openModal('details', {})}
          >
            {profile.details.bio}
          </p>
        </div>
        <ButtonTooltip content="Copy profile link">
          <button
            className="bg-transparent text-subtext self-start mt-2  "
            onClick={() =>
              navigator.clipboard
                .writeText(
                  import.meta.env.VITE_FRONTEND_URL +
                  `/digital-profile/${user?.username}`)
                .then(() => {
                  showToast('info', 'Profile link copied to clipboard')
                })
                .catch((err) => {
                  if (err.name === "NotAllowedError") {
                    showToast(
                      "error",
                      "Please allow clipboard access in your browser settings.",
                    );
                  } else {
                    showToast("error", "Failed to copy to clipboard");
                  }
                })
            }    >
            <Copy width={18} height={18} />
          </button>
        </ButtonTooltip>
      </div>
      <div className="flex items-center space-x-2 pl-2 ml-auto">
        <Button
          variant="dormant"
          onTap={() => modalFunctions.openModal('themeChange', {})}
          className="px-2 py-2 rounded-full text-sm "
        >
          <Palette size={20} />
        </Button>
      </div>
      <DisplayNameModal
        open={modals.details.open}
        onClose={modalFunctions.returnClose('details')}
        onSave={(payload) => {
          modalFunctions.closeModal('details')
          updateDetails(payload)
        }}
      />
      <ThemeChangeModal
        open={modals.themeChange.open}
        onClose={modalFunctions.returnClose('themeChange')}
        onSave={(theme) => {
          modalFunctions.closeModal('themeChange')
          updateTheme(theme)
        }}
      />
    </div>
  );
};

export default ProfileHeader;
