import { TeamId } from "./team";

export type StaffId = string;
export type LocationId = string;
type ServiceId = string;

export interface WorkingHours {
  start: string;
  end: string;
  isWorking: boolean;
}

export interface StaffWorkingHours {
  monday?: WorkingHours;
  tuesday?: WorkingHours;
  wednesday?: WorkingHours;
  thursday?: WorkingHours;
  friday?: WorkingHours;
  saturday?: WorkingHours;
  sunday?: WorkingHours;
}

export interface Staff {
  _id: StaffId;
  teamId: {
    name: string;
    ownerId: string;
    _id: string;
  };
  userId: string;
  name: string;
  email?: string;
  phone?: string;
  role: string;
  color: string;
  services: ServiceId[];
  locations: LocationId[];
  workingHours: StaffWorkingHours;
  /**
   * @dev mind you this is in minutes
   * */
  buffer: number;
  travelTime: number;
  capacity: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateStaffPayload {
  teamId: TeamId;
  name: string;
  email?: string;
  phone?: string;
  role: string;
  color?: string;
  services?: ServiceId[];
  locations?: LocationId[];
  workingHours?: Partial<StaffWorkingHours>;
  buffer?: number;
  travelTime?: number;
  capacity?: number;
}

export interface UpdateStaffPayload {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  role: string;
  color: string;
  services?: ServiceId[];
  locations?: LocationId[];
  workingHours: Partial<StaffWorkingHours>;
  buffer?: number;
  travelTime?: number;
  capacity?: number;
  isActive?: boolean;
}

// Staff availability and calendar interfaces
export interface StaffAvailability {
  staffId: StaffId;
  name: string;
  role: string;
  color: string;
  teamId: TeamId;
  availability: {
    isWorking: boolean;
    workingHours: WorkingHours | null;
    ptoRequests: PTORequest[];
    existingBookings: any[];
    capacity: number;
    currentBookings: number;
    availableSpots: number;
    isAvailable: boolean;
  };
}

export interface GetStaffAvailabilityParams {
  date: string;
  serviceId?: string;
  teamId?: string;
}

// PTO/Time-off interfaces
export interface PTORequest {
  _id: string;
  staffId: StaffId;
  userId: string;
  type: 'vacation' | 'sick' | 'personal' | 'other';
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
  description?: string;
  isApproved: boolean;
  createdAt: string;
  updatedAt: string;
}

// Staff assignment interfaces
export interface AssignStaffToServicePayload {
  serviceId: string;
  staffIds: StaffId[];
}

export interface AssignStaffToLocationsPayload {
  staffId: StaffId;
  locationIds: LocationId[];
}

// Staff performance interfaces
export interface StaffPerformance {
  _id: string;
  staffId: StaffId;
  userId: string;
  period: {
    startDate: string;
    endDate: string;
    type: 'daily' | 'weekly' | 'monthly' | 'yearly';
  };
  metrics: {
    totalBookings: number;
    totalRevenue: number;
    averageRating: number;
    completionRate: number;
    cancellationRate: number;
    noShowRate: number;
  };
  serviceBreakdown: Array<{
    serviceId: string;
    serviceName: string;
    bookings: number;
    revenue: number;
    averageRating: number;
  }>;
}

export interface GetStaffPerformanceParams {
  staffId?: StaffId;
  startDate: string;
  endDate: string;
  periodType: 'daily' | 'weekly' | 'monthly' | 'yearly';
  metric?: 'revenue' | 'bookings' | 'rating';
}

// Staff user management interfaces
export interface StaffUser {
  _id: string;
  username: string;
  email: string;
  role: string;
  isActive: boolean;
  staff: Staff;
}

export interface CreatePTOPayload {
  staffId: StaffId;
  type: 'vacation' | 'sick' | 'personal' | 'other';
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
  description?: string;
}

export interface UpdateStaffUserPayload {
  username?: string;
  email?: string;
  role?: string;
  isActive?: boolean;
}

export interface ResetStaffPasswordPayload {
  staffId: StaffId;
  newPassword: string;
}
