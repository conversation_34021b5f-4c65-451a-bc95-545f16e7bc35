import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import { capitalizeFirstWord, sleep } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import React, { useEffect, useRef } from "react";

interface TimeSlotSelectorProps {
  timeSlotsProps: {
    timeslots: 'custom' | (string & {});
    customTimeslotDuration: number | undefined
  }
  setTimeslot: (payload: TimeSlotSelectorProps['timeSlotsProps']) => void;
}

const TimeSlotSelector: React.FC<TimeSlotSelectorProps> = ({
  setTimeslot,
  ...props
}) => {
  const options = [10, 15, 30, 60, 'Custom']
  const inputRef = useRef<HTMLInputElement>(null)
  useEffect(() => {
    (async () => {
      if (props.timeSlotsProps.timeslots === 'custom') {
        await sleep(200)
        inputRef.current?.focus()
      }
    })()
  }, [props.timeSlotsProps.timeslots])
  return (
    <div className={cn("w-full flex items-center justify-between", { 'items-start': props.timeSlotsProps.timeslots === 'custom' })}>
      <Typography className=" text-paragraph -mt-1">
        Timeslot (minutes)
      </Typography>
      <div className="w-fit flex items-center gap-x-2">
        <div className="flex flex-col">
          <SearchableDropdown
            options={options.map(opt => ({ label: String(opt), value: String(opt).toLowerCase() }))}
            value={{ label: capitalizeFirstWord(props.timeSlotsProps.timeslots), value: props.timeSlotsProps.timeslots.toLowerCase() }}
            onChange={(opt) => {
              if (!opt) return;
              const value = opt.value
              if (value === 'custom')
                // set to custom with default duration
                setTimeslot({ timeslots: 'custom', customTimeslotDuration: 20 })
              else
                setTimeslot({ timeslots: opt.value, customTimeslotDuration: undefined })
            }}
            fullWidth
            className="max-w-36"
          />
          {
            props.timeSlotsProps.timeslots === 'custom' && (
              <Input.Numeric
                label=""
                inputRef={inputRef}
                name="timeslot"
                className="max-w-24"
                value={props.timeSlotsProps.customTimeslotDuration?.toString()}
                onChange={({ target: { value } }) => {
                  setTimeslot({ timeslots: 'custom', customTimeslotDuration: parseInt(value) })
                }}
              />
            )}
        </div>
      </div>
    </div>
  );
};

export default TimeSlotSelector;
