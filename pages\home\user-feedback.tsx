import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import Popover from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { useToast } from "~/contexts/hooks/toast";
import {
  SubmitUserFeedback,
  UserFeedbackPayload,
} from "~/services/api.service";
import { GetUserDetails } from "~/services/auth.service";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useFormik } from "formik";
import { AnimatePresence, motion } from "framer-motion";
import { X } from "lucide-react";
import React, { MutableRefObject, useMemo, useRef, useState } from "react";
import * as Yup from "yup";
import { ArrowLeft01Icon } from "hugeicons-react";
import { sleep } from "@/lib/helpers";

type Props = {
  trigger: React.JSX.Element;
  triggerRef: MutableRefObject<HTMLDivElement | null>;
};

/**
 * @dev add
 */
function UserFeedbackPopover(props: Props) {
  const [step, setStep] = useState<0 | 1 | 2>(0);
  const validationSchema = Yup.object({
    type: Yup.string().default("problem"),
    fullName: Yup.string().required("Full Name is required"),
    email: Yup.string().email("Invalid email").required("Email is required"),
    feedback: Yup.string().min(10).required("Feedback is required"),
  });
  const { data: user } = useQuery({
    queryFn: async () => (await GetUserDetails()).data.data.user,
    queryKey: ["feedback-user"],
  });
  const mutation = useMutation({
    mutationFn: SubmitUserFeedback,
    onSuccess: () => showToast("info", "Feedback submitted successfully!"),
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.message ||
          "An error occurred while submitting feedback."
      );
    },
  });
  const formik = useFormik({
    validationSchema,
    initialValues: {
      fullName: "",
      email: "",
      feedback: "",
      type: "",
    } as unknown as UserFeedbackPayload,
    onSubmit(values) {
      mutation.mutateAsync(values);
    },
  });
  const descriptionMemo = useMemo(() => {
    if (step === 0) return "Leave your feedback";
    else if (step === 1)
      return formik.values.type === "problem" ? "🐛 Problem" : "💡 Idea";
  }, [step, formik.values.type]);
  const showToast = useToast();
  const inputRef = useRef<HTMLTextAreaElement>(null);
  return (
    <Popover transformOrigin="top-right" onClose={() => setStep(0)} >
      <Popover.Trigger triggerRef={props.triggerRef} className=" ">{props.trigger}</Popover.Trigger>
      <Popover.Content className=" -right-14 min-h-fit w-[93vw] max-w-[256px] shadow-sm md:-right-2 ">
        <div className="w-full h-full pt-4 pb-6 px-4 flex flex-col gap-y-6">
          <div className="w-full flex items-start justify-between">
            <Button
              onTap={() => {
                setStep((p) => (p - 1) as 0);
                formik.resetForm();
              }}
              variant="icon"
              className={cn("!p-0 text-paragraph mt-1 bg-transparent ", {
                hidden: step === 0,
              })}
            >
              <ArrowLeft01Icon />
            </Button>
            <Typography variant={"h4"} className=" mx-auto ">
              {descriptionMemo}
            </Typography>
            <Popover.Close>
              <Button variant="icon" className="!p-0  bg-transparent ">
                <X />
              </Button>
            </Popover.Close>
          </div>
          <AnimatePresence presenceAffectsLayout>
            {step === 0 ? (
              <motion.div
                initial={{ opacity: 1, x: 0 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: "-100%" }}
                className="w-full flex items-center justify-center gap-x-4 "
              >
                {[
                  { label: "Problem", value: "problem", icon: "🐛" },
                  { label: "Idea", value: "suggestion", icon: "💡" },
                ].map((feedback) => {
                  return (
                    <Typography
                      key={feedback.value}
                      onClick={async () => {
                        formik.setFieldValue("type", feedback.value);
                        setStep(1);
                        await sleep(50)
                        inputRef.current?.focus()
                      }}
                      variant={"p"}
                      className=" font-semibold bg-blue-500/20 hover:bg-blue-50 cursor-pointer backdrop-blur-3xl min-w-28 min-h-32 rounded-xl flex flex-col justify-center items-center gap-y-4"
                    >
                      <span className="text-3xl">{feedback.icon}</span>
                      {feedback.label}
                    </Typography>
                  );
                })}
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, x: "100%" }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: "100%" }}
                className="w-full flex flex-col items-center justify-center gap-y-4  "
              >
                <textarea
                  onChange={(e) => {
                    formik.setFieldValue("feedback", e.target.value);
                  }}
                  name="feedback"
                  ref={inputRef}
                  className="w-full border-2 rounded-xl pl-3 pt-2 focus:outline-primary-400 !border-[#f2f2f2] min-h-32"
                ></textarea>
                <Popover.Close
                  className="w-full"
                  onClose={(close) => {
                    if (!user)
                      return showToast(
                        "error",
                        "Please login to submit feedback"
                      );
                    mutation
                      .mutateAsync({
                        ...formik.values,
                        fullName: `${user?.first_name} ${user?.last_name}`,
                        email: user?.email,
                      })
                      .then((_) => {
                        close();
                        setStep(0);
                        formik.resetForm();
                      });
                  }}
                >
                  <Button type="submit" className="w-full">
                    {mutation.isPending ? (
                      <CircularLoader color="white" />
                    ) : (
                      "Submit Feedback"
                    )}
                  </Button>
                </Popover.Close>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </Popover.Content>
    </Popover>
  );
}

export default UserFeedbackPopover;
