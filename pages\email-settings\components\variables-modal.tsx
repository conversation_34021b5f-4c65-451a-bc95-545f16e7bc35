import { Typography } from "@/components/typography";
import { StringVariable, TEMPLATE_VARIABLES, TEMPLATE_VARIABLES_MAP, TemplateVariableMapType, TVariable } from "../email-template-engine"
import Modal from "@/components/ui/modal";
import { entries, pick, sleep, truncateText } from "@/lib/helpers";
import { Button } from "@/components/buttons";
import { X } from "lucide-react";
import { Fragment, useEffect, useMemo, useRef, useState } from "react";
import { Copy, Plus } from "@gravity-ui/icons";
import { Input } from "@/components/inputs";
import { useToast } from "@/src/contexts/hooks/toast";

type Props = {
  open: boolean;
  onClose: VoidFunction
  onClickVariable(variable: TVariable): void,
}

const VariablesModal = (props: Props) => {
  const [searchTerm, setSearchTerm] = useState('')
  const groupedTemplateVariablesMemo = useMemo(() => {
    const groupedTemplateVariables = {} as Record<string,
      Array<{ variable: TVariable, } & TemplateVariableMapType[StringVariable]>
    >
    for (const templateVariable of TEMPLATE_VARIABLES) {
      const expandedVariable = TEMPLATE_VARIABLES_MAP[templateVariable]
      if (
        templateVariable.toLowerCase().includes(searchTerm.toLowerCase())
        || expandedVariable.name.toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        groupedTemplateVariables[expandedVariable.category] = [
          ...(groupedTemplateVariables[expandedVariable.category] || []),
          { ...expandedVariable, variable: templateVariable }
        ];
      } else continue
    }
    return groupedTemplateVariables
  }, [TEMPLATE_VARIABLES, TEMPLATE_VARIABLES_MAP, searchTerm])
  const showToast = useToast()
  const inputRef = useRef<HTMLInputElement>(null)
  async function focusOnInput() {
    if (!props.open) return
    await sleep(100)
    inputRef.current
      ?.focus?.()
  }
  useEffect(() => {
    focusOnInput()
  }, [props.open])
  return (
    <Modal {...pick(props, 'open', 'onClose')} >
      <Modal.Body
        className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white shadow-sm `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold font-Bricolage_Grotesque ">
            Add Variable
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="w-full" >
          <Input.Text
            label=""
            inputRef={inputRef}
            name="search_variables"
            placeholder="Search..."
            onChange={({ currentTarget: { value } }) => {
              setSearchTerm(value || '')
            }}
            className="w-full"
          />
        </div>
        <div className="pt-2 w-full flex flex-col gap-y-3 max-h-[280px] overflow-y-auto no-scrollbar ">
          {entries(groupedTemplateVariablesMemo)
            .map(([category, variables], index) => (
              <Fragment key={index} >
                <Typography className="px-2 bg-gray-50 rounded-md">
                  {category}
                </Typography>
                {
                  variables.map((tmplV, vIndex) => (
                    <div
                      key={vIndex} className="px-4 py-2.5 rounded-lg bg-subtle-gray flex flex-col gap-y-2 w-full text-paragraph items-start">
                      <div className="flex items-center justify-between w-full" >
                        <span className="bg-sky-200 text-sky-700 font-bold px-1.5 py-0.5 rounded-md" >
                          {tmplV.variable}
                        </span>
                        <div className="flex flex-col gap-y-2">
                          <Button variant="icon" className="!p-0 text-paragraph bg-transparent" onTap={() => {
                            navigator.clipboard
                              .writeText(tmplV.variable)
                              .then(() => {
                                showToast('info', 'Copied to clipboard')
                              })
                              .catch((err) => {
                                if (err.name === "NotAllowedError") {
                                  showToast(
                                    "error",
                                    "Please allow clipboard access in your browser settings.",
                                  );
                                } else {
                                  showToast("error", "Failed to copy to clipboard");
                                }
                              })
                          }}>
                            <Copy />
                          </Button>
                          <Button variant="icon" className="!p-0 text-paragraph bg-transparent" onTap={() => {
                            props.onClickVariable(tmplV.variable)
                          }}>
                            <Plus />
                          </Button>
                        </div>
                      </div>
                      <Typography className="font-normal text-paragraph/60 text-sm flex flex-col items-start " >
                        <span className="font-medium inline-block" >{tmplV.name}</span>
                        <span>
                          Example: {truncateText(tmplV.example,)}
                        </span>
                      </Typography>
                    </div>
                  ))
                }
              </Fragment>
            ))}
        </div>
      </Modal.Body>
    </Modal>
  )
}

export default VariablesModal
