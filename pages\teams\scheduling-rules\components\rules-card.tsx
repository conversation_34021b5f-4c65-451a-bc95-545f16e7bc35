import React from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { SchedulingRules } from "@/src/interfaces/scheduling-rules";
import DefaultRulesBanner from "./default-rules-banner";
import {
  Edit02Icon,
  PlusSignIcon,
  CreditCardIcon,
  Clock01Icon,
  RefreshIcon,
} from "hugeicons-react";
import { Service } from "@/src/interfaces/services";

interface RulesCardProps {
  service: Service;
  rules?: SchedulingRules;
  teamId?: string | null;
}

const RulesCard: React.FC<RulesCardProps> = ({ service, rules, teamId }) => {
  const hasCustomRules = rules && !rules.isDefault;
  const queryParams = teamId ? `?teamId=${teamId}` : "";

  const getDepositText = () => {
    if (!rules) return "No deposit";

    switch (rules.depositType) {
      case "none":
        return "No deposit";
      case "percentage":
        return `${rules.depositAmount}% deposit`;
      case "fixed":
        return `$${rules.depositAmount} deposit`;
      case "full":
        return "Full payment required";
      default:
        return "No deposit";
    }
  };
  const navigate = useNavigate();

  if (!hasCustomRules) {
    return (
      <div className="bg-white rounded-2xl border border-gray-200 p-6 space-y-4">
        <Typography variant="h4" className="font-semibold line-clamp-2">
          {service.name}
        </Typography>

        <DefaultRulesBanner serviceName={service.name} />

        <Button
          onTap={() => {
            navigate(`/teams/rules/create/${service._id}${queryParams}`);
          }}
          variant="dormant"
          className="w-full flex items-center justify-center gap-x-3"
        >
          <PlusSignIcon className="w-4 h-4 " />
          Create Custom Rules
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl border border-blue-200 p-6 space-y-4 hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-start justify-between">
          <Typography variant="h4" className="font-semibold line-clamp-2">
            {service.name}
          </Typography>
          <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
            Custom
          </span>
        </div>
      </div>

      {/* Rules Summary */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <CreditCardIcon className="w-4 h-4 text-gray-500" />
          <Typography className="text-sm">{getDepositText()}</Typography>
        </div>

        <div className="flex items-center gap-2">
          <Clock01Icon className="w-4 h-4 text-gray-500" />
          <Typography className="text-sm">
            Cancel {rules.cancelCutoff}h before
          </Typography>
        </div>

        <div className="flex items-center gap-2">
          <RefreshIcon className="w-4 h-4 text-gray-500" />
          <Typography className="text-sm">
            {rules.allowRescheduling
              ? `Reschedule ${rules.rescheduleCutoff}h before`
              : "No rescheduling"}
          </Typography>
        </div>
      </div>

      {/* Actions */}
      <div className="pt-2 border-t">
        <Button
          onTap={() =>
            navigate(`/teams/rules/edit/${service._id}${queryParams}`)
          }
          variant="dormant"
          className="w-full flex items-center justify-center gap-x-3"
        >
          <Edit02Icon className="w-4 h-4" />
          Edit Rules
        </Button>
      </div>
    </div>
  );
};

export default RulesCard;
