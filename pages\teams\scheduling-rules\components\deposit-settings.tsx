import React from "react";
import { FormikProps } from "formik";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import RadioBox from "@/components/ui/radio-box";
import { CreateSchedulingRulesPayload } from "@/src/interfaces/scheduling-rules";

interface DepositSettingsProps {
  formik: FormikProps<CreateSchedulingRulesPayload>;
}

const DEPOSIT_TYPE_OPTIONS = [
  { label: "No deposit required", value: "none" },
  { label: "Percentage of total", value: "percentage" },
  { label: "Fixed amount", value: "fixed" },
  { label: "Full payment upfront", value: "full" },
] as const;

const DepositSettings: React.FC<DepositSettingsProps> = ({ formik }) => {
  const showDepositAmount = formik.values.depositType === 'percentage' || formik.values.depositType === 'fixed';

  return (
    <div className="bg-white rounded-2xl border p-6 space-y-4">
      <div className="space-y-1">
        <Typography variant="h4" className="font-semibold">
          Deposit Requirements
        </Typography>
        <Typography className="text-sm text-gray-600">
          Configure upfront payment requirements for bookings
        </Typography>
      </div>

      <RadioBox
        label="Deposit Type"
        value={formik.values.depositType}
        onChange={(option) => {
          if (option) {
            formik.setFieldValue("depositType", option.value);
            if (option.value === 'none' || option.value === 'full') {
              formik.setFieldValue("depositAmount", undefined);
            }
          }
        }}
        options={DEPOSIT_TYPE_OPTIONS as Mutable<typeof DEPOSIT_TYPE_OPTIONS>}
      />

      {showDepositAmount && (
        <div className="pt-2">
          <Input.Numeric
            label={formik.values.depositType === 'percentage' ? "Percentage (%)" : "Amount ($)"}
            name="depositAmount"
            value={formik.values.depositAmount || 0}
            onChange={formik.handleChange}
            placeholder={formik.values.depositType === 'percentage' ? "50" : "25.00"}
            min={0}
            max={formik.values.depositType === 'percentage' ? 100 : undefined}
          />
          <Typography className="text-xs text-gray-500 mt-1">
            {formik.values.depositType === 'percentage'
              ? "Enter a percentage between 0-100%"
              : "Enter a fixed dollar amount"
            }
          </Typography>
        </div>
      )}
    </div>
  );
};

export default DepositSettings;
