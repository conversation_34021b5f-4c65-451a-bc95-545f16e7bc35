import ErrorScreen from "@/components/ui/error-screen";
import { GetPurchasedLeads } from "~/services/leads.service";
import { useQuery } from "@tanstack/react-query";
import { Link } from "react-router-dom";
import { CreditIcon, LocationIcon } from "./components/badges";
import useAuthStore from "../auth/components/auth-store";
import CircularLoader from "@/components/ui/circular-loader";
import { Button } from "@/components/buttons";

const LeadsDashboard = () => {
  const { initialState: { user } } = useAuthStore()
  const {
    data: purchases,
    isFetching,
    isError,
    refetch,
  } = useQuery({
    queryFn: GetPurchasedLeads,
    queryKey: ["purchases"],
    refetchOnWindowFocus: false,
    enabled: true,
  });
  if (isFetching)
    return (
      <div key={'is-fetching'} className="w-full py-64 flex justify-center">
        <CircularLoader />
      </div>
    );
  if (isError) return <ErrorScreen onRetry={() => refetch()} />;
  return (
    <section className="w-full max-w-5xl m-auto grid gap-y-5 md:px-2 ">
      {/* Header */}
      <div className="bg-subtle-gray w-full min-h-44 rounded-3xl p-6 flex flex-col items-start justify-between md:flex-row ">
        <div className="flex items-center gap-x-2">
          <div className="bg-yellow-400 text-white w-12 h-12 flex items-center justify-center rounded-full text-lg font-semibold">
            {user?.business_name.charAt(0).toUpperCase()}
          </div>
          <div>
            <h2 className="text-xl text-paragraph font-semibold capitalize">
              {user?.business_name}
            </h2>
            <div className="w-fit flex items-center gap-x-1">
              <LocationIcon className="fill-subtext " width={20} height={20} />
              {/* business location here */}
              <p className="text-base text-subtext font-medium">
                {user?.leadSettings.leadLocation}
              </p>
            </div>
          </div>
        </div>
        <Button className=" px-5 py-2 ">
          Edit
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-5 md:grid-cols-3 ">
        <div className="col-span-1">
          <div className="grid grid-cols-1 gap-5">
            <div className="bg-subtle-gray rounded-3xl p-6 flex flex-col gap-y-10">
              <div className="w-full flex items-center justify-between">
                <h3 className="text-2xl font-semibold">Credits</h3>
              </div>
              <div className="w-full grid place-content-center ">
                <div className="flex items-center gap-1.5">
                  <CreditIcon width={54} height={54} />
                  <p className="text-4xl font-medium">{user?.totalCredit}</p>
                </div>
              </div>
              <Button className=" text-white px-8 py-3 mx-auto">
                Buy More Credits
              </Button>
            </div>
            <div className="bg-subtle-gray rounded-3xl p-6 space-y-5 ">
              <div className="w-full flex items-center justify-between">
                <h3 className="text-2xl font-semibold">Purchased Leads</h3>
                <Link to={"/leads/purchases"} className="text-subtext ">
                  Browse
                </Link>
              </div>
              <div className="w-36 h-36 bg-sky-100 text-sky-500 grid place-content-center rounded-full mx-auto">
                <p className="text-5xl font-extrabold">
                  {purchases?.data?.length}
                </p>
              </div>
              <h3 className="text-xl font-semibold mx-auto w-fit">
                Total Purchased Leads
              </h3>
            </div>
          </div>
        </div>

        <div className="col-span-1 md:col-span-2 ">
          <div className="bg-subtle-gray rounded-3xl p-6 pb-9 flex flex-col gap-y-6">
            <h3 className="text-2xl font-semibold">Lead Settings</h3>
            <div className="space-y-2 ">
              <div className="w-full flex items-center justify-between">
                <p className="text-paragraph text-xl font-medium">Services</p>
                <Button
                  className="px-5 py-2"
                >
                  Edit
                </Button>
              </div>
              <p className="text-paragraph text-base font-medium ">
                You'll receive leads in this category:
              </p>
              <div className="flex flex-wrap">
                <span className="border border-subtext px-3 py-1 rounded-full text-xs mr-2">
                  {user?.leadSettings?.service}
                </span>
              </div>
              <div className="w-full bg-dark-gray h-0.5 mt-3" />
            </div>

            <div className="space-y-2 ">
              <div className="w-full flex items-center justify-between">
                <p className="text-paragraph text-xl font-medium">Location</p>
                <Button
                  className="px-5 py-2"
                >
                  Edit
                </Button>
              </div>
              <p className="text-paragraph text-base font-medium ">
                You'll receive customers within:
              </p>
              {(user?.leadSettings?.leadLocation || [])?.map((loc) => (
                <div className="w-fit flex items-center gap-x-1">
                  <LocationIcon
                    className="fill-subtext "
                    width={20}
                    height={20}
                  />
                  <p className="text-base text-subtext font-medium">{loc}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LeadsDashboard;
