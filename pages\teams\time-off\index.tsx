
import React, { useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import { PlusSignIcon } from "hugeicons-react";
import { useStaff } from "../hooks/use-staff";
import { useTimeOff } from "../hooks/use-time-off";
import TimeOffCard from "./components/time-off-card";
import TimeOffFilters from "./components/time-off-filters";
import { PTOType } from "@/src/interfaces/time-off";

const TimeOff: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const teamId = searchParams.get("teamId") || "";

  const [filters, setFilters] = useState({
    search: "",
    type: "" as PTOType | "",
    status: "" as "approved" | "pending" | "rejected" | "",
    staffId: "",
  });

  const { staffQuery } = useStaff(teamId);
  const { timeOffQuery } = useTimeOff();

  const filteredTimeOff = timeOffQuery.data?.filter((pto) => {
    const matchesSearch = pto.description.toLowerCase().includes(filters.search.toLowerCase());
    const matchesType = !filters.type || pto.type === filters.type;
    const matchesStatus = !filters.status ||
      (filters.status === "approved" && pto.isApproved) ||
      (filters.status === "pending" && !pto.isApproved && !pto.approvedAt) ||
      (filters.status === "rejected" && !pto.isApproved && pto.approvedAt);
    const matchesStaff = !filters.staffId || pto.staffId === filters.staffId;

    return matchesSearch && matchesType && matchesStatus && matchesStaff;
  }) || [];

  if (timeOffQuery.isLoading || staffQuery.isLoading) {
    return (
      <div className="flex w-full justify-center py-10">
        <CircularLoader />
      </div>
    );
  }

  return (
    <section className="w-full space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Typography
            variant="h1"
            className="font-bold font-Bricolage_Grotesque"
          >
            Time Off Management
          </Typography>
          <Typography variant="p" className="text-sm text-gray-600">
            Manage staff time-off requests and schedules
          </Typography>
        </div>
        <Button
          onTap={() => navigate(`/teams/time-off/create?teamId=${teamId}`)}
          className="flex items-center gap-2"
        >
          <PlusSignIcon size={16} />
          New Request
        </Button>
      </div>

      <TimeOffFilters
        filters={filters}
        onFiltersChange={setFilters}
        staff={staffQuery.data || []}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredTimeOff.map((pto) => {
          const staff = staffQuery.data?.find(s => s._id === pto.staffId);
          return (
            <TimeOffCard
              key={pto._id}
              pto={pto}
              staff={staff}
              onEdit={(ptoId) => navigate(`/teams/time-off/edit/${ptoId}?teamId=${teamId}`)}
            />
          );
        })}
      </div>

      {filteredTimeOff.length === 0 && (
        <div className="text-center py-12">
          <Typography variant="p" className="text-gray-500">
            No time-off requests found
          </Typography>
        </div>
      )}
    </section>
  );
};

export default TimeOff;
