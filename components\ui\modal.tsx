import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import React, { useMemo } from "react";

export type BaseModalProps = {
  open: boolean;
  onClose: VoidFunction;
}

type ModalUnderlayProps = Pick<Props, "children" | "className"> & BaseModalProps & {
  noBlur?: boolean;
};

const ModalUnderLay = ({
  children,
  className,
  ...props
}: ModalUnderlayProps) => {
  const isOpen = useMemo(() => {
    return Boolean(props.open);
  }, [props.open]);
  return (
    <AnimatePresence mode="sync" >
      {isOpen ? (
        <motion.section
          key={'modal-underlay'}
          layout
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{
            opacity: 0,
            transition: {
              duration: 0.2
            }
          }}
          className={cn(
            { "bg-white/25 backdrop-blur-[2px]": !props.noBlur },
            { "bg-transparent": props.noBlur },
            className,
            "!mt-0 w-screen h-screen fixed z-[10000000] top-0 left-0 flex items-center justify-center"
          )}
          onClick={(e) => {
            if (e.target === e.currentTarget) props.onClose();
          }}
        >
          {children}
        </motion.section>
      ) : (
        ""
      )}
    </AnimatePresence>
  );
};

type Props = {
  children?: React.ReactNode;
  className?: string;
};

const ModalBody: React.FC<Props> = ({ children, className }) => {
  return (
    <section className={cn("w-fit h-fit px-3 ")}>
      <motion.section
        key={'modal'}
        initial={{ opacity: 0, y: 60 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{
          opacity: 0, y: 60,
          transition: {
            duration: 0.2
          }
        }}
        className={cn(`min-h-[400px] bg-white rounded-3xl !border !border-gray-100 shadow-md px-6 py-6 md:px-6 md:py-6`, className)}
      >
        {children}
      </motion.section>
    </section>
  );
};

/**
 * @example
 * ```jsx
 * <Modal className="" open={open} >
 *  <Modal.Body className="p-5 flex justify-between items-center" >
 *    <button onClick={...} >
 *      <X />
 *    </button>
 *  </Modal.Body>
 * </Modal>
 * ```
 */
const Modal = Object.assign(ModalUnderLay, {
  Body: ModalBody,
});

export default Modal;
