import { AxiosResponse } from "axios";
import { api } from "./api.service";


export const GetExternalAccount = async (): Promise<AxiosResponse> => {
	try {
		const response = await api.get(`/auth/accounts`);
		return response;
	} catch (error) {
		console.error("get-account:", error);
		throw error;
	}
};

export const ConnectExternalAccount = async (
	serviceName: string,
): Promise<AxiosResponse> => {
	try {
		const response = await api.get(`/auth/accounts/connect`, {
			params: {
				service: serviceName,
			},
		});
		return response;
	} catch (error) {
		console.error("connect-account:", error);
		throw error;
	}
};
export const DisconnectExternalAccount = async (
	serviceName: string,
): Promise<AxiosResponse> => {
	try {
		const response = await api.post(`/auth/accounts/disconnect`, {
			service: serviceName,
		});
		return response;
	} catch (error) {
		console.error("disconnectconnect-account:", error);
		throw error;
	}
};
type activatePayload = {
	service: string;
	activate: boolean;
};
export const ToggleActivateExternalAccount = async (
	payload: activatePayload,
): Promise<AxiosResponse> => {
	try {
		const response = await api.post(`/auth/accounts/activate`, payload);
		return response;
	} catch (error) {
		console.error("connect-account:", error);
		throw error;
	}
};

export const GetOauthRedirect = async ({
	code,
	state,
}: {
	code: string;
	state?: string | null;
}): Promise<AxiosResponse> => {
	try {
		const response = await api.get(`/auth/accounts/oauth/callback`, {
			params: {
				code,
				service: state,
			},
		});
		return response;
	} catch (error) {
		console.error("connect-account:", error);
		throw error;
	}
};


/**
 * service to push the settings to the backend
 * @writtenby yours truly
 */
export const UpdateNotificationSettings = async (
	payload: {
		[key in "mentions" | "comments" | "follows" | "newLogin"]: {
			email: boolean;
			push: boolean;
			sms: boolean;
		};
	},
): Promise<AxiosResponse<boolean>> => {
	try {
		const response = await api.put(`/settings/notification/`, payload);
		return response;
	} catch (error) {
		console.error("Update notification settings ->", error);
		throw error;
	}
};

type UpdateBillingSettingsPayload = {
	preferredCurrency: UserPreferredCurrency;
};

export type TUserSettings = {
	Bookingsettings: {
		buttonColor?: string;
		description?: string;
		textColor?: string;
		backgroundColor?: string;
		backgroundImageUrl?: string;
	};
	billingSettings: {
		preferredCurrency?: UserPreferredCurrency;
	};
};

type UpdateBillingSettingsResponse = {
	message: string;
	settings: TUserSettings;
};

export const UpdateBillingSettings = async (
	payload: UpdateBillingSettingsPayload,
) => {
	try {
		const response = await api.put<UpdateBillingSettingsResponse>(
			`/settings/billing`,
			payload,
		);
		return response;
	} catch (error) {
		console.error("Update billing settings ->", error);
		throw error;
	}
};
