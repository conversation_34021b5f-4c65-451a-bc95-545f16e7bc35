import { noop } from "@/lib/helpers";
import { Service } from "@/src/interfaces/services";
import {
  FollowUpStage,
  FollowUpStagePayload,
  GetEmailSettings,
  InitialConfirmationStage,
  InitialConfirmationStagePayload,
  ReminderStage,
  ReminderStagePayload,
} from "@/src/services/email-settings.service";
import { GetServices } from "@/src/services/services.service";
import { useMutation, useQueries, useQueryClient } from "@tanstack/react-query";

/**
 * @dev all toasts can be done in the consumer component
 * @dev all query invalidations are handled inside here, no need for that in consumer component
 * */
const useEmailSettings = () => {
  const [servicesQuery, emailSettingsQuery] = useQueries({
    queries: [
      {
        queryKey: ["services"],
        queryFn: async () =>
          (await GetServices()).data.Services as unknown as Service[],
        refetchOnWindowFocus: false
      },
      {
        queryKey: ["email-settings"],
        queryFn: async () => (await GetEmailSettings()).data.emailSettings,
        refetchOnWindowFocus: false
      },
    ],
  });
  const queryClient = useQueryClient();

  function appendMutationProps() {
    return {
      onSuccess() {
        queryClient.invalidateQueries({
          queryKey: ["email-settings"],
        });
      },
      onError: noop,
    };
  }
  type TUpdate<T> = {
    id: string
    payload: T
  }
  /**
   * @dev can handle success and error states
   * */
  const followUpStage = {
    create: useMutation({
      mutationFn: FollowUpStage.Create,
      ...appendMutationProps(),
    }),
    update: useMutation({
      mutationFn: FollowUpStage.Update,
      ...appendMutationProps(),
    }),
    /** 
      * @dev the first update is done, with the firstPayload, then the second payload is done, this is used to swap attached service Ids
      * */
    swapAttachedService: useMutation({
      mutationFn: async (payload: { firstPayload: TUpdate<FollowUpStagePayload>, secondPayload: TUpdate<FollowUpStagePayload> }) => {
        await FollowUpStage.Update(payload.firstPayload)
        await FollowUpStage.Update(payload.secondPayload)
      },
      ...appendMutationProps()
    }),
    delete: useMutation({
      mutationFn: FollowUpStage.Delete,
      ...appendMutationProps(),
    }),
  } as const;

  /**
   * @dev can handle success and error states
   * */
  const reminderStage = {
    create: useMutation({
      mutationFn: ReminderStage.Create,
      ...appendMutationProps(),
    }),
    update: useMutation({
      mutationFn: ReminderStage.Update,
      ...appendMutationProps(),
    }),
    /** 
      * @dev the first update is done, with the firstPayload, then the second payload is done, this is used to swap attached service Ids
      * */
    swapAttachedService: useMutation({
      mutationFn: async (payload: { firstPayload: TUpdate<ReminderStagePayload>, secondPayload: TUpdate<ReminderStagePayload> }) => {
        await ReminderStage.Update(payload.firstPayload)
        await ReminderStage.Update(payload.secondPayload)
      },
      ...appendMutationProps()
    }),
    delete: useMutation({
      mutationFn: ReminderStage.Delete,
      ...appendMutationProps(),
    }),
  } as const;

  /**
   * @dev can handle success and error states
   * */
  const initialConfirmationStage = {
    create: useMutation({
      mutationFn: InitialConfirmationStage.Create,
      ...appendMutationProps(),
    }),
    update: useMutation({
      mutationFn: InitialConfirmationStage.Update,
      ...appendMutationProps(),
    }),
    /** 
     * @dev the first update is done, with the firstPayload, then the second payload is done, this is used to swap attached service Ids
     * */
    swapAttachedService: useMutation({
      mutationFn: async (payload: { firstPayload: TUpdate<InitialConfirmationStagePayload>, secondPayload: TUpdate<InitialConfirmationStagePayload> }) => {
        await InitialConfirmationStage.Update(payload.firstPayload)
        await InitialConfirmationStage.Update(payload.secondPayload)
      },
      ...appendMutationProps()
    }),
    delete: useMutation({
      mutationFn: InitialConfirmationStage.Delete,
      ...appendMutationProps(),
    }),
  } as const;

  return {
    servicesQuery,
    emailSettingsQuery,
    followUpStage,
    initialConfirmationStage,
    reminderStage,
  };
};

export default useEmailSettings;
