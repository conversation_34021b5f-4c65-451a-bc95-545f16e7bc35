import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Typography } from "@/components/typography";
import { But<PERSON> } from "@/components/buttons";
import CircularLoader from "@/components/ui/circular-loader";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowLeft02Icon, UserGroupIcon } from "hugeicons-react";
import { useNavigate } from "react-router-dom";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import {
  GetStaffRankings,
  GetStaffAnalytics,
  GetStaffRankingsParams,
  GetStaffAnalyticsParams,
} from "@/src/services/staff-performance.service";
import PerformanceTrendsChart from "./components/performance-trends-chart";
import TopPerformersTable from "./components/top-performers-table";
import DatePicker from "@/components/ui/calendar";
import { formatDate } from "date-fns";
import { useToast } from "@/src/contexts/hooks/toast";

type PeriodType = "daily" | "weekly" | "monthly" | "quarterly" | "yearly";

type MetricType =
  | "efficiency"
  | "reliability"
  | "customerSatisfaction"
  | "totalRevenue";

const TeamPerformance: React.FC = () => {
  const navigate = useNavigate();
  const [periodType, setPeriodType] = useState<PeriodType>("monthly");
  const [metric, _setMetric] = useState<MetricType>("efficiency");
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    endDate: new Date()
  });

  // Fetch staff rankings
  const rankingsQuery = useQuery({
    queryKey: ["staff-rankings", periodType, metric, dateRange],
    queryFn: async () => {
      const params: GetStaffRankingsParams = {
        periodType,
        metric,
        startDate: formatDate(dateRange.startDate, 'yyyy-MM-dd'),
        endDate: formatDate(dateRange.endDate, 'yyyy-MM-dd'),
      };
      return (await GetStaffRankings(params)).data;
    },
    enabled: !!dateRange.startDate && !!dateRange.endDate,
  });

  // Fetch analytics
  const analyticsQuery = useQuery({
    queryKey: ["staff-analytics", periodType, dateRange],
    queryFn: async () => {
      const params: GetStaffAnalyticsParams = {
        periodType,
        startDate: formatDate(dateRange.startDate, 'yyyy-MM-dd'),
        endDate: formatDate(dateRange.endDate, 'yyyy-MM-dd'),
      };
      return (await GetStaffAnalytics(params)).data;
    },
    enabled: !!dateRange.startDate && !!dateRange.endDate,
  });

  const rankings = rankingsQuery.data?.rankings || [];
  const analytics = analyticsQuery.data?.analytics;

  const isLoading = rankingsQuery.isLoading || analyticsQuery.isLoading;

  const showToast = useToast()

  return (
    <section className="w-full h-full max-w-7xl md:px-4 mx-auto flex flex-col gap-y-6 pb-32">
      {/* Header */}
      <div className="w-full flex justify-between items-start">
        <div className="flex items-start gap-x-4">
          <Button
            variant="icon"
            onTap={() => navigate("/teams")}
            className="p-1.5"
          >
            <ArrowLeft02Icon width={20} height={20} />
          </Button>
          <div>
            <Typography
              variant="h1"
              className="font-bold font-Bricolage_Grotesque"
            >
              Team Performance
            </Typography>
            <Typography className="text-gray-600 mt-1">
              Track and analyze your team's performance metrics
            </Typography>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <SearchableDropdown
              label="Period"
              value={periodType}
              options={[
                { label: "Daily", value: "daily" },
                { label: "Weekly", value: "weekly" },
                { label: "Monthly", value: "monthly" },
                { label: "Quarterly", value: "quarterly" },
                { label: "Yearly", value: "yearly" },
              ]}
              onChange={(option) => setPeriodType(option?.value as PeriodType)}
            />

            <div className="flex items-center gap-x-4 ">
              <DatePicker.FormControl
                name="startDate"
                value={dateRange.startDate}
                label="From"
                onChange={(value) => {
                  if (value >= dateRange.endDate) {
                    return showToast('info', 'Start date must be before end date')
                  }
                  setDateRange((prev) => ({
                    ...prev,
                    startDate: value,
                  }))
                }}
                className="flex-1"
              />
              <DatePicker.FormControl
                name="endDate"
                value={dateRange.endDate}
                label="To"
                onChange={(value) => {
                  if (value <= dateRange.startDate) {
                    return showToast('info', 'End date must be after start date')
                  }
                  setDateRange((prev) => ({
                    ...prev,
                    endDate: value,
                  }))
                }}
                className="flex-1"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {isLoading ? (
        <div className="flex justify-center py-20">
          <CircularLoader />
        </div>
      ) : (
        <>
          {/* Charts and Tables Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance Trends */}
            <PerformanceTrendsChart
              analytics={analytics}
              periodType={periodType}
            />
          </div>

          {/* Top Performers Table */}
          <TopPerformersTable
            topPerformers={analytics?.topPerformers || []}
            metric={metric}
            onStaffClick={(staffId) => {
              // use it to open that staff-rating-modal
              staffId
            }}
          />

          {/* Empty State */}
          {rankings.length === 0 && !isLoading && (
            <Card>
              <CardContent className="p-12 text-center">
                <UserGroupIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <Typography className="text-gray-600 mb-2">
                  No performance data available
                </Typography>
                <Typography className="text-sm text-gray-500">
                  Performance data will appear here once your team starts taking
                  bookings
                </Typography>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </section>
  );
};

export default TeamPerformance;
