export type ServiceId = string;

export interface SchedulingRules {
  _id: string;
  serviceId: ObjectId<{
    name: string;
    price: number
    urlName: string;
  }>;
  userId: string;
  depositType: "none" | "percentage" | "fixed" | "full";
  depositAmount?: number;
  cancelCutoff: number;
  noShowFee: number;
  allowRescheduling: boolean;
  rescheduleCutoff: number;
  lateCancellationFee: number;
  isActive: boolean;
  isDefault?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSchedulingRulesPayload {
  depositType: "none" | "percentage" | "fixed" | "full";
  depositAmount?: number;
  cancelCutoff: number;
  noShowFee: number;
  allowRescheduling: boolean;
  rescheduleCutoff: number;
  lateCancellationFee: number;
  isActive: boolean;
}

export interface UpdateSchedulingRulesPayload {
  depositType?: "none" | "percentage" | "fixed" | "full";
  depositAmount?: number;
  cancelCutoff?: number;
  noShowFee?: number;
  allowRescheduling?: boolean;
  rescheduleCutoff?: number;
  lateCancellationFee?: number;
  isActive?: boolean;
}

export interface SchedulingPolicy {
  depositType: "none" | "percentage" | "fixed" | "full";
  depositAmount?: number;
  cancelCutoff: number;
  noShowFee: number;
  allowRescheduling: boolean;
  rescheduleCutoff: number;
  lateCancellationFee: number;
  isDefault: boolean;
}
