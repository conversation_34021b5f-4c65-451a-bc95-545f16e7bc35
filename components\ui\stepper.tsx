import { toReversed, truncateText } from "@/lib/helpers";
import React, { useMemo, useState } from "react";
import { Button } from "../buttons";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Typography } from "../typography";

export type StepperStep = {
  id: string;
  toShow?(): boolean;
  title: string;
  shouldGoNext?(): Promise<boolean>
}

export type StepperSteps = Array<StepperStep>

type Props = {
  steps: StepperSteps;
  currentStep: StepperStep
  functions: {
    prev(): void;
    next(): void;
    getLast(): StepperStep | undefined;
    getNext(): StepperStep | undefined
  }
}

const Stepper = (props: Props) => {
  const { currentStep, functions } = props
  const [lastStep, nextStep] = [functions.getLast(), functions.getNext()]

  return (
    <div className="w-full max-w-2xl py-0 grid items-center justify-between grid-cols-[1fr_auto_1fr] gap-x-2">
      {lastStep ?
        <Button
          onClick={() => functions.prev()}
          variant="ghost"
          className="!px-0 flex items-center justify-center text-sm !text-[inherit] "
        >
          <ChevronLeft width={24} height={24} />{" "}
          <span className="font-medium inline-block -ml-0.5">
            {truncateText(lastStep.title, innerWidth <= 480 ? 8 : 50)}
          </span>
        </Button>
        : <div />
      }
      <Typography className="font-medium mx-auto ">
        {currentStep.title}
      </Typography>
      {nextStep &&
        <Button
          onClick={() => functions.next()}
          variant="ghost"
          className="!px-0 flex items-center justify-center !text-[inherit] "
        >
          <span className="font-medium text-sm whitespace-nowrap overflow-hidden overflow-ellipsis inline-block -mr-0.5">
            {truncateText(nextStep.title, innerWidth <= 480 ? 7 : 50)}
          </span>
          <ChevronRight width={24} height={24} />{" "}
        </Button>
      }
    </div>
  )
}

export const useStepper = <T extends StepperStep>(values: T[], deps?: React.DependencyList) => {
  const steps = useMemo(() => values, deps ?? []);
  const [currentStep, setCurrentStep] = useState(
    steps.find(s => s.toShow ? s.toShow() : true)
  )
  const functions = {
    getLast() {
      const stepIndex = steps.findIndex(s => s.id === currentStep?.id);
      if (stepIndex !== -1) {
        const previousSteps = steps.slice(0, stepIndex)
        const reversedPreviousSteps = toReversed(previousSteps)
        const firstStepToBeShown = reversedPreviousSteps.find(s => s.toShow ? s.toShow() : true)
        return firstStepToBeShown
      }
    },
    getNext() {
      const stepIndex = steps.findIndex(s => s.id === currentStep?.id);
      if (stepIndex !== -1) {
        // slice the array from current index to end and then find the one that has toShow() as true;
        const nextSteps = steps.slice(stepIndex + 1)
        const nextStep = nextSteps.find(s => s.toShow ? s.toShow() : true)
        return nextStep;
      }
    },
    prev: function() {
      const lastStep = functions.getLast()
      if (lastStep)
        setCurrentStep(lastStep)
    },
    next: async function() {
      const nextStep = functions.getNext()
      if (nextStep) {
        // if there is no shouldGoNext function or we should go next, then go next
        if (!currentStep?.shouldGoNext || (await currentStep?.shouldGoNext()))
          setCurrentStep(nextStep)
      }
    }
  }
  return {
    currentStep,
    matchStep(...step: Array<T['id']>) {
      return Boolean(step.find(s => s === currentStep?.id))
    },
    nextStep: functions.next,
    prevStep: functions.prev,
    Stepper: () => <Stepper steps={steps} currentStep={currentStep!}
      functions={functions}
    />
  }
}
