import MailFolder from "@/assets/images/email.png";
import { Typography } from "@/components/typography";
import { FormData } from "../types";

const VerifyEmail = (props: { formData: FormData }) => {
  return (
    <div>
      <section
        className={`h-fit w-fit flex px-0 py-10 flex-col gap-y-7 items-center max-w-[400px] justify-center rounded-[28px] bg-white `}
      >
        <div className=" flex flex-col gap-y-4  ">
          <div className="flex flex-col gap-y-4 pt-2 px-3">
            <div className="w-fit h-fit mx-auto">
              <img src={MailFolder} alt="Email" />
            </div>
            <Typography variant={"h4"} className=" text-center ">
              Email Verification
            </Typography>
            <Typography variant="p" className="!mt-0 text-center">
              We have sent an email to{" "}
              <span className="text-sky-600">{props.formData.email}</span> to
              confirm the validity of your email address. Please check your
              inbox and follow the instructions to complete the verification
              process.
            </Typography>
          </div>
        </div>
      </section>
    </div>
  );
};

export default VerifyEmail;
