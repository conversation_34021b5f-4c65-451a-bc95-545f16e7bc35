
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/src/contexts/hooks/toast";
import * as MultiLocationService from "@/src/services/multi-location.service";
import {
  LocationId,
  UpdateLocationPayload,
  AssignStaffToLocationsPayload,
  ProximitySearchParams,
  StaffId,
} from "@/src/interfaces/multi-location";

export const useMultiLocation = () => {
  const queryClient = useQueryClient();
  const showToast = useToast();

  const locationsQuery = useQuery({
    queryKey: ["locations"],
    queryFn: async () => (await MultiLocationService.GetLocations()).data.locations,
  });

  const createLocationMutation = useMutation({
    mutationFn: MultiLocationService.CreateLocation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["locations"] });
      showToast("success", "Location created successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to create location"
      );
    },
  });

  const updateLocationMutation = useMutation({
    mutationFn: ({
      locationId,
      payload,
    }: {
      locationId: LocationId;
      payload: UpdateLocationPayload;
    }) => MultiLocationService.UpdateLocation(locationId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["locations"] });
      showToast("success", "Location updated successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to update location"
      );
    },
  });

  const deleteLocationMutation = useMutation({
    mutationFn: MultiLocationService.DeleteLocation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["locations"] });
      showToast("success", "Location deleted successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to delete location"
      );
    },
  });

  const toggleLocationActivation = {
    activate: useMutation({
      mutationFn: MultiLocationService.ActivateLocation,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["locations"] });
        showToast("info", "Location activated successfully");
      },
      onError: (error: any) => {
        showToast(
          "error",
          error.response?.data?.error || "Failed to activate location"
        );
      },
    }),
    deactivate: useMutation({
      mutationFn: MultiLocationService.DeactivateLocation,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["locations"] });
        showToast("info", "Location deactivated successfully");
      },
      onError: (error: any) => {
        showToast(
          "error",
          error.response?.data?.error || "Failed to deactivate location"
        );
      },
    }),
  };

  const assignStaffToLocationsMutation = useMutation({
    mutationFn: ({
      staffId,
      payload,
    }: {
      staffId: StaffId;
      payload: AssignStaffToLocationsPayload;
    }) => MultiLocationService.AssignStaffToLocations(staffId, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["locations"] });
      queryClient.invalidateQueries({ queryKey: ["staff"] });
      showToast("success", "Staff assigned to locations successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to assign staff to locations"
      );
    },
  });

  const assignServiceToLocationMutation = useMutation({
    mutationFn: MultiLocationService.AssignServiceToLocation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["locations"] });
      queryClient.invalidateQueries({ queryKey: ["services"] });
      showToast("success", "Service assigned to location successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to assign service to location"
      );
    },
  });

  const removeServiceFromLocationMutation = useMutation({
    mutationFn: MultiLocationService.RemoveServiceFromLocation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["locations"] });
      queryClient.invalidateQueries({ queryKey: ["services"] });
      showToast("success", "Service removed from location successfully");
    },
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to remove service from location"
      );
    },
  });

  const geocodeAddressMutation = useMutation({
    mutationFn: MultiLocationService.GeocodeAddress,
    onError: (error: any) => {
      showToast(
        "error",
        error.response?.data?.error || "Failed to geocode address"
      );
    },
  });

  return {
    locationsQuery,
    createLocationMutation,
    updateLocationMutation,
    deleteLocationMutation,
    toggleLocationActivation,
    assignStaffToLocationsMutation,
    assignServiceToLocationMutation,
    removeServiceFromLocationMutation,
    geocodeAddressMutation,
  };
};

export const useLocationDetails = (locationId?: LocationId) => {
  const locationQuery = useQuery({
    queryKey: ["location", locationId],
    queryFn: async () => {
      if (!locationId) throw new Error("Location ID is required");
      return (await MultiLocationService.GetLocationById(locationId)).data.location;
    },
    enabled: !!locationId,
  });

  const staffByLocationQuery = useQuery({
    queryKey: ["staffByLocation", locationId],
    queryFn: async () => {
      if (!locationId) throw new Error("Location ID is required");
      return (await MultiLocationService.GetStaffByLocation(locationId)).data.staff;
    },
    enabled: !!locationId,
  });

  const locationServicesQuery = useQuery({
    queryKey: ["locationServices", locationId],
    queryFn: async () => {
      if (!locationId) throw new Error("Location ID is required");
      return (await MultiLocationService.GetLocationServices(locationId)).data.services;
    },
    enabled: !!locationId,
  });

  return {
    locationQuery,
    staffByLocationQuery,
    locationServicesQuery,
  };
};

export const useStaffLocations = (staffId?: StaffId) => {
  const staffLocationsQuery = useQuery({
    queryKey: ["staffLocations", staffId],
    queryFn: async () => {
      if (!staffId) throw new Error("Staff ID is required");
      return (await MultiLocationService.GetStaffLocations(staffId)).data.locations;
    },
    enabled: !!staffId,
  });

  return {
    staffLocationsQuery,
  };
};

export const useLocationAnalytics = (
  locationId?: LocationId,
  params?: { startDate: string; endDate: string }
) => {
  const locationAnalyticsQuery = useQuery({
    queryKey: ["locationAnalytics", locationId, params],
    queryFn: async () => {
      if (!locationId || !params) throw new Error("Location ID and date params are required");
      return (await MultiLocationService.GetLocationAnalytics(locationId, params)).data.analytics;
    },
    enabled: !!locationId && !!params,
  });

  return {
    locationAnalyticsQuery,
  };
};

export const useNearbyLocations = (params?: ProximitySearchParams) => {
  const nearbyLocationsQuery = useQuery({
    queryKey: ["nearbyLocations", params],
    queryFn: async () => {
      if (!params) throw new Error("Search parameters are required");
      return (await MultiLocationService.SearchNearbyLocations(params)).data.locations;
    },
    enabled: !!params,
  });

  return {
    nearbyLocationsQuery,
  };
};
