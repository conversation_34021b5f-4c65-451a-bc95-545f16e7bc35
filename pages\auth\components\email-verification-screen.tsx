import { Typography } from "@/components/typography";
import Logo from '@/assets/logo.png'
import { Mail01Icon } from "hugeicons-react";

type Props = {
  emailAddress: string;
};

const EmailVerificationScreen = (props: Props) => {
  return (
    <section
      className={`min-h-screen w-screen fixed z-[900] top-0 left-0 flex px-4 pb-10 flex-col gap-y-7 items-center justify-center rounded-[28px] bg-white `}
    >
      <div className="w-full flex flex-col items-center gap-y-3 justify-center">
        <img src={Logo} className="w-full size-28 object-contain  " />
        <Typography variant={'h3'} className="font-Satoshi hidden font-bold text-3xl text-center">
          Puzzle Piece <br /> Solutions
        </Typography>
      </div>
      <div className=" flex flex-col items-center gap-y-4 ">
        <Typography variant={"h1"}
          className="text-[28px] font-Bricolage_Grotesque overflow-hidden text-start font-bold "
        >
          Email Verification
        </Typography>
        <div className="flex flex-col max-w-[480px] gap-y-4 pt-2 px-3">
          <div className="p-8 w-fit h-fit bg-pink-500 rounded-full mx-auto">
            <Mail01Icon className="w-8 h-8 text-white " />
          </div>
          <Typography variant="p" className="!mt-0 text-center">
            We have sent an email to{" "}
            <span className="text-primary-400">{props.emailAddress}</span> to
            confirm the validity of your email address.
          </Typography>
          <Typography variant="p" className="!mt-0 text-center text-paragraph/50 text-sm ">
            Please check your inbox
            and follow the instructions to complete the verification process.
          </Typography>
        </div>
      </div>
    </section>
  );
};

export default EmailVerificationScreen;
