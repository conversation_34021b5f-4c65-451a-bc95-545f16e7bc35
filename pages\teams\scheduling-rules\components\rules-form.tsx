import React from "react";
import { FormikProps } from "formik";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import Toggle from "@/components/ui/toggle-2";
import { CreateSchedulingRulesPayload } from "@/src/interfaces/scheduling-rules";
import DepositSettings from "./deposit-settings";
import CancellationSettings from "./cancellation-settings";
import ReschedulingSettings from "./rescheduling-settings";

interface RulesFormProps {
  formik: FormikProps<CreateSchedulingRulesPayload>;
}

const RulesForm: React.FC<RulesFormProps> = ({ formik }) => {
  return (
    <>
      {/* Deposit Settings */}
      <DepositSettings formik={formik} />

      {/* Cancellation Settings */}
      <CancellationSettings formik={formik} />

      {/* No-Show Settings */}
      <div className="bg-white rounded-2xl border p-6 space-y-4">
        <div className="space-y-1">
          <Typography variant="h4" className="font-semibold">
            No-Show Policy
          </Typography>
          <Typography className="text-sm text-gray-600">
            Set fees for clients who don't show up for their appointment
          </Typography>
        </div>

        <Input.Numeric
          label="No-show fee (% of total price)"
          name="noShowFee"
          value={formik.values.noShowFee}
          onChange={formik.handleChange}
          placeholder="100"
          min={0}
          max={100}
        />
        <Typography className="text-xs text-gray-500 -mt-2">
          Fee charged when clients don't show up for their appointment
        </Typography>

        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <Typography className="text-red-800 text-sm">
            <strong>Note:</strong> A 100% no-show fee means clients pay the full service price if they don't attend.
          </Typography>
        </div>
      </div>

      {/* Rescheduling Settings */}
      <ReschedulingSettings formik={formik} />

      {/* Active Status */}
      <div className="bg-white rounded-2xl border p-6 space-y-3">
        <div className="space-y-1">
          <Typography variant="h4" className="font-semibold">
            Rule Status
          </Typography>
          <Typography className="text-sm text-gray-600">
            Control whether these rules are currently active
          </Typography>
        </div>

        <Toggle
          label="Rules are active"
          checked={formik.values.isActive}
          onChange={(checked) => formik.setFieldValue("isActive", checked)}
        />
        <Typography className="text-xs text-gray-500 -mt-1">
          When inactive, default rules will be used instead
        </Typography>
      </div>
    </>
  );
};

export default RulesForm;
