"use client";
import { <PERSON>, <PERSON><PERSON><PERSON> } from "recharts";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import { GetClientCountAndPercentage } from "~/services/analytics.service";
import { useQuery } from "@tanstack/react-query";
import { formatDate } from "date-fns";
import { useEffect, useMemo } from "react";
import CircularLoader from "@/components/ui/circular-loader";
import { useAnalyticsInterval } from "../analytics-interval-context";
import { inlineSwitch } from "@/lib/helpers";
import { GetServices } from "@/src/services/services.service";

export const allColors = [
  "#FF7F50", // Coral
  "#00BFFF", // Deep Sky Blue
  "#FFA07A", // Light Salmon
  "#DB7093", // Pale Violet Red
  "#FFB347", // Pastel Orange
  "#FFC0CB", // Pink Rose
  "#87CEEB", // Sky Blue
  "#6495ED", // Cornflower Blue
  "#CD853F", // Peru
  "#1E90FF", // Dodger Blue
  "#DEB887", // Burlywood
  "#ADD8E6", // Light Blue
  "#FF8C00", // Dark Orange
  "#B0E0E6", // Powder Blue
  "#D2691E", // Chocolate
  "#87CEFA", // Light Sky Blue
];

/**
 * @dev pass a fill prop to each
 */
function ClientDistributionChart() {
  const { interval } = useAnalyticsInterval();
  const { data: services = [] } = useQuery({
    queryKey: ["services"],
    queryFn: async () => (await GetServices()).data.Services,
  });
  const {
    data: clientDistributionData,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ["service-client-distribution"],
    queryFn: async () =>
      (
        await GetClientCountAndPercentage({
          timeInterval: interval,
          serviceId: services[0]._id,
        })
      ).data.data,
    enabled: !!services.length,
  });
  useEffect(() => (refetch(), void 0), [interval]);
  const clientDistributionDataMemo = useMemo(() => {
    return (
      clientDistributionData?.map((data, index) => ({
        name: data.serviceName,
        value: data.clientCount,
        fill: allColors[index] || allColors[0],
      })) || []
    );
  }, [clientDistributionData]);

  const getChartConfig = (): ChartConfig => {
    const config: ChartConfig = {};
    for (const data of clientDistributionDataMemo) {
      config[data.name] = {
        label: data.name,
        color: data.fill,
      };
    }
    return config;
  };

  if (isFetching) {
    return (
      <div className="w-full py-12 flex justify-center ">
        <CircularLoader />
      </div>
    );
  }

  return (
    <Card className="flex flex-col !rounded-3xl ">
      <CardHeader className="items-center pb-0">
        <CardTitle>Client Distribution</CardTitle>
        <CardDescription>{formatDate(new Date(), "MMMM, yyy")}</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={getChartConfig()}
          className="mx-auto aspect-square max-h-[500px]"
        >
          <PieChart>
            <Pie data={clientDistributionDataMemo} dataKey="value" />
            <ChartLegend
              content={<ChartLegendContent nameKey="value" />}
              className="-translate-y-2 flex-wrap gap-x-2"
            />
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col !items-start gap-2 text-sm">
        <div className="leading-none text-muted-foreground">
          Showing client distribution data for{" "}
          {inlineSwitch(
            interval,
            ["today", "today."],
            ["weekly", "this week."],
            ["monthly", "this month."],
            ["yearly", "this year."]
          )}
        </div>
      </CardFooter>
    </Card>
  );
}

export default ClientDistributionChart;
