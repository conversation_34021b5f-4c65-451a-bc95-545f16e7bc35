import { pick } from "@/lib/helpers";
import { useAdminUserLogsApis } from "./use-admin";
import { AdminNS } from "@/src/services/admin.service";
import CircularLoader from "@/components/ui/circular-loader";
import { useParams } from "react-router-dom";
import { useEffect } from "react";
import { useToast } from "@/src/contexts/hooks/toast";
import { Typography } from "@/components/typography";

const properties: Array<keyof AdminNS.UserLog> = [
  "type",
  "ip",
  "description",
  "timestamp",
  "user_agent",
] as const;

const UserLogs = () => {
  const { id } = useParams<{ id: string }>();
  const { getUserLogsMutation } = useAdminUserLogsApis();
  const showToast = useToast();
  useEffect(() => {
    if (id)
      getUserLogsMutation.mutateAsync(id, {
        onError(error) {
          showToast(
            "error",
            (error as unknown as ApiError).response?.data.error ||
              "Something went wrong"
          );
        },
      });
  }, [id]);
  return (
    <section className=" md:max-w-[760px] overflow-x-auto no-scrollbar ">
      <Typography className="mb-4" >
        User Id (<span className="text-primary-200" >{id}</span>) Logs
      </Typography>
      <div className=" w-full mr-6 grid grid-cols-[repeat(5,minmax(240px,350px))]">
        {properties.map((key, index) => (
          <div
            key={index}
            className=" border-b-2 last:border-r-0 px-1 pb-2 text-center text-sm"
          >
            <div className="w-full bg-gray-100 py-2 rounded-lg">{key}</div>
          </div>
        ))}
      </div>
      {getUserLogsMutation.isPending && (
        <div className="w-full flex items-center justify-center py-12">
          <CircularLoader />
        </div>
      )}
      {getUserLogsMutation.data?.map((userLogs, index) => {
        const toBeShown = pick(userLogs, ...properties);
        return (
          <div
            key={index}
            className=" w-full max-w-fit pr-6 grid grid-cols-[repeat(5,minmax(240px,350px))] group relative "
          >
            {Object.entries(toBeShown as AdminNS.UserLog).map(
              ([_, value], index) => {
                if (typeof value === 'string')
                  return (
                    <div
                      key={index}
                      className="first:border-x-2 border-r-2 border-b-2 pt-2 pb-3 px-4 group relative cursor-pointer"
                    >
                      <p className="text-center line-clamp-1 text-sm">
                        {!value ? "----" : value}
                      </p>
                    </div>
                  );
                else if (value instanceof Date)
                  return (
                    <div
                      key={index}
                      className="first:border-x-2 border-r-2 border-b-2 pt-2 pb-3 px-4 cursor-pointer"
                    >
                      <p className="text-center line-clamp-1 text-sm">
                        {value.toLocaleString()}
                      </p>
                    </div>
                  );
              }
            )}
          </div>
        );
      })}
    </section>
  );
};

export default UserLogs;
