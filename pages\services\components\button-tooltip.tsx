import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  <PERSON><PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const ButtonTooltip: React.FC<{
  children?: React.ReactNode;
  trigger?: React.ReactNode;
  content: string;
}> = ({ trigger, children, content, ...props }) => {
  return (
    <TooltipProvider delayDuration={300} {...props}>
      <Tooltip>
        <TooltipTrigger asChild>{trigger || children}</TooltipTrigger>
        <TooltipContent>
          <p className="font-medium">{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default ButtonTooltip;
