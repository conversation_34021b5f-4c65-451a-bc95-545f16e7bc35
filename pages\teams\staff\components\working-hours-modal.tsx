import React from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import Modal from "@/components/ui/modal";
import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Toggle from "@/components/ui/toggle-2";
import { Staff, StaffWorkingHours, WorkingHours } from "@/src/interfaces/staff";
import { useStaff } from "../../hooks/use-staff";
import { useToast } from "~/contexts/hooks/toast";
import DatePicker from "@/components/ui/calendar";

interface WorkingHoursModalProps {
  isOpen: boolean;
  onClose: () => void;
  staff: Staff;
}

const DAYS = [
  { key: "monday", label: "Monday" },
  { key: "tuesday", label: "Tuesday" },
  { key: "wednesday", label: "Wednesday" },
  { key: "thursday", label: "Thursday" },
  { key: "friday", label: "Friday" },
  { key: "saturday", label: "Saturday" },
  { key: "sunday", label: "Sunday" },
] as const;

const defaultWorkingHours: WorkingHours = {
  start: "09:00",
  end: "17:00",
  isWorking: true,
};

/**
 * @dev this updates the staff working hours internallyyyy
 */
const WorkingHoursModal: React.FC<WorkingHoursModalProps> = ({
  isOpen,
  onClose,
  staff,
}) => {
  const { updateStaffMutation } = useStaff();
  const showToast = useToast();

  const [first_name, last_name] = staff?.name.split(" ") || ["", ""];
  const formik = useFormik({
    initialValues: {
      first_name: first_name,
      last_name: last_name,
      email: staff?.email || "",
      phone: staff?.phone || "",
      role: staff?.role || "",
      color: staff?.color || "#3B82F6",
      buffer: staff?.buffer || 15,
      travelTime: staff?.travelTime || 30,
      capacity: staff?.capacity || 8,
      isActive: staff?.isActive,
      workingHours: DAYS.reduce((acc, day) => {
        acc[day.key] = staff.workingHours[day.key] || defaultWorkingHours;
        return acc;
      }, {} as StaffWorkingHours),
    },
    validationSchema: Yup.object({
      workingHours: Yup.object().shape(
        DAYS.reduce((acc, day) => {
          acc[day.key] = Yup.object({
            start: Yup.string().required(),
            end: Yup.string().required(),
            isWorking: Yup.boolean(),
          });
          return acc;
        }, {} as any),
      ),
    }),
    onSubmit: async (values) => {
      try {
        await updateStaffMutation.mutateAsync({
          staffId: staff._id,
          payload: values,
        });
        onClose();
      } catch (error) {
        showToast(
          "error",
          error ? String(error) : "Failed to update working hours",
        );
      }
    },
  });

  const toggleDay = (day: keyof StaffWorkingHours, isWorking: boolean) => {
    formik.setFieldValue(`workingHours.${day}.isWorking`, isWorking);
  };

  const setDayHours = (
    day: keyof StaffWorkingHours,
    field: "start" | "end",
    value: string,
  ) => {
    formik.setFieldValue(`workingHours.${day}.${field}`, value);
  };

  const copyToAllDays = (sourceDay: keyof StaffWorkingHours) => {
    const sourceHours = formik.values.workingHours[sourceDay];
    if (!sourceHours) return;

    DAYS.forEach((day) => {
      if (day.key !== sourceDay) {
        formik.setFieldValue(`workingHours.${day.key}`, { ...sourceHours });
      }
    });
  };

  return (
    <Modal open={isOpen} onClose={onClose}>
      <Modal.Body
        className={`min-h-fit max-w-[360px] h-fit w-fit flex flex-col gap-y-3 justify-center rounded-[32px] bg-white !p-0 `}
      >
        <div className="flex items-center justify-between px-6 pt-6 space-y-2">
          <Typography variant="h4" className="font-semibold">
            Working Hours - {staff.name}
          </Typography>
        </div>

        <div className="space-y-4 px-2 max-h-[300px] overflow-y-auto thin-scrollbar ">
          {DAYS.map((day) => {
            const dayHours = formik.values.workingHours[day.key];
            const isWorking = dayHours?.isWorking ?? false;

            return (
              <div key={day.key} className="flex flex-col gap-2 p-3 rounded-lg">
                <div className="flex items-center gap-x-3">
                  <div className="w-24">
                    <Typography className="font-medium capitalize">
                      {day.label}
                    </Typography>
                  </div>

                  <Toggle
                    checked={isWorking}
                    onChange={(checked) => toggleDay(day.key, checked)}
                  />
                </div>

                {isWorking && (
                  <>
                    <div className="flex items-center gap-2">
                      <DatePicker.Time
                        name="start"
                        value={dayHours?.start}
                        label=""
                        onChange={(value) =>
                          setDayHours(day.key, "start", value)
                        }
                        className="flex-1"
                      />
                      <span className="text-gray-500">to</span>
                      <DatePicker.Time
                        name="end"
                        value={dayHours?.end}
                        label=""
                        onChange={(value) => setDayHours(day.key, "end", value)}
                        className="flex-1"
                      />
                    </div>

                    <Button
                      type="button"
                      variant="dormant"
                      onTap={() => copyToAllDays(day.key)}
                      className="text-xs"
                    >
                      Copy to all
                    </Button>
                  </>
                )}
              </div>
            );
          })}
        </div>
        <div className="w-full flex justify-between gap-x-1 px-2 pb-1.5 items-center">
          <Button
            type="button"
            variant="dormant"
            className="w-full py-3 rounded-[32px] text-sm"
            onTap={onClose}
          >
            Cancel
          </Button>
          <Button
            onTap={() => formik.submitForm()}
            loading={updateStaffMutation.isPending}
            disabled={updateStaffMutation.isPending}
            className="w-full text-sm py-3 rounded-[32px]"
            variant="full"
          >
            Save
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default WorkingHoursModal;
