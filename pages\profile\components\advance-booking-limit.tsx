import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import { cn } from "@/lib/utils";
import React from "react";

interface AdvanceBookingLimitSelectProps {
  advanceBookingLimit?: number | string | null;
  setAdvanceBookingLimit: (newAdvanceBookingLimit: number | null) => void;
}

const timeUnits = ["days", "weeks", "months"];
const AdvanceBookingLimitSelect: React.FC<AdvanceBookingLimitSelectProps> = ({
  advanceBookingLimit,
  setAdvanceBookingLimit,
}) => {
  return (
    <div className="w-full flex items-start justify-between gap-x-3">
      <Typography className=" text-paragraph">Advance booking limit</Typography>
      <div className="flex flex-col gap-x-3">
        <Input.Numeric
          label="Period"
          name="period"
          required
          value={advanceBookingLimit?.toString()}
          onChange={(e) => {
            const value = parseInt(e.target.value) || null;
            setAdvanceBookingLimit(value);
          }}
        />
        <div className="py-2 grid hidden grid-cols-3 gap-x-2">
          {timeUnits.map((unit, index) => (
            <Button
              key={index}
              onTap={() => {
                //setSelectedAdvanceBookingLimit((p) => ({
                  //...p,
                  //timeframe: unit,
                //}));
              }}
              className={cn(
                "py-1 rounded-md bg-subtle-gray text-gray-400 font-medium flex items-center justify-center md:bg-gray-100",
                {
                  //"bg-primary-50/40 md:bg-primary-50/40 text-primary-500":
                    //selectedAdvanceBookingLimit.timeframe === unit,
                }
              )}
            >
              {unit}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AdvanceBookingLimitSelect;
