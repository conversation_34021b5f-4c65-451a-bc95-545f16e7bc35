import { percentage, pick, px } from "@/lib/helpers";
import { ReactNode, useRef } from "react";
import { useDeviceConfig } from "../hooks/use-device-config";
import DeviceFrame from "./svg/device-frame";
import { useDigitalProfileContext } from "../hooks/use-digital-profile";

// 352.467 and 717.433 are the dimensions which was tested for the iphone frame. It should be what we use to get our ratios for resizing of the iframe container;
const dimensions = {
  w: 393,
  h: 852,
};

const containerStyles = {
  overflow: "hidden",
  position: "absolute",
  zIndex: 300,
} as const;

const deviceWidthRatio = 42 / dimensions.w;

const deviceHeightRatio = 39 / dimensions.h;

const safeAreaInsetRatio = 60 / dimensions.h;

const virtualHomeButtonRatio = 130 / dimensions.w;

const clothoidRadiusRatio = 51 / dimensions.w;

const deviceBarRatios = [15 / dimensions.h, 6 / dimensions.h] as const;

const DevicePreview: React.FC<{ children?: ReactNode }> = ({ children }) => {
  const wrapperRef = useRef<HTMLElement>(null);
  const { iphoneConfig } = useDeviceConfig(wrapperRef, {
    deviceBarRatios,
    deviceHeightRatio,
    deviceWidthRatio,
    clothoidRadiusRatio,
    virtualHomeButtonRatio,
    safeAreaInsetRatio,
  });
  const { profile: {
    theme: {
      backgroundValue
    } = {}
  } } = useDigitalProfileContext()
  return (
    <section
      ref={wrapperRef}
      className="flex w-fit h-full relative items-center justify-center "
    >
      <DeviceFrame height={dimensions.h} />
      <div
        className={`h-auto w-auto transition-[background] duration-300 ease-[ease] bg-cover `}
        style={{
          ...pick(iphoneConfig, "width", "height"),
          ...containerStyles,
          clipPath: iphoneConfig.clothoidRadius,
          background: backgroundValue,
        }}
      >
        <div
          style={{
            paddingTop: iphoneConfig.safeAreaInset,
            width: percentage(100),
            height: percentage(100),
            clipPath: "inherit",
            overflow: "hidden",
            position: "relative",
          }}
        >
          {children}
          <div
            style={{
              display: 'none',
              width: iphoneConfig.virtualHomeButtonWidth,
              height: px(4),
              borderRadius: px(50),
              position: "absolute",
              bottom: px(5),
              left: percentage(50),
              transform: "translateX(-50%)",
              zIndex: 900,
              backgroundColor: "black",
            }}
          />
        </div>
      </div>
    </section>
  );
};

export default DevicePreview;
