import React from "react";
import { FormikProps } from "formik";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import { CreateSchedulingRulesPayload } from "@/src/interfaces/scheduling-rules";

interface CancellationSettingsProps {
  formik: FormikProps<CreateSchedulingRulesPayload>;
}

const CancellationSettings: React.FC<CancellationSettingsProps> = ({ formik }) => {
  return (
    <div className="bg-white rounded-2xl border p-6 space-y-4">
      <div className="space-y-1">
        <Typography variant="h4" className="font-semibold">
          Cancellation Policy
        </Typography>
        <Typography className="text-sm text-gray-600">
          Set cutoff times and fees for appointment cancellations
        </Typography>
      </div>

      <Input.Numeric
        label="Free cancellation cutoff (hours before appointment)"
        name="cancelCutoff"
        value={formik.values.cancelCutoff}
        onChange={formik.handleChange}
        placeholder="24"
        min={0}
      />
      <Typography className="text-xs text-gray-500 -mt-2">
        Clients can cancel for free up to this many hours before their appointment
      </Typography>

      <Input.Numeric
        label="Late cancellation fee (% of total price)"
        name="lateCancellationFee"
        value={formik.values.lateCancellationFee}
        onChange={formik.handleChange}
        placeholder="50"
        min={0}
        max={100}
      />
      <Typography className="text-xs text-gray-500 -mt-2">
        Fee charged when clients cancel after the cutoff time
      </Typography>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <Typography className="text-blue-800 text-sm">
          <strong>Example:</strong> With a 24-hour cutoff and 50% fee, clients canceling within 24 hours pay half the service price.
        </Typography>
      </div>
    </div>
  );
};

export default CancellationSettings;
