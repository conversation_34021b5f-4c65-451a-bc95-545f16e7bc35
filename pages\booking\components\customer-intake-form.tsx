
import { But<PERSON> } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import { hyphenSeparatedLowercase, inlineSwitch, pick } from "@/lib/helpers";
import { ExternalBookingContextType, useBookingContext } from "./use-booking-context";
import { IntakeFormQuestion } from "@/src/interfaces/intake-form";
import { useCallback, useState } from "react";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import RadioBox from "@/components/ui/radio-box";
import DatePicker from "@/components/ui/calendar";
import { ExternalBookingPayload } from "@/src/services/booking.service";

interface CustomerIntakeFormProps {
  className?: string;
  questions: IntakeFormQuestion[]
  onClickNext: () => void;
}

const FIELD_TYPE_MAP = {
  text: Input.Text,
  email: Input.Email,
  textarea: Input.TextArea,
  number: Input.Numeric,
  phone: Input.Phone,
  url: Input.Text
};

const TYPES_WITH_MIN_MAX_VALIDATION = [
  'number'
]

/** 
 * @dev this intake form is only for external booking
 * */
const CustomerIntakeForm: React.FC<CustomerIntakeFormProps> = ({
  className = "",
  onClickNext,
  questions,
}) => {
  const { values, setValue } = useBookingContext('external')
  const setBookingValue = setValue as ExternalBookingContextType['setValue']


  return (
    <section
      className={`h-fit w-full flex px-[18px] min-[360px]:px-8 py-10 flex-col gap-y-7 items-center justify-center rounded-[28px] bg-[#EAEBE5]/40 ${className} `}
    >
      <Typography variant={"h2"} className="font-bold">
        Enter Your Details
      </Typography>
      <div className="space-y-4">
        {questions.map((question, i) => (
          <Question
            key={i}
            question={{
              ...question,
              answer:
                values.intakeFormResponses.find(fr => fr.questionId == question.PP_Id)?.answer ||
                (['checkbox'].includes(question.type) ? [] : '')
            }}
            onChange={(PP_Id, value) => {
              const questionResponse = values.intakeFormResponses.find(fr => fr.questionId == question.PP_Id)
              const response: ExternalBookingPayload['intakeFormResponses'][number] = questionResponse ?? {
                ...pick(question, 'question', 'required'),
                questionId: PP_Id,
                questionType: question.type,
                answer: value
              }
              setBookingValue('intakeFormResponses', Boolean(questionResponse)
                ? values.intakeFormResponses.map(res => {
                  if (res.questionId === question.PP_Id)
                    return { ...res, answer: value }
                  else return res;
                })
                : [...values.intakeFormResponses, response]
              )
            }} />
        ))}
        <Button
          type="button"
          onTap={() => {
            onClickNext()
          }}
          variant="full"
          className="w-full"
        >
          Continue
        </Button>
      </div>
    </section>
  );
};

type Props = {
  question: IntakeFormQuestion & { answer: any };
  onChange(PP_Id: string, value: any): void
}

const Question = (props: Props) => {
  const { type, question, answer, ...intakeFormQuestion } = props.question
  const [optionValue, setOptionValue] = useState(answer)
  const [optionValues, setOptionValues] = useState(answer || [])
  const [date, setDate] = useState(answer)
  const getValue = useCallback(() => {
    const value = intakeFormQuestion.options?.find(opt => opt === optionValue);
    if (value) return { label: value, value: value }
    else return null
  }, [optionValue, intakeFormQuestion.options])
  const render = useCallback(() => {
    if (type in FIELD_TYPE_MAP) {
      const InputType = FIELD_TYPE_MAP[type as keyof typeof FIELD_TYPE_MAP]
      const validation: Record<string, any> = TYPES_WITH_MIN_MAX_VALIDATION
        .includes(type as any)
        ? pick(intakeFormQuestion.validation || {}, 'min', 'max')
        : pick(intakeFormQuestion.validation || {}, 'maxLength', 'minLength')
      return (
        <InputType
          {...validation}
          name=""
          value={answer}
          required={intakeFormQuestion.required}
          label={question}
          placeholder={intakeFormQuestion.placeholder}
          onChange={(e) => {
            const { value } = (e as any).currentTarget
            props.onChange(props.question.PP_Id, value)
          }}
        />
      )
    }
    return inlineSwitch(type,
      [
        'select',
        <SearchableDropdown
          fullWidth
          label={question}
          value={(() => {
            const value = intakeFormQuestion.options?.find(opt => opt === optionValue);
            if (value) return { label: value, value: value }
            else return null
          })()}
          required={intakeFormQuestion.required}
          placeholder={intakeFormQuestion.placeholder}
          options={intakeFormQuestion.options?.map(opt => ({ label: opt, value: opt })) || []}
          name={hyphenSeparatedLowercase(question)}
          onChange={(option) => {
            setOptionValue(option?.value ?? '')
            props.onChange(props.question.PP_Id, option?.value)
          }}
        />
      ],
      [
        'radio',
        <RadioBox
          label={question}
          onChange={(option) => {
            setOptionValue(option?.value ?? '')
            props.onChange(props.question.PP_Id, option?.value)
          }}
          required={intakeFormQuestion.required}
          value={getValue()}
          options={intakeFormQuestion.options?.map(opt => ({ label: opt, value: opt })) || []}
        />
      ],
      [
        'checkbox',
        <RadioBox.Multiple
          label={question}
          onChange={(vals) => {
            setOptionValues(vals)
            props.onChange(props.question.PP_Id, vals)
          }}
          required={intakeFormQuestion.required}
          values={optionValues}
          options={intakeFormQuestion.options?.map(opt => ({ label: opt, value: opt })) || []}
        />
      ],
      [
        'date',
        <DatePicker.FormControl
          label={question}
          onChange={(_, valueAsString) => {
            setDate(valueAsString)
            props.onChange(props.question.PP_Id, valueAsString)
          }}
          {...pick(intakeFormQuestion.validation || {}, 'min', 'max')}
          required={intakeFormQuestion.required}
          value={date as any}
        />
      ]
    )
  }, [props.question, optionValue, optionValues, date])
  return (
    <div className="flex flex-col py-2 " >
      {/* a very large inline switch 🤣🤣🤣 */}
      {render()}
    </div>
  )
}

export default CustomerIntakeForm;
