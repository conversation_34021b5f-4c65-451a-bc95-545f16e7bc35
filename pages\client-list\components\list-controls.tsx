import { useToast } from "~/contexts/hooks/toast";
import { TClient } from "~/services/clients.service";
import { PlusCircle, Trash2Icon } from "lucide-react";
import { useRef } from "react";
import { downloadAsCSV } from "../utils/download_csv";
import { importFromCSV } from "../utils/import_csv";
import { Button } from "@/components/buttons";
import Menu from "@/components/ui/menu";
import { Typography } from "@/components/typography";
import BasicTooltip from "@/components/ui/basic-tooltip";
import { motion } from "framer-motion";
import { FileArrowDown, Magnifier } from "@gravity-ui/icons";
import { debounce } from "@/lib/helpers";

type Props = {
  openAddClientModal: () => void;
  onImportFromCSV: (clients: TClient[]) => void;
  onDeleteSelectedClients: () => void;
  selectedClients: TClient[];
  onSearch: (searchTerm: string) => void;
};

/**
 * @todo sorting for clients here, `show all`, `show some`
 */
const ClientListControls = (props: Props) => {
  // const options: Option[] = [
  //   { value: "show-all", label: "Show all clients" },
  //   { value: "show-some", label: "Show some clients" },
  // ];
  // if it is large screen, show it at first
  // const [isSearchShown, setIsSearchShown] = useState(!isSmallScreen());
  const importRef = useRef<HTMLInputElement>(null);
  const showToast = useToast();

  // download and export csv and delete clients;
  const downloadElRef = useRef<HTMLAnchorElement>(null);
  return (
    <div className="space-y-3 w-full md:max-w-[560px]">
      <div className="w-full flex items-center gap-x-3">
        <div
          tabIndex={1}
          className="w-full max-w-96 ml-0 overflow-hidden relative border border-gray-300 rounded-xl focus:outline-none focus-within:ring-2 focus-within:ring-primary-500 focus-within:border-transparent"
        >
          <Magnifier className="text-gray-500 absolute left-3 top-1/2 -translate-y-[46%] " />
          <input
            onInput={debounce<React.FormEventHandler<HTMLInputElement>>((e) => {
              e.preventDefault();
              props.onSearch((e.target as unknown as HTMLInputElement).value);
            }, 200)}
            type="text"
            autoComplete="off"
            placeholder="Search for name, email..."
            className="w-full ml-0 border-none focus:outline-none pr-4 pl-9 py-2.5 text-sm md:block "
          />
        </div>
        <Menu transformOrigin="top-right" className="ml-auto">
          <Menu.Trigger className="ml-auto">
            <Button variant="icon" className="ml-auto">
              <PlusCircle />
            </Button>
          </Menu.Trigger>
          <Menu.Content className="h-fit !min-h-0 right-0 min-w-[200px] ">
            <Menu.Item onClick={() => importRef.current?.click()}>
              Import from CSV
            </Menu.Item>
            <Menu.Item onClick={() => props.openAddClientModal()}>
              Add Manually
            </Menu.Item>
          </Menu.Content>
        </Menu>
        <input
          type="file"
          ref={importRef}
          accept=".csv"
          className="hidden"
          onChange={(e) => {
            importFromCSV(e, props.onImportFromCSV, (reason) =>
              showToast("info", reason)
            );
            e.currentTarget.files = null;
            e.currentTarget.value = null as any;
          }}
        />
      </div>
      {props.selectedClients.length ? (
        <div className="w-full md:max-w-[560px] flex items-center mb-3 gap-x-3 px-0.5">
          <BasicTooltip
            trigger={
              <motion.div
                whileTap={{ scale: 0.9 }}
                whileHover={{ scale: 1.05 }}
                onTap={props.onDeleteSelectedClients}
              >
                <Trash2Icon
                  width={20}
                  height={20}
                  className="text-paragraph/80 cursor-pointer"
                />
              </motion.div>
            }
            content="Delete selected"
          />
          <BasicTooltip
            trigger={
              <motion.div
                whileTap={{ scale: 0.9 }}
                whileHover={{ scale: 1.05 }}
                onTap={downloadAsCSV(downloadElRef, props.selectedClients)}
              >
                <FileArrowDown
                  width={20}
                  height={20}
                  className="text-paragraph/80 cursor-pointer"
                />
              </motion.div>
            }
            content="Download CSV"
          />
          <Typography className="text-paragraph/60 text-xs px-1.5 ml-auto">
            {props.selectedClients.length} selected
          </Typography>
          <a ref={downloadElRef} className="hidden"></a>
        </div>
      ) : (
        ""
      )}
    </div>
  );
};

export default ClientListControls;
