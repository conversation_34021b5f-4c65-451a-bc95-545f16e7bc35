import { cn } from "@/lib/utils";
import { formatDate } from "date-fns";
import { ArrowLeft, ArrowRight, ChevronDown, ChevronUp, X } from "lucide-react";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Button } from "../buttons";
import { Typography } from "../typography";
import Modal from "./modal";
import { useModalsBuilder } from "@/lib/modals-builder";
import { and, noop, sleep } from "@/lib/helpers";
import { IosPickerItem } from "./ios-style-picker";
import { useFormik } from "formik";

const months = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const daysOfWeek = ["M", "T", "W", "T", "F", "S", "S"];

/**
 * @dev this function reverses the array of 12 years;
 * @example
 * ```jsx
 * const years = getYearRange(2024); // [2013, 2014, 2015, 2016, ...];
 * ```
 */
const getYearRange = (year: number) => {
  const currentYear = year;
  const years: number[] = [];
  for (let i = 0; i < 12; i++) {
    years.push(currentYear - i);
  }
  return years.reverse();
};
interface DatePickerModalProps {
  onChange?: (date: Date) => void;
  value?: Date;
  className?: string;
}

/**
 * @dev this modal has it's own trigger
 */
const DatePickerModal = (props: DatePickerModalProps) => {
  const [open, setOpen] = useState(false);
  const [dateValue, setDateValue] = useState(props.value ?? new Date());
  const [years, setYears] = useState(getYearRange(dateValue.getFullYear()));
  useEffect(() => {
    setDateValue(props.value ?? new Date());
  }, [props.value]);
  return (
    <>
      <Button
        onTap={() => setOpen(true)}
        className={cn(
          "w-fit flex items-center gap-x-2 px-4 py-2 !rounded-full  ",
          props.className,
        )}
      >
        <Typography variant={"span"} className="font-semibold">
          {formatDate(dateValue, "MMMM yyy")}
        </Typography>
        <div className="-space-y-1">
          <ChevronUp className="w-4 h-4 " />
          <ChevronDown className="w-4 h-4 " />
        </div>
      </Button>
      <Modal onClose={() => setOpen(false)} className="" open={open}>
        <Modal.Body className="p-4 !min-h-0 !h-fit min-w-[300px] shadow-md rounded-xl space-y-4 ">
          <div className="w-full space-y-3 ">
            <Typography
              variant={"p"}
              className=" font-semibold w-full pb-2 border-b-2 text-center flex items-center"
            >
              <span className="mx-auto">Select Month</span>
              <Button
                variant="icon"
                onTap={() => setOpen(false)}
                className="  bg-transparent !p-0"
              >
                <X className="w-5 h-5 " />
              </Button>
            </Typography>

            <div className="w-full grid grid-cols-3 gap-x-2 gap-y-5">
              {months.map((month, index) => {
                return (
                  <Typography
                    onClick={() => {
                      const selectedDate = new Date(
                        dateValue.getFullYear(),
                        index,
                        props.value?.getDate() || 1,
                      );
                      setDateValue(selectedDate);
                      props.onChange?.(selectedDate);
                    }}
                    key={index}
                    variant={"span"}
                    className={cn(
                      `font-semibold w-full text-center !mt-0 hover:bg-[#DFE1DB] hover: cursor-pointer `,
                      { " ": dateValue.getMonth() != index },
                      { "bg-black text-white": dateValue.getMonth() === index },
                    )}
                  >
                    {month.slice(0, 3)}
                  </Typography>
                );
              })}
            </div>
          </div>
          <div className="w-full space-y-3 ">
            <div className="w-full pb-2 border-b-2 flex items-center justify-between">
              <Button
                variant="icon"
                onTap={() => {
                  const newYears = getYearRange(years[0] - 1);
                  setYears(newYears);
                }}
                className="w-8 h-8 bg-black text-white"
              >
                <ArrowLeft className="w-4 h-4 " />
              </Button>
              <Typography
                variant={"p"}
                className=" !mt-0 font-semibold text-center"
              >
                Select Year
              </Typography>
              <Button
                variant="icon"
                onTap={() => {
                  const newYears = getYearRange(years[11] + 12);
                  setYears(newYears);
                }}
                className="w-8 h-8 bg-black text-white"
              >
                <ArrowRight className="w-4 h-4 " />
              </Button>
            </div>
            <div className="w-full grid grid-cols-3 gap-x-2 gap-y-5">
              {years.map((year, index) => {
                return (
                  <Typography
                    onClick={() => {
                      const selectedDate = new Date(
                        year,
                        dateValue.getMonth(),
                        props.value?.getDate() || 1,
                      );
                      setDateValue(selectedDate);
                      props.onChange?.(selectedDate);
                    }}
                    key={index}
                    variant={"span"}
                    className={cn(
                      `font-semibold w-full text-center !mt-0 hover:bg-[#DFE1DB] hover: cursor-pointer `,
                      { " ": dateValue.getFullYear() != year },
                      {
                        "bg-black text-white": dateValue.getFullYear() === year,
                      },
                    )}
                  >
                    {year}
                  </Typography>
                );
              })}
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
};

const getCalendarDays = (date: Date): (Date | null)[][] => {
  const year = date.getFullYear();
  const month = date.getMonth();

  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);

  let firstDayIndex = firstDay.getDay() - 1;
  if (firstDayIndex === -1) firstDayIndex = 6;

  const weeks: (Date | null)[][] = [];
  let currentWeek: (Date | null)[] = Array(7).fill(null);
  let currentDay = 1;

  // Fill first week with leading nulls
  for (let i = firstDayIndex; i < 7; i++) {
    currentWeek[i] = new Date(year, month, currentDay++);
  }
  weeks.push(currentWeek);

  // Fill remaining weeks
  while (currentDay <= lastDay.getDate()) {
    currentWeek = Array(7).fill(null);
    for (let i = 0; i < 7 && currentDay <= lastDay.getDate(); i++) {
      currentWeek[i] = new Date(year, month, currentDay++);
    }
    weeks.push(currentWeek);
  }
  return weeks;
};

/**
 * @todo remove double implementation for date comparison
 */
const isDateCurrent = (date: Date | null): boolean => {
  if (date === null) return false;
  const today = new Date();
  const checkDate = date;
  today.setHours(0, 0, 0, 0);
  checkDate.setHours(0, 0, 0, 0);
  return checkDate.getTime() === today.getTime();
};

const isDateCurrentOrFuture = (date: Date | null): boolean => {
  if (date === null) return false;
  const today = new Date();
  const checkDate = date;
  today.setHours(0, 0, 0, 0);
  checkDate.setHours(0, 0, 0, 0);
  return checkDate.getTime() >= today.getTime();
};

interface DatePickerProps {
  className?: string;
  onChange?: (date: Date) => void;
  value?: Date;
  disableOldDates?: boolean;
  disabledDays?: (
    | "Monday"
    | "Tuesday"
    | "Wednesday"
    | "Thursday"
    | "Friday"
    | "Saturday"
    | "Sunday"
  )[];
}

/**
 * @dev the selectedDate should change to props;
 */
const DatePickerImpl: React.FC<DatePickerProps> = ({
  disableOldDates = true,
  disabledDays = [],
  ...props
}) => {
  const [selectedDate, setSelectedDate] = useState(props.value ?? new Date());
  const [calendarDays, setCalendarDays] = useState(
    getCalendarDays(props.value ?? new Date()),
  );
  return (
    <section
      className={cn(
        `w-fit h-fit py-6 px-5 select-none bg-[#EAEBE5] `,
        props.className,
      )}
    >
      <Typography variant={"span"}>Select a Date</Typography>
      <Typography variant={"h3"} className="w-full mt-4 ">
        {formatDate(selectedDate, "E, MMM dd")}
      </Typography>
      <div className="w-full mt-4 flex items-center justify-between ">
        <DatePickerModal
          value={selectedDate}
          onChange={(date) => {
            setSelectedDate(date);
            setCalendarDays(getCalendarDays(date));
          }}
        />
        <div className="w-fit flex items-center ">
          <button
            onClick={() => {
              const newDate = new Date(
                selectedDate.getFullYear(),
                selectedDate.getMonth() - 1,
                selectedDate.getDate(),
              );
              setSelectedDate(newDate);
              setCalendarDays(getCalendarDays(newDate));
            }}
            className="w-fit bg-[#DFE1DB] pl-3 pr-2 py-2 rounded-l-full"
          >
            <ArrowLeft />
          </button>
          <button
            onClick={() => {
              const newDate = new Date(
                selectedDate.getFullYear(),
                selectedDate.getMonth() + 1,
                selectedDate.getDate(),
              );
              setSelectedDate(newDate);
              setCalendarDays(getCalendarDays(newDate));
            }}
            className="w-fit bg-[#DFE1DB] pl-2 pr-3 py-2 rounded-r-full"
          >
            <ArrowRight />
          </button>
        </div>
      </div>
      <div className={cn(`w-full mt-5 grid grid-cols-7 gap-x-2`)}>
        {daysOfWeek.map((day, index) => (
          <Typography
            key={index}
            variant={"p"}
            className="text-center text-base  !mt-0 font-semibold"
          >
            {day}
          </Typography>
        ))}
      </div>
      <div
        className={cn(
          "w-full mt-5 grid grid-cols-7 gap-x-2 gap-y-3",
          { "grid-rows-6": calendarDays.length === 6 },
          { "grid-rows-5": calendarDays.length === 5 },
        )}
      >
        {calendarDays.flat(Infinity).map((date, index) => {
          const isOldDate = !isDateCurrentOrFuture(date as Date);
          let isDisabledDate: boolean;
          if (date) {
            isDisabledDate = disabledDays.includes(
              formatDate(date as Date, "	EEEE")
                // replace any escape characters that formatDate uses
                .replace(/[\t\n\r]/g, "") as any,
            );
          } else isDisabledDate = false;
          const isDisabledTotally =
            (disableOldDates && isOldDate) || isDisabledDate;
          return (
            <div
              key={index}
              onClick={() => {
                if (!date || isDisabledTotally) return;
                const selected = new Date(
                  formatDate(date as Date, "yyyy-MM-dd"),
                );
                setSelectedDate(selected);
                props.onChange?.(selected);
              }}
              className={cn("flex justify-center cursor-pointer", {
                "!pointer-events-none !cursor-not-allowed opacity-30":
                  isDisabledTotally,
              })}
            >
              <Typography
                variant={"p"}
                className={cn(
                  "text-center text-base  !mt-0 font-semibold rounded-full min-w-10 min-h-10 flex flex-col items-center justify-center gap-y-0 relative hover:bg-white/80 ",
                  {
                    "bg-primary-100 ":
                      props.value &&
                      formatDate(selectedDate, "yyyy-MM-dd") ===
                        formatDate(date as Date, "yyyy-MM-dd"),
                  },
                  { "!bg-transparent": !date },
                  { "!bg-transparent": isDisabledTotally },
                )}
              >
                {(date as Date | null)?.getDate()}
                <span
                  className={cn(
                    "!mt-0 font-bold text-3xl block min-h-0 !h-fit absolute -bottom-0.5",
                    {
                      hidden: !date
                        ? true
                        : !isDateCurrent(date as Date | null),
                    },
                  )}
                >
                  .
                </span>
              </Typography>
            </div>
          );
        })}
      </div>
    </section>
  );
};

type DateString = `${string}-${string}-${string}`;

type DatePickerFormControlProps = {
  required?: boolean;
  name?: string;
  className?: string;
  label: string;
  disableOldDates?: boolean;
  value?: DateString | Date;
  min?: DateString | number;
  max?: DateString | number;
  onChange(valueAsDate: Date, valueAsString: DateString): void;
};

const DatePickerFormControl: React.FC<DatePickerFormControlProps> = (props) => {
  const { required, name, label, onChange, value, className } = props;
  const { modals, modalFunctions } = useModalsBuilder({
    calendar: { open: false },
  });
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState("");
  // Handle click outside to close dropdown
  const validateValue = useCallback(() => {
    let error = "" as any;
    if (and([required, !value]))
      // set error here and don't let another validation happen
      return ((error = "This field is required"), setError(error));
    const { min, max } = props;
    if (min) {
      if (
        new Date(value as NonNullable<typeof value>).getTime() <
        new Date(min).getTime()
      )
        // set error here and don't let another validation happen
        return ((error = "Selected Date is less than min"), setError(error));
    }
    if (max) {
      if (
        new Date(value as NonNullable<typeof value>).getTime() >
        new Date(max).getTime()
      )
        // set error here and don't let another validation happen
        return ((error = "Selected Date is more than max"), setError(error));
    }
    // final return
    setError(error);
  }, [value]);
  useEffect(() => {
    validateValue();
  }, [value]);
  return (
    <>
      <div
        tabIndex={1}
        onClick={() => {
          modalFunctions.openModal("calendar", {});
        }}
        className={`${className} cursor-pointer flex flex-col gap-y-3`}
        ref={containerRef}
      >
        <label
          htmlFor={name}
          className={`font-medium w-full flex pl-3 text-sm cursor-pointer text-subtext`}
        >
          {error ? <span className={"text-red-800"}>{error}</span> : label}
          <span className={cn("text-red-800", required ? "block" : "hidden")}>
            *
          </span>
        </label>
        <div className="w-fit min-w-32 px-4 pr-3 py-1 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent focus:outline-none text-[#080808] font-medium text-sm flex items-center justify-between">
          <Typography
            className={cn(
              "text-sm whitespace-nowrap overflow-hidden overflow-ellipsis ",
              { "text-gray-400": !value },
            )}
          >
            {value ? formatDate(new Date(value), "yyyy-MM-dd") : "Select Date"}
          </Typography>
          <div className="h-full flex flex-col items-center">
            <ChevronUp size={14} className="translate-y-[3px]" />
            <ChevronDown size={14} className="-translate-y-[3px]" />
          </div>
        </div>
      </div>
      <Modal
        open={modals.calendar.open}
        onClose={() => {
          validateValue();
          modalFunctions.closeModal("calendar");
        }}
        noBlur
      >
        <Modal.Body className="!border-none min-h-fit min-w-fit !px-0 !py-0">
          <DatePicker
            className="rounded-[28px] w-full bg-[#EAEBE5]/40 "
            onChange={async (date) => {
              onChange(date, formatDate(date, "yyyy-MM-dd") as DateString);
              modalFunctions.closeModal("calendar");
            }}
            disableOldDates={props.disableOldDates || false}
            value={value ? new Date(value) : undefined}
          />
        </Modal.Body>
      </Modal>
    </>
  );
};

type TimePickerProps = {
  required?: boolean;
  name?: string;
  className?: string;
  label: string;
  value?: string;
  min?: string;
  max?: string;
  onChange(value: string): void;
};

const TimePicker: React.FC<TimePickerProps> = (props) => {
  const { required, name, label, onChange, value = "", className } = props;
  const { modals, modalFunctions } = useModalsBuilder({
    selectTime: { open: false },
  });
  const [hour, minutes] = useMemo(() => {
    if (!value) return ["00", "00"];
    return value
      .trim()
      .split(":")
      .map((v) => {
        const int = String(parseInt(v) || 0);
        return int.length === 1 ? `0${int}` : int;
      });
  }, [value]);
  const [hoursSlides, minutesSlides] = [
    Array.from(Array(24).keys()).map((v) => {
      const int = String(v);
      return int.length === 1 ? `0${int}` : int;
    }),
    Array.from(Array(60).keys()).map((v) => {
      const int = String(v);
      return int.length === 1 ? `0${int}` : int;
    }),
  ];
  const timeFormik = useFormik({
    enableReinitialize: true,
    initialValues: {
      hour,
      minutes,
    },
    onSubmit: noop,
  });
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState("");
  // Handle click outside to close dropdown
  const validateValue = useCallback(() => {
    let error = "" as any;
    if (and([required, !value]))
      // set error here and don't let another validation happen
      return ((error = "This field is required"), setError(error));
    setError(error);
  }, [value]);
  useEffect(() => {
    validateValue();
  }, [value]);
  return (
    <>
      <div
        tabIndex={1}
        onClick={() => {
          modalFunctions.openModal("selectTime", {});
        }}
        className={`${className} cursor-pointer flex flex-col gap-y-3`}
        ref={containerRef}
      >
        <label
          htmlFor={name}
          className={`font-medium w-full flex pl-3 text-sm cursor-pointer text-subtext`}
        >
          {error ? <span className={"text-red-800"}>{error}</span> : label}
          <span className={cn("text-red-800", required ? "block" : "hidden")}>
            *
          </span>
        </label>
        <div className="w-fit min-w-32 px-4 pr-3 py-1 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent focus:outline-none text-[#080808] font-medium text-sm flex items-center justify-between">
          <Typography
            className={cn(
              "text-sm whitespace-nowrap overflow-hidden overflow-ellipsis ",
              { "text-gray-400": !value },
            )}
          >
            {value ? `${hour}:${minutes}` : "Select Time"}
          </Typography>
          <div className="h-full flex flex-col items-center">
            <ChevronUp size={14} className="translate-y-[3px]" />
            <ChevronDown size={14} className="-translate-y-[3px]" />
          </div>
        </div>
      </div>
      <Modal
        open={modals.selectTime.open}
        onClose={() => {
          validateValue();
          modalFunctions.closeModal("selectTime");
        }}
      >
        <div className="h-screen w-screen flex flex-col items-center justify-end pb-8 border-none bg-transparent relative -z-[400] md:justify-center  ">
          <Modal.Body
            className={`min-h-fit min-w-72 h-fit w-[90vw] flex flex-col gap-y-3 items-center justify-center rounded-[32px] bg-white mt-auto !p-0 bsm:w-[320px] `}
          >
            <div className="w-full flex justify-between items-center px-6 pt-6">
              <Typography variant={"h3"} className="font-bold ">
                Select Time
              </Typography>
              <Button
                variant="icon"
                onTap={modalFunctions.returnClose("selectTime")}
                className="p-1 !rounded-full"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            <div className="px-4 w-full">
              <div className="embla ">
                <IosPickerItem
                  label="hour"
                  slides={hoursSlides}
                  selectedIndex={hoursSlides.lastIndexOf(hour)}
                  onChange={(value) => {
                    timeFormik.setFieldValue("hour", value.toString());
                  }}
                  loop
                  perspective="left"
                />
                <IosPickerItem
                  label="min"
                  slides={minutesSlides}
                  selectedIndex={minutesSlides.lastIndexOf(minutes)}
                  onChange={(value) => {
                    timeFormik.setFieldValue("minutes", value.toString());
                  }}
                  loop
                  perspective="left"
                />
              </div>
            </div>
            <div className="w-full flex justify-between gap-x-1 px-2 pb-1.5 items-center">
              <Button
                type="button"
                variant="dormant"
                className="w-full py-3 rounded-[32px] text-sm"
                onTap={() => {
                  onChange("");
                  modalFunctions.closeModal("selectTime");
                }}
              >
                Reset
              </Button>
              <Button
                onTap={async () => {
                  modalFunctions.closeModal("selectTime");
                  await sleep(300);
                  const { hour, minutes } = timeFormik.values;
                  onChange(`${hour}:${minutes}`);
                }}
                className="w-full text-sm py-3 rounded-[32px]"
                variant="full"
              >
                Done
              </Button>
            </div>
          </Modal.Body>
        </div>
      </Modal>
    </>
  );
};

const DatePicker = Object.assign(DatePickerImpl, {
  Modal: DatePickerModal,
  /**
   * @dev renders a searchable-dropdown like interface to select date
   * */
  FormControl: DatePickerFormControl,
  Time: TimePicker,
});

export default DatePicker;
