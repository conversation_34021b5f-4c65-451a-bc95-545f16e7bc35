import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Modal, { BaseModalProps } from "@/components/ui/modal";
import Toggle from "@/components/ui/toggle-2";
import { noop, pick } from "@/lib/helpers";
import EmojiPicker from "emoji-picker-react";
import { motion } from "framer-motion";
import {
  PencilIcon,
  X,
} from "lucide-react";
import ButtonsChangeModal from "./buttons-change-modal";
import LinkChangeModal from "./link-modal";
import { IWebsiteLink } from "../types";
import { useModalsBuilder } from "@/lib/modals-builder";
import { useDigitalProfileContext } from "../hooks/use-digital-profile";
import { CommandIcon, Delete02Icon, DragDropVerticalIcon, SmileIcon } from "hugeicons-react";
import { useFormik } from "formik";

type PopoverProps = BaseModalProps & {
  icon: string | null;
  onSelect: (emoji: string | null) => void;
};

const EmojiPickerModal = (props: PopoverProps) => {
  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      icon: props.icon
    },
    onSubmit: noop
  })
  return (
    <Modal {...pick(props, 'onClose', 'open')} >
      <Modal.Body
        className={`min-h-fit min-w-80 h-screen w-screen flex flex-col gap-y-6 items-center rounded-none bg-white max-[768px]:border-none md:h-fit md:w-fit md:justify-center md:items-center md:rounded-[32px] px-4 md:max-w-[480px] md:min-h-[540px] md:px-6 md:py-6`}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h3"} className="font-bold ">
            Change icon
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        {formik.values.icon && (
          <div className="w-full flex gap-x-4 items-center">
            <Typography variant={"p"} className="font-bold !mt-0 ">
              Current Icon: {formik.values.icon}
            </Typography>
            <Button
              onTap={() => {
                formik.setFieldValue('icon', null)
              }}
              className="text-sm"
            >
              Delete
            </Button>
          </div>
        )}
        <EmojiPicker
          autoFocusSearch
          open
          previewConfig={{
            showPreview: false
          }}
          width={'100%'}
          allowExpandReactions={false}
          lazyLoadEmojis={true}
          style={{
            border: 'none'
          }}
          onEmojiClick={(emoji) => {
            formik.setFieldValue('icon', emoji.emoji)
          }}
        />
        <div className="w-full mt-auto">
          <Button
            onTap={() => {
              props.onSelect(formik.values.icon);
            }}
            className="w-full py-3 !rounded-full font-medium "
          >
            Save
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
};

type Props = {
  link: IWebsiteLink;
  key: number;
};

/**
 * @dev this can update and delete itself using DigitalProfileContext 🪄
 */
const LinkCard = ({ link }: Props) => {
  const { modals, modalFunctions } = useModalsBuilder({
    buttonType: { open: false },
    linkChange: { open: false },
    icon: { open: false }
  });
  const { linkSettings: { update, ...rest } } = useDigitalProfileContext()
  return (
    <motion.div
      initial={{ y: -20 }}
      animate={{ y: 0 }}
      className="bg-subtle-gray w-full rounded-3xl shadow-sm pl-2 pr-4 py-5 mb-3 relative group flex gap-x-3 cursor-pointer"
    >
      <button className="p-2 hover:bg-gray-100 rounded-lg flex items-center">
        <DragDropVerticalIcon size={25} strokeWidth={2.7} />
      </button>
      <div className="flex-grow flex flex-col gap-y-4">
        <div className="flex items-start justify-between ">
          <div
            className="items-center space-y-1 flex-1"
            onClick={() => modalFunctions.openModal('linkChange', {})}
          >
            <div className="flex items-center space-x-2">
              <h2 className="font-medium  line-clamp-1 max-w-32 hover:underline hover:font-bold min-[480px]:max-w-48">
                {link.title}
              </h2>
              <Button variant="icon" className="!p-0 bg-transparent">
                <PencilIcon className="size-4 text-paragraph" />
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <p className="font-medium text-sm text-subtext line-clamp-1 max-w-32 hover:underline hover:font-bold min-[480px]:max-w-48 ">
                {link.url}
              </p>
              <Button variant="icon" className="!p-0 bg-transparent">
                <PencilIcon className="size-4 text-paragraph" />
              </Button>
            </div>
          </div>

          <div className="flex items-start space-x-2">
            <Toggle
              className=""
              checked={link.isActive}
              onChange={(checked) => {
                update(
                  link._id,
                  {
                    ...link,
                    isActive: checked
                  }
                )
              }}
            />
          </div>
        </div>
        <div className="flex w-full items-center justify-end gap-0 mt-3 border-t pt-2">
          <Button
            variant="icon"
            className="bg-transparent hover:bg-gray-100 rounded-lg"
            onTap={() => modalFunctions.openModal('buttonType', {})}
          >
            <CommandIcon size={20} className="text-gray-500 " />
          </Button>
          <Button
            onTap={() => modalFunctions.openModal('icon', {})}
            variant="icon"
            className="bg-transparent hover:bg-gray-100 rounded-lg"
          >
            <SmileIcon size={20} className="text-gray-500 " />
          </Button>
          <Button
            variant="icon"
            className="bg-transparent hover:bg-gray-100 rounded-lg"
            onTap={() => rest.delete(link._id)}
          >
            <Delete02Icon size={20} className="text-gray-500 " />
          </Button>
        </div>
      </div>
      <LinkChangeModal
        open={modals.linkChange.open}
        onClose={modalFunctions.returnClose('linkChange')}
        link={pick(link, "title", "url")}
        onSave={(data) => {
          modalFunctions.closeModal('linkChange')
          update(
            link._id,
            {
              ...link,
              ...data
            }
          )
        }}
      />
      <ButtonsChangeModal
        open={modals.buttonType.open}
        onClose={modalFunctions.returnClose('buttonType')}
        onSave={(buttonStyle) => {
          modalFunctions.closeModal('buttonType')
          update(
            link._id,
            {
              ...link,
              buttonStyle
            }
          )
        }}
      />
      <EmojiPickerModal
        icon={link.icon}
        open={modals.icon.open}
        onClose={modalFunctions.returnClose('icon')}
        onSelect={(emoji) => {
          modalFunctions.closeModal('icon')
          update(
            link._id,
            {
              ...link,
              icon: emoji
            }
          )
        }}
      />
    </motion.div>
  );
};

export default LinkCard;
