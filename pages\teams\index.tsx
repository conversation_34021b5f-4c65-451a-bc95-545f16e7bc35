import { But<PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Menu from "@/components/ui/menu";
import CircularLoader from "@/components/ui/circular-loader";
import Dialog from "@/components/ui/dialog";
import { useNavigate } from "react-router-dom";
import { useToast } from "~/contexts/hooks/toast";
import { useModalsBuilder } from "@/lib/modals-builder";
import PendingOverlay from "@/components/ui/pending-overlay";
import {
  MoreVerticalIcon,
  PlusSignIcon,
  UserGroupIcon,
  UserAdd01Icon,
  Calendar02Icon,
  ChartBreakoutSquareIcon,
} from "hugeicons-react";
import { useTeam } from "./hooks/use-team";
import CreateTeam from "./create";
import EditTeam from "./edit";
import TeamCard from "./components/team-card";
import StaffList from "./staff";
import Tabs from "@/components/ui/tabs";
import { useMemo } from "react";
import { entries } from "@/lib/helpers";
import CreateStaff from "./staff/create";
import EditStaff from "./staff/[id]/edit";
import TeamPerformance from "./performance";
import TimeOff from "./time-off";
import CreateTimeOff from "./time-off/create";
import EditTimeOff from "./time-off/edit";
import SchedulingRules from "./scheduling-rules";
import CreateSchedulingRules from "./scheduling-rules/create";
import EditSchedulingRules from "./scheduling-rules/edit";

/**
 * @dev there should be a tab for all, inactive and active
 * */
const TeamsImpl = () => {
  const navigate = useNavigate();
  const showToast = useToast();
  const { teamsQuery, deleteTeamMutation } = useTeam();
  const teams = teamsQuery.data?.teams
  const limits = teamsQuery.data?.limits

  const { modals, modalFunctions } = useModalsBuilder({
    deleteTeam: {
      open: false,
      props: null as {
        action: {
          title?: string;
          onConfirm: () => void;
        };
        title: string;
        description?: string;
      } | null,
    },
  });

  const menuFunctions = {
    createTeam: () => {
      if (limits && limits.currentCount >= limits.maxAllowed) {

        showToast("info", "You have reached the maximum number of teams");

        return;

      }
      navigate("/teams/create");
    },
    viewStaff: () => {
      navigate("/teams/staff");
    },
    viewRules: () => {
      navigate("/teams/rules");
    },
  };

  const tabsMemo = useMemo(() => {
    if (!teams)
      return {
        all: [],
        inactive: [],
        active: [],
      };
    else
      return {
        all: teams,
        active: teams.filter(
          (t) => t.isActive
        ),
        inactive: teams.filter(
          (t) => !t.isActive
        ),
      };
  }, [teams]);

  if (teamsQuery.isLoading) {
    return (
      <div className="flex w-full justify-center py-10">
        <CircularLoader />
      </div>
    );
  }

  return (
    <section className="w-full px-2 pb-20 flex flex-col gap-y-5">
      <div className="flex items-center justify-between">
        <Typography variant="h1" className="font-bold font-Bricolage_Grotesque">
          Teams
        </Typography>
        <div className="flex items-center gap-x-4">
          {limits && (
            <div className="flex items-center gap-x-2 text-sm text-gray-600">
              <span className="font-medium">
                {limits.currentCount}/{limits.maxAllowed} teams
              </span>
              <span className="text-xs bg-gray-100 px-2 py-1 rounded-full">
                {limits.plan}
              </span>
            </div>
          )}
          <Menu transformOrigin="top-right">
            <Menu.Trigger className="cursor-pointer">
              <MoreVerticalIcon size={28} strokeWidth={3} />
            </Menu.Trigger>
            <Menu.Content className="h-fit focus:outline-none min-w-fit !min-h-0 right-0 whitespace-nowrap">
              <Menu.Item
                className="flex items-center gap-x-4 pr-5"
                onClick={menuFunctions.createTeam}
              >
                Create Team <UserAdd01Icon width={20} height={20} />
              </Menu.Item>
              <Menu.Item
                className="flex items-center gap-x-4 pr-5"
                onClick={menuFunctions.viewStaff}
              >
                View All Staff <UserGroupIcon width={20} height={20} />
              </Menu.Item>
            </Menu.Content>
          </Menu>
        </div>
      </div>

      <div className="overflow-x-auto -mr-4 pr-4 thin-scrollbar py-2 flex items-center gap-x-2">
        <Button
          className="text-sm whitespace-nowrap flex items-center gap-x-2 pl-2"
          onTap={menuFunctions.createTeam}
        >
          <PlusSignIcon size={18} /> New Team
        </Button>
        <Button
          variant="dormant"
          className="text-sm whitespace-nowrap flex items-center gap-x-2 pl-2"
          onTap={() => navigate("/teams/time-off")}
        >
          <Calendar02Icon size={18} /> Time Off
        </Button>
        <Button
          variant="dormant"
          className="text-sm whitespace-nowrap flex items-center gap-x-2 pl-2"
          onTap={() => navigate("/teams/performance")}
        >
          <ChartBreakoutSquareIcon size={18} /> Performance
        </Button>
      </div>

      {teams?.length === 0 ? (
        <div className="flex flex-col items-center justify-center text-center py-10">
          <div className="mb-6">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <UserGroupIcon className="w-8 h-8 text-gray-400" />
            </div>
          </div>

          <h3 className="text-lg font-semibold text-gray-900 ">
            No teams created yet
          </h3>

          <p className="text-gray-500 mb-6 text-sm max-w-sm">
            Create your first team to start managing your staff and scheduling.
          </p>

          <Button
            onTap={menuFunctions.createTeam}
            className="font-medium text-sm flex items-center gap-2"
          >
            <PlusSignIcon className="w-5 h-5" />
            Create Team
          </Button>
        </div>
      ) : (
        <Tabs
          onValueChange={() => {
          }}
          defaultValue="all"
          className="w-full "
        >
          <Tabs.List className="grid w-full max-w-[320px] grid-cols-3 md:flex-grow ">
            <Tabs.Trigger
              value="all"
              className=" px-0 flex items-center justify-center gap-x-2"
            >
              All
            </Tabs.Trigger>
            <Tabs.Trigger
              value="active"
              className=" px-0 flex items-center justify-center gap-x-2"
            >
              Active
            </Tabs.Trigger>
            <Tabs.Trigger
              value="inactive"
              className="px-0 flex items-center justify-center gap-x-2"
            >
              <div className="min-w-1.5 min-h-1.5 mt-0.5 rounded-full bg-red-600" />{" "}
              Inactive
            </Tabs.Trigger>
          </Tabs.List>
          {
            entries(tabsMemo)
              .map(([tabValue, teams]) =>
              (
                <Tabs.Content value={tabValue} key={tabValue} className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                  {!teams.length && (
                    <p className="text-gray-500 mb-6 text-sm font-medium w-full text-center min-[320px]:w-fit ">
                      No Teams here
                    </p>
                  )}
                  {teams.map((team) => (
                    <TeamCard
                      key={team._id}
                      team={team}
                      onEdit={(teamId) => navigate(`/teams/edit/${teamId}`)}
                      onDelete={(teamId) => {
                        modalFunctions.openModal("deleteTeam", {
                          props: {
                            title: `Delete ${team.name}?`,
                            description: "This action cannot be undone.",
                            action: {
                              title: "Delete",
                              onConfirm: () => {
                                deleteTeamMutation.mutate(teamId, {
                                  onSuccess: () => {
                                    showToast("success", "Team deleted successfully");
                                  },
                                  onError: () => {
                                    showToast("error", "Failed to delete team");
                                  },
                                });
                              },
                            },
                          },
                        });
                      }}
                    />
                  ))}
                </Tabs.Content>
              ))
          }
        </Tabs>
      )}

      {modals.deleteTeam.props && (
        <Dialog
          open={modals.deleteTeam.open}
          onClose={() => modalFunctions.closeModal("deleteTeam")}
          {...modals.deleteTeam.props}
        />
      )}

      <PendingOverlay isPending={deleteTeamMutation.isPending} />
    </section>
  );
};

const Teams = Object.assign(TeamsImpl, {
  CreateTeam: CreateTeam,
  EditTeam: EditTeam,
  Staff: StaffList,
  CreateStaff: CreateStaff,
  EditStaff: EditStaff,
  Rules: SchedulingRules,
  CreateRules: CreateSchedulingRules,
  EditRules: EditSchedulingRules,
  Performance: TeamPerformance,
  TimeOff: TimeOff,
  CreateTimeOff: CreateTimeOff,
  EditTimeOff: EditTimeOff
});

export default Teams
