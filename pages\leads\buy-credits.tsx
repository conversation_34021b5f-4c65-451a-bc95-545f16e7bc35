import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import CircularLoader from "@/components/ui/circular-loader";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import {
	capitalizeFirstWord,
	capitalizeWords,
	getTimeFromNowInMonths,
	noop,
	processPaymentMethods,
} from "@/lib/helpers";
import { loadStripe } from "@stripe/stripe-js";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { formatDate } from "date-fns";
import { useMemo, useState } from "react";
import { useToast } from "~/contexts/hooks/toast";
import {
	CreditPackage,
	GetAllCreditPackages,
	PurchaseCredits,
	PurchaseCreditsPayload,
	SavePaymentIntentId,
} from "~/services/credits.service";
import { GetPaymentMethods } from "~/services/stripe.service";
import { CreditIcon } from "./components/badges";
import useAuthStore from "../auth/components/auth-store";
import PendingOverlay from "@/components/ui/pending-overlay";
import { Typography } from "@/components/typography";
import AddCardModal from "../profile/components/add-card-modal";
import { useModalsBuilder } from "@/lib/modals-builder";
import MoreInfo from "@/components/ui/more-info";

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY);

/**
 * @todo adding payment method will req to server and redi to stripe add payment method `card` type and come back, the webhooks Ahmed will do will help
 */
const CreditsPurchase = () => {
	const showToast = useToast();
	const { refetchUser } = useAuthStore();
	const { data: creditPackages = [], isFetching } = useQuery({
		queryKey: ["credit-packages"],
		queryFn: async () => (await GetAllCreditPackages()).data.credits,
	});
	const { data: paymentMethods = [] } = useQuery({
		queryKey: ["payment-methods"],
		queryFn: async () => {
			const res = await GetPaymentMethods();
			return res.data.paymentMethods;
		},
	});
	const filteredPaymentMethods = useMemo(
		() => processPaymentMethods(paymentMethods),
		[paymentMethods],
	);
	const [paymentDetails, setPaymentDetails] = useState<{
		selectedPackage: CreditPackage | null;
		selectedPaymentMethod: PaymentMethod | null;
		promoCode: string | null;
	}>({
		selectedPackage: null,
		selectedPaymentMethod: null,
		promoCode: null,
	});
	const [customAmount, setCustomAmount] = useState("");
	const [loading, setLoading] = useState(false);
	const handlePayment = async () => {
		const stripe = await stripePromise;
		if (
			!stripe ||
			!paymentDetails.selectedPackage ||
			!paymentDetails.selectedPaymentMethod
		)
			return;
		setLoading(true);
		const payload: PurchaseCreditsPayload = {
			creditId: paymentDetails.selectedPackage._id,
			paymentId: paymentDetails.selectedPaymentMethod._id,
			promocode: paymentDetails.promoCode || "",
		};
		try {
			const res = await PurchaseCredits(payload);
			if ([200, 201].includes(res.status)) {
				const { clientSecret, stripePaymentMethodId: paymentMethodId } =
					res.data;
				if (clientSecret && paymentMethodId) {
					const { error, paymentIntent } = await stripe.confirmCardPayment(
						clientSecret,
						{
							payment_method: paymentMethodId,
						},
					);
					error &&
						showToast("error", "Payment failed", {
							description:
								error.message ||
								"An error occurred while confirming the payment",
						});
					if (paymentIntent?.status === "succeeded") {
						await SavePaymentIntentId(paymentIntent.id);
						showToast("success", "Payment completed successfully");
						// reset state
						setPaymentDetails((p) => ({ ...p, selectedPackage: null }));
						await refetchUser();
					}
				} else showToast("info", "Please select a payment method and package");
			} else
				showToast("error", "An error occurred while processing your payment");
		} catch (error) {
			console.log(error);
		} finally {
			setLoading(false);
		}
	};
	const formatPaymentMethod = (method: PaymentMethod | null) =>
		method
			? {
					value: method?._id || "",
					label: `${capitalizeFirstWord(method?.provider || "")} ************${
						method?.last4Digits
					} (expires ${formatDate(
						new Date(Number(method.expiryYear!), Number(method.expiryMonth)),
						"LL/yy",
					)})`,
				}
			: { value: "", label: "Select a payment method" };
	const addPaymentMethodOption = [
		{
			label: "Add a new payment method",
			value: "add-new-payment-method",
		},
	] as const;
	const paymentMethodOptions = [
		...(filteredPaymentMethods.map(formatPaymentMethod) || []),
		...addPaymentMethodOption,
	];
	const {
		initialState: { user },
	} = useAuthStore();
	const queryClient = useQueryClient();
	const { modals, modalFunctions } = useModalsBuilder({
		addCard: {
			open: false,
		},
	});
	// this is for the add card modal
	const [clientSecret, setClientSecret] = useState("");

	return (
		<section className="w-full max-w-3xl space-y-4 px-3 mx-auto">
			<div className="">
				<Typography
					variant={"h1"}
					className="font-bold font-Bricolage_Grotesque  "
				>
					Buy Credits
				</Typography>{" "}
			</div>
			<section className="space-y-6">
				<div>
					<h3 className="text-sm font-medium mb-2">Your available Credits</h3>
					<div className="flex items-center gap-1.5">
						<CreditIcon width={32} height={32} />
						<p className="text-2xl font-medium">{user?.totalCredit}</p>
					</div>
				</div>

				<div>
					<h3 className="text-sm font-medium mb-4">Select a bundle deal</h3>
					{isFetching ? (
						<div className="flex items-center justify-center py-10 gap-2">
							<CircularLoader />
						</div>
					) : (
						<div className="flex gap-4 mb-6  ">
							{creditPackages.map((pkg) => (
								<Button
									key={pkg._id}
									className={`border rounded-3xl max-w-[160px] bg-transparent text-paragraph p-4 cursor-pointer transition-all text-start ${
										paymentDetails.selectedPackage?._id === pkg._id
											? "border-primary-500 bg-primary-500/10 "
											: "hover:border-primary-500/40"
									}`}
									onTap={() => {
										setPaymentDetails((p) => ({
											...p,
											selectedPackage: pkg,
										}));
										setCustomAmount("");
									}}
								>
									<h4 className="font-medium">{capitalizeWords(pkg.name)}</h4>
									<div className="text-2xl font-bold my-2">
										{pkg.creditAmount} credits
									</div>
									<div className="text-sm text-gray-600 mb-2">
										${pkg.amount.toFixed(2)}
									</div>
								</Button>
							))}
						</div>
					)}
				</div>
				<div>
					<MoreInfo.Static
						className="text-sm text-gray-600"
						content={`These Credits will expire on ${formatDate(getTimeFromNowInMonths(1), "MMMM d, yyyy")}`}
					/>
				</div>
				<div className="hidden">
					<h3 className="text-sm font-medium mb-4 ">
						Or enter a custom amount
					</h3>
					<div className="flex items-center gap-4">
						<div className="relative flex-1">
							<input
								type="text"
								value={customAmount}
								onChange={noop}
								placeholder="Enter number of credits"
								className="w-full p-2 pr-32 border rounded-md"
							/>
							<div className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500"></div>
						</div>
					</div>
					<p className="text-sm text-gray-500 mt-2"></p>
				</div>
				<div>
					<h3 className="text-sm font-medium mb-4">Payment Method</h3>
					<div className="space-y-4">
						<SearchableDropdown
							options={paymentMethodOptions}
							value={
								formatPaymentMethod(paymentDetails.selectedPaymentMethod) || {
									value: "",
									label: "",
								}
							}
							fullWidth
							onChange={(option) => {
								if (option?.value === "add-new-payment-method") {
									setPaymentDetails((p) => ({
										...p,
										selectedPaymentMethod: null,
									}));
									return modalFunctions.openModal("addCard", {});
								}
								option?.value &&
									setPaymentDetails((p) => ({
										...p,
										selectedPaymentMethod:
											paymentMethods?.find(
												(method: any) => method._id === option.value,
											) || null,
									}));
							}}
						/>
					</div>
				</div>

				<div>
					<h3 className="text-sm font-medium mb-2">Promo code</h3>
					<div className="flex gap-2 items-end">
						<Input.Text
							label=""
							className="flex-1 "
							name="promo_code"
							onChange={({ currentTarget: { value } }) =>
								setPaymentDetails((p) => ({
									...p,
									promoCode: value,
								}))
							}
						/>
						<Button
							disabled={!paymentDetails.promoCode}
							className="px-10 !py-2.5 h-fit "
						>
							Apply
						</Button>
					</div>
				</div>

				<div className="border-t pt-4">
					<div className="flex justify-between items-center mb-4">
						<span className="font-medium">Total</span>
						<span className="text-xl font-bold">
							$
							{paymentDetails.selectedPackage
								? paymentDetails.selectedPackage.amount.toFixed(2)
								: "0.00"}
						</span>
					</div>
					<div className="flex justify-end gap-4">
						<Button variant="ghost" className="px-4 py-2 text-gray-600">
							Cancel
						</Button>
						<Button
							disabled={
								!paymentDetails.selectedPackage ||
								!paymentDetails.selectedPaymentMethod
							}
							onTap={() => {
								if (
									!paymentDetails.selectedPaymentMethod ||
									!paymentDetails.selectedPackage
								)
									return showToast(
										"info",
										"Please select a payment method and package to proceed",
									);
								handlePayment();
							}}
							className="px-4 py-2"
						>
							Buy Credits
						</Button>
					</div>
				</div>
				<PendingOverlay isPending={loading} />
				{modals.addCard.open && (
					<AddCardModal
						hasPaymentMethods={filteredPaymentMethods.length === 0}
						clientSecret={clientSecret}
						setClientSecret={setClientSecret}
						open={modals.addCard.open}
						onClose={() => {
							modalFunctions.closeModal("addCard");
						}}
						onSuccess={() => {
							queryClient.invalidateQueries({ queryKey: ["payment-methods"] });
							showToast("info", "You can select the new payment method now");
						}}
					/>
				)}
			</section>
		</section>
	);
};

export default CreditsPurchase;
