import { Button } from "@/components/buttons";
import useAuthStore from "../auth/components/auth-store";
import { Typography } from "@/components/typography";
import { Key, Lock, Phone } from "lucide-react";
import { noop } from "@/lib/helpers";

const TwoFASettings = () => {
  const {
    initialState: { user },
  } = useAuthStore();
  const twoFaSettings = [
    {
      icon: Key,
      name: "Security Keys",
      description: "No Security Keys",
      onClick: noop,
    },
    {
      icon: Phone,
      name: "SMS Number",
      description: user?.phone || "Phone number not verified",
      onClick: noop,
    },
    {
      icon: Lock,
      name: "Authenticator app",
      description: "Not Configured",
      onClick: noop,
    },
  ];
  return (
    <section className="mt-4">
      <div className="space-y-3">
        <div className="">
          {twoFaSettings.map((twoFaSetting, index) => (
            <div key={index} className="border-t-2 border-gray-500/20 py-6 flex items-start gap-x-3 md:gap-x-5 ">
              <div className="bg-gray-200 w-14 h-14 rounded-xl grid place-content-center">
                <twoFaSetting.icon />
              </div>
              <div className="-mt-1">
                <Typography variant={"p"} className="font-bold pb-1">
                  {twoFaSetting.name}
                </Typography>
                <Typography className="font-medium text-sm">
                  {twoFaSetting.description}
                </Typography>
              </div>
              <Button
                onTap={twoFaSetting.onClick}
                variant="full"
                className="ml-auto"
              >
                Add
              </Button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TwoFASettings;
