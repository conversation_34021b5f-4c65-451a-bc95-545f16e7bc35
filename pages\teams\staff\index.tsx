import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import Dialog from "@/components/ui/dialog";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useToast } from "~/contexts/hooks/toast";
import { useModalsBuilder } from "@/lib/modals-builder";
import PendingOverlay from "@/components/ui/pending-overlay";
import {
  PlusSignIcon,
  UserAdd01Icon,
  ArrowLeft02Icon,
} from "hugeicons-react";
import { useStaff } from "../hooks/use-staff";
import { useTeam } from "../hooks/use-team";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import StaffCard from "../components/staff-card";
import StaffServicesSection from "./components/staff-services-section";

const teamIdQueryParam = "teamId";

const StaffList = () => {
  const navigate = useNavigate();
  const showToast = useToast();
  const [sParams, setSParams] = useSearchParams();
  const selectedTeamId = sParams.get(teamIdQueryParam) || "";
  const { teamsQuery } = useTeam();

  const { staffQuery, deleteStaffMutation, toggleStaffActivation } =
    useStaff(selectedTeamId);
  const { modals, modalFunctions } = useModalsBuilder({
    deleteStaff: {
      open: false,
      props: null as {
        action: { title?: string; onConfirm: () => void };
        title: string;
        description?: string;
      } | null,
    },
    createStaff: { open: false },
  });
  const menuFunctions = {
    createStaff: () => {
      navigate(`/teams/staff/create?${teamIdQueryParam}=${selectedTeamId}`);
    },
    goBack: () => {
      navigate("/teams");
    },
  };
  if (teamsQuery.isLoading) {
    return (
      <div className="flex w-full justify-center py-10">
        <CircularLoader />{" "}
      </div>
    );
  }
  return (
    <section className="w-full px-2 flex flex-col gap-y-5 pb-40 md:pb-20">
      <div className="flex flex-col md:flex-row items-center justify-between">
        <div className="flex items-start md:items-center gap-x-4">
          <Button
            variant="icon"
            onTap={menuFunctions.goBack}
            className="p-1.5 mt-0.5"
          >
            <ArrowLeft02Icon width={20} height={20} />{" "}
          </Button>
          <Typography
            variant="h1"
            className="font-bold font-Bricolage_Grotesque"
          >
            Staff Management
          </Typography>
        </div>
        <div className="flex items-center w-full md:w-fit gap-4 justify-between">
          {teamsQuery.data && teamsQuery.data.teams.length > 0 && (
            <SearchableDropdown
              fullWidth
              value={selectedTeamId}
              options={teamsQuery.data.teams.map((t) => ({
                label: t.name,
                value: t._id,
              }))}
              name=""
              className="max-w-60 "
              placeholder="Select a Team"
              onChange={(option) => {
                if (!option) return;
                setSParams((searchParams) => {
                  searchParams.set(teamIdQueryParam, option.value);
                  return searchParams;
                });
              }}
            />
          )}
          <Button
            onTap={menuFunctions.createStaff}
            variant="dormant"
            className="font-medium mt-1 text-sm flex items-center gap-2 max-[480px]:px-2 "
            disabled={!selectedTeamId}
          >
            <PlusSignIcon className="w-5 h-5" />
            <span className="hidden lg:inline">Add Staff</span>
          </Button>
        </div>
      </div>
      {!selectedTeamId ? (
        <div className="text-center py-20">
          <Typography className="text-gray-500">
            {" "}
            Please select a team to view staff members
          </Typography>{" "}
        </div>
      ) : staffQuery.isLoading ? (
        <div className="flex w-full justify-center py-10">
          <CircularLoader />{" "}
        </div>
      ) : !staffQuery.data || staffQuery.data.length === 0 ? (
        <div className="text-center py-20">
          <Typography className="text-gray-500 mb-4">
            {" "}
            No staff members found for this team
          </Typography>{" "}
          <Button
            onTap={menuFunctions.createStaff}
            className="font-medium text-sm flex items-center gap-2 mx-auto"
          >
            <UserAdd01Icon className="w-5 h-5" />
            Add First Staff Member{" "}
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {" "}
          {staffQuery.data.map((staff) => (
            <StaffCard
              key={staff._id}
              staff={staff}
              onEdit={(staffId) => navigate(`/teams/staff/edit/${staffId}`)}
              onToggleActivation={(staffId, isActive) => {
                if (isActive)
                  toggleStaffActivation.activate.mutateAsync(staffId);
                else toggleStaffActivation.deactivate.mutateAsync(staffId);
              }}
              onDelete={(staffId) => {
                const staffMember = staffQuery.data?.find(
                  (s) => s._id === staffId
                );
                modalFunctions.openModal("deleteStaff", {
                  props: {
                    title: `Delete ${staffMember?.name}?`,
                    description: "This action cannot be undone.",
                    action: {
                      title: "Delete",
                      onConfirm: () => {
                        deleteStaffMutation.mutate(staffId, {
                          onSuccess: () => {
                            showToast(
                              "success",
                              "Staff member deleted successfully"
                            );
                          },
                          onError: (error: any) => {
                            showToast(
                              "error",
                              error.response.data?.error ||
                                "Failed to delete staff member"
                            );
                          },
                        });
                      },
                    },
                  },
                });
              }}
            />
          ))}
        </div>
      )}
      {selectedTeamId && (
        <StaffServicesSection
          selectedTeamId={selectedTeamId}
        />
      )}
      {modals.deleteStaff.props && (
        <Dialog
          open={modals.deleteStaff.open}
          onClose={() => modalFunctions.closeModal("deleteStaff")}
          {...modals.deleteStaff.props}
        />
      )}
      <PendingOverlay isPending={deleteStaffMutation.isPending} />
    </section>
  );
};

export default StaffList;
