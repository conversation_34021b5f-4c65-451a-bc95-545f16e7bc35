export type PTOType = 'vacation' | 'sick' | 'personal' | 'meeting' | 'training' | 'other';

export type RecurringFrequency = 'daily' | 'weekly' | 'monthly';

export interface RecurringPattern {
  frequency: RecurringFrequency;
  interval: number;
  daysOfWeek?: number[]; // 0=Sunday, 1=Monday, etc. (required for weekly)
  endDate?: string; // ISO date string
}

export interface PTORequest {
  _id?: string;
  staffId: string;
  userId: string; // Business owner ID
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  startTime?: string; // Format: "HH:MM" (24-hour)
  endTime?: string; // Format: "HH:MM" (24-hour)
  type: PTOType;
  description: string;
  isApproved: boolean;
  approvedBy?: string;
  approvedAt?: string; // ISO date string
  isRecurring: boolean;
  recurringPattern?: RecurringPattern;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreatePTORequest {
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
  type: PTOType;
  description: string;
  isRecurring?: boolean;
  recurringPattern?: RecurringPattern;
}

export interface UpdatePTORequest extends Partial<CreatePTORequest> {}

export interface ApprovePTORequest {
  isApproved: boolean;
  rejectionReason?: string; // Required if isApproved is false
}

export interface PTOResponse {
  success: boolean;
  message: string;
  pto?: PTORequest;
  ptos?: PTORequest[];
}

export interface PTOValidationError {
  success: false;
  message: string;
  errors?: {
    field: string;
    message: string;
  }[];
}

export type StaffId = string;
