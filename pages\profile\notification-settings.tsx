import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import Toggle from "@/components/ui/toggle-2";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { useToast } from "~/contexts/hooks/toast";
import { UpdateNotificationSettings } from "~/services/settings.service";

type KeyofSettings = "email" | "push" | "sms";

/**
 * @dev make the service work to fetch the settings
 */
const NotificationSettings = () => {
  const [settings, setSettings] = useState({
    mentions: {
      email: true,
      push: true,
      sms: false,
    },
    comments: {
      email: false,
      push: true,
      sms: false,
    },
    follows: {
      email: true,
      push: true,
      sms: false,
    },
    newLogin: {
      email: true,
      push: true,
      sms: false,
    },
  });
  const [hasChanges, setHasChanges] = useState(false);
  const handleSettingsChange = (
    category: keyof typeof settings,
    key: "email" | "push" | "sms",
    value: boolean
  ) => {
    setSettings((prev) => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value,
      },
    }));
    if (!hasChanges) setHasChanges(true);
  };
  const queryclient = useQueryClient();
  const showToast = useToast();
  const updateNotificationMutation = useMutation({
    mutationFn: UpdateNotificationSettings,
    onSuccess: () => {
      queryclient.invalidateQueries({ queryKey: ["userDetails"] });
      showToast("success", "Notification settings updated successfully");
    },
    onError: (error) => {
      showToast("error", "Failed to update notification settings");
      console.error("Error updating notification settings:", error);
    },
  });
  const saveChanges = () => {
    setHasChanges(false);
    updateNotificationMutation.mutate(settings);
  };
  const settingsNameMap = {
    mentions: "Mentions",
    comments: "Comments",
    follows: "Follows",
    newLogin: "New Logins",
  } as const;

  return (
    <section className="mt-4 pb-4">
      <div className="pb-3 ">
        <Typography className="text-gray-500 pb-5 rounded-xl ">
          Choose how you receive notifications. These notification settings
          apply to the things you're watching.
        </Typography>
      </div>
      <div className="w-full ">
        <div className="max-w-full grid grid-cols-[1fr_.3fr_.3fr_.3fr] gap-x-5 ">
          <Typography className="mb-0">Activity</Typography>
          <Typography className="mb-0">Email</Typography>
          <Typography className="mb-0">Push</Typography>
          <Typography className="mb-0">SMS</Typography>
        </div>
        <div className="pt-5">
          {Object.entries(settingsNameMap).map(
            ([category, categoryName], index) => (
              <div
                key={index}
                className="max-w-full py-3 grid grid-cols-[1fr_.3fr_.3fr_.3fr] gap-x-5 "
              >
                <Typography className="text-sm">{categoryName}</Typography>
                {Object.entries(
                  settings[category as keyof typeof settingsNameMap]
                ).map(([key, checked], index) => (
                  <div key={index} className="flex items-center !px-0">
                    <Toggle
                      checked={checked}
                      onChange={() =>
                        handleSettingsChange(
                          category as keyof typeof settingsNameMap,
                          key as KeyofSettings,
                          !checked
                        )
                      }
                    />
                  </div>
                ))}
              </div>
            )
          )}
        </div>
      </div>
      <div className="w-full flex justify-end pt-8">
        <Button
          className="mt-4 px-7 ml-auto "
          onTap={saveChanges}
          type="button"
          disabled={!hasChanges}
        >
          {updateNotificationMutation.isPending ? (
            <CircularLoader color="white" />
          ) : (
            "Save"
          )}
        </Button>
      </div>
    </section>
  );
};

export default NotificationSettings;
