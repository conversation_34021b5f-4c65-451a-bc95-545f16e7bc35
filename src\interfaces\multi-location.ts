export type LocationId = string;

export interface Location {
  _id: LocationId;
  userId: string;
  name: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  timezone: string;
  phone?: string;
  email?: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateLocationPayload {
  name: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  timezone: string;
  phone?: string;
  email?: string;
  description?: string;
}

export interface UpdateLocationPayload {
  name: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  timezone: string;
  phone?: string;
  email?: string;
  description?: string;
  isActive?: boolean;
}

export interface AssignStaffToLocationsPayload {
  locationIds: LocationId[];
}

export interface AssignServiceToLocationPayload {
  serviceId: string;
  locationId: LocationId;
  room?: string;
  resource?: string;
}

export interface ServiceLocation {
  _id: string;
  serviceId: string;
  locationId: LocationId;
  room?: string;
  resource?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LocationAnalytics {
  locationId: LocationId;
  location: Location;
  metrics: {
    totalBookings: number;
    totalRevenue: number;
    averageRating: number;
    staffCount: number;
    serviceCount: number;
  };
  period: {
    startDate: string;
    endDate: string;
  };
}

export interface ProximitySearchParams {
  latitude: number;
  longitude: number;
  radius: number; // in kilometers
  limit?: number;
}

export type StaffId = string;
