import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";
import TrackAnalyticsButton from "~/components/track-analytics-button";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useFormik } from "formik";
import React, { useState } from "react";
import * as Yup from "yup"; // For form validation
import { useToast } from "~/contexts/hooks/toast";
import { CreateInternalBooking } from "~/services/booking.service";
import { GetServices } from "~/services/services.service";
import { calculateEndTime } from "./helpers";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import { Input } from "@/components/inputs";
import { Button } from "@/components/buttons";
import { X } from "lucide-react";
import PendingOverlay from "@/components/ui/pending-overlay";
import { isUnwantedInArray } from "@/lib/helpers";
import useAuthStore from "@/pages/auth/components/auth-store";
interface BookAppointmentModalProps {
  open: boolean;
  onClose: () => void;
}

export const BookAppointmentModal: React.FC<BookAppointmentModalProps> = ({
  open,
  onClose,
}) => {
  const [duration, setDuration] = useState<number | null>(null);
  const showToast = useToast();
  const queryClient = useQueryClient();
  const BookingMutation = useMutation({
    mutationFn: CreateInternalBooking,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["bookings"] });
      showToast("success", "Booking Successful");
      formik.resetForm();
      onClose();
    },
    onError: (error: ApiError) => {
      showToast("error", error?.response?.data?.error || error?.message);
    },
  });
  const { initialState: { user } } = useAuthStore()
  const formik = useFormik({
    initialValues: {
      date: "",
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      service: "",
      notes: "",
      startTime: "",
      endTime: "",
    },
    validationSchema: Yup.object({
      firstName: Yup.string().required("First Name is required"),
      startTime: Yup.string().required("Start time is required"),
      service: Yup.string().required("Appointment service is required"),
      lastName: Yup.string().required("Last Name is required"),
      email: Yup.string()
        .email("Invalid email address")
        .required("Email is required"),
      phone: Yup.string().required("Phone number is required"),
    }),
    onSubmit: (values) => {
      const payload = {
        serviceId: values.service,
        date: values.date,
        timeSlot: `${values.startTime}-${values.endTime}`,
        notes: values.notes,
        userId: user?._id as any,
        serviceName: '',
        price: 0,
        customerInfo: {
          lastName: values.lastName,
          firstName: values.firstName,
          email: values.email,
          phone: values.phone,
        },
      };
      BookingMutation.mutateAsync(payload);
    },
  });
  const { data: services = [], error } = useQuery({
    queryKey: ["services"],
    queryFn: async () => (await GetServices()).data?.Services,
  });

  if (error) {
    const axiosError = error as ApiError;
    showToast(
      "error",
      axiosError.response?.data?.error || "An unexpected error occurred"
    );
  }
  const handleStartTimeChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { value } = event.target;
    if (value !== null && duration) {
      formik.setFieldValue("startTime", value);
      const endTime = calculateEndTime(value, duration);
      formik.setFieldValue("endTime", endTime);
    } else {
      console.warn("Value or duration is missing");
    }
  };

  return (
    <Modal open={open} onClose={onClose}>
      <PendingOverlay isPending={BookingMutation.isPending} />
      <Modal.Body className=" flex flex-col justify-center md:w-[700px] outline-none overflow-auto rounded-[32px] space-y-5 ">
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Book Appointment
          </Typography>
          <Button
            variant="icon"
            onTap={onClose}
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="space-y-4 ">
          <div className="flex flex-col gap-y-2 mr-9 ">
            <Typography className=" text-paragraph text-sm">Service</Typography>
            <SearchableDropdown
              options={services.map((service) => ({
                label: service.name,
                value: service._id,
              }))}
              fullWidth
              value={(() => {
                const service = services?.find(
                  (service) => service?._id === formik.values?.service
                );
                return {
                  value: service?._id || "",
                  label: service?.name || "",
                };
              })()}
              onChange={(newValue) => {
                const service = services?.find(
                  (service) => service?._id === newValue?.value
                );
                if (service?.duration) {
                  setDuration(service?.duration);
                }
                formik.setFieldValue("service", newValue?.value || ""); // Updated to 'service'
              }}
            />
          </div>
          <div className="flex gap-x-3">
            <div className="flex flex-col gap-y-2 mr-9 ">
              <Typography className=" text-paragraph text-sm">Date</Typography>
              <input
                name="date"
                type="date"
                value={formik.values.date}
                onChange={formik.handleChange}
                className="w-fit flex items-center justify-center px-1 py-1 rounded-md bg-primary-300/10 text-primary-500 focus:outline-none  "
              />
            </div>
            <div className="flex flex-col gap-y-2  ">
              <Typography className=" text-paragraph text-sm">
                Start time
              </Typography>
              <input
                type="time"
                name="startTime"
                value={formik.values.startTime}
                onChange={handleStartTimeChange}
                className="w-fit flex items-center justify-center px-1 py-1 rounded-md bg-primary-300/10 text-primary-500 focus:outline-none  "
              />
            </div>
            <div className="flex flex-col gap-y-2  ">
              <Typography className=" text-paragraph text-sm">
                End time
              </Typography>
              <input
                type="time"
                name="endTime"
                value={formik.values.endTime}
                disabled // Disable the end time field
                className="w-fit flex items-center justify-center px-1 py-1 rounded-md bg-primary-300/10 text-primary-500 focus:outline-none disabled:opacity-60 disabled:cursor-not-allowed  "
              />
            </div>
          </div>
          <div className="flex space-x-1">
            <Input.Text
              label="First Name"
              name="firstName"
              value={formik.values.firstName}
              onChange={formik.handleChange}
              className="mb-4"
            />
            <Input.Text
              label="Last Name"
              name="lastName"
              value={formik.values.lastName}
              onChange={formik.handleChange}
              className="mb-4"
            />
          </div>
          <div className="flex space-x-1">
            <Input.Email
              label="Email"
              name="email"
              value={formik.values.email}
              onChange={formik.handleChange}
              className="mb-4"
            />
            <Input.Phone
              label="Phone"
              name="phone"
              value={formik.values.phone}
              onChange={(value, e) => {
                formik.setFieldValue(e.currentTarget.name, value);
              }}
              className="mb-4"
            />
          </div>
          <div className="flex space-x-1">
            <Input.TextArea
              label="Notes"
              name="notes"
              value={formik.values.notes}
              onChange={formik.handleChange}
              className="mb-4"
            />
          </div>
          <TrackAnalyticsButton
            className="w-full"
            disabled={
              BookingMutation.isPending ||
              isUnwantedInArray(Object.values(formik.values), [
                undefined,
                null,
                "",
              ])
            }
            type="button"
            onTap={() => {
              formik.handleSubmit();
            }}
          >
            Book Appointment
          </TrackAnalyticsButton>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default BookAppointmentModal;
