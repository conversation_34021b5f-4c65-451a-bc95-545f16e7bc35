import CircularLoader from "@/components/ui/circular-loader";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe, Stripe } from "@stripe/stripe-js";
import { useEffect, useState } from "react";
import { usePayment } from "./payment-context";
import StripeCheckoutForm from "./stripe-checkout-form";

/**
 * @dev this reads from PaymentContext
 */
const StripeCheckout = () => {
  const [stripe, setStripe] = useState<Stripe | null>(null);
  useEffect(() => {
    loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY).then((val) =>
      setStripe(val)
    );
  }, []);
  const { clientSecret } = usePayment();

  return (
    <section className="w-full h-full ">
      <div className="w-full h-full flex items-center justify-center">
        {clientSecret ? (
          <Elements stripe={stripe} options={{ clientSecret }}>
            <StripeCheckoutForm />
          </Elements>
        ) : (
          <CircularLoader />
        )}
      </div>
    </section>
  );
};

export default StripeCheckout;
