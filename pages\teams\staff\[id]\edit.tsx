import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { <PERSON><PERSON>, SaveButton } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import PendingOverlay from "@/components/ui/pending-overlay";
import CircularLoader from "@/components/ui/circular-loader";
import { ArrowLeft02Icon } from "hugeicons-react";
import { useStaff } from "../../hooks/use-staff";
import { useToast } from "~/contexts/hooks/toast";
import { noop, pickByType } from "@/lib/helpers";
import { useQuery } from "@tanstack/react-query";
import * as StaffService from "@/src/services/staff.service";
import { StaffWorkingHours, UpdateStaffPayload, WorkingHours } from "@/src/interfaces/staff";

const DAYS = [
  { key: "monday", label: "Monday" },
  { key: "tuesday", label: "Tuesday" },
  { key: "wednesday", label: "Wednesday" },
  { key: "thursday", label: "Thursday" },
  { key: "friday", label: "Friday" },
  { key: "saturday", label: "Saturday" },
  { key: "sunday", label: "Sunday" },
] as const;

const defaultWorkingHours: WorkingHours = {
  start: "09:00",
  end: "17:00",
  isWorking: true,
};

const EditStaff: React.FC = () => {
  const navigate = useNavigate();
  const { staffId } = useParams<{ staffId: string }>();
  const showToast = useToast();
  const { updateStaffMutation } = useStaff();

  const staffQuery = useQuery({
    queryKey: ["staff", staffId],
    queryFn: async () => (await StaffService.GetStaffById(staffId!)).data.staff,
    enabled: !!staffId,
  });

  const staff = staffQuery.data;

  const [first_name, last_name] = staff?.name.split(" ") || ["", ""];
  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      first_name: first_name,
      last_name: last_name,
      email: staff?.email || "",
      phone: staff?.phone || "",
      role: staff?.role || "",
      color: staff?.color || "#3B82F6",
      buffer: staff?.buffer || 15,
      travelTime: staff?.travelTime || 30,
      capacity: staff?.capacity || 8,
      isActive: staff?.isActive,
      locations: staff?.locations || [],
      services: staff?.services || [],
      workingHours: DAYS.reduce((acc, day) => {
        acc[day.key] = staff?.workingHours[day.key] || defaultWorkingHours;
        return acc;
      }, {} as StaffWorkingHours),
    } as UpdateStaffPayload,
    validationSchema: Yup.object({
      first_name: Yup.string()
        .required("First name is required")
        .min(2, "First name must be at least 2 characters")
        .max(100, "First name must be less than 100 characters"),
      last_name: Yup.string()
        .required("Last name is required")
        .min(2, "Last name must be at least 2 characters")
        .max(100, "Last name must be less than 100 characters"),
      email: Yup.string()
        .email("Invalid email format")
        .max(255, "Email must be less than 255 characters"),
      phone: Yup.string()
        .max(20, "Phone must be less than 20 characters"),
      role: Yup.string()
        .required("Role is required")
        .max(100, "Role must be less than 100 characters"),
      color: Yup.string()
        .matches(/^#[0-9A-F]{6}$/i, "Invalid color format"),
      buffer: Yup.number()
        .min(0, "Buffer must be 0 or greater")
        .max(120, "Buffer must be 120 minutes or less"),
      travelTime: Yup.number()
        .min(0, "Travel time must be 0 or greater")
        .max(240, "Travel time must be 240 minutes or less"),
      capacity: Yup.number()
        .required()
        .min(1, "Capacity must be at least 1")
        .max(50, "Capacity must be 50 or less"),
      isActive: Yup.boolean(),
    }),
    validateOnMount: true,
    onSubmit: noop,
  });

  const updateStaffHandler = async () => {
    if (!staffId) return;

    formik.validateForm().then((val) => {
      const errors = Object.values(
        pickByType(val, 'string')
      );
      if (errors.length === 0) {
        const { values } = formik;
        const payloadData = {
          staffId,
          payload: values as unknown as UpdateStaffPayload,
        }
        updateStaffMutation.mutateAsync(payloadData).then(() => {
          navigate(`/teams/staff?teamId=${staff?.teamId?._id}`);
        });
      } else {
        showToast("error", "Invalid Field", {
          description: errors[0]
        });
      }
    });
  };

  if (staffQuery.isLoading) {
    return (
      <div className="flex w-full justify-center py-10">
        <CircularLoader />
      </div>
    );
  }

  if (staffQuery.isError || !staff) {
    return (
      <div className="text-center py-20">
        <Typography className="text-red-500">
          Failed to load staff member
        </Typography>
        <Button
          onTap={() => navigate("/teams/staff")}
          className="mt-4"
        >
          Go Back
        </Button>
      </div>
    );
  }

  return (
    <section className="w-full h-full max-w-2xl mx-auto flex flex-col gap-y-10 pb-32">
      <div className="w-full flex justify-between items-center">
        <div className="flex items-start gap-x-4">
          <Button
            variant="icon"
            className="p-1.5"
            onTap={() => navigate("/teams/staff")}
          >
            <ArrowLeft02Icon width={20} height={20} />
          </Button>
          <Typography
            variant={"h1"}
            className="font-bold font-Bricolage_Grotesque"
          >
            Edit Staff Member
          </Typography>
        </div>
      </div>

      <div className="w-full flex flex-col gap-y-6">
        <div className="grid grid-cols-2 gap-x-5 gap-y-4 md:grid-cols-2">
          <Input.Text
            label="First Name"
            name="first_name"
            value={formik.values.first_name}
            onChange={formik.handleChange}
            required
          />

          <Input.Text
            label="Last Name"
            name="last_name"
            value={formik.values.last_name}
            onChange={formik.handleChange}
            required
          />

          <Input.Email
            label="Email Address"
            name="email"
            value={formik.values.email}
            onChange={formik.handleChange}
          />

          <Input.Phone
            label="Phone Number"
            name="phone"
            value={formik.values.phone}
            onChange={(value, e) => {
              formik.setFieldValue(e.currentTarget.name, value);
            }}
          />

          <Input.Text
            label="Role/Position"
            name="role"
            value={formik.values.role}
            onChange={formik.handleChange}
            required
          />
        </div>

        <div className="w-full flex items-start justify-between">
          <div className="flex flex-col gap-y-1">
            <Typography className="text-paragraph">
              Display Color
            </Typography>
          </div>
          <Input.ColorPicker
            name="color"
            value={formik.values.color}
            onChange={formik.handleChange}
          />
        </div>

        {/* Schedule Settings */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input.Numeric
            label="Buffer Time (minutes)"
            name="buffer"
            value={formik.values.buffer}
            onChange={formik.handleChange}
          />

          <Input.Numeric
            label="Travel Time (minutes)"
            name="travelTime"
            value={formik.values.travelTime}
            onChange={formik.handleChange}
          />

          <Input.Numeric
            label="Daily Capacity"
            name="capacity"
            value={formik.values.capacity}
            onChange={formik.handleChange}
          />
        </div>

        {/* Submit Button */}
        <SaveButton
          title="Update Staff Member"
          loading={updateStaffMutation.isPending}
          disabled={updateStaffMutation.isPending || !formik.isValid}
          onTap={updateStaffHandler}
        />
      </div>

      <PendingOverlay isPending={updateStaffMutation.isPending} />
    </section>
  );
};

export default EditStaff
