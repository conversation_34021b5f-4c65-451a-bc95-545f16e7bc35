import React from "react";
import { Typography } from "@/components/typography";
import Menu from "@/components/ui/menu";
import Toggle from "@/components/ui/toggle-2";
import { cn } from "@/lib/utils";
import { Location } from "@/src/interfaces/multi-location";
import {
  Edit02Icon,
  Delete02Icon,
  MoreVerticalIcon,
  Location01Icon,
  SmartPhone01Icon,
  Mail01Icon,
  EyeIcon,
} from "hugeicons-react";

interface LocationCardProps {
  location: Location;
  onEdit: () => void;
  onDelete: () => void;
  onToggleActivation: (isActive: boolean) => void;
  onViewDetails: () => void;
}

const LocationCard: React.FC<LocationCardProps> = ({
  location,
  onEdit,
  onDelete,
  onToggleActivation,
  onViewDetails,
}) => {
  return (
    <div
      className={cn(
        "bg-white rounded-lg p-6 shadow-sm border transition-all duration-200 hover:shadow-md",
        {
          "border-green-200 bg-green-50": location.isActive,
          "border-gray-200 bg-gray-50": !location.isActive,
        },
      )}
    >
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <Typography variant="h4" className="font-semibold mb-1">
            {location.name}
          </Typography>
          <div className="flex items-center gap-1 text-gray-600">
            <Location01Icon className="w-4 h-4" />
            <Typography variant="p" className="text-sm">
              {location.address.city}, {location.address.state}
            </Typography>
          </div>
        </div>

        <Menu transformOrigin="top-right">
          <Menu.Trigger className="cursor-pointer">
            <MoreVerticalIcon size={28} strokeWidth={3} />
          </Menu.Trigger>
          <Menu.Content className="h-fit focus:outline-none min-w-fit !min-h-0 right-0 whitespace-nowrap">
            <Menu.Item
              className="flex items-center gap-x-4 pr-5"
              onClick={onViewDetails}
            >
              View Details
              <EyeIcon width={20} height={20} />
            </Menu.Item>
            <Menu.Item
              className="flex items-center gap-x-4 pr-5"
              onClick={onEdit}
            >
              Edit
              <Edit02Icon width={20} height={20} />
            </Menu.Item>
            <Menu.Item
              className="flex items-center gap-x-4 pr-5 text-red-600"
              onClick={onDelete}
            >
              Delete
              <Delete02Icon width={20} height={20} />
            </Menu.Item>
          </Menu.Content>
        </Menu>
      </div>

      <div className="space-y-2 mb-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Location01Icon className="w-4 h-4" />
          <Typography variant={"span"}>{location.address.street}</Typography>
        </div>

        {location.phone && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <SmartPhone01Icon className="w-4 h-4" />
            <Typography variant={"span"}>{location.phone}</Typography>
          </div>
        )}

        {location.email && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Mail01Icon className="w-4 h-4" />
            <Typography variant={"span"}>{location.email}</Typography>
          </div>
        )}
      </div>

      {location.description && (
        <Typography
          variant="p"
          className="text-sm text-gray-600 mb-4 line-clamp-2"
        >
          {location.description}
        </Typography>
      )}

      <div className="flex items-center justify-between pt-4 border-t">
        <div className="flex items-center gap-2">
          <Typography variant="p" className="text-sm font-medium">
            Active
          </Typography>
          <Toggle checked={location.isActive} onChange={onToggleActivation} />
        </div>

        <Typography variant="p" className="text-xs text-gray-500">
          {location.timezone}
        </Typography>
      </div>
    </div>
  );
};

export default LocationCard;
