import { toReversed } from "@/lib/helpers";
import { useState } from "react";

// type declarations
export type WeekDays = [Date, Date, Date, Date, Date, Date, Date];
/**
 * each element represents start and end of a week
 */
export type Weeks = [
  [Date, Date],
  [Date, Date],
  [Date, Date],
  [Date, Date],
  [Date, Date],
  [Date, Date],
  [Date, Date]
];

/**
 * each element represents start and end of a month
 */
export type Months = [
  [Date, Date],
  [Date, Date],
  [Date, Date],
  [Date, Date],
  [Date, Date],
  [Date, Date],
  [Date, Date]
];

/**
 * Gets all weeks within a given month, with the last week potentially having fewer than 7 days
 * @param monthRange A tuple [startDate, endDate] representing first and last day of the month
 * @returns An array of weeks, where each week is an array of Date objects (last week may have < 7 days)
 */
export function getWeekRangesInMonth(monthRange: Months[number]): Date[][] {
  const [monthStart, monthEnd] = monthRange;
  
  // Create a new date object for the first day of the month
  const firstDay = new Date(monthStart);
  
  // Array to store all weeks in the month
  const weeks: Date[][] = [];
  
  // Current date we're processing
  let currentDate = new Date(firstDay);
  
  // Keep generating weeks until we've covered the entire month
  while (currentDate <= monthEnd) {
    // Create a week array starting from the current date
    const week: Date[] = [];
    
    // Add up to 7 consecutive days to the week, as long as we're still in the month
    for (let i = 0; i < 7 && currentDate <= monthEnd; i++) {
      // Add the current date to the week
      week.push(new Date(currentDate));
      
      // Move to the next day
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    // Add the completed week to our weeks array
    weeks.push(week);
  }
  
  return weeks;
}


/**
 * Gets the week range [start, end] for a given date
 * @param date The date to get the week range for
 * @returns A tuple containing the start date (Monday) and end date (Sunday) of the week
 */
function getWeekRange(date: Date): [Date, Date] {
  // Create a new date object to avoid modifying the original
  const current = new Date(date);

  // Calculate the Monday of the current week
  // If Sunday (0), go back 6 days to previous Monday
  // Otherwise, go back to the start of the week (Monday)
  const dayOfWeek = current.getDay();
  const diff = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;

  // Set date to Monday
  current.setDate(current.getDate() + diff);

  // Create start date (Monday)
  const start = new Date(current);

  // Create end date (Sunday) by adding 6 days to Monday
  const end = new Date(current);
  end.setDate(end.getDate() + 6);

  return [start, end];
}

/**
 * Gets the month range [start, end] for a given date
 * @param date The date to get the month range for
 * @returns A tuple containing the first day and last day of the month containing the given date
 */
export function getMonthRange(date: Date): [Date, Date] {
  // Create a new date object to avoid modifying the original
  const current = new Date(date);

  // Set to the first day of the current month
  const start = new Date(current.getFullYear(), current.getMonth(), 1);

  // Set to the last day of the current month
  // By setting the day to 0 of the next month, we get the last day of the current month
  const end = new Date(current.getFullYear(), current.getMonth() + 1, 0);

  return [start, end];
}

/**
 * @dev should be used in the event calendar component
 */
const useCalendar = () => {
  const today = new Date();
  const [selectedDate, setSelectedDate] = useState(new Date());

  // end of utility variables
  const [weekDays, setWeekDays] = useState<WeekDays>(() => {
    const current = new Date(today);
    const week: Date[] = [];
    current.setDate(
      current.getDate() - current.getDay() + (current.getDay() === 0 ? -6 : 1)
    );
    for (let i = 0; i < 7; i++) {
      week.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }
    return week as WeekDays;
  });
  /**
   * @dev goes to previous week and also changes selected date month of previous if it isn't in current month
   */
  function goToPrevWeek() {
    const week: Date[] = [];
    const current = weekDays[0];
    // keep going back from first element in weekDays array until we reach 7th iter.
    for (let i = 0; i < 7; i++) {
      current.setDate(current.getDate() - 1);
      week.push(new Date(current));
    }
    // reverse the array to get the correct order
    const correctOrderWeek = toReversed(week);
    setWeekDays(correctOrderWeek as WeekDays);
  }
  /**
   * @dev goes to next week and also changes selected date month of next if it isn't in current month
   */
  function goToNextWeek() {
    const week: Date[] = [];
    const current = weekDays[6];
    // keep moving forward form last element in weekDays array until we reach 7th iter.
    for (let i = 0; i < 7; i++) {
      current.setDate(current.getDate() + 1);
      week.push(new Date(current));
    }
    // no need to reverse the array, it's already in correct order
    setWeekDays(week as WeekDays);
  }

  const [weeks, setWeeks] = useState<Weeks>(() => {
    const current = new Date(today);
    const currentWeek = getWeekRange(current);
    const weekRanges: [Date, Date][] = [currentWeek];
    for (let i = 0; i < 6; i++) {
      // get the last week from weeks array and get its end date
      const endOfLastAddedWeek = new Date(weekRanges[weekRanges.length - 1][1]);
      const newWeek = getWeekRange(
        new Date(endOfLastAddedWeek.setDate(endOfLastAddedWeek.getDate() + 1))
      );
      weekRanges.push(newWeek);
    }
    return weekRanges as any;
  });

  /**
   * @dev advances to the prev set of week ranges
   * @returns A new array of seven week ranges
   */
  function goToPrevWeekRange() {
    const currentWeeks = weeks;
    // Get the beginning date of the first week in the current weeks array
    const firstWeekBeginningDate = new Date(currentWeeks[0][0]);

    // Create a new array to hold the last set of week ranges
    const lastWeeks: [Date, Date][] = [];

    // Start from the day before the first week's beginning date
    const startDate = new Date(firstWeekBeginningDate);
    startDate.setDate(startDate.getDate() - 1);

    // Generate 7 consecutive week ranges
    for (let i = 0; i < 7; i++) {
      const weekRange = getWeekRange(startDate);
      // Add the week range to the beginning of the array
      lastWeeks.unshift(weekRange);
      // Move to the start of the next week
      startDate.setDate(startDate.getDate() - 7);
    }

    setWeeks(lastWeeks as Weeks);
  }

  /**
   * @dev advances to the next set of week ranges
   * @returns A new array of seven week ranges
   */
  function goToNextWeekRange() {
    const currentWeeks = weeks;
    // Get the end date of the last week in the current weeks array
    const lastWeekEndDate = new Date(currentWeeks[6][1]);

    // Create a new array to hold the next set of week ranges
    const nextWeeks: [Date, Date][] = [];

    // Start from the day after the end of the last week
    const startDate = new Date(lastWeekEndDate);
    startDate.setDate(startDate.getDate() + 1);

    // Generate 7 consecutive week ranges
    for (let i = 0; i < 7; i++) {
      const weekRange = getWeekRange(startDate);
      nextWeeks.push(weekRange);

      // Move to the start of the next week
      startDate.setDate(startDate.getDate() + 7);
    }

    setWeeks(nextWeeks as Weeks);
  }

  const [months, setMonths] = useState<Months>(() => {
    const current = new Date(today);
    const currentWeek = getMonthRange(current);
    const monthRanges: [Date, Date][] = [currentWeek];
    for (let i = 0; i < 6; i++) {
      // get the last month from monthRanges array and get its end date
      const endOfLastAddedMonth = new Date(
        monthRanges[monthRanges.length - 1][1]
      );
      const newMonth = getMonthRange(
        new Date(endOfLastAddedMonth.setDate(endOfLastAddedMonth.getDate() + 1))
      );
      monthRanges.push(newMonth);
    }
    return monthRanges as any;
  });

  /**
   * @dev moves to the last set of month ranges
   * @returns A new array of seven month ranges
   */
  function goToPrevMonthRange() {
    const currentMonths = months;
    // Get the beginning date of the first month in the current months array
    const firstMonthBeginningDate = new Date(currentMonths[0][0]);

    // Create a new array to hold the prev set of month ranges
    const prevMonths: [Date, Date][] = [];

    // Start from the day after the end of the last month
    const startDate = new Date(firstMonthBeginningDate);
    startDate.setMonth(startDate.getMonth() - 1, 1);

    // Generate 7 consecutive month ranges
    for (let i = 0; i < 7; i++) {
      const monthRange = getMonthRange(startDate);
      // Add the month range to the beginning of the array
      prevMonths.unshift(monthRange);

      // Move to the start of the last month
      startDate.setMonth(startDate.getMonth() - 1);
    }

    setMonths(prevMonths as Weeks);
  }

  /**
   * @dev advances to the next set of month ranges
   * @returns A new array of seven month ranges
   */
  function goToNextMonthRange() {
    const currentMonths = months;
    // Get the end date of the last month in the current months array
    const lastMonthEndDate = new Date(currentMonths[6][1]);

    // Create a new array to hold the next set of month ranges
    const nextMonths: [Date, Date][] = [];

    // Start from the day after the end of the last month
    const startDate = new Date(lastMonthEndDate);
    startDate.setMonth(startDate.getMonth() + 1, 1);

    // Generate 7 consecutive month ranges
    for (let i = 0; i < 7; i++) {
      const monthRange = getMonthRange(startDate);
      nextMonths.push(monthRange);

      // Move to the start of the next month
      startDate.setMonth(startDate.getMonth() + 1);
    }

    setMonths(nextMonths as Weeks);
  }

  // start of utility functions
  function changeDate(date: Date) {
    setSelectedDate(date);
    const current = new Date(date);
    const week: Date[] = [];
    current.setDate(
      current.getDate() - current.getDay() + (current.getDay() === 0 ? -6 : 1)
    );
    for (let i = 0; i < 7; i++) {
      week.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }
    setWeekDays(week as WeekDays);
  }

  return {
    setSelectedDate,
    selectedDate,
    changeDate,

    /**
     * `weekDays type Array<7, Date>`
     */
    weekDays,
    /**
     * `weeksrange type Array<7, [Date, Date]>`
     */
    weeks,
    /**
     * `monthsRanges type Array<7, [Date, Date]> first element being the start of the month and second being the end of the month`
     */
    months,

    // modifiers
    goToPrev: () => {
      goToPrevWeek();
      goToPrevWeekRange();
      goToPrevMonthRange()
    },
    goToNext: () => {
      goToNextWeek();
      goToNextWeekRange();
      goToNextMonthRange();
    },
  };
};

export default useCalendar;
