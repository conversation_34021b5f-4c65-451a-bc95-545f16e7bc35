import React from "react";
import { But<PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";
import CircularLoader from "@/components/ui/circular-loader";
import PendingOverlay from "@/components/ui/pending-overlay";
import { useNavigate } from "react-router-dom";
import { PlusSignIcon } from "hugeicons-react";
import { useMultiLocation } from "./hooks/use-location";
import LocationCard from "./components/location-card";
import { useState } from "react";
import CreateLocationPage from "./create";
import EditLocationPage from "./edit";
import LocationDetailsPage from "./[id]/details";

const LocationsPage: React.FC = () => {
  const navigate = useNavigate();
  const { locationsQuery, deleteLocationMutation, toggleLocationActivation } =
    useMultiLocation();
  const [searchTerm, setSearchTerm] = useState("");

  const filteredLocations =
    locationsQuery.data?.filter(
      (location) =>
        location.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        location.address.city.toLowerCase().includes(searchTerm.toLowerCase()),
    ) || [];

  return (
    <section className="w-full h-screen overflow-y-auto p-4 space-y-6">
      <PendingOverlay isPending={deleteLocationMutation.isPending} />

      <div className="flex items-center justify-between">
        <Typography variant="h2" className="font-semibold">
          Locations Management
        </Typography>
        <Button
          variant="full"
          onTap={() => navigate("/locations/create")}
          className="flex items-center gap-2"
        >
          <PlusSignIcon className="w-4 h-4" />
          Add Location
        </Button>
      </div>

      <div className="flex items-center gap-4">
        <div className="flex-1 max-w-md">
          <Input.Text
            label=""
            name="search"
            placeholder="Search locations..."
            value={searchTerm}
            onChange={({ target: { value } }) => setSearchTerm(value)}
            className="w-full"
          />
        </div>
      </div>

      {locationsQuery.isLoading ? (
        <div className="flex justify-center py-20">
          <CircularLoader />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredLocations.map((location) => (
            <LocationCard
              key={location._id}
              location={location}
              onEdit={() => navigate(`/locations/edit/${location._id}`)}
              onDelete={() => deleteLocationMutation.mutate(location._id)}
              onToggleActivation={(isActive) => {
                if (isActive) {
                  toggleLocationActivation.activate.mutate(location._id);
                } else {
                  toggleLocationActivation.deactivate.mutate(location._id);
                }
              }}
              onViewDetails={() =>
                navigate(`/locations/${location._id}/details`)
              }
            />
          ))}
        </div>
      )}

      {!locationsQuery.isLoading && filteredLocations.length === 0 && (
        <div className="text-center py-20">
          <Typography variant="p" className="text-gray-500">
            {searchTerm
              ? "No locations found matching your search."
              : "No locations created yet."}
          </Typography>
        </div>
      )}
    </section>
  );
};

const Locations = Object.assign(LocationsPage, {
  CreateLocation: CreateLocationPage,
  EditLocation: EditLocationPage,
  LocationDetails: LocationDetailsPage,
});

export default Locations;
