import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Modal, { BaseModalProps } from "@/components/ui/modal";
import { X } from "lucide-react";
import { Input } from "@/components/inputs";
import { useFormik } from "formik";
import { noop } from "@/lib/helpers";
import { useDigitalProfileContext } from "../hooks/use-digital-profile";

type Props = BaseModalProps & {
  onSave: (props: { title: string; bio: string; displayName?: string }) => void;
};

export default function DisplayNameModal({ open, onClose, onSave }: Props) {
  const { profile } = useDigitalProfileContext()
  const formik = useFormik({
    initialValues: profile.details,
    enableReinitialize: true,
    onSubmit: noop
  })

  return (
    <Modal open={open} onClose={onClose}>
      <Modal.Body
        className={`min-h-fit min-w-80 h-fit w-fit flex flex-col gap-y-7 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Edit Title and Bio
          </Typography>
          <Button
            variant="icon"
            onTap={onClose}
            className="p-1 !rounded-full"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Form */}
        <div className="space-y-4 w-full">
          <Input.Text label="Profile Title"
            name="title"
            value={formik.values.title}
            required
            onChange={formik.handleChange}
          />
          <Input.Text label="Display Name"
            name="displayName"
            value={formik.values.displayName}
            required
            onChange={formik.handleChange}
          />
          <div className="flex w-full flex-col gap-y-2 items-end">
            <Input.TextArea label="Bio"
              name="bio"
              value={formik.values.bio}
              required
              className="w-full"
              onChange={formik.handleChange}
            />
            <span className="absolute bottom-2 right-2 text-xs text-gray-400">
              {formik.values.bio.length} / 80
            </span>
          </div>
        </div>

        {/* Footer */}
        <div className="w-full pt-0">
          <button
            onClick={() => {
              onSave(formik.values);
            }}
            className="w-full bg-primary-500 text-white py-3 rounded-full font-medium hover:bg-primary-500/40 transition-colors"
          >
            Save
          </button>
        </div>
      </Modal.Body>
    </Modal>
  );
}
