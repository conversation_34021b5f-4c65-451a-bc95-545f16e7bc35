import Components from "@/components/index";
import AuthProtectedRoute from "@/pages/auth/auth-protected";
import Onboarding from "@/pages/auth/onboarding";
import Signin from "@/pages/auth/signin";
import ExternalBooking from "@/pages/booking/external-booking";
import ConfirmEmail from "@/pages/confirm-email";
import OAuthCallback from "@/pages/home/<USER>";
import RootLayout from "@/pages/home/<USER>";
import LeadsPage from "@/pages/leads";
import CreditsPurchase from "@/pages/leads/buy-credits";
import LeadsDashboard from "@/pages/leads/dashboard";
import LeadsLayout from "@/pages/leads/layout";
import LeadDetails from "@/pages/leads/leads-details";
import NotFound from "@/pages/not-found";
import {
  matchPath,
  Navigate,
  Outlet,
  Route,
  Routes,
  useLocation,
} from "react-router-dom";
import { usePageSessionAnalytics } from "./hooks/use-page-session-analytics";
import { usePageViewAnalytics } from "./hooks/use-page-view-analytics";
import "./index.css";
import ViewLeadPurchases from "@/pages/leads/view-lead-purchases";
import Profile from "@/pages/profile";
import PaymentSuccess from "@/pages/auth/payment-success";
import PaymentPage from "@/pages/auth/payment";
import Admin from "@/pages/admin";
import Services from "@/pages/services";
import DigitalProfile from "@/pages/digital-profile";
import DigitalProfileEditor from "@/pages/digital-profile-editor";
import SchedulingAnalytics from "@/pages/scheduling-analytics";
import DashBoard from "@/pages/scheduling-dashboard";
import useAuthStore from "@/pages/auth/components/auth-store";
import ClientListLayout from "@/pages/client-list/layout";
import ClientList from "@/pages/client-list";
import InternalBooking from "@/pages/booking/internal-booking";
import ClientReEngagement from "@/pages/client-re-engagement";
import SubscriptionPlanProtectedRoute from "@/pages/auth/subscription-plan-protected";
import { useEffect } from "react";
import { objectKeys } from "@/lib/helpers";
import EmailSettingsLayout, {
  emailSettingsLinks,
} from "@/pages/email-settings";
import ViewBusinessServices from "@/pages/services/view-services";
import RequestResetPassword from "@/pages/forgot-password/RequestResetPassword";
import ResetPassword from "@/pages/forgot-password/ResetPassword";
import IntakeForm from "@/pages/services/intake-form";
import RoleProtected from "@/pages/admin/role-protected";
import CreateBlog from "@/pages/admin/blog/create-blog";
import EditBlog from "@/pages/admin/blog/edit-blog";
import Blogs from "@/pages/admin/blog";
import Teams from "@/pages/teams";
import Locations from "@/pages/locations";

function prependAppName(name: string) {
  return `${name} • Puzzle Piece Solutions`;
}

const pathNameMap = {
  "/": `Puzzle Piece Solutions`,
  "/dashboard/:username": prependAppName("Dashboard"),
  "/signin": prependAppName("Sign In"),
  "/onboarding": prependAppName("Onboarding"),
  "/confirm-email": prependAppName("Confirm Email"),
  "/oauth/callback": prependAppName("OAuth Callback"),
  "/booking/external/:username/:urlName": prependAppName("External Booking"),
  "/booking/internal/:username/:urlName": prependAppName("Internal Booking"),
  "/leads": prependAppName("Leads"),
  "/leads/dashboard": prependAppName("Dashboard • Leads"),
  "/leads/buy-credits": prependAppName("Buy Credits • Leads"),
  "/leads/:id": prependAppName("Lead Details"),
  "/leads/purchases": prependAppName("Lead Purchases"),
  "/digital-profile/:username": prependAppName("Digital Profile"),
  "/digital-profile/edit": prependAppName("Digital Profile Editor"),
  "/analytics": prependAppName("Analytics"),
  "/analytics/re-engagement": prependAppName("Client Re-engagement"),
  "/clients": prependAppName("Clients"),
  "/profile": prependAppName("Profile"),
  "/profile/basic-info": prependAppName("Basic Info • Profile"),
  "/profile/change-password": prependAppName("Change Password • Profile"),
  "/profile/billing": prependAppName("Billing • Profile"),
  "/profile/2fa": prependAppName("2FA • Profile"),
  "/profile/accounts": prependAppName("Accounts • Profile"),
  "/profile/calendar-settings": prependAppName("Calendar Settings • Profile"),
  "/profile/notifications": prependAppName("Notifications • Profile"),
  "/profile/sessions": prependAppName("Sessions • Profile"),
  "/admin": prependAppName("Admin"),
  "/admin/users": prependAppName("Users • Admin"),
  "/admin/users/logs": prependAppName("Users Logs • Admin"),
  "/admin/support-tickets": prependAppName("Support Tickets • Admin"),
  "/admin/analytics": prependAppName("Usage Analytics • Admin"),
  "/admin/subscription-plans": prependAppName("Subscription Plans • Admin"),
  "/admin/faq": prependAppName("FAQ • Admin"),
  "/blog": prependAppName("Blog"),
  "/blog/create": prependAppName("Create New Blog"),
  "/blog/edit/:uniqueKey": prependAppName("Edit Blog"),
  "/scheduling/services": prependAppName("Services"),
  "/scheduling/services/create": prependAppName("Create Service"),
  "/schedulinig/services/preview": prependAppName("Preview Services"),
  "/scheduling/services/edit": prependAppName("Edit Service"),
  "/scheduling/intake-form/:urlName": prependAppName("Intake Form"),
  "/email-settings/initial-confirmation": prependAppName(
    "Initial Confirmation",
  ),
  "/email-settings/follow-ups": prependAppName("Follow Ups"),
  "/email-settings/reminders": prependAppName("Reminders"),
  "/services/:username": prependAppName("View Services"),

  // Add Teams & Staff title mappings
  "/teams": prependAppName("Teams Overview"),
  "/teams/staff": prependAppName("Staff Management"),
  "/teams/staff/create": prependAppName("Add Staff Member"),
  "/teams/staff/:id/edit": prependAppName("Edit Staff Member"),
  "/teams/rules": prependAppName("Scheduling Rules"),
  "/teams/rules/create/:serviceId": prependAppName("Create Scheduling Rules"),
  "/teams/rules/edit/:serviceId": prependAppName("Edit Scheduling Rules"),
  "/teams/performance": prependAppName("Team Performance"),
  "/teams/time-off": prependAppName("Time Off Management"),

  // ✅ Added forgot password pages to title mapping
  "/request-reset-password": prependAppName("Request Password Reset"),
  "/reset-password/:token": prependAppName("Reset Password"),
};

function App() {
  const { trackPageView } = usePageViewAnalytics();
  const { trackPageSession } = usePageSessionAnalytics();
  trackPageView();
  trackPageSession();
  const { innerWidth } = window;
  const {
    initialState: { user },
  } = useAuthStore();
  const location = useLocation();
  useEffect(() => {
    const matchedPathTitle =
      pathNameMap[
        objectKeys(pathNameMap).find((path) => {
          return matchPath(path, location.pathname);
        }) || "/"
      ];
    document.title = matchedPathTitle;
  }, [location.pathname]);
  return (
    <Routes>
      <Route path="/showcase" element={<Components />} />
      <Route path="/onboarding" element={<Onboarding />} />
      <Route path="/payment" element={<PaymentPage />} />
      <Route path="/onboarding/success" element={<PaymentSuccess />} />
      <Route path="/signin" element={<Signin />} />
      <Route path="/confirm-email/:token" element={<ConfirmEmail />} />
      <Route path="/oauth/callback" element={<OAuthCallback />} />
      <Route
        path="/booking/external/:username/:urlName"
        element={<ExternalBooking />}
      />
      <Route path="/services/:username" element={<ViewBusinessServices />} />
      <Route path="/digital-profile/:username" element={<DigitalProfile />} />
      <Route
        path="/request-password-reset"
        element={<RequestResetPassword />}
      />
      <Route path="/reset-password/:token" element={<ResetPassword />} />

      <Route
        path="/"
        element={
          <AuthProtectedRoute>
            <RootLayout />
          </AuthProtectedRoute>
        }
      >
        {/* internal booking must be viewed with a logged in account - business */}
        <Route
          path="/booking/internal/:username/:urlName"
          element={<InternalBooking />}
        />
        <Route
          index
          element={<Navigate to={`/dashboard/${user?.username}`} replace />}
        />
        <Route path={"/dashboard/:username"} element={<DashBoard />} />
        <Route path="/scheduling/services" element={<Services />} />
        <Route
          path="/scheduling/services/create"
          element={<Services.CreateService />}
        />
        <Route
          path="/scheduling/services/edit/:urlName"
          element={<Services.EditService />}
        />
        {/* though it's a roundtrip, fetch service by url name and then fetch intake form */}
        <Route
          path="/scheduling/intake-form/:urlName"
          element={<IntakeForm />}
        />
        <Route
          path="/scheduling/services/preview"
          element={<Services.PreviewServices />}
        />
        <Route
          path="/analytics"
          element={<SubscriptionPlanProtectedRoute planName="premium" />}
        >
          <Route index element={<SchedulingAnalytics />}></Route>
          <Route path="re-engagement" element={<ClientReEngagement />} />
        </Route>
        <Route path="email-settings" element={<EmailSettingsLayout />}>
          {emailSettingsLinks.map((props, index) => (
            <Route key={index} {...props} />
          ))}
        </Route>
        <Route path="/leads" element={<LeadsLayout />}>
          <Route index element={<LeadsPage />} />
          <Route path="buy-credits" element={<CreditsPurchase />} />
          <Route path="dashboard" element={<LeadsDashboard />} />
          <Route path=":id" element={<LeadDetails />} />
          <Route path="purchases" element={<ViewLeadPurchases />} />
        </Route>
        <Route path="/profile" element={<Profile />}>
          <Route
            index
            element={
              innerWidth < 1024 ? (
                <Profile.MobileProfileLinks />
              ) : (
                <Navigate to={"/profile/basic-info"} />
              )
            }
          />
          <Route path="basic-info" element={<Profile.BasicInfoForm />} />
          <Route path="change-password" element={<Profile.ChangePassword />} />
          <Route path="billing" element={<Profile.Billing />} />
          <Route path="2fa" element={<Profile.TwoFASettings />} />
          <Route path="accounts" element={<Profile.AccountSettings />} />
          <Route
            path="calendar-settings"
            element={<Profile.CalendarSettings />}
          />
          <Route
            path="notifications"
            element={<Profile.NotificationSettings />}
          />
          <Route path="sessions" element={<Profile.SessionsSettings />} />
          <Route path="delete-account" element={<Profile.DeleteAccount />} />
        </Route>
        <Route
          path="/digital-profile/edit"
          element={<DigitalProfileEditor />}
        />
        <Route path="/clients" element={<ClientListLayout />}>
          <Route index element={<ClientList />} />
        </Route>
        {/* protect other pages from these roles */}
        <Route
          path="/blog"
          element={
            <RoleProtected roles={["admin", "blogger"]}>
              <Outlet />
            </RoleProtected>
          }
        >
          <Route index element={<Blogs />} />
          <Route path="edit/:uniqueKey" element={<EditBlog />} />
          <Route path="create" element={<CreateBlog />} />
        </Route>
        <Route
          path="/admin"
          element={
            <RoleProtected roles={["admin"]}>
              <Admin />
            </RoleProtected>
          }
        >
          <Route
            index
            element={
              innerWidth < 768 ? (
                <Admin.MobileAdminLinks />
              ) : (
                <Navigate to={"/admin/users"} />
              )
            }
          />
          <Route path="users" element={<Admin.Users />} />
          <Route path="users/logs" element={<Admin.UsersWithLogs />} />
          <Route path="users/logs/:id" element={<Admin.UserLogs />} />
          <Route path="support-tickets" element={<Admin.SupportTickets />} />
          <Route path="analytics" element={<Admin.Analytics />} />
          <Route
            path="subscription-plans"
            element={<Admin.SubscriptionPlans />}
          />
          <Route path="faq" element={<Admin.FaqPage />} />
        </Route>

        {/* Multi-Location Management */}
        <Route path="locations">
          <Route index element={<Locations />} />
          <Route path="create" element={<Locations.CreateLocation />} />
          <Route path="edit/:locationId" element={<Locations.EditLocation />} />
          <Route
            path=":locationId/details"
            element={<Locations.LocationDetails />}
          />
          {/*<Route
            path=":locationId/staff"
            element={<Locations.LocationStaff />}
          />
          <Route
            path=":locationId/services"
            element={<Locations.LocationServices />}
          />
          <Route
            path=":locationId/analytics"
            element={<Locations.LocationAnalytics />}
          />*/}
        </Route>

        {/* Teams & Staff Management Routes */}
        <Route path="/teams">
          <Route index element={<Teams />} />
          <Route path="create" element={<Teams.CreateTeam />} />
          <Route path="edit/:teamId" element={<Teams.EditTeam />} />

          {/* Staff Management */}
          <Route path="staff">
            <Route index element={<Teams.Staff />} />
            <Route path="create" element={<Teams.CreateStaff />} />
            <Route path="edit/:staffId" element={<Teams.EditStaff />} />
          </Route>

          {/* Schedule Rules */}
          <Route path="rules">
            <Route index element={<Teams.Rules />} />
            <Route path="create/:serviceId" element={<Teams.CreateRules />} />
            <Route path="edit/:serviceId" element={<Teams.EditRules />} />
          </Route>

          {/* Performance - Protected by Premium Plan */}
          <Route
            path="performance"
            element={<SubscriptionPlanProtectedRoute planName="premium" />}
          >
            <Route index element={<Teams.Performance />} />
          </Route>

          {/* Time Off Management - nested under teams */}
          <Route path="time-off">
            <Route index element={<Teams.TimeOff />} />
            <Route path="create" element={<Teams.CreateTimeOff />} />
            <Route path="edit/:ptoId" element={<Teams.EditTimeOff />} />
          </Route>
        </Route>
      </Route>
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default App;
