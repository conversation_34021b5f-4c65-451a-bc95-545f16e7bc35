import { AxiosResponse } from "axios";
import { api } from "../api.service";

export interface Product {
  _id: string;
  stripeProductId: string;
  name: string;
  description: string;
  active: boolean;
  features: string[];
}

export type ProductPayload = {
  name: string;
  description: string;
  active: boolean;
  features: string[];
};

export interface ProductPrice {
  _id: string;
  stripeProductId: string;
  productId: string;
  stripePriceId: string;
  currency: string;
  unitAmount: number;
  interval: "day" | "week" | "month" | "year";
}

export interface ProductPricePayload {
  productId: string;
  currency: string;
  amount: number;
  interval: "day" | "week" | "month" | "year";
}
export const CreateProduct = async (
  payload: ProductPayload
): Promise<AxiosResponse> => {
  try {
    const response = await api.post(`/admin/products`, payload);
    return response;
  } catch (error) {
    console.error("user-interaction-error", error);
    throw error;
  }
};

export const GetAllProducts = async (): Promise<
  AxiosResponse<{
    products: Product[];
  }>
> => {
  try {
    const response = await api.get(`/admin/products`);
    return response;
  } catch (error) {
    console.error("user-interaction-error", error);
    throw error;
  }
};

export const GetProduct = async (productId: string): Promise<AxiosResponse> => {
  try {
    const response = await api.get(`/admin/products/${productId}`);
    return response;
  } catch (error) {
    console.error("user-interaction-error", error);
    throw error;
  }
};

export const UpdateProduct = async (
  productId: string,
  payload: ProductPayload
): Promise<AxiosResponse> => {
  try {
    const response = await api.put(`/admin/products/${productId}`, payload);
    return response;
  } catch (error) {
    console.error("user-interaction-error", error);
    throw error;
  }
};

export const DeleteProduct = async (
  productId: string
): Promise<AxiosResponse> => {
  try {
    const response = await api.delete(`/admin/products/${productId}`);
    return response;
  } catch (error) {
    console.error("user-interaction-error", error);
    throw error;
  }
};

export const CreateProductPrice = async (
  payload: ProductPricePayload
): Promise<AxiosResponse> => {
  try {
    const response = await api.post(`/admin/price`, payload);
    return response;
  } catch (error) {
    console.error("user-interaction-error", error);
    throw error;
  }
};
export const GetAllProductPrices = async (): Promise<
  AxiosResponse<{
    productPrices: ProductPrice[];
  }>
> => {
  try {
    const response = await api.get(`/admin/price`);
    return response;
  } catch (error) {
    console.error("user-interaction-error", error);
    throw error;
  }
};
export const GetProductPrice = async (Id: string): Promise<AxiosResponse> => {
  try {
    const response = await api.get(`/admin/price/${Id}`);
    return response;
  } catch (error) {
    console.error("user-interaction-error", error);
    throw error;
  }
};
export const UpdateProductPrice = async (
  Id: string,
  payload: ProductPricePayload
): Promise<AxiosResponse> => {
  try {
    const response = await api.put(`/admin/price/${Id}`, payload);
    return response;
  } catch (error) {
    console.error("user-interaction-error", error);
    throw error;
  }
};
export const DeleteProductPrice = async (
  productPriceId: string
): Promise<AxiosResponse> => {
  try {
    const response = await api.delete(`/admin/price/${productPriceId}`);
    return response;
  } catch (error) {
    console.error("user-interaction-error", error);
    throw error;
  }
};
