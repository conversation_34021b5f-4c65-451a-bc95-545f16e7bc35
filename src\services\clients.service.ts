import { AxiosResponse } from "axios";
import { api } from "./api.service";

export type TClient = {
  _id: string;
  clientName: string;
  clientEmail: string;
  phoneNumber: string;
  lastAppointmentDate: Date | null;
};

export const GetAllClients = async (): Promise<AxiosResponse<TClient[]>> => {
  try {
    // wrong api endpoint, fix later
    const response = await api.get(`/business-client/`);
    return response;
  } catch (error) {
    console.error("Get all client error:", error);
    throw error;
  }
};

export const AddNewClient = async (
  payload: TClient
): Promise<AxiosResponse<{ success: boolean; message: string }>> => {
  try {
    const response = await api.post(`/business-client/`, payload);
    return response;
  } catch (error) {
    console.error("Add new client error:", error);
    throw error;
  }
};

export const DeleteClient = async (
  clientId: string
): Promise<AxiosResponse<{ success: boolean; message: string }>> => {
  try {
    const response = await api.delete(`/business-client/${clientId}`);
    return response;
  } catch (error) {
    console.error("Add new client error:", error);
    throw error;
  }
};

export const UpdateClient = async ({
  clientId,
  payload,
}: {
  clientId: string;
  payload: TClient;
}): Promise<AxiosResponse<{ success: boolean; message: string }>> => {
  try {
    const response = await api.put(`/business-client/${clientId}`, payload);
    return response;
  } catch (error) {
    console.error("Add new client error:", error);
    throw error;
  }
};
