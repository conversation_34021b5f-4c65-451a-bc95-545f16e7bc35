import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import DatePicker from "@/components/ui/calendar";
import ErrorScreen from "@/components/ui/error-screen";
import Menu from "@/components/ui/menu";
import { inlineSwitch, objectKeys, sleep } from "@/lib/helpers";
import { useModalsBuilder } from "@/lib/modals-builder";
import { cn } from "@/lib/utils";
import { Calendar, EllipsisVertical, Plus } from "@gravity-ui/icons";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft, ArrowRight } from "lucide-react";
import { useEffect, useState } from "react";
import { useToast } from "~/contexts/hooks/toast";
import {
  CalendarEvent,
  convertBookingsToCalendarEvents,
} from "~/interfaces/booking";
import { GetBookings } from "~/services/booking.service";
import useAuthStore from "../auth/components/auth-store";
import BookAppointmentModal from "../services/components/book-appoinment-modal";
import BlockTimesList from "./components/block-time-list";
import EventCalendar from "./components/event-calendar";
import EventModal from "./components/event-modal";
import useCalendar from "./use-calendar";
import { RevenueChart } from "../scheduling-analytics/_components/revenue-chart";
import { AnalyticsIntervalProvider } from "../scheduling-analytics/analytics-interval-context";
import { ReturnRateChart } from "../scheduling-analytics/_components/return-rate-chart";
import { ServiceUsageChart } from "../scheduling-analytics/_components/service-usage-chart";
import Modal from "@/components/ui/modal";
import { Link, useNavigate } from "react-router-dom";
import { GetServices } from "@/src/services/services.service";
/**
 * @todo change the business hours in event calendar
 */
export default function DashBoard() {
  const today = Object.freeze(new Date());
  const { modals, modalFunctions } = useModalsBuilder({
    dailyCalendar: {
      open: false,
    },
    blockTimes: {
      open: false,
    },
    bookAppointment: {
      open: false,
    },
    event: {
      open: false,
      data: null as CalendarEvent | null,
    },
  });
  const {
    initialState: { user },
  } = useAuthStore();
  // data fetching here
  const {
    data: calendarBookings,
    error,
    refetch,
  } = useQuery({
    queryFn: async () => {
      const data = await GetBookings();
      return data.data.bookings;
    },
    queryKey: ["bookings"],
  });
  const { data: services = [] } = useQuery({
    queryKey: ["services"],
    queryFn: async () => (await GetServices()).data.Services,
  });
  const showToast = useToast();
  useEffect(() => {
    if (calendarBookings && calendarBookings.length === 0 && user?.role === 'user')
      showToast("info", "No bookings found", {
        description:
          "Copy your booking link and share it with your customers to start receiving bookings.",
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [calendarBookings, user?.role]);
  const filters = {
    daily: "Daily",
    weekly: "Weekly",
    monthly: "Monthly",
    list: 'List'
  } as const;
  const [activeFilter, setActiveFilter] = useState<keyof typeof filters>(
    (localStorage.getItem("time-frame-filter") as "daily") || "daily"
  );
  useEffect(() => {
    localStorage.setItem("time-frame-filter", activeFilter);
  }, [activeFilter]);
  const {
    changeDate,
    selectedDate,
    setSelectedDate,
    weeks,
    months,
    // state modifiers
    goToPrev,
    goToNext,
  } = useCalendar();
  // for daily calendar
  const [selectedDay, setSelectedDay] = useState(new Date());
  const dailyCalendarFunctions = {
    prev() {
      setSelectedDay(new Date(selectedDay.setDate(selectedDay.getDate() - 1)));
    },
    next() {
      setSelectedDay(new Date(selectedDay.setDate(selectedDay.getDate() + 1)));
    },
    changeDateIntent() {
      modalFunctions.openModal("dailyCalendar", {});
    },
  };
  const navigate = useNavigate()
  let closeParentMenu = (() => undefined) as VoidFunction

  if (error) return <ErrorScreen className="py-40" onRetry={() => refetch()} />;

  return (
    <div className="!h-full pb-10 flex flex-col gap-y-10 ">
      <div className="w-full flex flex-col items-start gap-y-3 justify-between md:gap-y-12">
        <Typography
          variant={"h1"}
          className="!font-bold font-Bricolage_Grotesque text-paragraph max-[400px]:leading-[40px] "
        >
          {`Good ${today.getHours() < 12
            ? "morning"
            : today.getHours() < 17
              ? "afternoon"
              : "evening"
            }, ${user?.first_name}`}{" "}
        </Typography>
        <AnalyticsIntervalProvider
          value={{
            interval: "today",
          }}
        >
          <Link
            to={"/analytics"}
            className="w-full grid-cols-[repeat(3,minmax(0,280px))] gap-4 hidden md:grid md:w-fit"
          >
            <RevenueChart />
            <ReturnRateChart />
            <ServiceUsageChart />
          </Link>
        </AnalyticsIntervalProvider>
        <div className="flex items-center justify-between w-full">
          {/* this has fixed height on mobile screen so sibling element doesn't keep moving unnecessarily when we run the button click animation */}
          <section className="w-full flex items-center justify-normal gap-x-4 stroke-peach fill-none h-[48px] md:h-fit md:w-fit md:justify-normal  ">
            {objectKeys(filters).map((filter, i) => (
              <button
                key={i}
                onClick={() => {
                  setActiveFilter(filter);
                }}
                className={cn(
                  `cursor-pointer text-sm font-medium lg:block transition-all duration-200 ease-[ease] bg-transparent p-0`,
                  {
                    "text-paragraph ": filter !== activeFilter,
                    " text-primary-500 !font-bold shadow-sm shadow-primary-100 bg-primary-200/20 rounded-full py-1 px-4":
                      filter === activeFilter,
                  }
                )}
              >
                {filters[filter]}
              </button>
            ))}
          </section>
          <Menu transformOrigin="top-right" noBlur={innerWidth >= 480} >
            <Menu.Trigger className="-mb-">
              <Button
                className="!rounded-full !p-0 bg-transparent text-paragraph !-mb-10 "
                variant="icon"
              >
                <EllipsisVertical width={22} height={22} />
              </Button>
            </Menu.Trigger>
            <Menu.Content className="h-fit min-w-fit !min-h-0 right-0 whitespace-nowrap">
              <Menu.Item
                className="flex items-center justify-between gap-x-4 pr-5"
                onClick={() => modalFunctions.openModal("blockTimes", {})}
              >
                Block Times{" "}
                <Calendar width={20} height={20} strokeWidth={0.5} />
              </Menu.Item>
              <Menu transformOrigin="top-right" noBlur >
                <Menu.Trigger className="-mb-">
                  <Menu.Item
                    onTap={(_, close) => {
                      closeParentMenu = close
                    }}
                  >
                    Create Appointment{" "}
                    <Plus width={20} height={20} strokeWidth={0.5} />
                  </Menu.Item>
                </Menu.Trigger>
                <Menu.Content className="h-fit min-w-fit !min-h-0 -left-6 whitespace-nowrap" >
                  {services.map((service, index) => (
                    <Menu.Item
                      key={index}
                      onTap={async (_, close) => {
                        close()
                        // this will sha be noop
                        closeParentMenu?.()
                        await sleep(200)
                        navigate(
                          `/booking/internal/${user?.username}/${service.urlName}`
                        )
                      }}
                    >
                      {service.name} <span
                        className="size-3 rounded-full "
                        style={{ background: service.color || "#E91E63" }} ></span>
                    </Menu.Item>
                  ))}
                </Menu.Content>
              </Menu>
              <Menu.Item
                onClick={async () => {
                  await sleep(300)
                  navigate('/scheduling/services')
                }}
              >
                View Services{" "}
              </Menu.Item>
            </Menu.Content>
          </Menu>
        </div>
      </div>
      <section className="flex items-center gap-x-2">
        {inlineSwitch(activeFilter, ["daily", <></>], {
          default: (
            <>
              <DatePicker.Modal
                className="md:py-3 !font-extrabold md:!text-xl"
                onChange={changeDate}
                value={selectedDate}
              />
              <div className="w-fit flex items-center ">
                <button
                  onClick={goToPrev}
                  className="w-fit pl-3 pr-2 py-2 rounded-l-full"
                >
                  <ArrowLeft className="text-[#171717] " />
                </button>
                <button
                  onClick={goToNext}
                  className="w-fit pl-2 pr-3 py-2 rounded-r-full"
                >
                  <ArrowRight className="text-[#171717]" />
                </button>
              </div>
            </>
          ),
        })}
      </section>
      {inlineSwitch(
        activeFilter,
        [
          "daily",
          <EventCalendar.Daily
            events={
              calendarBookings
                ? convertBookingsToCalendarEvents(calendarBookings)
                : []
            }
            onEventClick={(event) => {
              modalFunctions.openModal("event", {
                data: event,
              });
            }}
            selectedDay={selectedDay}
            dateModifierFunctions={dailyCalendarFunctions}
          />,
        ],
        [
          "weekly",
          <EventCalendar.Weekly
            events={
              calendarBookings
                ? convertBookingsToCalendarEvents(calendarBookings)
                : []
            }
            onEventClick={(event) => {
              modalFunctions.openModal("event", {
                data: event,
              });
            }}
            selectedDate={selectedDate}
            setSelectedDate={setSelectedDate}
            weeks={weeks}
          />,
        ],
        [
          "monthly",
          <EventCalendar.Monthly
            events={
              calendarBookings
                ? convertBookingsToCalendarEvents(calendarBookings)
                : []
            }
            onEventClick={(event) => {
              modalFunctions.openModal("event", {
                data: event,
              });
            }}
            selectedDate={selectedDate}
            setSelectedDate={setSelectedDate}
            months={months}
          />,
        ]
      )}
      {modals.event.data && (
        <EventModal
          event={modals.event.data as Required<CalendarEvent>}
          open={modals.event.open}
          onClose={() => modalFunctions.closeModal("event", { data: null })}
        />
      )}
      <BookAppointmentModal
        open={modals.bookAppointment.open}
        onClose={() => modalFunctions.closeModal("bookAppointment")}
      />
      <BlockTimesList
        open={modals.blockTimes.open}
        onClose={() => modalFunctions.closeModal("blockTimes")}
      />
      {activeFilter === "daily" && (
        <Modal
          open={modals.dailyCalendar.open}
          onClose={() => {
            modalFunctions.closeModal("dailyCalendar");
          }}
        >
          <Modal.Body className="!border-none min-h-fit min-w-fit !px-0 !py-0">
            <DatePicker
              className="rounded-[28px] w-full bg-[#EAEBE5]/40 "
              onChange={(date) => {
                setSelectedDay(date);
                modalFunctions.closeModal("dailyCalendar");
              }}
              disableOldDates={false}
              value={selectedDay}
            />
          </Modal.Body>
        </Modal>
      )}
    </div>
  );
}
