import React from "react";

type Props = React.SVGProps<SVGSVGElement> & {
  height: number;
};

const DeviceFrame: React.FC<Props> = ({ className, height, ...rest }) => {
  return (
    <svg
      style={{
        ...rest.style,
        width: "auto",
        position: "relative",
      }}
      className={`h-full ${className}`}
      height={height}
      viewBox="0 0 480 975"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M0.833006 191.23L0.833008 221.475C0.833008 222.291 0.870718 223.038 0.933675 223.589C0.964774 223.86 1.00398 224.102 1.05351 224.286C1.07721 224.373 1.11079 224.477 1.16216 224.568C1.18771 224.614 1.23093 224.681 1.29967 224.742C1.37144 224.806 1.49598 224.884 1.66565 224.884L11.667 224.884C11.8367 224.884 11.9612 224.806 12.033 224.742C12.1018 224.681 12.145 224.614 12.1705 224.568C12.2219 224.477 12.2555 224.373 12.2792 224.286C12.3287 224.102 12.3679 223.86 12.399 223.589C12.462 223.038 12.4997 222.291 12.4997 221.475L12.4997 191.23C12.4997 190.414 12.462 189.667 12.399 189.117C12.3679 188.845 12.3287 188.603 12.2792 188.42C12.2555 188.332 12.2219 188.229 12.1705 188.137C12.145 188.091 12.1018 188.024 12.033 187.963C11.9612 187.899 11.8367 187.822 11.667 187.822L1.66565 187.822C1.49598 187.822 1.37144 187.899 1.29967 187.963C1.23092 188.024 1.18771 188.091 1.16216 188.137C1.11079 188.229 1.07721 188.332 1.05351 188.42C1.00398 188.603 0.964773 188.845 0.933674 189.117C0.870716 189.667 0.833006 190.414 0.833006 191.23Z"
        fill="black"
        stroke="url(#paint0_linear_311_735)"
      />
      <path
        d="M0.833005 265.034L0.833008 326.644C0.833008 328.287 0.870354 329.777 0.931223 330.861C0.961553 331.402 0.998224 331.85 1.04052 332.169C1.06123 332.325 1.08561 332.468 1.11643 332.58C1.13077 332.633 1.1542 332.708 1.19407 332.78C1.2138 332.816 1.251 332.876 1.31348 332.932C1.37878 332.991 1.49864 333.068 1.66565 333.068L11.667 333.068C11.834 333.068 11.9539 332.991 12.0192 332.932C12.0817 332.876 12.1189 332.816 12.1386 332.78C12.1785 332.708 12.2019 332.633 12.2163 332.58C12.2471 332.468 12.2715 332.325 12.2922 332.169C12.3345 331.85 12.3711 331.402 12.4015 330.861C12.4623 329.777 12.4997 328.287 12.4997 326.644L12.4997 265.034C12.4997 263.392 12.4623 261.901 12.4015 260.817C12.3711 260.277 12.3345 259.828 12.2922 259.509C12.2715 259.353 12.2471 259.21 12.2162 259.098C12.2019 259.045 12.1785 258.97 12.1386 258.898C12.1189 258.863 12.0817 258.803 12.0192 258.746C11.9539 258.687 11.834 258.61 11.667 258.61L1.66565 258.61C1.49864 258.61 1.37878 258.687 1.31348 258.746C1.251 258.803 1.21379 258.863 1.19406 258.898C1.15419 258.97 1.13077 259.045 1.11643 259.098C1.08561 259.21 1.06123 259.353 1.04052 259.509C0.998221 259.828 0.96155 260.277 0.931219 260.817C0.870351 261.901 0.833005 263.392 0.833005 265.034Z"
        fill="black"
        stroke="url(#paint1_linear_311_735)"
      />
      <path
        d="M0.833005 359.862L0.833008 421.473C0.833008 423.115 0.870354 424.606 0.931223 425.69C0.961553 426.23 0.998224 426.679 1.04052 426.997C1.06123 427.154 1.08561 427.297 1.11643 427.409C1.13077 427.461 1.1542 427.536 1.19407 427.609C1.2138 427.644 1.251 427.704 1.31348 427.761C1.37878 427.82 1.49864 427.897 1.66565 427.897L11.667 427.897C11.834 427.897 11.9539 427.82 12.0192 427.761C12.0817 427.704 12.1189 427.644 12.1386 427.609C12.1785 427.536 12.2019 427.461 12.2163 427.409C12.2471 427.297 12.2715 427.153 12.2922 426.997C12.3345 426.679 12.3711 426.23 12.4015 425.69C12.4623 424.606 12.4997 423.115 12.4997 421.473L12.4997 359.862C12.4997 358.22 12.4623 356.729 12.4015 355.645C12.3711 355.105 12.3345 354.657 12.2922 354.338C12.2715 354.182 12.2471 354.039 12.2162 353.926C12.2019 353.874 12.1785 353.799 12.1386 353.727C12.1189 353.691 12.0817 353.631 12.0192 353.575C11.9539 353.516 11.834 353.438 11.667 353.438L1.66565 353.438C1.49864 353.438 1.37878 353.516 1.31348 353.575C1.251 353.631 1.21379 353.691 1.19406 353.727C1.15419 353.799 1.13077 353.874 1.11643 353.926C1.08561 354.039 1.06123 354.182 1.04052 354.338C0.998221 354.657 0.96155 355.105 0.931219 355.645C0.870351 356.729 0.833005 358.22 0.833005 359.862Z"
        fill="black"
        stroke="url(#paint2_linear_311_735)"
      />
      <path
        d="M466.166 320.354L466.166 415.571C466.166 418.108 466.179 420.399 466.2 422.056C466.21 422.885 466.223 423.557 466.237 424.024C466.244 424.256 466.252 424.442 466.26 424.572C466.264 424.635 466.268 424.694 466.274 424.741C466.277 424.762 466.282 424.798 466.291 424.836C466.295 424.851 466.307 424.897 466.333 424.948C466.345 424.973 466.374 425.026 466.428 425.08C466.484 425.135 466.604 425.226 466.782 425.226L478.55 425.226C478.728 425.226 478.848 425.135 478.904 425.08C478.958 425.026 478.987 424.973 478.999 424.948C479.025 424.897 479.037 424.851 479.041 424.836C479.05 424.798 479.055 424.762 479.058 424.741C479.064 424.694 479.068 424.635 479.072 424.572C479.08 424.442 479.088 424.256 479.095 424.024C479.109 423.558 479.122 422.885 479.132 422.057C479.153 420.399 479.166 418.108 479.166 415.571L479.166 320.354C479.166 317.818 479.153 315.526 479.132 313.869C479.122 313.04 479.109 312.368 479.095 311.901C479.088 311.669 479.08 311.483 479.072 311.353C479.068 311.29 479.064 311.231 479.058 311.183C479.055 311.163 479.05 311.127 479.041 311.088C479.037 311.073 479.025 311.028 478.999 310.977C478.987 310.952 478.958 310.898 478.904 310.845C478.848 310.789 478.728 310.699 478.55 310.699L466.782 310.699C466.604 310.699 466.484 310.789 466.428 310.844C466.374 310.898 466.345 310.952 466.333 310.977C466.307 311.028 466.295 311.073 466.291 311.088C466.282 311.127 466.277 311.163 466.274 311.183C466.268 311.231 466.264 311.29 466.26 311.353C466.252 311.483 466.244 311.669 466.237 311.901C466.223 312.367 466.21 313.04 466.2 313.868C466.179 315.526 466.166 317.817 466.166 320.354Z"
        fill="black"
        stroke="url(#paint3_linear_311_735)"
      />
      <path
        d="M393.439 1.3356H86.5604C41.4867 1.3356 4.94727 37.9376 4.94727 83.0885V891.912C4.94727 937.062 41.4867 973.664 86.5604 973.664H393.439C438.512 973.664 475.052 937.062 475.052 891.912V83.0885C475.052 37.9376 438.512 1.3356 393.439 1.3356Z"
        fill="black"
        stroke="url(#paint4_linear_311_735)"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M87.9429 7.55655H392.056C433.704 7.55655 467.468 41.3773 467.468 83.0991V891.901C467.468 933.623 433.704 967.443 392.056 967.443H87.9429C46.2945 967.443 12.5303 933.623 12.5303 891.901V83.0991C12.5303 41.3773 46.2945 7.55655 87.9429 7.55655Z"
        fill="black"
        stroke="black"
      />
      <path
        d="M87.9429 7.30655H392.056C433.843 7.30655 467.718 41.2396 467.718 83.0991V891.901C467.718 933.76 433.843 967.693 392.056 967.693H87.9429C46.156 967.693 12.2803 933.76 12.2803 891.901V83.0991C12.2803 41.2396 46.156 7.30655 87.9429 7.30655Z"
        fill="black"
        stroke="#434343"
        strokeWidth="0.5"
      />
      <path
        d="M376.465 1.3356H370.12V7.05656H376.465V1.3356Z"
        fill="black"
        fillOpacity="0.666667"
      />
      <path
        d="M475.052 109.829V103.473H467.968V109.829H475.052Z"
        fill="black"
        fillOpacity="0.666667"
      />
      <path
        d="M4.94727 109.829V103.473H12.0305V109.829H4.94727Z"
        fill="black"
        fillOpacity="0.666667"
      />
      <path
        d="M103.534 973.664H109.879V967.943H103.534V973.664Z"
        fill="black"
        fillOpacity="0.666667"
      />
      <path
        d="M4.94727 865.171V871.527H12.0305V865.171H4.94727Z"
        fill="black"
        fillOpacity="0.666667"
      />
      <path
        d="M475.052 865.171V871.527H467.968V865.171H475.052Z"
        fill="black"
        fillOpacity="0.666667"
      />
      <rect x="25" y="22" width="430" height="932" rx="59" fill="black" />
      <defs>
        <linearGradient
          id="paint0_linear_311_735"
          x1="1.33301"
          y1="206.353"
          x2="11.9997"
          y2="206.353"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#474643" />
          <stop offset="1" stopColor="#5E5E5E" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_311_735"
          x1="1.33301"
          y1="295.839"
          x2="11.9997"
          y2="295.839"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#474643" />
          <stop offset="1" stopColor="#5E5E5E" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_311_735"
          x1="1.33301"
          y1="390.668"
          x2="11.9997"
          y2="390.668"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#474643" />
          <stop offset="1" stopColor="#5E5E5E" />
        </linearGradient>
        <linearGradient
          id="paint3_linear_311_735"
          x1="466.666"
          y1="367.962"
          x2="478.666"
          y2="367.962"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#474643" />
          <stop offset="1" stopColor="#5E5E5E" />
        </linearGradient>
        <linearGradient
          id="paint4_linear_311_735"
          x1="240"
          y1="1.3356"
          x2="240"
          y2="973.664"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#474643" />
          <stop offset="1" stopColor="#5E5E5E" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default DeviceFrame;
