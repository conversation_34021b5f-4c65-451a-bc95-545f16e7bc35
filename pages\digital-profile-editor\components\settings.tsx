import { Typography } from "@/components/typography"
import Toggle from "@/components/ui/toggle-2"
import { ISettingsPayload, useDigitalProfileContext } from "../hooks/use-digital-profile"
import RadioBox from "@/components/ui/radio-box"
import { capitalizeFirstWord } from "@/lib/helpers"

const defaultSettings: ISettingsPayload = {
  isPublic: true,
  showAnalytics: true,
  socialMediaIconsPosition: 'top'
}

/** 
 * @dev no props, every thing even functions are passed through context
 * */
const Settings = () => {
  const { profile, settingSettings: { update } } = useDigitalProfileContext()
  const settings = profile.settings || defaultSettings
  return (
    <div className="flex flex-col gap-y-3 mt-5">
      <div className="w-full flex flex-col gap-y-2 justify-between items-start">
        <Typography
          variant={"h2"}
          className="font-bold font-Bricolage_Grotesque  "
        >
          Settings
        </Typography>
        <Typography className="text-sm hidden text-subtext pl-1">

        </Typography>
      </div>
      <div className="flex flex-col mt-4 gap-y-8" >
        <div className="w-full flex items-start justify-between ">
          <div className="flex flex-col gap-y-1">
            <Typography className=" text-paragraph">
              Make Public
            </Typography>
            <Typography className="text-xs text-subtext">
              Whether your profile should be publicly viewable
            </Typography>
          </div>
          <Toggle
            className=""
            checked={Boolean(profile.settings?.isPublic)}
            onChange={(checked) => {
              update({
                ...settings,
                isPublic: checked
              })
            }}
          />
        </div>
        <div className="w-full flex flex-col gap-y-1 justify-between ">
          <div className="flex flex-col gap-y-1">
            <Typography className=" text-paragraph">
              Social Icons Position
            </Typography>
            <Typography className="text-xs text-subtext">
              Display icons at the top or bottom of your profile
            </Typography>
          </div>
          <RadioBox
            label=""
            onChange={(option) => {
              console.log(option)
              if (!option || !['top', 'bottom'].includes(option.value)) return
              console.log(option)
              update({
                ...settings,
                socialMediaIconsPosition: option.value as 'top'
              })
            }}
            value={{ label: capitalizeFirstWord(settings.socialMediaIconsPosition), value: settings.socialMediaIconsPosition }}
            options={[
              { label: 'Top', value: 'top' },
              { label: 'Bottom', value: 'bottom' }
            ]}
          />
        </div>
        <div className="w-full flex items-start justify-between ">
          <div className="flex flex-col gap-y-1">
            <Typography className=" text-paragraph">
              Show Analytics
            </Typography>
          </div>
          <Toggle
            className=""
            checked={Boolean(profile.settings?.showAnalytics)}
            onChange={(checked) => {
              update({
                ...settings,
                showAnalytics: checked
              })
            }}
          />
        </div>
      </div>

    </div>
  )
}

export default Settings
