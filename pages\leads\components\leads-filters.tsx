import { useToast } from "@/src/contexts/hooks/toast";
import React from "react";
import data from "../data/uk_cities.json";
import { X } from "lucide-react";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import Chip from "@/components/ui/chip";
import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";
import { pick } from "@/lib/helpers";

interface LeadsFiltersProps {
  locations: string[];
  setLocations: (locs: string[]) => void;
  isOpen: boolean;
  onClose: () => void;
}

const FiltersBody: React.FC<
  Pick<LeadsFiltersProps, "locations" | "setLocations">
> = ({ locations, setLocations }) => {
  const showToast = useToast();
  const handleDeleteLocation = (locationToDelete: string) => {
    const newLocs = locations.filter(
      (location) => location !== locationToDelete
    );
    if (!newLocs.length)
      return showToast("error", "Locations must be atleast one.");
    setLocations(newLocs);
  };
  const handleChangeLocation = (newLocation: string | null) => {
    if (newLocation && !locations.includes(newLocation)) {
      setLocations([newLocation]);
    }
  };

  return (
    <section className="w-full space-y-3">
      <div>
        <p className="font-medium mb-2">Locations</p>
        <div className="flex items-center gap-2 bg-white rounded-3xl p-3 border border-gray-300 flex-wrap">
          {locations.map((location) => (
            <Chip
              key={location}
              label={location}
              onDelete={() => handleDeleteLocation(location)}
              className="line-clamp-1"
            />
          ))}
          <SearchableDropdown
            options={data.cities.map((city) => ({
              label: city,
              value: city,
            }))}
            onChange={(option) => {
              if (!option) return;
              handleChangeLocation(option.value);
            }}
            value={{
              value: locations[0] || "",
              label: locations[0] || "",
            }}
            inputProps={{
              className: "placeholder-shown:!text-sm !text-sm !pr-10  ",
            }}
            fullWidth
            placeholder="Search location"
            className="flex-grow"
          />
        </div>
      </div>
    </section>
  );
};

const LeadsFilters: React.FC<LeadsFiltersProps> = (props) => {
  return (
    <Modal open={props.isOpen} onClose={props.onClose}>
      <Modal.Body
        className={`min-h-fit min-w-80 h-fit w-fit flex flex-col gap-y-7 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Leads Filters
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="w-full py-2">
          <FiltersBody {...pick(props, "locations", "setLocations")} />
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default LeadsFilters;
