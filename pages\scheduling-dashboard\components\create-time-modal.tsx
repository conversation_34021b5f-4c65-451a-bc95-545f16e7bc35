import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import Modal from "@/components/ui/modal";
import PendingOverlay from "@/components/ui/pending-overlay";
import Toggle from "@/components/ui/toggle-2";
import useAuthStore from "@/pages/auth/components/auth-store";
import { useToast } from "@/src/contexts/hooks/toast";
import {
  BlockedTimeI,
  CreateBlockTime,
  UpdateBlockTime,
} from "@/src/services/calendar-settings.service";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useFormik } from "formik";
import { X } from "lucide-react";
import React, { useEffect, useState } from "react";
import * as Yup from "yup";
interface CreateTimeModalProps {
  open: boolean;
  onClose: () => void;
  blockedTime: BlockedTimeI | null;
}

const CreateTimeModal: React.FC<CreateTimeModalProps> = ({
  open,
  onClose,
  blockedTime,
}) => {
  const {
    initialState: { calendarSettings },
  } = useAuthStore();
  const [isMultipleDays, setIsMultipleDays] = useState(false);
  const showToast = useToast();
  const queryClient = useQueryClient();
  const CreateBlockTimeMutation = useMutation({
    mutationFn: CreateBlockTime,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["blocktimes"] });
      showToast("success", "Blocked time created");
      onClose();
    },
    onError: (error: ApiError) => {
      showToast("error", error?.response?.data?.error || error?.message);
    },
  });
  const EditBlockTimeMutation = useMutation({
    mutationFn: UpdateBlockTime,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["blocktimes"] });
      showToast("success", "Blocked time Edited");
      onClose();
    },
    onError: () => {
      showToast("error", "An error has occurred, please try again");
    },
  });

  const validationSchema = Yup.object().shape({
    startTime: Yup.string().required("Start time is required"),
    endTime: Yup.string()
      .required("End time is required")
      .test(
        "is-greater",
        "End time must be after start time",
        function (value) {
          const { startTime } = this.parent;
          if (!value || !startTime) return false; // Ensure both values exist
          return (
            new Date(`1970-01-01T${value}`) >
            new Date(`1970-01-01T${startTime}`)
          );
        }
      ),
    startDate: Yup.string().required("Start date is required"),

    notes: Yup.string(),
  });
  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      startTime: "",
      endTime: "",
      startDate: "",
      endDate: "",
      notes: "",
    },
    validationSchema: validationSchema,
    onSubmit: (values) => {
      if (calendarSettings) {
        CreateBlockTimeMutation.mutate({
          Id: calendarSettings._id,
          payload: values,
        });
      }
    },
  });

  useEffect(() => {
    if (!open) {
      formik.resetForm();
    } else if (blockedTime) {
      formik.setValues({
        startTime: blockedTime.startTime ?? "",
        endTime: blockedTime.endTime ?? "",
        startDate: blockedTime.startDate
          ? `${new Date(blockedTime.startDate).toISOString().split("T")[0]}`
          : "",
        endDate: blockedTime.endDate
          ? `${new Date(blockedTime.endDate).toISOString().split("T")[0]}`
          : "",
        notes: blockedTime.notes ?? "",
      });
      setIsMultipleDays(
        new Date(blockedTime.startDate).toISOString().split("T")[0] !==
          new Date(blockedTime.endDate).toISOString().split("T")[0]
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, blockedTime]);

  // Handle change for the start date
  const handleStartDateChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { value } = event.target;
    formik.setFieldValue("startDate", value);

    // If not blocking multiple days, set endDate equal to startDate
    if (!isMultipleDays) {
      formik.setFieldValue("endDate", value);
    }
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Modal.Body className="md:w-[500px] w-[94vw] max-w-[360px] space-y-4 bg-white rounded-[32px] ">
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            {blockedTime ? "Edit Block Time" : "Add Block Time"}
          </Typography>
          <Button
            variant="icon"
            onTap={onClose}
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        <form onSubmit={formik.handleSubmit} className="space-y-4">
          <Toggle
            name="isMultipleDays"
            checked={isMultipleDays}
            onChange={() => setIsMultipleDays(!isMultipleDays)}
            label="Block multiple days?"
          />

          <Typography variant="p" className="mb-2">
            Time
          </Typography>
          <div className="flex items-center space-x-2">
            <input
              name="startTime"
              type="time"
              value={formik.values.startTime}
              className="flex-1"
              onChange={formik.handleChange}
            />
            <Typography variant="p">to</Typography>
            <input
              name="endTime"
              type="time"
              className="flex-1"
              onChange={formik.handleChange}
              value={formik.values.endTime}
            />
          </div>

          {/* Date Inputs */}
          <Typography className="mt-4 mb-2">Date</Typography>
          <div className="flex items-center space-x-2">
            <input
              name="startDate"
              type="date"
              className="flex-1"
              value={formik.values.startDate}
              onChange={handleStartDateChange}
            />
            {isMultipleDays && (
              <>
                <Typography>to</Typography>
                <input
                  name="endDate"
                  type="date"
                  className="flex-1"
                  onChange={formik.handleChange}
                  value={formik.values.endDate}
                />
              </>
            )}
          </div>

          {/* Notes */}
          <Input.TextArea
            label="Notes"
            name="notes"
            value={formik.values.notes}
            onChange={formik.handleChange}
          />

          <div className="flex justify-end gap-x-2 mt-6">
            <Button className="w-full" onTap={onClose} variant="ghost">
              Close
            </Button>
            <Button
              className="w-full"
              disabled={
                EditBlockTimeMutation.isPending ||
                CreateBlockTimeMutation.isPending
              }
              type="submit"
            >
              {blockedTime ? "Save" : "Add"}
            </Button>
          </div>
        </form>
        <PendingOverlay isPending={CreateBlockTimeMutation.isPending} />
      </Modal.Body>
    </Modal>
  );
};

export default CreateTimeModal;
