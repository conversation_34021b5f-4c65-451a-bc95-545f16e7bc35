import { AxiosResponse } from "axios";
import { api } from "./api.service";

export type CreditPackage = ObjectId<{
  name: string;
  currency: "usd" | (string & {});
  creditAmount: number;
  amount: number;
}>;

export const GetAllCreditPackages = async (): Promise<
  AxiosResponse<{
    credits: CreditPackage[];
  }>
> => {
  try {
    const response = await api.get(`/credit-packages`);
    return response;
  } catch (error) {
    console.error("get-business-leads error:", error);
    throw error;
  }
};

export type PurchaseCreditsPayload = {
  creditId: string;
  promocode: string;
  paymentId: string;
};

export const PurchaseCredits = async (
  payload: PurchaseCreditsPayload
): Promise<
  AxiosResponse<{
    stripePaymentMethodId: string;
    clientSecret: string;
  }>
> => {
  const response = await api.post(`/purchase-credit`, payload);
  return response;
};

export const SavePaymentIntentId = async (
  paymentIntentId: string
): Promise<AxiosResponse> => {
  const response = await api.post(`/purchase-credit/success`, {
    paymentIntentId
  });
  return response;
};
