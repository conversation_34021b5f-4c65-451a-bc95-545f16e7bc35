import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useToast } from "~/contexts/hooks/toast";
import { SubscriptionPlan } from "~/interfaces/subscription-plan";
import { PaymentContextProvider } from "./components/payment-context";
import StripeCheckout from "./components/stripe-checkout";
import SubscriptionPlanSection from "./components/subscription-plan-section";
import SubscriptionPlanSkeleton from "./components/subscription-plan-section-skeleton";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { convertPriceToDecimalFormat } from "@/lib/helpers";

export default function PaymentPage() {
  const {
    clientSecret,
    plan,
    error,
  }: {
    error: string | null;
    clientSecret: string | null;
    plan: Prettify<SubscriptionPlan>;
    status: string | null;
  } = useLocation().state || {};
  const showToast = useToast();
  useEffect(() => {
    if (!error) return;
    showToast("info", "Payment Required", {
      description: error,
    });
  }, []);
  return (
    // will be read by stripe checkout and subscription plan section
    <PaymentContextProvider
      value={{
        clientSecret,
        plan: plan,
      }}
    >
      <section className="w-screen h-screen flex flex-col-reverse items-center md:flex-row">
        <section className="w-screen h-screen relative md:max-w-[430px] no-scrollbar overflow-x-hidden  ">
          <div className="bg-primary-400 w-full aspect-square rounded-full absolute -top-[40%] -left-[30%] -z-10" />
          <div className="bg-primary-500 w-[150%] aspect-square rounded-full absolute -top-[42%] -left-[30%] -z-20 " />
          <div className="bg-primary-600 // old [#FFC0CB] w-full aspect-square rounded-full absolute -top-[10%] left-[60%] -z-30" />
          <Typography
            variant={"h1"}
            className="text-white absolute top-[10%] font-Bricolage_Grotesque left-8 text-[54px] leading-[1.1] "
          >
            Complete <br /> Payment
          </Typography>
          {clientSecret ? (
            <section className="w-full h-[63%] absolute pt-6 px-6 bottom-0 pb-10 md:pt-14">
              <section className="w-full pb-9 md:hidden">
                <Accordion
                  type="single"
                  defaultValue="item-0"
                  collapsible
                  className="w-full"
                >
                  <AccordionItem value={`subscription-details`}>
                    <AccordionTrigger >
                      <Typography
                        variant={"h1"}
                        className="text-3xl font-Satoshi capitalize font-semibold"
                      >
                        Details
                      </Typography>
                      <Typography className="w-full text-right pr-1" >
                        <span className="text-base font-Aeonik_Fono font-bold text-gray-900">
                          ${convertPriceToDecimalFormat(plan?.price || 0)}
                        </span>
                        <span className="text-gray-500 text-sm ml-1">
                          /{plan?.recurring}
                        </span>
                      </Typography>
                    </AccordionTrigger>
                    <AccordionContent>
                      <SubscriptionPlanSection />
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </section>
              <StripeCheckout />
            </section>
          ) : (
            <div className="w-full h-full absolute py-14 px-6 bottom-0 pb-10 flex items-center justify-center">
              <CircularLoader />
            </div>
          )}
        </section>
        <section className={`hidden h-full flex-grow md:block bg-white`}>
          {clientSecret ? (
            <SubscriptionPlanSection />
          ) : (
            <SubscriptionPlanSkeleton />
          )}
        </section>
      </section>
    </PaymentContextProvider>
  );
}
