import { debounce, pick } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { ChevronRight } from "lucide-react";
import React, { MutableRefObject, useEffect, useMemo, useRef, useState } from "react";
import { AnyObject, StringSchema, ValidationError } from "yup";
import { Typography } from "./typography";
import ColorWheel from '@/assets/images/color-wheel.png'

type TextInputProps = {
  label: string;
  name: string;
  value?: string;
  defaultValue?: string
  maxLength?: number;
  minLength?: number;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  inputRef?: MutableRefObject<HTMLInputElement | null>
  className?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

type NumericInputProps = Omit<TextInputProps, "value" | 'defaultValue' | "onChange" | 'minLength' | 'maxLength'> & {
  value?: number | string;
  allowFloat?: boolean;
  defaultValue?: number | string
  min?: number | string;
  max?: number | string;
  onChange?: (
    e: React.ChangeEvent<HTMLInputElement>,
    valueAsNumber: number | string
  ) => void;
};

const NumericInput: React.FC<NumericInputProps> = ({
  allowFloat = true,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const hasError = useMemo(() => Boolean(error), [error]);
  const inputRef = props.inputRef || useRef<HTMLInputElement>(null);
  useEffect(() => {
    if (isFocused) inputRef.current?.focus();
  }, [isFocused]);
  return (
    <div className={cn("flex flex-col gap-y-2 ", { "opacity-[.5] ": props.disabled }, props.className)}>
      <label
        htmlFor={props.name}
        data-error={hasError}
        className={`font-medium w-full flex pl-3 text-sm cursor-pointer text-subtext data-[error=true]:font-semibold data-[error=true]:text-red-800
        `}
      >
        {error || props.label}{" "}
        <span
          className={cn("text-red-800", props.required ? "block" : "hidden")}
        >
          *
        </span>
      </label>
      <div
        onClick={() => setIsFocused(true)}
        data-hovered={isFocused}
        data-error={hasError}
        data-disabled={props.disabled}
        className={cn(
          `w-full rounded-2xl pl-4 pr-3 py-3 bg-stone-700/10 backdrop-blur-[12px] flex items-center border border-transparent data-[hovered=true]:border-blue-400 data-[disabled=true]:!border-none data-[error=true]:border-red-800 `
        )}
      >
        <input
          ref={inputRef}
          value={props.value}
          disabled={props.disabled}
          defaultValue={props.defaultValue}
          onBlur={({ currentTarget: { value } }) => {
            if (value === "") {
              if (props.required) setError(`${props.label} is required`);
              else setIsFocused(false);
            } else setIsFocused(false);
          }}
          {...pick(props, 'min', 'max')}
          // handle remove error
          onChange={(e) => {
            if (props.disabled) return;
            const { value } = e.currentTarget;
            // we allow for null values, maybe user is deleting or is a digit allow for decimal numbers
            if (!value || /^(\d*\.?\d*)$/.test(e.currentTarget.value)) {
              if (allowFloat) {
                if (/^(\d*\.?\d*)$/.test(e.currentTarget.value))
                  props.onChange?.(e, value);
              } else {
                if (/^(\d*)$/.test(e.currentTarget.value))
                  props.onChange?.(e, value);
              }
            }
            if (value) error && setError(null);
          }}
          placeholder={props.placeholder}
          onFocus={() => {
            setIsFocused(true);
          }}
          type="text"
          name={props.name}
          className={`font-medium text-[#080808] w-full text-base bg-transparent focus:outline-none `}
        />
      </div>
    </div>
  );
};

const TextInput: React.FC<TextInputProps> = (props) => {
  const [isFocused, setIsFocused] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const hasError = useMemo(() => Boolean(error), [error]);
  const inputRef = props.inputRef || useRef<HTMLInputElement>(null);
  useEffect(() => {
    if (isFocused) inputRef.current?.focus();
  }, [isFocused]);
  return (
    <div className={cn("flex flex-col gap-y-2", { 'opacity-[.4]': props.disabled }, props.className)}>
      <label
        htmlFor={props.name}
        data-error={hasError}
        className={`font-medium w-full flex pl-3 text-sm cursor-pointer text-subtext data-[error=true]:font-semibold data-[error=true]:text-red-800
        `}
      >
        {error || props.label}{" "}
        <span
          className={cn("text-red-800", props.required ? "block" : "hidden")}
        >
          *
        </span>
      </label>
      <div
        onClick={() => setIsFocused(true)}
        data-hovered={isFocused}
        data-error={hasError}
        data-name={props.name}
        data-disabled={props.disabled}
        className={cn(
          `w-full rounded-2xl pl-4 pr-3 py-3 bg-stone-700/10 backdrop-blur-[12px] flex items-center border border-transparent data-[hovered=true]:border-blue-400 data-[disabled=true]:!border-none data-[error=true]:border-red-800`
        )}
      >
        <input
          ref={inputRef}
          value={props.value}
          disabled={props.disabled}
          onBlur={({ currentTarget: { value } }) => {
            if (value === "") {
              if (props.required) setError(`${props.label} is required`);
              else setIsFocused(false);
            } else setIsFocused(false);
          }}
          autoComplete="off"
          onFocus={() => {
            setIsFocused(true);
          }}
          maxLength={props.maxLength}
          minLength={props.minLength}
          // handle remove error
          onChange={(e) => {
            if (props.disabled) return;
            props.onChange?.(e);
            if (e.currentTarget.value) error && setError(null);
          }}
          placeholder={props.placeholder}
          type="text"
          name={props.name}
          className={`font-medium text-[#080808] w-full text-base bg-transparent focus:outline-none`}
        />
      </div>
    </div>
  );
};

type TextAreaProps = Omit<TextInputProps, "onChange" | 'inputRef'> & {
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  inputRef?: MutableRefObject<HTMLTextAreaElement | null>
};

const TextArea: React.FC<TextAreaProps> = (props) => {
  const [isFocused, setIsFocused] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const hasError = useMemo(() => Boolean(error), [error]);
  const inputRef = props.inputRef || useRef<HTMLTextAreaElement>(null);
  useEffect(() => {
    if (isFocused) inputRef.current?.focus();
  }, [isFocused]);
  return (
    <div className={cn("flex flex-col gap-y-2", { 'opacity-[.4]': props.disabled }, props.className)}>
      <label
        htmlFor={props.name}
        data-error={hasError}
        className={`font-medium w-full flex pl-3 text-sm cursor-pointer text-subtext data-[error=true]:font-semibold data-[error=true]:text-red-800
        `}
      >
        {error || props.label}{" "}
        <span
          className={cn("text-red-800", props.required ? "block" : "hidden")}
        >
          *
        </span>
      </label>
      <div
        onClick={() => setIsFocused(true)}
        data-hovered={isFocused}
        data-error={hasError}
        data-disabled={props.disabled}
        className={cn(
          `w-full rounded-2xl pl-4 pr-3 py-3 bg-stone-700/10 backdrop-blur-[12px] flex items-center border border-transparent data-[hovered=true]:border-blue-400 data-[disabled=true]:!border-none data-[error=true]:border-red-800 `
        )}
      >
        <textarea
          ref={inputRef}
          value={props.value}
          disabled={props.disabled}
          onBlur={({ currentTarget: { value } }) => {
            if (value === "") {
              if (props.required) setError(`${props.label} is required`);
              else setIsFocused(false);
            } else setIsFocused(false);
          }}
          autoComplete="off"
          onFocus={() => {
            setIsFocused(true);
          }}
          maxLength={props.maxLength}
          minLength={props.minLength}
          placeholder={props.placeholder}
          // handle remove error
          onChange={(e) => {
            if (props.disabled) return;
            props.onChange?.(e);
            if (e.currentTarget.value) error && setError(null);
          }}
          name={props.name}
          className={`font-medium text-[#080808] w-full text-base bg-transparent focus:outline-none `}
        />
      </div>
    </div>
  );
};

const basicEmailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

const EmailInput: React.FC<TextInputProps> = (props) => {
  const [isFocused, setIsFocused] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const hasError = useMemo(() => Boolean(error), [error]);
  const inputRef = props.inputRef || useRef<HTMLInputElement>(null);
  useEffect(() => {
    if (isFocused) inputRef.current?.focus();
  }, [isFocused]);
  return (
    <div className={cn("flex flex-col gap-y-2", { 'opacity-[.4]': props.disabled }, props.className)}>
      <label
        htmlFor={props.name}
        data-error={hasError}
        className={`font-medium w-full flex pl-3 text-sm cursor-pointer text-subtext data-[error=true]:font-semibold data-[error=true]:text-red-800
        `}
      >
        {error || props.label}{" "}
        <span
          className={cn("text-red-800", props.required ? "block" : "hidden")}
        >
          *
        </span>
      </label>
      <div
        onClick={() => setIsFocused(true)}
        data-hovered={isFocused}
        data-error={hasError}
        data-disabled={props.disabled}
        className={cn(
          `w-full rounded-2xl pl-4 pr-3 py-3 bg-stone-700/10 backdrop-blur-[12px] flex items-center border border-transparent data-[hovered=true]:border-blue-400 data-[disabled=true]:!border-none data-[error=true]:border-red-800 `
        )}
      >
        <input
          ref={inputRef}
          value={props.value}
          disabled={props.disabled}
          onBlur={({ currentTarget: { value } }) => {
            if (value === "") {
              if (props.required) setError(`${props.label} is required`);
              else setIsFocused(false);
            } else setIsFocused(false);
          }}
          onFocus={() => {
            setIsFocused(true);
          }}
          maxLength={props.maxLength}
          minLength={props.minLength}
          placeholder={props.placeholder}
          // handle remove error
          onChange={(e) => {
            if (props.disabled) return
            props.onChange?.(e);
            if (e.currentTarget.value) {
              if (!error)
                !basicEmailRegex.test(e.currentTarget.value) &&
                  setError(`Email is not a valid email address`);
              else setError(null);
            }
          }}
          type="email"
          name={props.name}
          className={`font-medium text-[#080808] w-full text-base bg-transparent focus:outline-none `}
        />
      </div>
    </div>
  );
};

type PasswordInputProps = TextInputProps & {
  required?: boolean;
  validationSchema?: StringSchema<string, AnyObject, undefined, "">;
};

const PasswordInput: React.FC<PasswordInputProps> = ({
  required = true,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [type, setType] = useState<"password" | "text">("password");
  const [errors, setErrors] = useState([] as string[]);
  const hasError = useMemo(() => Boolean(error), [error]);
  const inputRef = props.inputRef || useRef<HTMLInputElement>(null);
  const validate_DEBOUNCED = debounce((value: string) => {
    if (!props.validationSchema) return;
    try {
      props.validationSchema.validateSync(value, {
        abortEarly: false,
      });
      setErrors([]);
    } catch (err) {
      console.log('ss')
      if (err instanceof ValidationError) {
        setErrors(err.errors);
      }
    }
  }, 200);
  useEffect(() => {
    if (isFocused) inputRef.current?.focus();
  }, [isFocused]);
  return (
    <div>
      <div className={cn("flex flex-col gap-y-2", { 'opacity-[.4]': props.disabled }, props.className)}>
        <label
          htmlFor={props.name}
          data-error={hasError}
          className={`font-medium w-full flex pl-3 text-sm cursor-pointer text-subtext data-[error=true]:font-semibold data-[error=true]:text-red-800
        `}
        >
          {error || props.label}{" "}
          <span className={cn("text-red-800", "block")}>*</span>
        </label>
        <div
          onClick={() => setIsFocused(true)}
          data-hovered={isFocused}
          data-error={hasError}
          data-disabled={props.disabled}
          className={cn(
            `w-full rounded-2xl pl-4 pr-3 py-3 bg-stone-700/10 backdrop-blur-[12px] flex items-center border border-transparent data-[hovered=true]:border-blue-400 data-[disabled=true]:!border-none data-[error=true]:border-red-800 relative `
          )}
        >
          <input
            ref={inputRef}
            disabled={props.disabled}
            onBlur={({ currentTarget: { value } }) => {
              if (value === "")
                if (required) setError(`${props.label} is required`);
                else setIsFocused(false);
            }}
            // handle remove error
            onChange={(e) => {
              if (props.disabled) return;
              props.onChange?.(e);
              const { value } = e.currentTarget;
              if (value) {
                if (!props.validationSchema) return;
                validate_DEBOUNCED(value);
              } else setError(null);
            }}
            placeholder={props.placeholder}
            onFocus={() => {
              setIsFocused(true);
            }}
            type={type}
            name={props.name}
            className={`font-medium text-[#080808] w-full text-base bg-transparent focus:outline-none `}
          />
          <div
            className="absolute right-4 bottom-3.5 cursor-pointer"
            onClick={() =>
              setType((p) => (p === "password" ? "text" : "password"))
            }
          >
            {type === "password" ? (
              <Typography className="text-sm">Show</Typography>
            ) : (
              <Typography className="text-sm">Hide</Typography>
            )}
          </div>
        </div>
      </div>
      <div
        className={cn("w-full flex flex-col gap-y-2 ", {
          "mt-3": Boolean(errors.length),
        })}
      >
        {errors.map((error, index) => (
          <Typography
            key={index}
            className="!mt-0 text-xs flex items-center text-gray-400 gap-x-2"
          >
            <div className="p-1 w-fit h-fit rounded-full bg-red-500">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2.9"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="lucide lucide-x text-white"
              >
                <motion.path
                  initial={{ pathLength: 0, opacity: 0 }}
                  animate={{
                    pathLength: 1,
                    opacity: 1,
                  }}
                  d="M18 6 6 18"
                ></motion.path>
                <motion.path
                  initial={{ pathLength: 0, opacity: 0 }}
                  animate={{
                    pathLength: 1,
                    opacity: 1,
                  }}
                  d="m6 6 12 12"
                ></motion.path>
              </svg>
            </div>
            {error}
          </Typography>
        ))}
      </div>
    </div>
  );
};

type PhoneInputProps = Omit<TextInputProps, "onChange"> & {
  /**
   * @dev use second parameter for value
   */
  onChange?: (value: string, e: React.ChangeEvent<HTMLInputElement>) => void;
};

const formatPhoneNumber = (
  oldValue: string,
  newValue: string,
  countryFormat: {
    dialCode: string;
    format: string;
    max: number;
  }
) => {
  let formatted = "";
  const splitNumbers = newValue
    // remove dialCode if its there
    .replace(countryFormat.dialCode, "")
    // only digits in the string
    .replace(/\D/g, "")
    .split("");
  if (splitNumbers.length > countryFormat.max) return oldValue;
  const dotsFormat = countryFormat.format;
  for (let i = 0; i < splitNumbers.length; i++) {
    if (dotsFormat[i] === ".") formatted += splitNumbers[i];
    else if (dotsFormat[i] === " ") formatted += ` ${splitNumbers[i]}`;
  }
  return formatted;
};

const PhoneInput: React.FC<PhoneInputProps> = (props) => {
  const [isFocused, setIsFocused] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const COUNTRY_FORMAT = {
    name: "UNITED KINGDOM",
    dialCode: "+44",
    format: ".... ......",
    max: 10,
  } as const;
  const [value, setValue] = useState<string>(
    props.value
      ? () => {
        return formatPhoneNumber("", props.value!, COUNTRY_FORMAT);
      }
      : ""
  );
  const hasError = useMemo(() => Boolean(error), [error]);
  const inputRef = props.inputRef || useRef<HTMLInputElement>(null);
  /**
   * @dev pass value after it has been formatted
   * @dev pass {dialCode}+value to props.onChange
   * @dev test {dialCode}+value later on
   */
  const handleChange = (value: string) => {
    if (value) error && setError(null);
    if (inputRef.current) setValue(value);
  };
  useEffect(() => {
    if (isFocused) inputRef.current?.focus();
  }, [isFocused]);
  return (
    <div className={cn("flex flex-col gap-y-2", { 'opacity-[.4]': props.disabled }, props.className)}>
      <label
        htmlFor={props.name}
        data-error={hasError}
        className={`font-medium w-full flex pl-3 text-sm cursor-pointer text-subtext data-[error=true]:font-semibold data-[error=true]:text-red-800
        `}
      >
        {error || props.label}{" "}
        <span
          className={cn("text-red-800", props.required ? "block" : "hidden")}
        >
          *
        </span>
      </label>
      <div
        onClick={() => setIsFocused(true)}
        data-hovered={isFocused}
        data-error={hasError}
        data-disabled={props.disabled}
        className={cn(
          `w-full rounded-2xl pl-4 pr-3 py-3 bg-stone-700/10 backdrop-blur-[12px] flex items-center border border-transparent data-[hovered=true]:border-blue-400 data-[disabled=true]:!border-none data-[error=true]:border-red-800 `
        )}
      >
        <div>
          <div className="text-start flex items-center gap-x-1">
            <p
              className={cn({
                "opacity-0":
                  !isFocused && !inputRef.current?.value && !props.value,
              })}
            >
              +44
            </p>
            <input
              ref={inputRef}
              value={value}
              disabled={props.disabled}
              onBlur={() => {
                if (value === "") {
                  if (props.required) setError(`${props.label} is required`);
                  else setIsFocused(false);
                } else if (
                  value.replace(/\D/g, "").length !== COUNTRY_FORMAT.max
                )
                  setError(`${props.label} is incomplete`);
                else setIsFocused(false);
              }}
              onFocus={() => {
                setIsFocused(true);
              }}
              maxLength={props.maxLength}
              minLength={props.minLength}
              onChange={(e) => {
                if (props.disabled) return
                const {
                  currentTarget: { value: newValue },
                } = e;
                const oldValue = value;
                const formatted = formatPhoneNumber(
                  oldValue,
                  newValue,
                  COUNTRY_FORMAT
                );
                handleChange(formatted);
                props.onChange?.(COUNTRY_FORMAT.dialCode.concat(formatted), e);
              }}
              type="text"
              name={props.name}
              placeholder={isFocused ? "3939 292939" : ""}
              className={`font-medium text-[#080808] w-full text-base bg-transparent focus:outline-none `}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

type ColorPickerProps = Pick<TextInputProps, 'name' | 'value' | 'defaultValue' | 'required' | 'inputRef' | 'disabled' | 'className' | 'onChange'> & {

}

const ColorInput: React.FC<ColorPickerProps> = (props) => {
  const [isFocused, setIsFocused] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const hasError = useMemo(() => Boolean(error), [error]);
  const inputRef = props.inputRef || useRef<HTMLInputElement>(null);
  useEffect(() => {
    if (isFocused) inputRef.current?.focus();
  }, [isFocused]);
  return (
    <div onClick={() => inputRef.current?.click()} className={cn("flex w-fit flex-col gap-y-2", { 'opacity-[.4]': props.disabled }, props.className)}>
      <div
        onClick={() => setIsFocused(true)}
        data-hovered={isFocused}
        data-error={hasError}
        data-disabled={props.disabled}
        className={cn(
          `w-fit rounded-full px-2 py-2 flex items-center gap-x-1.5 cursor-pointer border border-gray-200 group data-[hovered=true]:border-blue-400 data-[disabled=true]:!border-none data-[error=true]:border-red-800`
        )}
      >
        <div className="rounded-full border border-black">
          {!props.defaultValue
            && !props.value
            && <img src={ColorWheel} className="size-4 rounded-full object-contain " />
          }
          {(props.defaultValue || props.value) && (
            <div className="size-4 rounded-full" style={{ background: props.value || props.defaultValue }} />
          )}
        </div>
        <ChevronRight size={20} className="text-paragraph group-data-[hovered=true]:rotate-90 transition-transform duration-300 ease-[ease]  " />
        <input
          ref={inputRef}
          value={props.value}
          disabled={props.disabled}
          onBlur={({ currentTarget: { value } }) => {
            if (value === "") {
              if (props.required) setError(`Color  is required`);
              else setIsFocused(false);
            } else setIsFocused(false);
          }}
          autoComplete="off"
          onFocus={() => {
            setIsFocused(true);
          }}
          // handle remove error
          onChange={(e) => {
            if (props.disabled) return;
            props.onChange?.(e);
            if (e.currentTarget.value) error && setError(null);
          }}
          type="color"
          name={props.name}
          className={`sr-only`}
        />
      </div>
    </div>
  );
};

/**
 * @note this can be used with formik
 * @example
 * ```jsx
 * const formik = useFormik({....});
 * <form onSubmit={formik.handleSubmit}>
 *  <Input.Text label="First Name" name="first_name" required />
 *  <Input.Password label="Password" name="password" />
 * </form>
 * ```
 */
export const Input = {
  Text: TextInput,
  TextArea: TextArea,
  Numeric: NumericInput,
  Password: PasswordInput,
  Phone: PhoneInput,
  Email: EmailInput,
  ColorPicker: ColorInput
};
