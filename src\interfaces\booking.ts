export interface Booking {
  _id: string;
  userId: string;
  serviceId: {
    _id: string;
    name: string;
    color: string;
  };
  bookingType: string;
  date: string;
  timeSlot: string;
  bookedBy: string;
  notes: string;
  status: "pending" | "confirmed" | "canceled";
  customerInfo?: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CalendarEvent {
  id: string;
  title: string;
  start: string;
  end: string;
  backgroundColor?: string;
  extendedProps?: {
    bookedBy: string;
    status: string;
    notes: string;
    service: {
      _id: string;
      name: string;
    };
    customerInfo?: {
      email: string;
      firstName: string;
      lastName: string;
      phone: string;
    };
  };
}

export function convertBookingsToCalendarEvents(
  bookings: Booking[]
): CalendarEvent[] {
  return bookings.map((booking) => {
    // Parse the date and timeslot
    const [startTime, endTime] = booking.timeSlot.split("-");
    const start = `${booking.date.split("T")[0]}T${startTime}:00`; // Combine date with start time
    const end = `${booking.date.split("T")[0]}T${endTime}:00`; // Combine date with end time

    return {
      id: booking._id,
      title: booking.notes || "No Title",
      start: start,
      end: end,
      backgroundColor: booking.serviceId.color,
      extendedProps: {
        service: booking.serviceId,
        bookedBy: booking.bookedBy,
        status: booking.status,
        notes: booking.notes,
        customerInfo: booking.customerInfo,
      },
    };
  });
}
