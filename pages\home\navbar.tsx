import { Button } from "@/components/buttons";
import { cn } from "@/lib/utils";
import { noop, sleep } from "@/lib/helpers";
import { motion, useScroll } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { UserMenu } from "./components/user-dropdown";
import UserFeedbackPopover from "./user-feedback";
import { BubbleChatAddIcon, Menu01Icon } from "hugeicons-react";
interface NavigationProps {
  toggleSidebar: () => void;
  className?: string;
}

export const Navbar = ({ toggleSidebar, ...props }: NavigationProps) => {
  const triggerDivRef = useRef<HTMLDivElement>(null);
  const { scrollY } = useScroll(); // Get scrollY from useScroll
  const [hasScrolled, setHasScrolled] = useState(false);
  // Add useEffect to track scroll position
  useEffect(() => {
    const unsubscribe = scrollY.on("change", (latest) => {
      const hasScrolledPast50Px = latest > 50;
      setHasScrolled(hasScrolledPast50Px)
    });
    return () => unsubscribe();
  }, [scrollY]);
  return (
    <section className={cn("w-full px-2 pt-3 ", props.className)}>
      <nav className="w-full px-2 py-2 rounded-lg flex items-center gap-x-3 ">
        <UserMenu />
        <span className="ml-auto"></span>
        <UserFeedbackPopover
          triggerRef={triggerDivRef}
          trigger={
            // onclick here bubbles up
            <motion.button
              onTap={noop}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.9 }}
              className="px-2 py-2 rounded-full !text-paragraph bg-stone-200 hover:bg-stone-300 text-sm "
            >
              <BubbleChatAddIcon width={20} height={20} className="pr-[1px] " />
            </motion.button>
          }
        />
        <Button
          onTap={toggleSidebar}
          variant="icon"
          className="px-2 py-2 rounded-full !text-paragraph bg-stone-200 hover:bg-stone-300 text-sm md:hidden "
        >
          <Menu01Icon className="" size={20} />
        </Button>
      </nav>
      {/* fixed positioning */}
      <Button
        onTap={async () => {
          window.scrollTo({ top: 0, behavior: "smooth" });
          await sleep(100)
          triggerDivRef.current?.click()
        }}
        variant="icon"
        initial=""
        animate={{
          opacity: hasScrolled ? 1 : 0,
          y: hasScrolled ? 0 : 50,
        }}
        className={
          cn("fixed bottom-5 right-3 z-[300000] px-2 py-2 !rounded-full bg-primary-500 text-white text-sm md:bottom-4 md:right-5 ", 
          )
        }
      >
        <BubbleChatAddIcon width={24} height={24} className="pr-[1px] " />
      </Button>
    </section>
  );
};

export default Navbar;
