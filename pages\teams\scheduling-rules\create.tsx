import React from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { ActionButtonGroup, But<PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import PendingOverlay from "@/components/ui/pending-overlay";
import { ArrowLeft02Icon } from "hugeicons-react";
import { useSchedulingRules } from "../hooks/use-scheduling-rules";
import { GetServiceById } from "@/src/services/services.service";
import { useToast } from "~/contexts/hooks/toast";
import { CreateSchedulingRulesPayload } from "@/src/interfaces/scheduling-rules";
import RulesForm from "./components/rules-form";
import { noop, pickByType } from "@/lib/helpers";
import { useQuery } from "@tanstack/react-query";

/**
 * @likely_bug {@link GetServiceById} un-implemented api endpoint in backend
 */
const CreateSchedulingRules: React.FC = () => {
  const navigate = useNavigate();
  const { serviceId } = useParams<{ serviceId: string }>();
  const [searchParams] = useSearchParams();
  const teamId = searchParams.get("teamId");
  const showToast = useToast();

  const { createSchedulingRulesMutation } = useSchedulingRules();
  const serviceQuery = useQuery({
    queryKey: ["create-scheduling-rules", serviceId],
    queryFn: async () => {
      return (await GetServiceById(serviceId!)).data.Service
    },
    enabled: Boolean(serviceId),
  });

  const service = serviceQuery.data;

  const formik = useFormik({
    initialValues: {
      depositType: "none" as const,
      depositAmount: undefined,
      cancelCutoff: 24,
      noShowFee: 0,
      allowRescheduling: true,
      rescheduleCutoff: 2,
      lateCancellationFee: 0,
      isActive: true,
    } as CreateSchedulingRulesPayload,
    validationSchema: Yup.object({
      depositType: Yup.string().oneOf(['none', 'percentage', 'fixed', 'full']).required(),
      depositAmount: Yup.number().when('depositType', {
        is: (val: string) => val === 'percentage' || val === 'fixed',
        then: (schema) => schema.required('Deposit amount is required'),
        otherwise: (schema) => schema,
      }),
      cancelCutoff: Yup.number().min(0).required('Cancel cutoff is required'),
      noShowFee: Yup.number().min(0).max(100).required('No-show fee is required'),
      allowRescheduling: Yup.boolean().required(),
      rescheduleCutoff: Yup.number().when('allowRescheduling', {
        is: true,
        then: (schema) => schema.min(0).required('Reschedule cutoff is required'),
        otherwise: (schema) => schema,
      }),
      lateCancellationFee: Yup.number().min(0).max(100).required('Late cancellation fee is required'),
      isActive: Yup.boolean().required(),
    }),
    validateOnMount: true,
    onSubmit: noop,
  });

  const createRulesHandler = async () => {
    if (!serviceId) return;

    formik.validateForm().then((val) => {
      const errors = Object.values(pickByType(val, "string"));
      if (errors.length === 0) {
        const { values } = formik;

        createSchedulingRulesMutation.mutateAsync(
          {
            serviceId,
            payload: {
              ...values,
              // @ts-expect-error
              serviceId
            },
          },
          {
            onSuccess() {
              const queryParams = teamId ? `?teamId=${teamId}` : "";
              navigate(`/teams/rules${queryParams}`);
            },
          },
        );
      } else {
        showToast("error", "Invalid Field", {
          description: errors[0],
        });
      }
    });
  };

  const goBack = () => {
    const queryParams = teamId ? `?teamId=${teamId}` : "";
    navigate(`/teams/rules${queryParams}`);
  };

  return (
    <section className="w-full max-w-2xl mx-auto space-y-6 pb-20">
      <div className="flex items-center gap-4">
        <Button variant="icon" onTap={goBack} className="p-1">
          <ArrowLeft02Icon className="w-5 h-5" />
        </Button>
        <div>
          <Typography variant="h2" className="font-semibold">
            Create Scheduling Rules
          </Typography>
          {service && (
            <Typography className="text-gray-600 mt-1">
              Setting up rules for {service.name}
            </Typography>
          )}
        </div>
      </div>

      <RulesForm formik={formik} />

      <ActionButtonGroup
        cancel={{
          title: "Cancel",
          disabled: createSchedulingRulesMutation.isPending,
          onTap: goBack
        }}
        next={{
          loading: createSchedulingRulesMutation.isPending,
          disabled: createSchedulingRulesMutation.isPending || !formik.isValid,
          title: 'Create',
          onTap: createRulesHandler,
        }}
      />

      <PendingOverlay isPending={createSchedulingRulesMutation.isPending} />
    </section>
  );
};

export default CreateSchedulingRules;
