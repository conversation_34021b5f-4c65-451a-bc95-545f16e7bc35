import { AxiosResponse } from "axios";
import { api } from "./api.service";

export type AnalyticsInterval = "today" | "weekly" | "monthly" | "yearly";

interface InteractionAnalyticsPayload<T = string> {
  userId: T;
  actionType: "click" | "hover";
  elementId: `${"submit"}-button` | "button";
}

export const TrackUserInteraction = async (
  payload: InteractionAnalyticsPayload
): Promise<
  AxiosResponse<{
    success: boolean;
    message: string;
  }>
> => {
  try {
    const response = await api.post(`/interaction/track`, payload);
    return response;
  } catch (error) {
    console.error("user-interaction-error", error);
    throw error;
  }
};

export type PageViewAnalyticsPayload<T = string> = {
  pageUrl: T;
};

export const TrackPageView = async (
  payload: PageViewAnalyticsPayload
): Promise<
  AxiosResponse<{
    success: boolean;
    message: string;
  }>
> => {
  try {
    const response = await api.post(`/page-view/track`, payload);
    return response;
  } catch (error) {
    console.error("page-view-error", error);
    throw error;
  }
};

export type PageSessionAnalyticsPayload<T = string> = {
  pageUrl: T;
  startTime: Date;
  endTime?: Date;
};

export const TrackPageSession = async (
  payload: PageSessionAnalyticsPayload
): Promise<
  AxiosResponse<{
    success: boolean;
    message: string;
  }>
> => {
  try {
    const response = await api.post(`/page-session/track`, payload);
    return response;
  } catch (error) {
    console.error("page-session-error", error);
    throw error;
  }
};

export type ConversionAnalyticsPayload<T = string> = {
  userId: T;
  conversionType:
    | "signup"
    | "login"
    | "purchase"
    | "contact_request"
    | "download"
    | "add_to_cart"
    | "appointment_book";
};

export const TrackConversion = async (
  payload: ConversionAnalyticsPayload
): Promise<
  AxiosResponse<{
    success: boolean;
    message: string;
  }>
> => {
  try {
    const response = await api.post(`/conversion/track`, payload);
    return response;
  } catch (error) {
    console.error("conversion-analytics-error", error);
    throw error;
  }
};

// Client Appointment Analytics

export const GetTodayAppointmentRevenue = async () => {
  const response = await api.get("/appointment-renvenue/today");
  return response as AxiosResponse<{
    totalRevenue: number;
  }>;
};

export const GetCurrentMonthAppointmentRevenue = async () => {
  const response = await api.get("/appointment-renvenue/current-month");
  return response as AxiosResponse<{
    totalRevenue: number;
  }>;
};

export type ReturnRateType = {
  serviceId: ObjectId<{
    name: string;
  }>
  totalClients: number;
  totalReturningClients: number;
  returnRate: `${number}%`;
};

export const GetAppointmentReturnRate = async ({
  serviceId,
  timeInterval,
}: {
  timeInterval: AnalyticsInterval;
  serviceId: string;
}) => {
  const response = await api.get(
    `/appointment/return-rate/${serviceId}?timeInterval=${timeInterval}`
  );
  return response as AxiosResponse<ReturnRateType>;
};

/**
 * @dev this should be not be used yet, incoming data type may not be correct
 */
export const GetMostUsedServices = async (timeInterval: AnalyticsInterval) => {
  const response = await api.get(
    `/appointment/most-used-services?timeInterval=${timeInterval}`
  );
  return response as AxiosResponse<{
    message: string;
    service: ReturnRateType[];
    count: number;
  }>;
};

export const GetClientCountAndPercentage = async ({
  serviceId,
  timeInterval,
}: {
  timeInterval: AnalyticsInterval;
  serviceId: string;
}) => {
  const response = await api.get(
    `/appointment/client-percentage/${serviceId}?timeInterval=${timeInterval}`
  );
  return response as AxiosResponse<{
    message: string;
    data: {
      serviceName: string;
      clientCount: number;
      percentage: string | number;
    }[];
  }>;
};

export type Month =
  | "January"
  | "February"
  | "March"
  | "April"
  | "May"
  | "June"
  | "July"
  | "August"
  | "September"
  | "October"
  | "November"
  | "December";

export type ServiceRevenueValueMonthMapping = {
  [index in Month]?: {
    averageRevenues: {
      serviceName: string;
      averageRevenue: number;
    }[];
    highestAverageRevenueService: {
      serviceName: string;
      averageRevenue: number;
    };
  };
};

// Highest value service identification analytics
export const GetServiceRevenueValue = async ({
  serviceId,
  timeInterval,
}: {
  timeInterval: AnalyticsInterval;
  serviceId: string;
}) => {
  const response = await api.get(
    `/appointment/average-revenue-service/${serviceId}?timeInterval=${timeInterval}`
  );
  return response as AxiosResponse<{
    data: ServiceRevenueValueMonthMapping;
  }>;
};

// Client re-engagement analytics

export interface ClientToReEngage {
  client: {
    _id: string;
    name: string;
    email: string;
    phone: string;
  };
  lastAppointment: Date | null;
  daysSinceLastAppointment: number | null;
  averageReturnInterval: number;
  status: "Overdue" | "Not booked this service";
  daysOverdue?: number;
  potentialValue: "High" | "Medium";
}

export interface GetClientsToReEngageResponse {
  message: string;
  service: {
    id: string;
    name: string;
  };
  averageReturnIntervalDays: number;
  totalClientsForReEngagement: number;
  clientsForReEngagement: ClientToReEngage[];
}

export const GetClientsToReEngage = async ({
  serviceId,
  dayThreshold = 30,
}: {
  serviceId: string;
  dayThreshold?: 30 | (number & {});
}) => {
  const response = await api.get<GetClientsToReEngageResponse>(
    `/clients-reengagement/service/${serviceId}?dayThreshold=${dayThreshold}`
  );
  return response;
  // return FakeData.asyncFaked({
  //   message: "",
  //   service: {
  //     id: serviceId,
  //     name: "Service Name",
  //   },
  //   averageReturnIntervalDays: 30,
  //   totalClientsForReEngagement: FakeData.clientsToReEngage.length,
  //   clientsForReEngagement: FakeData.clientsToReEngage,
  // } satisfies GetClientsToReEngageResponse);
};

export interface GetReEngagementStrategiesResponse {
  message: string;
  client: {
    _id: string;
    name: string;
    email: string;
    phone: string;
    totalAppointments: number;
  };
  clientStatus: "Active" | "Recently inactive" | "Long-term inactive";
  lastAppointment: Date;
  daysSinceLastAppointment: number;
  favoriteServices: {
    service: {
      _id: string;
      name: string;
    };
    bookingCount: number;
    totalRevenue: number;
  }[];
  unbookedServices: {
    _id: string;
    name: string;
  }[];
  recommendedStrategies: string[];
  appointmentHistory: {
    _id: string;
    date: Date;
    service: {
      _id: string;
      name: string;
      price?: number;
    };
    revenue: number;
    status: "completed" | "canceled" | "no-show";
  }[];
}

export const GetReEngagementStrategies = async (clientId: string) => {
  const response = await api.get<GetReEngagementStrategiesResponse>(
    `/analytics/client/${clientId}/re-engagement`
  );
  return response;
};
