import { api } from "../api.service";
import { AxiosResponse } from "axios";

interface Credit {
  name: string;
  currency: "usd" | "eur";
  creditAmount: string;
  amount: string;
}

export const CreatePuzzleCredit = async (
  payload: Credit
): Promise<
  AxiosResponse<{
    newCredit: Credit;
  }>
> => {
  try {
    const response = await api.post(`/admin/credit`, payload);
    return response;
  } catch (error) {
    console.error("create-puzzleprice-error", error);
    throw error;
  }
};

export const GetAllPuzzleCredits = async (): Promise<AxiosResponse<{
  credits: Credit[];
}>> => {
  try {
    const response = await api.get(`/admin/credit`);
    return response;
  } catch (error) {
    console.error("get-puzzleprice-error", error);
    throw error;
  }
};
export const GetPuzzleCredit = async (Id: string): Promise<AxiosResponse> => {
  try {
    const response = await api.get(`/admin/credit/${Id}`);
    return response;
  } catch (error) {
    console.error("get-puzzleprice-error", error);
    throw error;
  }
};
export const UpdatePuzzleCredit = async (
  Id: string,
  payload: Credit
): Promise<AxiosResponse> => {
  try {
    const response = await api.put(`/admin/credit/${Id}`, payload);
    return response;
  } catch (error) {
    console.error("update-puzzleprice-error", error);
    throw error;
  }
};

export const DeletePuzzleCredit = async (
  Id: string
): Promise<AxiosResponse> => {
  try {
    const response = await api.delete(`/admin/credit/${Id}`);
    return response;
  } catch (error) {
    console.error("delete-puzzleprice-error", error);
    throw error;
  }
};
