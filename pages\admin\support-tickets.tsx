import { entries, pick } from "@/lib/helpers";
import { useAdminSupportTicketsApis } from "./use-admin";
import { AdminNS } from "@/src/services/admin.service";
import CircularLoader from "@/components/ui/circular-loader";
import { Button } from "@/components/buttons";
import { Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import AddTicketModal from "./components/add-ticket-modal";
import PendingOverlay from "@/components/ui/pending-overlay";

const properties: Array<keyof AdminNS.SupportTicket> = [
  "userId",
  "title",
  "description",
  "status",
  "priority",
  "ip",
] as const;

type ModalsType = {
  addTicket: {
    open: boolean;
    payload: AdminNS.CreateSupportTicketPayload;
  };
  deleteTicket: {
    open: boolean;
    payload: {
      ticketId: string;
    };
  };
};

const emptyPayloads = {
  addTicket: {
    userId: "",
    title: "",
    description: "",
    priority: "",
    ip: "",
  },
};

const SupportTickets = () => {
  const {
    supportTickets,
    isSupportTicketsFetching,
    createSupportTicketMutation,
    deleteSupportTicketMutation,
  } = useAdminSupportTicketsApis();
  const [modals, setModals] = useState<ModalsType>({
    addTicket: {
      open: false,
      // start with empty payload
      payload: emptyPayloads.addTicket as any,
    },
    deleteTicket: {
      open: false,
      // start with empty payload
      payload: {} as any,
    },
  });
  const modalFunctions = {
    openModal: (modal: keyof typeof modals) => {
      setModals((p) => ({
        ...p,
        [modal]: {
          ...p[modal],
          open: true,
        },
      }));
    },
    closeModal: (modal: keyof typeof modals) => {
      setModals((p) => ({
        ...p,
        [modal]: {
          ...p[modal],
          open: false,
        },
      }));
    },
  };
  return (
    <section className=" md:max-w-[760px] overflow-x-auto no-scrollbar ">
      {supportTickets.length > 0 && (
        <Button
          onTap={() => modalFunctions.openModal("addTicket")}
          className="flex items-center gap-x-2 mb-6"
        >
          Add Support Ticket <Plus />
        </Button>
      )}
      <div className=" w-full mr-6 grid grid-cols-[64px_repeat(6,minmax(240px,350px))]">
        <div className="border-b-2"></div>
        {properties.map((key, index) => (
          <div
            key={index}
            className=" border-b-2 last:border-r-0 px-1 pb-2 text-center text-sm"
          >
            <div className="w-full bg-gray-100 py-2 rounded-lg">{key}</div>
          </div>
        ))}
      </div>
      {isSupportTicketsFetching && (
        <div className="w-full flex items-center justify-center py-12">
          <CircularLoader />
        </div>
      )}
      {!isSupportTicketsFetching && !supportTickets.length && (
        <Button
          onTap={() => modalFunctions.openModal("addTicket")}
          variant="ghost"
          className="flex items-center gap-x-2 mx-auto mt-8"
        >
          Add <Plus />
        </Button>
      )}
      {supportTickets.map((supportTicket, index) => {
        const toBeShown = pick(supportTicket, ...properties);
        return (
          <div
            key={index}
            className=" w-full max-w-fit pr-6 grid grid-cols-[64px_repeat(6,minmax(240px,350px))] group relative "
          >
            <div
              key={index}
              className="first:border-x-2 border-r-2 border-b-2 flex items-center justify-center px-4"
            >
              <Button
                onTap={() => {
                  deleteSupportTicketMutation.mutateAsync(supportTicket._id);
                }}
                whileTap={{ scale: 0.9 }}
                whileHover={{ scale: 1.05 }}
                className="bg-transparent p-0"
              >
                <Trash2 size={16} className="" />
              </Button>
            </div>
            {entries(toBeShown as AdminNS.SupportTicket).map(
              ([key, value], index) => {
                return (
                  <div
                    key={index}
                    className="first:border-x-2 border-r-2 border-b-2 pt-2 pb-3 px-4 cursor-pointer"
                  >
                    <p className="text-center line-clamp-1 text-sm">
                      {!value
                        ? "----"
                        : key === "userId"
                        ? (value as AdminNS.SupportTicket["userId"])._id
                        : (value as string)}
                    </p>
                  </div>
                );
              }
            )}
          </div>
        );
      })}
      <AddTicketModal
        open={modals.addTicket.open}
        onClose={modalFunctions.closeModal.bind(null, "addTicket")}
        values={modals.addTicket.payload}
        onChange={(name, value) => {
          setModals((p) => ({
            ...p,
            addTicket: {
              ...p.addTicket,
              payload: {
                ...p.addTicket.payload,
                [name]: value,
              },
            },
          }));
        }}
        onSubmit={() => {
          createSupportTicketMutation.mutateAsync(modals.addTicket.payload, {
            onSuccess: () => {
              modalFunctions.closeModal("addTicket");
              setModals((p) => ({
                ...p,
                addTicket: {
                  ...p.addTicket,
                  payload: emptyPayloads.addTicket as any,
                },
              }));
            },
          });
        }}
      />
      <PendingOverlay
        isPending={
          createSupportTicketMutation.isPending ||
          deleteSupportTicketMutation.isPending
        }
      />
    </section>
  );
};

export default SupportTickets;
