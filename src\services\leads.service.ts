import { AxiosResponse } from "axios";
import { TLead, TLeadDetails } from "../interfaces/leads";
import { api } from "./api.service";

type BusinessLeadsData = {
  data: TLead[];
  page: number;
  limit: number;
  totalPages: number;
  totalBusinesses: number;
};

export const GetBusinessLeads = async (payload: {
  keyword: string;
  location: string[];
  page: number
}): Promise<
  AxiosResponse<BusinessLeadsData>
> => {
  try {
    const response = await api.get<BusinessLeadsData>(
      `/leads?keyword=${payload.keyword}&location=${payload.location.join(
        "&location="
      )}&page=${payload.page}`
    );
    console.log(response.data.data)
    return response;
  } catch (error) {
    console.error("get-business-leads error:", error);
    throw error;
  }
};

export type LeadsCart = {
  _id: string;
  user: string;
  leads: Pick<
    TLead,
    "_id" | "name" | "industry" | "description" | "requiredCredit"
  >[];
  status: "pending" | "purchased";
  totalAmount: number;
};

export const UpsertLeadsCart = async (payload: {
  leadIds: string[];
  cartId: string | null;
}): Promise<AxiosResponse<LeadsCart>> => {
  try {
    const response = await api.post("/leads/buy", payload);
    return response;
  } catch (error) {
    console.error("buy-business-leads error:", error);
    throw error;
  }
};

export const GetPendingCart = async (): Promise<AxiosResponse<LeadsCart>> => {
  try {
    const response = await api.get(`/leads/cart/pending/`);
    return response;
  } catch (error) {
    console.error("get-pending-cart error:", error);
    throw error;
  }
};

type LeadPurchase = {
  _id: string;
  user: string;
  leads: Pick<
    TLead,
    "_id" | "name" | "industry" | "description" | "requiredCredit"
  >[];
};

/**
 * @note despite the name, this function is actually for checking out the cart and returning updated data.
 */
export const CheckoutCart = async (
  cartId: string
): Promise<
  AxiosResponse<{
    message: string;
    purchase: LeadPurchase;
  }>
> => {
  try {
    const response = await api.get(`/leads/checkout/${cartId}`);
    return response;
  } catch (error) {
    console.error("checkout-cart error:", error);
    throw error;
  }
};

export type PurchasedLead = {
  name: string;
  industry: string;
  description: string;
  requiredCredit: number;
};

export type PurchasedCartLead = {
  _id: string;
  user: string;
  leads: Pick<
    TLeadDetails,
    | "name"
    | "industry"
    | "requiredCredit"
    | "address"
    | "telephone_numbers"
    | "town"
    | "postcode"
    | "email_addresses"
  >[];
  status: "purchased";
  totalAmount: number;
  __v: number;
  purchaseDate: string;
};

export const GetPurchasedLeads = async (): Promise<
  AxiosResponse<Array<PurchasedCartLead>>
> => {
  try {
    const response = await api.get("/leads/carts/purchased");
    return response;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const GetLeadById = async (
  leadId: string
): Promise<
  AxiosResponse<{
    Lead: TLeadDetails;
    success: boolean;
  }>
> => {
  try {
    const response = await api.get(`/leads/${leadId}`);
    return response;
  } catch (error) {
    console.error("get-lead-by-id error:", error);
    throw error;
  }
};
