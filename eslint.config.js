import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import unicorn from 'eslint-plugin-unicorn'
import tseslint from 'typescript-eslint'

export default tseslint.config(
  { ignores: ['dist', 'node_modules', 'eslint.config.js', '*.*'] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
      'unicorn': unicorn
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-empty-object-type': 'off',
      "no-unused-vars": "off",
      '@typescript-eslint/no-unused-vars': [
        "error",
        {
          argsIgnorePattern: '^_',
        },
      ],
      "no-unused-expressions": "off",
      '@typescript-eslint/no-unused-expressions': [
        "error",
        {
          allowShortCircuit: true,
          allowTernary: true,
        },
      ],
      // File case naming rules
      'unicorn/filename-case': [
        'error',
        {
          case: 'kebabCase',
          ignore: ['.ts$', '.tsx$']
        }
      ],
    },
  },
  {
    files: ['src/services/**/*.service.ts'],
    rules: {
      '@typescript-eslint/naming-convention': [
        'error',
        {
          selector: 'variable',
          modifiers: ['global', 'async', 'const'],
          format: ['PascalCase'],
          leadingUnderscore: 'allow'
        },
        {
          selector: 'function',
          modifiers: ['async', 'global'],
          format: ['PascalCase'],
          leadingUnderscore: 'allow'
        }
      ]
    }
  }
)
