import { Typography } from "@/components/typography";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import moment from "moment-timezone";
import React from "react";

interface TimezoneSelectorProps {
  timezone?: string;
  setTimezone: (newTimezone: string) => void;
}

const TimezoneSelector: React.FC<TimezoneSelectorProps> = ({
  timezone,
  setTimezone,
}) => {
  const timezones = moment.tz.names();

  return (
    <div className="w-full flex items-start justify-between gap-x-5">
      <Typography className=" text-paragraph -mt-1">Timezone</Typography>
      <SearchableDropdown
        options={timezones.map((timezone) => ({
          label: timezone,
          value: timezone,
        }))}
        value={{
          value: timezone || "",
          label: timezone || "",
        }}
        onChange={(option) => {
          setTimezone(option?.value || "");
        }}
        className="max-w-[240px] text-sm"
        fullWidth
      />
    </div>
  );
};

export default TimezoneSelector;
