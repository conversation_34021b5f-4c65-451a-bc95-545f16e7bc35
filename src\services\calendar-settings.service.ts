
import { AxiosResponse } from "axios";
import { GlobalIdParams } from "../interfaces/globals";
import { type CalendarPayload } from "@/pages/profile/calendar-settings";
import { api } from "./api.service";

export interface BlockedTimeI {
  _id: string;
  userId: string;
  calendarId: string;
  startTime: string; // Time in "HH:mm" format
  endTime: string; // Time in "HH:mm" format
  startDate: string; // ISO date string
  endDate: string; // ISO date string
  notes: string;
}

export const GetBlockedTimes = async (): Promise<
  AxiosResponse<{
    blockedTimes: BlockedTimeI[];
  }>
> => {
  try {
    const response = await api.get(`/time-block`);
    return response;
  } catch (error) {
    console.error("Get-block:", error);
    throw error;
  }
};


interface IWorkday {
  day: 'sunday' | 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday';
  startTime: string;  // HH:MM format
  closingTime: string;  // HH:MM format
}

export interface ICalendarSettings {
  workdays: IWorkday[]; // Each workday with its own start and closing time
  timeslots: '10' | '15' | '30' | '60' | 'custom';
  customTimeslotDuration?: number;
  userId: string;
  timezone: string;
  advanceBookingLimit?: number;
}

export const CreateBlockTime = async ({
  Id,
  payload,
}: GlobalIdParams): Promise<AxiosResponse> => {
  try {
    const response = await api.post(`/time-block/${Id}`, payload);
    return response;
  } catch (error) {
    console.error("Create-block:", error);
    throw error;
  }
};

export const UpdateBlockTime = async ({
  Id,
  payload,
}: GlobalIdParams): Promise<AxiosResponse> => {
  try {
    const response = await api.put(`/time-block/${Id}`, payload);
    return response;
  } catch (error) {
    console.error("edit-block:", error);
    throw error;
  }
};

export const DeleteBlockTime = async (Id: string): Promise<AxiosResponse> => {
  try {
    const response = await api.delete(`/time-block/${Id}`);
    return response;
  } catch (error) {
    console.error("delete-block:", error);
    throw error;
  }
};

export const GetCalendarSettings = async (
) => {
  try {
    const response = await api.get<{ calendarSettings: ICalendarSettings }>(`/calendar-settings`);
    return response;
  } catch (error) {
    console.error("Update calendar settings:", error);
    throw error;
  }
};
/**
 * service to push the settings to the backend
 * @writtenby yours truly
 */
export const UpdateCalendarSettings = async (
  payload: CalendarPayload,
) => {
  try {
    const response = await api.put(`/update/calendar-settings`, payload);
    return response;
  } catch (error) {
    console.error("Update calendar settings:", error);
    throw error;
  }
};

