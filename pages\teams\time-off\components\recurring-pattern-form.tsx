import React from "react";
import { Input } from "@/components/inputs";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import DatePicker from "@/components/ui/calendar";
import { Typography } from "@/components/typography";
import {
  RecurringPattern,
  RecurringFrequency,
} from "@/src/interfaces/time-off";
import CheckBox from "@/components/ui/checkbox";
import { formatDate } from "date-fns";

interface RecurringPatternFormProps {
  pattern: RecurringPattern;
  onChange: (pattern: RecurringPattern) => void;
}

const FREQUENCY_OPTIONS = [
  { value: "daily", label: "Daily" },
  { value: "weekly", label: "Weekly" },
  { value: "monthly", label: "Monthly" },
];

const DAYS_OF_WEEK = [
  { value: 0, label: "Sunday" },
  { value: 1, label: "Monday" },
  { value: 2, label: "Tuesday" },
  { value: 3, label: "Wednesday" },
  { value: 4, label: "Thursday" },
  { value: 5, label: "Friday" },
  { value: 6, label: "Saturday" },
];

const RecurringPatternForm: React.FC<RecurringPatternFormProps> = ({
  pattern,
  onChange,
}) => {
  const updatePattern = (updates: Partial<RecurringPattern>) => {
    onChange({ ...pattern, ...updates });
  };

  const toggleDayOfWeek = (day: number) => {
    const currentDays = pattern.daysOfWeek || [];
    const newDays = currentDays.includes(day)
      ? currentDays.filter((d) => d !== day)
      : [...currentDays, day].sort();

    updatePattern({ daysOfWeek: newDays });
  };

  return (
    <div className="space-y-4 p-4 border rounded-xl bg-stone-50 ">
      <Typography variant="h4" className="font-medium">
        Recurring Pattern
      </Typography>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <SearchableDropdown
          label="Frequency"
          name="frequency"
          fullWidth
          required
          value={pattern.frequency}
          onChange={(value) =>
            updatePattern({ frequency: value?.value as RecurringFrequency })
          }
          options={FREQUENCY_OPTIONS}
        />

        <Input.Numeric
          label="Repeat every"
          name="interval"
          required
          value={pattern.interval.toString()}
          onChange={(e) =>
            updatePattern({ interval: parseInt(e.target.value) || 1 })
          }
          min={1}
          max={365}
        />
      </div>

      {pattern.frequency === "weekly" && (
        <div>
          <Typography variant="p" className="text-sm font-medium mb-2">
            Days of Week
          </Typography>
          <div className="flex flex-wrap gap-2">
            {DAYS_OF_WEEK.map((day) => (
              <div
                key={day.value}
                className="relative cursor-pointer flex items-center gap-x-2"
              >
                <CheckBox
                  className="!rounded-lg"
                  checked={(pattern.daysOfWeek || []).includes(day.value)}
                  onChange={() => toggleDayOfWeek(day.value)}
                />
                <Typography className="text-sm text-gray-500">
                  {day.label}
                </Typography>
              </div>
            ))}
          </div>
        </div>
      )}

      <DatePicker.FormControl
        label="End Date (Optional)"
        name="endDate"
        value={(pattern.endDate || "") as any}
        onChange={(value) => updatePattern({
          endDate: formatDate(value, "yyyy-MM-dd"),
        })}
      />
    </div>
  );
};

export default RecurringPatternForm;
