import { AxiosResponse } from "axios";
import { Booking } from "../interfaces/booking";
import { pick } from "@/lib/helpers";
import { api } from "./api.service";
import { TService } from "./services.service";
import { QuestionType } from "../interfaces/intake-form";

export const GetBookings = async (): Promise<
  AxiosResponse<{
    success: true;
    bookings: Booking[];
  }>
> => {
  try {
    const response = await api.get(`/bookings`);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

export type ServiceOption = {
  name: string;
  description?: string;
  price: number;
  _id: string;
};

type Service = {
  _id: string;
  userId: string;
  name: string;
  description: string;
  messageAfterScheduling: string;
  duration: number;
  blockExtraTimeBefore: number;
  blockExtraTimeAfter: number;
  price: number;
  category: {
    _id: string;
    name: string;
  };
  color: string;
  picture: string | null;
  access: "public" | "private";
  isGroupEvent: boolean;
  directBookingLink: string;
  urlToken: string;
  serviceOptions?: Array<ServiceOption>;
};

type GetBusinessServicesResponse = {
  services: TService[],
  user: {
    businessName: string,
    firstName: string,
    lastName: string,
    billingSettings: {
      preferredCurrency: UserPreferredCurrency
    },
    isPremiumPlan: boolean
  }
}

/** 
 * @dev this is used to display an according of all the business' services to the client
 * */
export const GetBusinessServices = async (username: string) => {
  try {
    const response = await api.get<GetBusinessServicesResponse>(`/service/bookings/${username}`);
    return response
  } catch (error) {
    console.error("get-booking-services error:", error);
    throw error;
  }
};

type UserId = {
  _id: string;
  first_name: string;
  last_name: string;
  billingSettings: {
    preferredCurrency: UserPreferredCurrency
  },
  isStripeConnected: boolean;
  isPremiumPlan: boolean
};

export const GetExternalBookingService = async (
  { username, urlName }: { username: string, urlName: string }
) => {
  try {
    const response = await api.get<BookingServiceResponse>(`/service/bookings/external/${username}/${urlName}`);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

export type BookingServiceResponse = {
  success: true;
  Service: Service;
  userId: UserId;
}
export const GetInternalBookingService = async (
  { username, urlName }: { username: string, urlName: string }
) => {
  try {
    const response = await api.get<BookingServiceResponse>(`/service/bookings/internal/${username}/${urlName}`);
    return response;
  } catch (error) {
    console.error("internal booking fetch:", error);
    throw error;
  }
};

export interface BookingPayload {
  serviceId: string;
  userId: string;
  serviceName: string;
  date: string;
  timeSlot: string;
  notes: string;
  price: number | undefined;
  customerInfo: {
    lastName: string;
    firstName: string;
    email: string;
    phone: string;
  };
  serviceOptions?: string[] | null;
}

export interface ExternalBookingPayload extends BookingPayload {
  intakeFormResponses: Array<
    {
      "questionId": string,
      "question": string,
      "answer": string,
      "questionType": QuestionType,
      "required": boolean
    }
  >

}

export const CreateInternalBooking = async (
  payload: BookingPayload
) => {
  try {
    const response = await api.post<{
      success: boolean, 
      message: string
    }>(`/create-bookings/internal`, payload);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

export const CreateExternalBooking = async (
  payload: ExternalBookingPayload
) => {
  try {
    const response = await api.post<{
      success: boolean,
      message: string
    }>(`/create-bookings/external`, payload);
    return response;
  } catch (error) {
    console.error("Signup error:", error);
    throw error;
  }
};

export type WorkDays = Array<
  | "monday"
  | "tuesday"
  | "wednesday"
  | "thursday"
  | "friday"
  | "saturday"
  | "sunday"
>;

export const GetAvailableWorkDays = async (
  userId: string
): Promise<
  AxiosResponse<{
    workdays: WorkDays;
  }>
> => {
  try {
    const response = await api.get(`/get-workdays/${userId}`);
    return response;
  } catch (error) {
    console.error("get-workdays", error);
    throw error;
  }
};

export type GetTimeSlotParams = {
  userId: string;
  payload: {
    timezone: string;
    date: string; // Corrected type
  };
};

export type TimeSlots = Array<`${string}:${string}`>;

export const GetAvailableTimeslot = async ({
  userId,
  payload,
}: GetTimeSlotParams): Promise<
  AxiosResponse<{
    success: boolean;
    availableTimeSlots: TimeSlots;
  }>
> => {
  try {
    const response = await api.post(`/available-times/${userId}`, payload);
    return response;
  } catch (error) {
    console.error("get-time-slot", error);
    throw error;
  }
};

export type BookServicePaymentPayload = {
  clientEmail: string;
  serviceId: string;
  serviceOptions?: Array<string> | null;
};
export type BookServicePaymentResponse = {
  success: boolean;
  message: string;
  clientSecret: string;
};
export const InitiateServicePayment = async (
  payload: BookServicePaymentPayload
) => {
  try {
    const response = await api.post<BookServicePaymentResponse>(
      `/pay-service`,
      pick(payload, "clientEmail", "serviceId", 'serviceOptions')
    );
    return response;
  } catch (error) {
    console.error("book-service-payment-error", error);
    throw error;
  }
};

export const CompleteServicePayment = async (payload: {
  paymentIntentId: string;
}) => {
  try {
    const response = await api.post<{
      success: true;
      message: "Completed";
    }>(`/pay-service/success`, payload);
    return response;
  } catch (error) {
    console.error("book-service-payment-success", error);
    throw error;
  }
};
