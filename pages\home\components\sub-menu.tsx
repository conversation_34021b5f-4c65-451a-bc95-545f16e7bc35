import { Typography } from "@/components/typography";
import { AnimatePresence, motion } from "framer-motion";
import { Link, useLocation } from "react-router-dom";
import { variants } from "../constants";
import { MenuItem } from "../types";
import SubMenuTrails from "./sub-menu-trails";

type Props = {
  isOpen: boolean;
  closeSidebar: () => void;
  subItems: MenuItem[];
};

const SubMenu: React.FC<Props> = (props) => {
  const { pathname } = useLocation();
  const isActive = (link: string | undefined) => {
    return pathname === link;
  };
  return (
    <AnimatePresence>
      {props.isOpen ? (
        <motion.div
          initial={{ height: 0 }}
          animate={{ height: "auto" }}
          exit={{ height: 0 }}
          className="w-full pt-0 flex"
        >
          <SubMenuTrails branches={props.subItems.length} />
          <div className="h-full flex-grow flex flex-col gap-y-1.5 ">
            {props.subItems.map((item, index) => (
              <Link
                to={item.link || "#"}
                onClick={() => {
                  item.onClick?.();
                  props.closeSidebar();
                }}
                key={index}
              >
                <Typography
                  key={index}
                  variant={"p"}
                  className={`${
                    isActive(item.link) ? variants["open"] : variants["closed"]
                  } cursor-pointer pl-3 py-2.5 rounded-xl flex items-center gap-x-3 !mt-0 `}
                >
                  {item.icon}
                  {item.title}
                </Typography>
              </Link>
            ))}
          </div>
        </motion.div>
      ) : (
        ""
      )}
    </AnimatePresence>
  );
};

export default SubMenu;
