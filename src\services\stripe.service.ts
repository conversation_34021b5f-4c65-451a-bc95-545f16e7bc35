import { AxiosResponse } from "axios";
import { api } from "./api.service";

/**
 * @dev creates a setup intent and returns client secret, the client secret is then used
 */
export const CreateSetupIntent = async (): Promise<
	AxiosResponse<{
		message: string;
		clientSecret: string;
	}>
> => {
	try {
		const response = await api.get(`/setup-intent`);
		return response;
	} catch (error) {
		throw error;
	}
};

interface PaymentMethodPayload {
	paymentId: string;
	type: "default" | "others";
}

/**
 * @dev invalidates query for fetching payment methods.
 */
export const AddPaymentMethod = async (
	payload: PaymentMethodPayload,
): Promise<AxiosResponse> => {
	try {
		const response = await api.post(`/add-payment-method`, payload);
		return response;
	} catch (error) {
		console.error("add-payment-method:", error);
		throw error;
	}
};

export const DetachPaymentMethod = async (paymentMethodId: string) => {
	try {
		const response = await api.delete<{ paymentMethod: PaymentMethod }>(
			`/delete-payment-method/${paymentMethodId}`,
		);
		return response;
	} catch (error) {
		console.error("delete-payment-method error:", error);
	}
};

export interface SaveDefaultPaymentMethodPayload {
	userId: string;
	paymentId: string;
	type: string;
	provider?: string;
	paymentType?: string;
}

export const SaveDefaultPaymentMethod = async (
	payload: SaveDefaultPaymentMethodPayload,
): Promise<AxiosResponse> => {
	try {
		const response = await api.post(`/add-payment/on-signup`, payload);
		return response;
	} catch (error) {
		throw error;
	}
};

export const GetPaymentMethods = async (): Promise<
	AxiosResponse<{
		success: boolean;
		paymentMethods: PaymentMethod[];
	}>
> => {
	try {
		const response = await api.get(`/payment-method`);
		return response;
	} catch (error) {
		console.error("get-payment-method:", error);
		throw error;
	}
};
