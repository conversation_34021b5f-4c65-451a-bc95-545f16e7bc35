import { Button } from "@/components/buttons";
import BasicTooltip from "@/components/ui/basic-tooltip";
import PendingOverlay from "@/components/ui/pending-overlay";
import { Plus, X } from "lucide-react";
import { AdminNS } from "@/src/services/admin.service";
import { useAdminFaqApis } from "../use-admin";
import ErrorScreen from "@/components/ui/error-screen";
import { useModalsBuilder } from "@/lib/modals-builder";
import CreateFaqModal from "./create-faq-modal";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { Pencil } from "@gravity-ui/icons";
import Dialog from "@/components/ui/dialog";
import EditFaqModal from "./edit-faq-modal";

const Faq: React.FC<{
  faq: AdminNS.Faq;
}> = ({ faq }) => {
  const { deleteFaqMutation, editFaqMutation } = useAdminFaqApis();
  const { modals, modalFunctions } = useModalsBuilder({
    editFaq: {
      open: false,
      faqId: "",
      faqPayload: {
        question: "",
        answer: "",
        category: "",
      },
    },
    deleteFaq: {
      open: false,
      faqId: "",
    },
  });
  return (
    <div className="w-full h-fit flex gap-x-3 items-start">
      <PendingOverlay
        isPending={deleteFaqMutation.isPending || editFaqMutation.isPending}
      />
      <div className="flex-grow">
        <p className="w-full  flex justify-between items-center pt-2 pb-3 border-b-2 border-subtle-gray text-sm md:border-dark-gray">
          <span>{faq.question}</span>
        </p>
        <p className="mt-2 pl-4 text-gray-600 text-sm">{faq.answer}</p>
      </div>
      <div className="w-fit pt-1 flex flex-col gap-y-2 justify-end">
        <BasicTooltip content="Edit Faq">
          <div>
            <Button
              onTap={() => {
                modalFunctions.openModal("editFaq", {
                  faqId: faq._id,
                  faqPayload: {
                    question: faq.question,
                    answer: faq.answer,
                    category: faq.category || "Other",
                  },
                });
              }}
              variant="icon"
              className="rounded-full !p-[5px] bg-[#DFE1DB]/60 "
            >
              <Pencil className="text-gray-500 " width={14} height={14} />
            </Button>
          </div>
        </BasicTooltip>
        <BasicTooltip content="Delete Faq">
          <div>
            <Button
              onTap={() => {
                modalFunctions.openModal("deleteFaq", {
                  faqId: faq._id,
                });
              }}
              variant="icon"
              className="rounded-full !p-1 bg-[#DFE1DB]/60 "
            >
              <X className="text-gray-500 " size={15} />
            </Button>
          </div>
        </BasicTooltip>
      </div>
      <Dialog
        open={modals.deleteFaq.open}
        onClose={() => {
          modalFunctions.closeModal("deleteFaq", {
            faqId: "",
          });
        }}
        title="Delete Faq"
        description="Are you sure you want to delete this faq?"
        action={{
          title: "Delete",
          onConfirm: () => {
            deleteFaqMutation.mutateAsync(faq._id, {
              onSuccess: () => {
                modalFunctions.closeModal("deleteFaq", {
                  faqId: "",
                });
              },
            });
          },
        }}
      />
      <EditFaqModal
        open={modals.editFaq.open}
        values={{
          ...modals.editFaq.faqPayload,
        }}
        onClose={() => {
          modalFunctions.closeModal("editFaq", {
            faqId: "",
            faqPayload: {
              question: "",
              answer: "",
              category: "",
            },
          });
        }}
        onSubmit={() => {
          editFaqMutation.mutateAsync(
            {
              faqId: modals.editFaq.faqId,
              faqDetails: modals.editFaq.faqPayload,
            },
            {
              onSuccess() {
                modalFunctions.closeModal("editFaq", {
                  faqId: "",
                  faqPayload: {
                    question: "",
                    answer: "",
                    category: "",
                  },
                });
              },
            }
          );
        }}
        onChange={(name, value) => {
          modalFunctions.updateModalData("editFaq", {
            ...modals.editFaq,
            faqPayload: {
              ...modals.editFaq.faqPayload,
              [name]: value,
            },
          });
        }}
      />
    </div>
  );
};

export default function FaqPage() {
  const { faqs, createFaqMutation } = useAdminFaqApis();
  const { modals, modalFunctions } = useModalsBuilder({
    addFaq: {
      open: false,
      faqPayload: {
        question: "",
        answer: "",
        category: "",
      },
    },
  });

  return (
    <section className="shadow-none pb-20 w-full max-w-screen-2xl space-y-4 mx-auto ">
      <PendingOverlay isPending={createFaqMutation.isPending} />
      <div
        className={cn("w-full h-fit flex gap-x-7 gap-y-7 justify-end", {
          hidden: faqs.length === 0,
        })}
      >
        <motion.button
          onTap={() =>
            modalFunctions.openModal("addFaq", {
              faqPayload: {
                question: "",
                answer: "",
                category: "",
              },
            })
          }
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.9, rotate: 160 }}
          className="px-2 py-2 rounded-full  bg-stone-200 hover:bg-stone-300 text-sm"
        >
          <Plus width={20} height={20} className="pr-[1px] " />
        </motion.button>
      </div>
      <div className="w-full h-fit flex gap-x-7 gap-y-7 flex-wrap">
        {Object.entries(
          faqs.reduce((acc, faq) => {
            const category = faq.category || "Other";
            acc[category] = acc[category] || [];
            acc[category].push(faq);
            return acc;
          }, {} as Record<string, AdminNS.Faq[]>)
        ).map(([category, categoryFaqs]) => (
          <div
            key={category}
            className="min-w-fit w-full md:min-w-0 max-w-[360px] h-[480px] overflow-y-auto no-scrollbar border-2 px-4 py-3 rounded-2xl relative transition-[height] duration-500 ease-[ease] border-primary-500/50 "
          >
            <h2 className="text-2xl font-semibold pt-3 pb-6">{category}</h2>
            <div className="flex flex-wrap gap-x-7 gap-y-7">
              {categoryFaqs.map((faq, index) => (
                <Faq faq={faq} key={index} />
              ))}
            </div>
          </div>
        ))}
      </div>
      {faqs.length === 0 && (
        <div>
          <ErrorScreen
            className="shadow-md md:shadow-none max-w-[480px] mx-auto rounded-[32px] pb-6"
            message="Click the add button to create one"
            title="No faqs found"
            onRetry={{
              title: "Add Faq",
              action: () => {
                modalFunctions.openModal("addFaq", {
                  faqPayload: {
                    question: "",
                    answer: "",
                    category: "",
                  },
                });
              },
            }}
          />
        </div>
      )}
      <CreateFaqModal
        open={modals.addFaq.open}
        values={modals.addFaq.faqPayload}
        onClose={() => {
          modalFunctions.closeModal("addFaq", {
            faqPayload: {
              question: "",
              answer: "",
              category: "",
            },
          });
        }}
        onSubmit={() => {
          createFaqMutation.mutateAsync(modals.addFaq.faqPayload, {
            onSuccess() {
              modalFunctions.closeModal("addFaq", {
                faqPayload: {
                  question: "",
                  answer: "",
                  category: "",
                },
              });
            },
          });
        }}
        onChange={(name, value) => {
          modalFunctions.updateModalData("addFaq", {
            faqPayload: {
              ...modals.addFaq.faqPayload,
              [name]: value,
            },
          });
        }}
      />
    </section>
  );
}
