import { deduplicateElements, inlineSwitch, objectKeys, sleep } from "@/lib/helpers";

export type StringVariable = `{{${string}}}`

export type TemplateVariableMapType = Record<StringVariable, {
  name: string;
  description: string;
  example: string;
  category: string
}>

export const TEMPLATE_VARIABLES_MAP = {
  '{{firstName}}': {
    name: 'First Name',
    description: 'Client\'s first name',
    example: '<PERSON>',
    category: 'Personal'
  },
  '{{lastName}}': {
    name: 'Last Name',
    description: 'Client\'s last name',
    example: '<PERSON>',
    category: 'Personal'
  },
  '{{serviceName}}': {
    name: 'Appointment Type',
    description: 'Service or appointment type',
    example: 'Hair Braiding',
    category: 'Appointment'
  },
  '{{time}}': {
    name: 'Appointment Time',
    description: 'Scheduled time',
    example: '2:30pm',
    category: 'Appointment'
  },
  '{{date}}': {
    name: 'Appointment Date',
    description: 'Scheduled date',
    example: 'July 26, 2025',
    category: 'Appointment'
  },
  '{{duration}}': {
    name: 'Duration',
    description: 'Length of appointment',
    example: '60 minutes',
    category: 'Appointment'
  },
  '{{phone}}': {
    name: 'Phone Number',
    description: 'Client\'s phone number',
    example: '(*************',
    category: 'Appointment'
  },
  '{{email}}': {
    name: 'Email Address',
    description: 'Client\'s email address',
    example: '<EMAIL>',
    category: 'Personal'
  },
  '{{businessName}}': {
    name: 'Business Name',
    description: 'Your business\'s name',
    example: 'Nina\'s Hair Place',
    category: 'Appointment'
  },
  '{{location}}': {
    name: 'Location',
    description: 'Appointment location',
    example: '123 Main St',
    category: 'Appointment'
  },
  '{{rescheduleLink}}': {
    name: 'Reschedule Link',
    description: 'Link to reschedule the appointment',
    example: 'https://example.com/reschedule',
    category: 'Appointment'
  },
  '{{price}}': {
    name: 'Price',
    description: 'Appointment price',
    example: '$100',
    category: 'Appointment'
  },
  '{{deposit}}': {
    name: 'Deposit',
    description: 'Appointment deposit',
    example: '$50',
    category: 'Appointment'
  },
  '{{cancelLink}}': {
    name: 'Cancel Link',
    description: 'Link to cancel the appointment',
    example: 'https://example.com/cancel',
    category: 'Appointment'
  },
};

export const TEMPLATE_VARIABLES = objectKeys(TEMPLATE_VARIABLES_MAP)

export type TVariable = keyof typeof TEMPLATE_VARIABLES_MAP

export type TVariableMap = Record<TVariable, string>

export default class EmailTemplateVariableEngine {

  static SAMPLE_DATA: TVariableMap = {
    '{{firstName}}': 'Charles',
    '{{lastName}}': 'Okondu',
    '{{serviceName}}': 'Hair Braiding',
    '{{time}}': '4:30pm',
    '{{date}}': 'July 28, 2025',
    '{{duration}}': '45 minutes',
    '{{location}}': '123 Main St',
    '{{phone}}': '(*************',
    '{{email}}': '<EMAIL>',
    '{{businessName}}': 'Nina\'s Hair Palace',
    '{{rescheduleLink}}': 'https://example.com/reschedule',
    '{{cancelLink}}': 'https://example.com/cancel',
    '{{price}}': '$100',
    '{{deposit}}': '$50',
  }

  private static TEMPLATE_VARIABLE_REGEX = /{{(\w+)}}/g
  private static DEPRECATED_TEMPLATE_VARIABLE_REGEX = /%(\w+)%/g

  // Replace variables in template
  static replaceVariables(text: string, data: TVariableMap) {
    return text.replace(this.TEMPLATE_VARIABLE_REGEX, (match, key) => {
      const fullKey: TVariable = `{{${key}}}` as TVariable;
      return data[fullKey] || match;
    });
  }

  /**
   * @dev Insert variable into template at cursor position
   * @dev inputElement should be a controlled input
   */
  static async insertVariable(variable: TVariable, inputElement: HTMLInputElement | HTMLTextAreaElement, options: {
    onVariableNotIncluded?: (variable: TVariable) => void
    setText: (template: string) => void
  }) {
    if (!(variable in TEMPLATE_VARIABLES_MAP))
      return options?.onVariableNotIncluded?.(variable)
    const template = inputElement.value
    const start = inputElement.selectionStart || 0;
    const end = inputElement.selectionEnd || 0;
    const newText = template.substring(0, start) + variable + template.substring(end);
    options.setText(newText)
    await sleep(100)
    // Move cursor to end of inserted variable
    inputElement.setSelectionRange(
      start + variable.length,
      start + variable.length
    );
    // Keep focus on input
    inputElement.focus();
    return newText
  }

  // Copy variable to clipboard
  static copyVariable(variable: TVariable, onComplete?: VoidFunction) {
    navigator.clipboard.writeText(variable)
      .then(_ => onComplete?.())
  }

  /**
   * @dev validates a template with the internal TEMPLATE_VARIABLES and returns an array of errors
   * @dev validation for old template variable syntax
   * */
  static validateTemplate(template: string, where: 'subject' | 'body') {
    const usedTemplateVariables = deduplicateElements(
      template.match(this.TEMPLATE_VARIABLE_REGEX) || []
    ) as TVariable[]
    const unsupportedTemplateVariables = usedTemplateVariables.filter(tv => !(TEMPLATE_VARIABLES.includes(tv)))
    const deprecatedTemplateVaribales = deduplicateElements(
      template.match(this.DEPRECATED_TEMPLATE_VARIABLE_REGEX) || []
    )
    return [
      ...unsupportedTemplateVariables
        .map(tv =>
          `Unknown template variable "${tv}"${inlineSwitch(where,
            ['subject', ' in Email Subject'], { default: ' in Email Body' })}`
      ),
      ...deprecatedTemplateVaribales
        .map(tv =>
          `Deprecated template variable "${tv}"${inlineSwitch(where,
            ['subject', ' in Email Subject'], { default: ' in Email Body' })}. Use {{${tv.replace(/%/g, '')}}} instead `
      ),
    ]
  }

  static countVariables(template: string) {
    return (template.match(this.TEMPLATE_VARIABLE_REGEX) || []).length
  }
}

// const previewText = replaceVariables(template, SAMPLE_DATA);
