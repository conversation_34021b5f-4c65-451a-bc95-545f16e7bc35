import { useToast } from "~/contexts/hooks/toast";
import { Search } from "lucide-react";
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";
import { flushSync } from "react-dom";
import data from "../data/uk_cities.json";
import { Typography } from "@/components/typography";
import { Button } from "@/components/buttons";
interface Props {
  industryAndLocation: {
    industry: string;
    locations: string[];
  };
  setIndustryAndLocation: Dispatch<
    SetStateAction<{
      industry: string;
      locations: string[];
    }>
  >;
}

const SearchPage = ({ industryAndLocation, setIndustryAndLocation }: Props) => {
  const [isLocationDropdownOpen, setIsLocationDropdownOpen] = useState(true);
  const [industryAndLoc, setIndustryAndLoc] = useState(industryAndLocation);
  const [locationSearch, setLocationSearch] = useState("");
  const searchableLocations = data.cities.filter((city) =>
    city.toLowerCase().includes(locationSearch.toLowerCase())
  );
  const inputRef = useRef<HTMLInputElement>(null);
  const toggleLocation = (location: string) => {
    setIndustryAndLoc((prev) => {
      const locations = prev.locations.includes(location)
        ? prev.locations.filter((loc) => loc !== location)
        : [location];
      return {
        ...prev,
        locations,
      };
    });
    setIsLocationDropdownOpen(false);
  };
  const showToast = useToast();
  const firstFormSearch = () => {
    const { industry, locations } = industryAndLoc;
    if (!industry || locations.length === 0) {
      showToast("info", "Please select an industry and a location");
      return;
    }
    setIndustryAndLocation(industryAndLoc);
  };
  useEffect(() => {
    if (isLocationDropdownOpen) {
      inputRef.current?.focus();
    }
  }, [isLocationDropdownOpen]);

  return (
    <div className="min-h-fit w-full flex items-center justify-center ">
      {industryAndLocation.industry &&
        industryAndLocation.locations.length > 0 && (
          <form
            onSubmit={(e) => {
              e.preventDefault();
              const { industry } = industryAndLoc;
              if (!industry)
                return showToast("error", "Please select an industry");
              setIndustryAndLocation((p) => ({
                ...p,
                industry: industryAndLoc.industry,
              }));
            }}
            className="w-full relative "
          >
            <input
              type="text"
              value={industryAndLoc.industry}
              onChange={(e) =>
                flushSync(() =>
                  setIndustryAndLoc((p) => ({ ...p, industry: e.target.value }))
                )
              }
              placeholder="Search by industry (e.g., House cleaning, Web design)"
              className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
            />
            <button type="submit" className="absolute right-4 top-[30%] ">
              <Search size={20} className="stroke-primary " />
            </button>
          </form>
        )}
      {!(
        industryAndLocation.industry && industryAndLocation.locations.length > 0
      ) && (
        <div className="w-full max-w-2xl space-y-6">
          <Typography variant={'h1'} className="font-semibold font-Bricolage_Grotesque text-center mb-8">
            Find Leads
          </Typography>
          <div className="relative">
            <input
              type="text"
              value={industryAndLoc.industry}
              onChange={(e) =>
                setIndustryAndLoc((p) => ({ ...p, industry: e.target.value }))
              }
              placeholder="Search by industry (e.g., House cleaning, Web design)"
              className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
            />
          </div>

          <div className="relative">
            <div
              onClick={() => setIsLocationDropdownOpen(!isLocationDropdownOpen)}
              className="w-full px-4 py-3 rounded-xl border border-gray-200 cursor-pointer flex items-center justify-between"
            >
              <div className="flex flex-wrap gap-2">
                {industryAndLoc.locations.length === 0 ? (
                  <span className="text-gray-400">Select location</span>
                ) : (
                  industryAndLoc.locations.map((location) => (
                    <span
                      key={location}
                      className="bg-gray-100 px-2 py-1 rounded-lg text-sm"
                    >
                      {location}
                    </span>
                  ))
                )}
              </div>
            </div>

            {isLocationDropdownOpen && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg z-10">
                <div className="p-4">
                  <input
                    type="text"
                    value={locationSearch}
                    ref={inputRef}
                    onChange={(e) => setLocationSearch(e.target.value)}
                    placeholder="Search locations..."
                    className="w-full px-3 py-2 mb-3 rounded-lg border border-gray-200 focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary"
                  />
                  <h3 className="text-sm font-medium text-gray-500 mb-3">
                    Locations
                  </h3>
                  <div className="flex flex-wrap gap-2 max-h-[180px] overflow-y-auto no-scrollbar ">
                    {searchableLocations.map((location) => (
                      <Button
                        key={location}
                        onTap={() => toggleLocation(location)}
                        className={`px-3 py-1.5 rounded-lg text-sm transition-colors ${
                          industryAndLoc.locations.includes(location)
                            ? "bg-primary-500 text-white"
                            : "bg-gray-100 text-gray-700 hover:bg-gray-200 focus:bg-gray-200 focus:outline-none"
                        }`}
                      >
                        {location}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          <Button
            onTap={firstFormSearch}
            disabled={
              !industryAndLoc.industry && industryAndLoc.locations.length === 0
            }
            className="w-full flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed "
          >
            <Search size={20} />
            Search
          </Button>
        </div>
      )}
    </div>
  );
};

export default SearchPage;
