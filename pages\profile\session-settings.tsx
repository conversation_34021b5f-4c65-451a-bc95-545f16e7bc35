import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { formatDate } from "date-fns";
import ChromeBrowser from "@/assets/images/svg-logos/chrome-browser.svg";
import SafariBrowser from "@/assets/images/svg-logos/safari-browser.svg";
import { useState } from "react";
import { noop } from "@/lib/helpers";

const browserSvgIconMap = {
  chrome: ChromeBrowser,
  safari: SafariBrowser,
};

/**
 * @note this component takes care of it's own state, mutations to database.
 */
const SessionsSettings = () => {
  const [sessions, _setSessions] = useState([
    {
      browser: "chrome",
      ip: "***********",
      device: "Macbook Pro",
      date: new Date(),
    },
    {
      browser: "safari",
      ip: "***********",
      device: "Iphone",
      date: new Date("2024-05-15"),
    },
    {
      browser: "chrome",
      ip: "***********",
      device: "Windows NT",
      date: new Date("2023-12-01"),
    },
  ]);
  return (
    <section className="">
      <div className="mt-4 px-2 space-y-2">
        <div className="pb-3 ">
          <Typography className="text-gray-500 pb-5 rounded-xl ">
            This is a list of devices that have logged into your account. Remove
            those that you do not recognize.
          </Typography>
        </div>
        <div className="space-y-3">
          <Typography className="text-gray-500 ">Devices</Typography>
          <div className="">
            {sessions.map((session) => (
              <div className="border-t-2 border-gray-500/20 py-6 flex items-start gap-x-3 md:gap-x-5 ">
                <div className="bg-gray-200 w-14 h-14 rounded-xl grid place-content-center">
                  <img
                    src={
                      browserSvgIconMap[
                        (session.browser as keyof typeof browserSvgIconMap) ||
                          "chrome"
                      ]
                    }
                    alt=""
                    className="w-10 h-10"
                  />
                </div>
                <div className="-mt-1">
                  <Typography variant={"p"} className="font-bold pb-1">
                    <span className="capitalize">{session.browser}</span> on{" "}
                    {session.device}
                  </Typography>
                  <Typography className="font-medium text-sm">
                    {session.ip}
                  </Typography>
                  <Typography className="font-medium text-sm">
                    Signed in on {formatDate(session.date, "MMM d, yyy")}{" "}
                  </Typography>
                </div>
                <Button onTap={noop} variant="outline" className="ml-auto">
                  Revoke
                </Button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SessionsSettings;
