/* Acuity-style toolbar */
.ql-toolbar.ql-snow {
  background-color: #6b7280;
  border: none;
  border-radius: 4px 4px 0 0;
  padding: 8px;
}

.ql-toolbar .ql-stroke {
  stroke: #ffffff;
}

.ql-toolbar .ql-fill {
  fill: #ffffff;
}

.ql-toolbar button,
.ql-toolbar .ql-picker-label {
  color: #ffffff;
}

.ql-toolbar button:hover,
.ql-toolbar .ql-picker-label:hover,
.ql-toolbar button.ql-active {
  background-color: #4b5563;
  border-radius: 3px;
}

.ql-toolbar .ql-picker-options {
  background-color: #ffffff;
  border: 1px solid #d1d5db;
}

/* Editor container */
.ql-container.ql-snow {
  border: 1px solid #d1d5db;
  border-top: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Custom tooltip styling */
.ql-toolbar [title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: -35px;
  left: 50%;
  transform: translateX(-50%);
  background: #1f2937;
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}
