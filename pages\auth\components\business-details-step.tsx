import { Input } from "@/components/inputs";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import industryData from "@/src/data/all_industries.json";
import { motion } from "framer-motion";
import { StepProps } from "../types";

const BusinessDetailsStep: React.FC<MakeRequired<StepProps, "values">> = ({
  onChange,
  ...props
}) => {
  return (
    <motion.div
      initial={{ x: 300, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: -300, opacity: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
      className="w-full flex flex-col gap-y-4"
    >
      <Input.Text
        label="Business Name"
        name="business_name"
        value={props.values.business_name}
        required
        onChange={onChange}
      />
      <SearchableDropdown
        options={industryData.map((option) => ({
          label: option,
          value: option,
        }))}
        inputProps={{
          className: "py-3"
        }}
        fullWidth
        placeholder="Select an industry"
        value={{ label: props.values.industry, value: props.values.industry }}
        onChange={(value) => {
          if (!value) return;
          onChange({
            target: { name: "industry", value: value.value || "" },
          } as any);
        }}
      />
    </motion.div>
  );
};

export default BusinessDetailsStep;
