import { AddQuestionRequest, UpdateQuestionRequest } from "@/src/interfaces/intake-form";
import { createContext, useContext } from "react";

type FormQuestionMutationsContextType = {
  addQuestion(data: AddQuestionRequest): Promise<any>;
  updateQuestion(questionId: string, data: UpdateQuestionRequest): Promise<any>;
  deleteQuestion(questionId: string): void;
}

export const FormQuestionMutationsContext = createContext<FormQuestionMutationsContextType | undefined>(
  undefined
);

export const useFormQuestionMutations = () => {
  const context = useContext(FormQuestionMutationsContext);
  if (!context) {
    throw new Error("useFormQuestionMutations must be used within a FormQuestionMutationsContext");
  }
  return context;
};
