import { cn } from "@/lib/utils";
import { useEffect, useRef, useState } from "react";
import CheckB<PERSON> from "./checkbox";
import { Typography } from "../typography";
import { and, negate } from "@/lib/helpers";

export type Option<AdditionalData = {}> = {
  value: string;
  label: string;
  data?: AdditionalData;
};

interface RadioBoxProps<AdditionalData = {}, Value = Option<AdditionalData>> {
  options: Value[];
  label: string;
  value: Nullable<Value | string>;
  onChange: (option: Nullable<Value>) => void;
  className?: string;
  name?: string;
  required?: boolean;
  fullWidth?: boolean;
}

const RadioBoxImpl = <T = {},>({
  options,
  value,
  onChange,
  className = "",
  ...props
}: RadioBoxProps<T>) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const selectedValue = value
    ? (
      (typeof value === 'string')
      ? value
      : value.value
    )
    : ''
  function isChecked(opt: Option<T>) {
    return selectedValue === opt.value;
  }
  const [error, setError] = useState("");
  // <PERSON>le click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        if (and([!selectedValue, props.required]))
          // set error here
          setError("This field is required");
        else setError("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [value]);

  return (
    <div
      tabIndex={1}
      className="focus:outline-primary-600 flex flex-col select-none gap-y-3"
      ref={containerRef}
    >
      <label
        htmlFor={props.name}
        className={`font-medium w-full flex pl-3 text-sm cursor-pointer text-subtext`}
      >
        {error ? <span className={"text-red-800"}>{error}</span> : props.label}
        <span
          className={cn("text-red-800", props.required ? "block" : "hidden")}
        >
          *
        </span>
      </label>
      <div
        className={cn(
          `relative flex gap-3 flex-wrap items-center `,
          className,
          {
            "w-full": props.fullWidth,
          },
        )}
      >
        {options.map((opt, index) => (
          <div key={index} className="relative flex items-center gap-x-2">
            <CheckBox
              checked={isChecked(opt)}
              onChange={() => {
                onChange(opt);
              }}
            />
            <Typography className="text-sm text-gray-500">
              {opt.label}
            </Typography>
          </div>
        ))}
      </div>
    </div>
  );
};

interface RadioBoxMultipleProps<
  AdditionalData = {},
  Values = Option<AdditionalData | null>[],
> extends Omit<RadioBoxProps<AdditionalData>, "value" | "onChange"> {
  values: Values;
  onChange: (option: Values) => void;
}

/**
 * @dev a radio box but with multiple options and rounded corners
 * */
const RadioBoxMultiple = <T = {},>({
  options,
  values,
  onChange,
  className = "",
  ...props
}: RadioBoxMultipleProps<T>) => {
  function isChecked(opt: Option<T>) {
    return Boolean(
      values.find((value) =>
        and([value.label === opt.label, value.value === opt.value]),
      ),
    );
  }
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState("");
  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        if (and([props.required, values.length === 0]))
          // set error here
          setError("This field is required");
        else setError("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [values]);

  return (
    <div
      tabIndex={1}
      className="focus:outline-primary-600 flex flex-col select-none gap-y-3"
      ref={containerRef}
    >
      <label
        htmlFor={props.name}
        className={`font-medium w-full flex pl-3 text-sm cursor-pointer text-subtext`}
      >
        {error ? <span className={"text-red-800"}>{error}</span> : props.label}
        <span
          className={cn("text-red-800", props.required ? "block" : "hidden")}
        >
          *
        </span>
      </label>
      <div
        tabIndex={1}
        className={cn(
          `relative flex gap-3 flex-wrap items-center `,
          className,
          {
            "w-full": props.fullWidth,
          },
        )}
      >
        {options.map((opt, index) => (
          <div key={index} className="relative flex items-center gap-x-2">
            <CheckBox
              className="!rounded-lg"
              checked={isChecked(opt)}
              onChange={() => {
                if (isChecked(opt))
                  onChange(
                    values.filter((_) =>
                      negate(
                        and([_.label === opt.label, _.value === opt.value]),
                      ),
                    ),
                  );
                else onChange([...values, opt]);
              }}
            />
            <Typography className="text-sm text-gray-500">
              {opt.label}
            </Typography>
          </div>
        ))}
      </div>
    </div>
  );
};

const RadioBox = Object.assign(RadioBoxImpl, {
  Multiple: RadioBoxMultiple,
});

export default RadioBox;
