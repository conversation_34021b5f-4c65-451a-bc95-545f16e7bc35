import { But<PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import { capitalizeWords } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import LongArrow from "@/src/icons/long-arrow";
import { SignupUser } from "@/src/services/auth.service";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { AnimatePresence, motion } from "framer-motion";
import React, { useState } from "react";
import { Link } from "react-router-dom";
import { object, string } from "yup";
import { useToast } from "~/contexts/hooks/toast";
import { useAuth } from "./auth-context";
import BusinessDetailsStep from "./business-details-step";
import EmailVerificationScreen from "./email-verification-screen";
import LocationInfoStep from "./location-info-step";
import PersonalInfoStep from "./personal-info-step";
import Logo from '@/assets/logo.png'

type MultiStepFormProps = {};
// show steps
const signupValidationSchema = object({
  business_name: string().required("Business name is required"),
  industry: string().required("Industry is required"),
  first_name: string().required("First name is required"),
  last_name: string().required("Last name is required"),
  email: string().required("Email is required"),
  phone: string().required("Phone number is required"),
  password: string()
    .required("Password is required")
    .matches(
      /[A-Za-z\d@$!%*?&]{8,}/,
      "Must Match 8+ characters from this consisting of lowercase, uppercase and special characters",
    )

    .matches(/(?=.*\d)/, "Must contain at least one number")
    .matches(/^(?=.*[A-Z])/, "Must contain at least one uppercase letter")
    .matches(/^(?=.*[a-z])/, "Must contain at least one lowercase letter")
    .matches(/(?=.*[@$!%*?&])/, "Must contain at least one special character"),
  country: string().required("Country is required"),
  username: string().required("Username is required"),
  address: string().required("Address is required"),
  address_2: string(),
  town: string().required("Town is required"),
  postcode: string().required("Postcode is required"),
});

type FormStep = 1 | 2 | 3 | 4;

/**
 * @note they put in all details, they verify email, they try to login, pay, and then use the software
 * @dev the last step will be reached when there is success for the user
 * @todo change all input fields here
 */
const MultiStepForm: React.FC<MultiStepFormProps> = (_props) => {
  const MAX_STEP = 3,
    EMAIL_VERIFICATION_STEP = 4;
  const [step, setStep] = useState<Steps<typeof EMAIL_VERIFICATION_STEP>>(1);
  const formik = useFormik({
    initialValues: {
      business_name: "",
      industry: "",
      first_name: "",
      last_name: "",
      email: "",
      phone: "",
      password: "",
      username: "",
      address: "",
      country: "",
      address_2: "",
      town: "",
      postcode: "",
    },
    validationSchema: signupValidationSchema,
    onSubmit(_values) { },
  });
  const { setClientSecret, plan, setPlan } = useAuth();
  const showToast = useToast();
  const { isPending, mutateAsync } = useMutation({
    mutationFn: SignupUser,
    onSuccess: (data) => {
      setClientSecret(data.data.clientSecret);
      // user id set here oh
      localStorage.setItem("___user___", data.data.userId);
      setStep(EMAIL_VERIFICATION_STEP);
    },
    onError: (error: ApiError) => {
      switch (true) {
        case error.message.includes("Network"):
          showToast("error", "Network Error", {
            description: "Please check your internet connection and try again.",
            duration: 7000,
          });
          break;
        case error.status === 400:
          showToast("error", "Signup Error", {
            description:
              error.response?.data?.error ||
              "An error occurred while signing up. Please try again later.",
            duration: 7000,
          });
          break;
        default:
          showToast("error", "Signup Error", {
            description:
              "An error occurred while signing up. Please try again later.",
            duration: 7000,
          });
          break;
      }
    },
  });
  type NameValuePair = {
    name: string;
    value: string;
  };
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement> | NameValuePair,
  ): void => {
    const E_NAMEVALUEPAIR = e as NameValuePair;
    const E_CHANGE_EVENT = e as React.ChangeEvent<HTMLInputElement>;
    if (E_NAMEVALUEPAIR.name) {
      const { name, value } = E_NAMEVALUEPAIR;
      formik.setFieldValue(name, value);
    } else if (E_CHANGE_EVENT.target) {
      const { name, value } = E_CHANGE_EVENT.target;
      formik.setFieldValue(name, value);
    }
  };
  const handleNext = (): void => {
    if (step === MAX_STEP) handleSubmit();
    else setStep((prev) => (prev + 1) as FormStep);
  };
  const handleBack = (): void => {
    if (step === 1) {
      // user want to select another plan
      setPlan(null);
      return;
    }
    setStep((prev) => (prev - 1) as FormStep);
  };

  const handleSubmit = (): void => {
    formik.validateForm().then((val) => {
      const fields = Object.keys(val);
      if (fields.length === 0) {
        mutateAsync({
          ...formik.values,
          planId: plan!._id,
          planInterval: plan!.selectedInterval,
        });
      } else
        showToast("error", "All fields are required", {
          description: `${fields
            .map((field) =>
              capitalizeWords(field.replace(/[^a-zA-Z0-9]/g, " ")),
            )
            .join(", ")} are required `,
          duration: 10000,
        });
    });
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <BusinessDetailsStep
            values={formik.values}
            onChange={handleInputChange}
          />
        );
      case 2:
        return (
          <PersonalInfoStep
            onChange={handleInputChange}
            values={formik.values}
          />
        );
      case 3:
        return (
          <LocationInfoStep
            values={formik.values}
            onChange={handleInputChange}
          />
        );
      default:
        return null;
    }
  };

  // since email verification step is last we render it as the whole page
  if (step === EMAIL_VERIFICATION_STEP) return <EmailVerificationScreen emailAddress={formik.values.email} />
  return (
    <section className="w-screen h-screen md:min-w-[400px] overflow-y-auto flex flex-col thin-scrollbar ">
      <div className="w-full flex flex-col flex-grow pt-10 items-center gap-y-3 justify-center">
        <img src={Logo} className="w-full size-28 object-contain  " />
        <Typography variant={'h3'} className="font-bold text-3xl text-center">
          Puzzle Piece <br /> Solutions
        </Typography>
      </div>

      <form
        onSubmit={(e) => {
          e.preventDefault();
          handleNext();
        }}
        className="w-full py-5 px-4 bottom-0 gap-y-10 flex-grow flex flex-col md:px-6"
      >
        <AnimatePresence mode="wait">{renderStep()}</AnimatePresence>
        <div className="w-full flex items-center justify-between">
          <Button
            type="button"
            onTap={handleBack}
            style={{
              width: 64,
              height: 64,
            }}
            className="!rounded-full !px-0 !py-0 flex items-center justify-center"
          >
            <LongArrow fill="white" className={cn("-rotate-180")} />
          </Button>
          {/* submit works on this button but internally calls handle next  */}
          <Button
            type="submit"
            variant="icon"
            className={cn(
              "flex items-center justify-center !px-0 py-0 !rounded-full w-[128px] h-[48px] ",
            )}
          >
            <motion.span
              className={cn(" ", {
                hidden: isPending,
              })}
            >
              {step === MAX_STEP ? 'Sign up' : 'Next'}
            </motion.span>
            <CircularLoader
              color="white"
              className={cn({
                hidden: !isPending,
                block: isPending,
              })}
            />
          </Button>
        </div>
        <div className="w-full flex items-center justify-center !mt-auto">
          <Typography variant={"span"} className="text-subtext text-sm ">
            Have an account?{" "}
            <Link className="text-primary-600 underline" to={"/signin"}>
              Sign in
            </Link>{" "}
          </Typography>
        </div>
      </form>
    </section>
  );
};

export default MultiStepForm;
