import { TClient } from "~/services/clients.service";
import { X } from "lucide-react";
import * as React from "react";
import Modal from "@/components/ui/modal";
import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { Input } from "@/components/inputs";

export type NewClient = Omit<TClient, "_id">;

type Props = {
  open: boolean;
  onClose: () => void;
  onSave: (props: NewClient) => void;
};

export default function AddClientModal({ open, onClose, onSave }: Props) {
  const [newClient, setNewClient] = React.useState<NewClient>({
    clientName: "",
    clientEmail: "",
    phoneNumber: "",
    lastAppointmentDate: null,
  });

  return (
    <Modal open={open} onClose={onClose}>
      <Modal.Body
        className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Add Client
          </Typography>
          <Button
            variant="icon"
            onTap={onClose}
            className="p-1 !rounded-full hover:bg-gray-100"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="py-6 w-full space-y-5">
          <Input.Text
            label="Client Name"
            name="client_name"
            value={newClient.clientName}
            onChange={(e) => {
              setNewClient({ ...newClient, clientName: e.target.value });
            }}
          />
          <Input.Email
            label="Email Address"
            name="client_email"
            value={newClient.clientEmail}
            onChange={(e) => {
              setNewClient({ ...newClient, clientEmail: e.target.value });
            }}
          />
          <Input.Phone
            label="Phone Number"
            name="client_phone"
            value={newClient.phoneNumber}
            onChange={(value) => {
              setNewClient({ ...newClient, phoneNumber: value });
            }}
          />
        </div>
        <div className="w-full flex justify-between items-center mt-6">
          <Button
            onTap={() => {
              onSave(newClient);
              onClose();
              setNewClient({
                clientName: "",
                clientEmail: "",
                phoneNumber: "",
                lastAppointmentDate: null,
              });
            }}
            className="w-full"
          >
            Save
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
}
