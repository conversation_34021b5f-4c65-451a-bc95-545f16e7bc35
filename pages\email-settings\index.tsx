import { Typography } from "@/components/typography";
import { Outlet, useLocation } from "react-router-dom";
import InitialConfirmation from "./initial-confirmation";
import RemindersPage from "./reminders";
import FollowUpPage from "./follow-ups";
import { last } from "@/lib/helpers";

export const emailSettingsLinksMap = {
  'initial-confirmation': { title: 'Initial Confirmation', desc: "An Initial Confirmation email is sent to you and your clients immediately after an appointment is scheduled." },
  'follow-ups': { title: 'Follow-ups', desc: "Send automatic follow-up emails after appointments to thank clients or remind them to book their next appointment." },
  'reminders': { title: "Reminders", desc: "Up to 2 Reminder emails can be sent to your clients (and optionally admins) prior to the appointment." }
} as Record<string, { title: string, desc: string }>

function findSettingName(str: string) {
  // remove trailing slash
  const trimmedPath = last(str) === '/' ? str.slice(0, str.length) : str;
  const lastIndexOfSlash = trimmedPath.lastIndexOf('/')
  return trimmedPath.slice(lastIndexOfSlash + 1)
}

const EmailSettingsLayout = () => {
  const { pathname } = useLocation()
  const setting = emailSettingsLinksMap[findSettingName(pathname)]
  return (
    <section className="w-full h-fit flex flex-col flex-grow mb-10 ">
      <div className="w-full flex flex-col gap-y-5 px-2 md:gap-y-8 md:px-3 ">
        <Typography variant={"h1"} className="font-Bricolage_Grotesque">
          {setting?.title}
        </Typography>
        <Typography variant={'p'} className="!mt-0text-base font-semibold">
          {setting?.desc}
        </Typography>
      </div>
      <Outlet />
    </section>
  );
};

/** 
 * @dev the links for these are here;
 * */
export const emailSettingsLinks = [
  { path: 'initial-confirmation', element: <InitialConfirmation /> },
  { path: 'reminders', element: <RemindersPage /> },
  { path: 'follow-ups', element: <FollowUpPage /> }
] as const 

export default EmailSettingsLayout;
