import { cn } from "@/lib/utils";
import { AnimatePresence, HTMLMotionProps, motion } from "framer-motion";
import React, { PropsWithChildren, useEffect, useState } from "react";
import CircularLoader from "./ui/circular-loader";
import { createPortal } from "react-dom";
import { inlineSwitch, pick } from "@/lib/helpers";

type Variants = "full" | "outline" | "icon" | "ghost" | "dormant";

type ButtonProps = PropsWithChildren<HTMLMotionProps<"button">> & {
  variant?: Variants;
  loading?: boolean;
};

/**
 * @animated with motion.button
 */
export const Button: React.FC<ButtonProps> = ({
  children,
  variant = "full",
  className,
  onTap,
  loading,
  ...props
}) => {
  return (
    <motion.button
      whileTap={{ scale: props.disabled ? 1 : 0.9 }}
      whileHover={{ scale: props.disabled ? 1 : 1.05 }}
      {...props}
      onTap={
        onTap
          ? (e, info) => {
              if (props.disabled) return;
              else onTap(e, info);
            }
          : undefined
      }
      className={cn(
        `font-semibold rounded-xl  `,
        { "bg-primary-500 text-white": variant === "full" },
        {
          "border-2 border-primary-500 text-primary-400 ":
            variant === "outline",
        },
        { "text-primary-500": variant === "ghost" },
        { "text-paragraph bg-stone-200 ": variant === "dormant" },
        { "px-4 py-2 text-sm": variant !== "icon" },
        { "bg-primary-500 text-white px-2 py-2": variant === "icon" },
        { "cursor-not-allowed opacity-50": props.disabled },
        { "flex items-center gap-x-3 justify-center ": loading },
        className,
      )}
    >
      {children}
      {loading && (
        <CircularLoader
        size={20}
          color={inlineSwitch(
            variant,
            ["dormant", "#2f2f2f"],
            ["ghost", "#2f2f2f"],
            ["outline", "#e91e63"],
            {
              default: "white" as any,
            },
          )}
        />
      )}
    </motion.button>
  );
};

type SaveButtonProps = {
  loading?: boolean;
  onTap?: ButtonProps["onTap"];
  title?: "Save" | (string & {});
  disabled?: boolean;
  /**
   * @dev this is to show the save button only after a change has been made on the page
   * */
  hasChanges?: boolean;
};

export const SaveButton: React.FC<SaveButtonProps> = ({
  title,
  onTap,
  ...props
}) => {
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);
  const isMobile = innerWidth <= 480;
  const container = (
    <motion.div
      initial={isMobile ? { y: 100 } : { y: -100 }}
      animate={
        isMobile
          ? { y: 0, transition: { delay: 0.2 } }
          : { y: 0, transition: { delay: 0.2 } }
      }
      exit={isMobile ? { y: 100 } : { y: -100 }}
      className={cn(
        "bg-white py-5 px-5 fixed z-[400000] left-0 w-screen",
        // mobile screen
        "bottom-0 border-t-2 border-subtle-gray",
        "md:static md:border-0 md:w-full md:flex md:justify-end md:py-3 md:px-2 md:border-b-2 md:border-l-2 lg:md:!border-l-white md:border-[#f2f2f2]  ",
      )}
    >
      <Button
        onTap={onTap}
        className={cn(
          "w-full rounded-full",
          "md:min-w-[140px] md:rounded-xl md:w-fit md:!py-1.5 ",
        )}
        disabled={props.disabled}
        type="button"
        loading={props.loading}
        variant={innerWidth <= 480 ? "full" : "dormant"}
      >
        {title || "Save Changes"}
      </Button>
    </motion.div>
  );
  // if has changes was passed or hasChanges render
  if (
    [undefined, null].includes(props.hasChanges as any) ||
    props.hasChanges === true
  ) {
    if (innerWidth <= 480) return container;
    if (!mounted) return null;
    return createPortal(
      <AnimatePresence>{container}</AnimatePresence>,
      document.querySelector<HTMLDivElement>(
        "div#global_save_button_container",
      )!,
    );
  } else return null;
};

export function useSaveButton() {
  const [hasChanges, setHasChanges] = useState(false);
  return {
    hasChanges,
    setHasChanges,
    SaveButton: (props: Omit<SaveButtonProps, "hasChanges">) => (
      <SaveButton {...props} hasChanges={hasChanges} />
    ),
  };
}

/**
 * @dev defines props for cancel and save button group for mobile responsiveness
 */
type ActionButtonGroupProps = {
  /**
   * @dev cancel button with `Button` props
   * @buttonvariant `dormant`
   * @see {@link Button}
   */
  cancel: {
    loading?: boolean;
    onTap?: ButtonProps["onTap"];
    title?: "Cancel" | (string & {});
    disabled?: boolean;
  };
  /**
   * @dev continue|save|next button with `Button` props
   * @buttonvariant `full`
   * @see {@link Button}
   */
  next: {
    loading?: boolean;
    onTap?: ButtonProps["onTap"];
    title?: "Save" | (string & {});
    disabled?: boolean;
    /**
     * @dev this is to show the action button only after a change has been made on the page
     * */
    hasChanges?: boolean;
  }
};

/**
 * @dev button group for cancel and next buttons (save button type)
 */
export const ActionButtonGroup: React.FC<ActionButtonGroupProps> = ({
  cancel: cancelProps,
  next: nextProps
}) => {
  const [mounted, setMounted] = useState(false);
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);
  const isMobile = innerWidth <= 480;
  const container = (
    <motion.div
      initial={isMobile ? { y: 100 } : { y: -100 }}
      animate={
        isMobile
          ? { y: 0, transition: { delay: 0.2 } }
          : { y: 0, transition: { delay: 0.2 } }
      }
      exit={isMobile ? { y: 100 } : { y: -100 }}
      className={cn(
        "bg-white py-5 px-5 fixed z-[400000] left-0 w-screen",
        // mobile screen
        "bottom-0 border-t-2 border-subtle-gray flex items-center gap-x-3",
        "md:static md:border-0 md:w-full md:flex md:items-center md:gap-x-3 md:justify-end md:py-3 md:px-2 md:border-b-2 md:border-l-2 lg:md:!border-l-white md:border-[#f2f2f2]  ",
      )}
    >
      <Button
        {...(pick(cancelProps,
          'disabled', 'loading', 'onTap'
        ))}
        className={cn(
          "w-full rounded-full ",
          "md:min-w-[140px] md:rounded-xl md:w-fit md:!py-1.5 ",
        )}
        type="button"
        variant={"dormant"}
      >
        {cancelProps.title || "Cancel"}
      </Button>
      <Button
        {...(pick(nextProps,
          'disabled', 'loading', 'onTap'
        ))}
        className={cn(
          "w-full rounded-full",
          "md:min-w-[140px] md:rounded-xl md:w-fit md:!py-1.5 ",
        )}
        type="button"
        variant={'full'}
      >
        {nextProps.title || "Save"}
      </Button>
    </motion.div>
  );
  // show actions buttons only when hasChanges is strictly `false`
  if (
    [undefined, null].includes(nextProps.hasChanges as any) ||
    nextProps.hasChanges === true
  ) {
    if (innerWidth <= 480) return container;
    if (!mounted) return null;
    return createPortal(
      <AnimatePresence>{container}</AnimatePresence>,
      document.querySelector<HTMLDivElement>(
        "div#global_save_button_container",
      )!,
    );
  } else return null;
};
