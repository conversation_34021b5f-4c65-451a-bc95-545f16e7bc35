import { useToast } from "@/src/contexts/hooks/toast";
import React, { FC, useEffect, useRef, useState } from "react";
import { motion } from "framer-motion";
import { Camera, Picture } from "@gravity-ui/icons";
import { ErrorMatcher } from "@/lib/error-matcher";
import { RevertError } from "@/lib/errors";

type Props = {
  onImageAddition: (file: Blob) => Promise<void>;
  image: File | null;
};

const AddBlogImage: FC<Props> = ({ onImageAddition, image }) => {
  const [imageUrl, setImageUrl] = useState<string | null>(
    image ? URL.createObjectURL(image) : null
  );
  const fileInputRef = useRef<HTMLInputElement>(null);
  const showToast = useToast();
  const handleImageAddition = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      setImageUrl(URL.createObjectURL(file as Blob));
      try {
        await onImageAddition(file);
      } catch (error) {
        ErrorMatcher.use(error).match(RevertError, (error) => {
          setImageUrl(null);
          showToast("info", error.message);
        });
      }
    }
  };
  useEffect(() => {
    if (image) setImageUrl(URL.createObjectURL(image));
    else setImageUrl(null);
  }, [image]);
  return (
    <div className="relative ">
      <div
        onClick={() => fileInputRef.current?.click()}
        className="w-full aspect-[5/2] rounded-[inherit] cursor-pointer bg-dark-gray/20 flex items-center justify-center text-white font-medium"
      >
        {imageUrl ? (
          <img
            src={imageUrl}
            alt="Uploaded Image"
            className="w-full h-full object-cover "
          />
        ) : (
          <Picture width={64} height={64} />
        )}
      </div>
      <motion.button
        whileTap={{ scale: 0.9 }}
        onTap={() => fileInputRef.current?.click()}
        className="absolute bottom-1 right-1 bg-white rounded-full p-1.5 shadow-sm"
      >
        <Camera width={16} height={16} />
      </motion.button>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg, image/png, image/gif"
        onChange={handleImageAddition}
        className="hidden"
      />
    </div>
  );
};

export default AddBlogImage;
