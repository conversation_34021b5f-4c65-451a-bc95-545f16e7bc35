import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import Modal, { BaseModalProps } from "@/components/ui/modal";
import { noop, pick } from "@/lib/helpers";
import { useToast } from "@/src/contexts/hooks/toast";
import { useFormik } from "formik";
import { X } from "lucide-react";
import { object, string } from "yup";
type Props = BaseModalProps & {
  link: {
    title: string;
    url: string;
  };
  onSave: (data: { title: string; url: string }) => void;
};

export default function LinkChangeModal(props: Props) {
  const formik = useFormik({
    initialValues: props.link,
    enableReinitialize: true,
    validationSchema: object({
      title: string().required("Title is required"),
      url: string().required("URL is required"),
    }),
    onSubmit: noop
  });
  const showToast = useToast();

  return (
    <Modal {...pick(props, 'onClose', 'open')} >
      <Modal.Body
        className={`min-h-fit min-w-80 h-fit w-fit flex flex-col gap-y-7 items-center justify-center rounded-[32px] bg-white `}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h4"} className="font-bold ">
            Edit Link
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full "
          >
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="space-y-4 w-full">
          <Input.Text
            label="Title"
            name="title"
            required
            value={formik.values.title}
            onChange={formik.handleChange}
            className="mb-4"
          />
          <Input.Text
            label="Link Url"
            name="url"
            required
            value={formik.values.url}
            onChange={formik.handleChange}
            className="mb-4"
          />
          <Button
            type="button"
            onTap={() => {
              formik.validateForm().then((val) => {
                const errors = Object.values(val);
                if (errors.length === 0) {
                  props.onSave(pick(formik.values, 'url', 'title'))
                  formik.resetForm()
                } else showToast("error", errors[0]);
              });
            }}
            variant="full"
            className="w-full py-3 !rounded-full"
          >
            Save
          </Button>
        </div>
      </Modal.Body>
    </Modal>
  );
}
