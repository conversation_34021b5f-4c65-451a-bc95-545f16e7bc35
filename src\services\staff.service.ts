import { api } from "./api.service";
import {
  Staff,
  CreateStaffPayload,
  UpdateStaffPayload,
  StaffId,
  GetStaffAvailabilityParams,
  AssignStaffToServicePayload,
  UpdateStaffUserPayload,
  CreatePTOPayload,
  PTORequest,
  StaffAvailability,
  StaffUser,
} from "../interfaces/staff";
import { TeamId } from "../interfaces/team";
import { LocationId } from "../interfaces/location";

export const GetStaff = async (teamId?: TeamId) => {
  const response = await api.get<{
    success: true;
    staff: Staff[];
  }>("/staff", { params: teamId ? { teamId } : undefined });
  return response;
};

export const GetStaffById = async (staffId: StaffId) => {
  const response = await api.get<{
    success: true;
    staff: Staff;
  }>(`/staff/${staffId}`);
  return response;
};

export const CreateStaffUser = async (payload: CreateStaffPayload) => {
  const response = await api.post<{
    success: true;
    staff: Staff;
    user: {
      _id: string;
      username: string;
      email: string;
      role: string;
    };
    limits: {
      currentCount: number;
      maxAllowed: number;
      plan: string;
    };
  }>("/staff/", payload);
  return response;
};

export const UpdateStaff = async (
  staffId: StaffId,
  payload: UpdateStaffPayload
) => {
  const response = await api.put<{
    success: true;
    message: string;
    staff: Staff;
  }>(`/staff/${staffId}`, payload);
  return response;
};

export const DeleteStaff = async (staffId: StaffId) => {
  const response = await api.delete<{
    success: true;
    message: string;
  }>(`/staff/${staffId}`);
  return response;
};

export const ActivateStaff = async (staffId: StaffId) => {
  const response = await api.put<{
    success: true;
    message: string;
  }>(`/staff/${staffId}/active`);
  return response;
};

export const DeactivateStaff = async (staffId: StaffId) => {
  const response = await api.put<{
    success: true;
    message: string;
  }>(`/staff/${staffId}/deactivate`);
  return response;
};

// Staff availability and calendar
export const GetStaffAvailability = async (params: GetStaffAvailabilityParams) => {
  const response = await api.get<{
    success: true;
    availability: StaffAvailability[];
  }>("/staff/availability", { params });
  return response;
};

export const GetEnhancedStaffAvailability = async (params: GetStaffAvailabilityParams) => {
  const response = await api.get<{
    success: true;
    availability: StaffAvailability[];
  }>("/staff/enhanced-availability", { params });
  return response;
};

export const GetStaffCalendar = async (params: { staffId?: StaffId; startDate: string; endDate: string }) => {
  const response = await api.get<{
    success: true;
    calendar: any[];
  }>("/staff/calendar", { params });
  return response;
};

// Staff assignment
export const AssignStaffToService = async (payload: AssignStaffToServicePayload) => {
  const response = await api.post<{
    success: true;
    message: string;
  }>("/staff/assign-to-service", payload);
  return response;
};

export const AssignStaffToLocations = async (staffId: StaffId, payload: { locationIds: LocationId[] }) => {
  const response = await api.put<{
    success: true;
    message: string;
  }>(`/staff/${staffId}/locations`, payload);
  return response;
};

export const GetStaffLocations = async (staffId: StaffId) => {
  const response = await api.get<{
    success: true;
    locations: any[];
  }>(`/staff/locations/${staffId}`);
  return response;
};

export const GetStaffByLocation = async (locationId: LocationId) => {
  const response = await api.get<{
    success: true;
    staff: Staff[];
  }>(`/staff/by-location/${locationId}`);
  return response;
};

// Staff user management
export const GetStaffUsers = async () => {
  const response = await api.get<{
    success: true;
    staffUsers: StaffUser[];
  }>("/staff/users");
  return response;
};

export const UpdateStaffUser = async (staffUserId: string, payload: UpdateStaffUserPayload) => {
  const response = await api.put<{
    success: true;
    message: string;
    staffUser: StaffUser;
  }>(`/staff/users/${staffUserId}`, payload);
  return response;
};

export const ResetStaffPassword = async (staffUserId: string, payload: { newPassword: string }) => {
  const response = await api.post<{
    success: true;
    message: string;
  }>(`/staff/users/${staffUserId}/reset-password`, payload);
  return response;
};

export const DeactivateStaffUser = async (staffUserId: string) => {
  const response = await api.delete<{
    success: true;
    message: string;
  }>(`/staff/users/${staffUserId}`);
  return response;
};

export const GetStaffUserProfile = async () => {
  const response = await api.get<{
    success: true;
    profile: StaffUser;
  }>("/staff/profile");
  return response;
};

// PTO/Time-off management
export const CreatePTORequest = async (payload: CreatePTOPayload) => {
  const response = await api.post<{
    success: true;
    ptoRequest: PTORequest;
  }>("/staff/pto", payload);
  return response;
};

export const GetStaffPTO = async (staffId: StaffId, params?: { startDate?: string; endDate?: string }) => {
  const response = await api.get<{
    success: true;
    ptoRequests: PTORequest[];
  }>(`/staff/${staffId}/pto`, { params });
  return response;
};

export const UpdatePTORequest = async (ptoId: string, payload: Partial<CreatePTOPayload>) => {
  const response = await api.put<{
    success: true;
    message: string;
    ptoRequest: PTORequest;
  }>(`/staff/pto/${ptoId}`, payload);
  return response;
};

export const DeletePTORequest = async (ptoId: string) => {
  const response = await api.delete<{
    success: true;
    message: string;
  }>(`/staff/pto/${ptoId}`);
  return response;
};

export const ApprovePTORequest = async (ptoId: string) => {
  const response = await api.put<{
    success: true;
    message: string;
    ptoRequest: PTORequest;
  }>(`/staff/pto/${ptoId}/approve`);
  return response;
};
