import { GetDigitalProfile, UploadAnyImage } from "@/src/services/digital-profile.service";
import {
  UpdateDigitalProfile,
  AddWebsiteLink,
  UpdateWebsiteLinkSettings,
  RemoveWebsiteLink,
  ReorderWebsiteLinks,
  UploadProfileImage,
} from "@/src/services/digital-profile.service";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/src/services/api.service";
import {
  DigitalProfileType,
  ISettings,
  ISocialMedia,
  ITheme,
  IWebsiteLink,
} from "../types";
import { createContext, useContext } from "react";
import SocialMediaIcons from "@/src/icons/digital-profile-socials";
import { useToast } from "@/src/contexts/hooks/toast";
import { pick } from "@/lib/helpers";

export const useDigitalProfile = () => {
  const queryClient = useQueryClient();
  const showToast = useToast()
  const digitalProfileQuery = useQuery({
    queryKey: ["digital-profile"],
    queryFn: async () => {
      const res = await GetDigitalProfile();
      return res.data.profile;
    },
    refetchOnWindowFocus: false,
  });

  const updateProfileMutation = useMutation({
    mutationFn: UpdateDigitalProfile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["digital-profile"] });
      showToast('info', "Profile updated");
    },
    onError: (err: any) => {
      console.error("updateProfileMutation error:", err);
      showToast('error', "Unable to update profile");
    },
  });

  const uploadProfileImageMutation = useMutation({
    mutationFn: UploadProfileImage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["digital-profile"] });
      showToast('info', "Profile image uploaded");
    },
    onError: (err: ApiError) => {
      console.error("uploadProfileImageMutation error:", err);
      showToast('error', err.response?.data.error || "Unable to upload image");
    },
  });

  const uploadAnyImageMutation = useMutation({
    mutationFn: UploadAnyImage,
    onSuccess: () => {
      showToast('info', "Profile image uploaded");
    },
    onError: (err: ApiError) => {
      console.error("uploadProfileImageMutation error:", err);
      showToast('error', err.response?.data.error || "Unable to upload image");
    },
  });

  const addLinkMutation = useMutation({
    mutationFn: AddWebsiteLink,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["digital-profile"] });
      showToast('info', "Link added");
    },
    onError: (err: any) => {
      console.error("addLinkMutation error:", err);
      showToast('error', err.response?.data.error || "Unable to add link");
    },
  });

  const updateLinkMutation = useMutation({
    mutationFn: (vars: {
      linkId: string;
      settings: Parameters<typeof UpdateWebsiteLinkSettings>[1];
    }) => UpdateWebsiteLinkSettings(vars.linkId, vars.settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["digital-profile"] });
      showToast('info', "Link updated");
    },
    onError: (err: any) => {
      console.error("updateLinkMutation error:", err);
      showToast('error', err.response?.data.error || "Unable to update link");
    },
  });

  const removeLinkMutation = useMutation({
    mutationFn: RemoveWebsiteLink,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["digital-profile"] });
      showToast('info', "Link removed");
    },
    onError: (err: any) => {
      console.error("removeLinkMutation error:", err);
      showToast('error', err.response?.data.error || "Unable to remove link");
    },
  });

  const reorderLinksMutation = useMutation({
    mutationFn: (order: string[]) => ReorderWebsiteLinks(order),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["digital-profile"] });
      showToast('info', "Links reordered");
    },
    onError: (err: any) => {
      console.error("reorderLinksMutation error:", err);
      showToast('error', err.response?.data.error || "Unable to reorder links");
    },
  });

  const addSocialMutation = useMutation({
    mutationFn: (payload: any) =>
      api.post(`/digital-profiles/me/social`, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["digital-profile"] });
      showToast('info', "Social icon added");
    },
    onError: (err: any) => {
      console.error("addSocialMutation error:", err);
      showToast('error', err.response?.data.error || "Unable to add social");
    },
  });

  const updateSocialMutation = useMutation({
    mutationFn: (vars: { index: number; payload: any }) =>
      api.put(`/digital-profiles/me/social/${vars.index}`, vars.payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["digital-profile"] });
      showToast('info', "Social icon updated");
    },
    onError: (err: any) => {
      console.error("updateSocialMutation error:", err);
      showToast('error', err.response?.data.error || "Unable to update social");
    },
  });

  const deleteSocialMutation = useMutation({
    mutationFn: (vars: { index?: number; url?: string }) => {
      if (typeof vars.index !== "undefined") {
        return api.delete(`/digital-profiles/me/social/${vars.index}`);
      }
      if (vars.url) {
        return api.delete(
          `/digital-profiles/me/social/url/${encodeURIComponent(vars.url)}`
        );
      }
      return Promise.reject(new Error("index or url required"));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["digital-profile"] });
      showToast('info', "Social icon removed");
    },
    onError: (err: any) => {
      console.error("deleteSocialMutation error:", err);
      showToast('error', err.response?.data.error || "Unable to remove social");
    },
  });

  return {
    digitalProfileQuery,
    actions: {
      updateProfile: (payload: Parameters<typeof UpdateDigitalProfile>[0]) =>
        updateProfileMutation.mutateAsync(payload),
      uploadProfileImage: (formData: FormData) => uploadProfileImageMutation.mutateAsync(formData),
      /** 
       * @dev upload image e.g thumbnail and get imageUrl and imagePublicId
       * */
      uploadAnyImage: (formData: FormData) => uploadAnyImageMutation.mutateAsync(formData).then(v => pick(v.data, 'imageUrl', 'imagePublicId')),
      addLink: (payload: Parameters<typeof AddWebsiteLink>[0]) =>
        addLinkMutation.mutateAsync(payload),
      updateLink: (
        linkId: string,
        settings: Parameters<typeof UpdateWebsiteLinkSettings>[1]
      ) => updateLinkMutation.mutateAsync({ linkId, settings }),
      deleteLink: (linkId: string) => removeLinkMutation.mutateAsync(linkId),
      reorderLinks: (order: string[]) => reorderLinksMutation.mutateAsync(order),
      addSocial: (payload: any) => addSocialMutation.mutateAsync(payload),
      updateSocial: (index: number, payload: any) =>
        updateSocialMutation.mutateAsync({ index, payload }),
      deleteSocial: (index?: number, url?: string) =>
        deleteSocialMutation.mutateAsync({ index, url }),
    },
  };
};

export type ISettingsPayload = ISettings;

export interface DigitalProfileContextType {
  /**
   * @dev this should be the digitalProfileFormik.values oh 👂👂
   * */
  profile: DigitalProfileType;
  /**
   * @dev has one api 😂😂
   * */
  basicDetailsSettings: {
    update(payload: DigitalProfileType["details"]): void;
  };
  /**
   * @dev the image upload happens and then we update with the url string;
   * */
  imageSettings: {
    uploadProfileImage(image: File): void;
    /** 
     * @dev this can be used to any image at allllll, maybe for the thumbnail and thumbnail 
     * */
    uploadAnyImage(image: File): Promise<{ imageUrl: string, imagePublicId: string }>
  };
  /**
   * @dev set of api endpoints for this;
   * */
  themeSettings: {
    /**
     * @dev this updates an already existing theme; say change background or font 🙆
     * */
    update(theme: ITheme): void;
  };
  /**
   * @dev set of api endpoints
   * */
  linkSettings: {
    /**
     * @dev add a new link to the top of the array
     * */
    add(link: Prettify<Omit<IWebsiteLink, "_id">>): void;
    delete(linkId: string): void;
    /**
     * @dev update the whole link
     * */
    update(linkId: string, link: IWebsiteLink): void;
    reorder(order: Array<string>): void;
  };
  /**
   * @set of apis for this toooo
   * */
  socialsSettings: {
    add(payload: ISocialMedia): void;
    update(index: number, payload: ISocialMedia): void;
    /**
     * @dev delete by url string;
     * */
    delete(index: number, url: string): void;
  };
  /**
   * @dev set of apis for this tooo;
   * */
  settingSettings: /** 😂😂 funny property name */ {
    update(payload: ISettingsPayload): void;
  };
}

export const DigitalProfileContext =
  createContext<DigitalProfileContextType | null>(null);

export const useDigitalProfileContext = () => {
  const context = useContext(DigitalProfileContext);
  if (!context) {
    throw new Error(
      "useDigitalProfileContext must be used within a DigitalProfileContextProvider"
    );
  }
  return context;
};

export const socialMediaMatcher: Array<
  [
    RegExp,
    {
      type: Exclude<ISocialMedia["type"], "custom">;
      icon: typeof SocialMediaIcons.Facebook;
    }
  ]
> = [
    [
      new RegExp(/^https:\/\/(www\.)?facebook\.com/i),
      { type: "facebook", icon: SocialMediaIcons.Facebook },
    ],
    [
      new RegExp(/^https:\/\/(twitter\.com|x\.com)/i),
      { type: "twitter", icon: SocialMediaIcons.Twitter },
    ],
    [
      new RegExp(/^https:\/\/(www\.)?instagram\.com/i),
      { type: "instagram", icon: SocialMediaIcons.Instagram },
    ],
    [
      new RegExp(/^https:\/\/(www\.)?linkedin\.com/i),
      { type: "linkedin", icon: SocialMediaIcons.LinkedIn },
    ],
    [
      new RegExp(/^https:\/\/(www\.)?youtube\.com/i),
      { type: "youtube", icon: SocialMediaIcons.Youtube },
    ],
    [
      new RegExp(/^https:\/\/(www\.)?tiktok\.com/i),
      { type: "tiktok", icon: SocialMediaIcons.Tiktok },
    ],
    [
      new RegExp(/^https:\/\/(www\.)?pinterest\.com/i),
      { type: "pinterest", icon: SocialMediaIcons.Pinterest },
    ],
    [
      new RegExp(/^https:\/\/(www\.)?snapchat\.com/i),
      { type: "snapchat", icon: SocialMediaIcons.Snapchat },
    ],
    [
      new RegExp(/^https:\/\/(wa\.me|api\.whatsapp\.com|web\.whatsapp\.com)/i),
      { type: "whatsapp", icon: SocialMediaIcons.Whatsapp },
    ],
  ];
