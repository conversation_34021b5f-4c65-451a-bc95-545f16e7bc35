import { Typography } from "@/components/typography";
import { motion } from "framer-motion";
import { useEffect, useRef } from "react";
import { Link, useLocation } from "react-router-dom";
import { variants } from "../constants";
import { MenuItem as TMenuItem } from "../types";
import { sleep } from "@/lib/helpers";

type Props = {
  closeSidebar: () => void;
  subItems: TMenuItem[];
};

export const SubMenu: React.FC<Props> = (props) => {
  return (
    <motion.div
      className="w-full pt-0 flex"
    >
      <div className="h-full flex-grow flex flex-col gap-y-2 ">
        {props.subItems.map((item, index) => (
          <MenuItem
            closeSidebar={props.closeSidebar}
            key={index}
            {...item}
          />
        ))}
      </div>
    </motion.div>
  );
};

export const MenuItem = (props: TMenuItem & { closeSidebar: () => void }) => {
  const { pathname } = useLocation();
  const isActive = (link: string | undefined) => {
    return link ? pathname === link || pathname.startsWith(link) : false;
  };
  const isToBeShown = props.toShow ? props.toShow?.() : true;
  const planTag = props.planTag?.() || null;
  const containerRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    if (isActive(props.link) && containerRef.current)
      (async () => {
        await sleep(400)
        containerRef.current?.scrollIntoView({ behavior: 'smooth' })
      })()
  }, [pathname, props.link])
  return (
    <>
      {isToBeShown ? (
        <div ref={containerRef} >
          {planTag ? (
            <Link
              to={planTag.link}
              onClick={() => {
                props.onClick?.();
                props.closeSidebar();
              }}
              className="group"
            >
              <Typography
                variant={"p"}
                className={`${isActive(props.link) ? variants["open"] : variants["closed"]
                  } border border-transparent cursor-pointer pl-2 pr-5 py-1.5 rounded-xl flex items-center gap-x-3 !mt-0 relative `}
              >
                {props.icon}
                {props.title}
                <span className="absolute right-0 top-0 px-2 py-1 rounded-md bg-primary-300 text-white font-bold text-[10px] leading-[10px] transition-[border-radius] duration-1000 group-hover:rounded-tl-none group-hover:rounded-tr-xl group-hover:rounded-br-none ">
                  {planTag.tag}
                </span>
              </Typography>
            </Link>
          ) : props.link ? (
            <Link
              to={props.link}
              onClick={() => {
                props.onClick?.();
                props.closeSidebar();
              }}
              className="group"
            >
              <Typography
                variant={"p"}
                className={`${isActive(props.link) ? variants["open"] : variants["closed"]
                  } border border-transparent cursor-pointer pl-2 pr-5 py-1.5 rounded-xl flex items-center gap-x-3 !mt-0 relative `}
              >
                {props.icon}
                <span className="line-clamp-1">{props.title}</span>
              </Typography>
            </Link>
          ) : (
            <Typography
              variant={"p"}
              className={`pr-2 text-[#989898] py-2.5 cursor-pointer text-sm rounded-xl flex items-center gap-x-3 !mt-0`}
            >
              {props.title}
            </Typography>
          )}
          {/* if the plan tag is present, we dont show the sub menu */}
          {props.subItems && !planTag && (
            <SubMenu
              closeSidebar={() => props.closeSidebar()}
              subItems={props.subItems}
            />
          )}
        </div>
      ) : null}
    </>
  );
};
