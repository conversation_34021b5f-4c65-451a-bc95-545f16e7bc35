{"name": "scheduling", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --fix", "check": "tsc -b", "preview": "vite preview"}, "dependencies": {"@editorjs/editorjs": "^2.31.0", "@editorjs/header": "^2.8.8", "@editorjs/image": "^2.10.3", "@editorjs/list": "^2.0.8", "@editorjs/quote": "^2.7.6", "@gravity-ui/icons": "^2.16.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@reduxjs/toolkit": "^2.9.0", "@stripe/react-stripe-js": "^5.2.0", "@stripe/stripe-js": "^8.0.0", "@tanstack/react-query": "^5.90.2", "@tiptap/extension-highlight": "^3.6.5", "@tiptap/extension-link": "^3.6.5", "@tiptap/extension-placeholder": "^3.6.5", "@tiptap/extension-subscript": "^3.6.5", "@tiptap/extension-superscript": "^3.6.5", "@tiptap/extension-text-align": "^3.6.5", "@tiptap/extension-underline": "^3.6.5", "@tiptap/react": "^3.6.5", "@tiptap/starter-kit": "^3.6.5", "@vingeray/editorjs-markdown-converter": "^0.1.2", "axios": "^1.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "country-state-city": "^3.2.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "embla-carousel-react": "^8.6.0", "emoji-picker-react": "^4.14.0", "formik": "^2.4.6", "framer-motion": "^12.23.22", "hugeicons-react": "^0.3.0", "lottie-react": "^2.4.1", "lucide-react": "^0.545.0", "moment-timezone": "^0.6.0", "react": "^19.2.0", "react-dom": "^19.2.0", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "react-router-dom": "7.9.4", "recharts": "^3.2.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "yup": "^1.7.1", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.37.0", "@types/node": "^24.7.0", "@types/react": "^19.2.2", "@types/react-dom": "^19.2.1", "@vitejs/plugin-react": "^5.0.4", "autoprefixer": "^10.4.21", "eslint": "^9.37.0", "eslint-plugin-react-hooks": "^6.1.1", "eslint-plugin-react-refresh": "^0.4.23", "eslint-plugin-unicorn": "^61.0.2", "globals": "^16.4.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.9", "typescript": "^5.9.3", "typescript-eslint": "^8.46.0", "vite": "^7.1.9", "vite-plugin-eslint": "^1.8.1"}}