import React from "react";
import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { ActionButtonGroup, But<PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import PendingOverlay from "@/components/ui/pending-overlay";
import CircularLoader from "@/components/ui/circular-loader";
import { ArrowLeft02Icon } from "hugeicons-react";
import { useSchedulingRules } from "../hooks/use-scheduling-rules";
import { useToast } from "~/contexts/hooks/toast";
import { UpdateSchedulingRulesPayload } from "@/src/interfaces/scheduling-rules";
import RulesForm from "./components/rules-form";
import { noop, pickByType } from "@/lib/helpers";
import { GetServiceById } from "@/src/services/services.service";
import { useQuery } from "@tanstack/react-query";

/**
 * @likely_bug {@link GetServiceById} un-implemented api endpoint in backend
 */
const EditSchedulingRules: React.FC = () => {
  const navigate = useNavigate();
  const { serviceId } = useParams<{ serviceId: string }>();
  const [searchParams] = useSearchParams();
  const teamId = searchParams.get("teamId");
  const showToast = useToast();

  const { schedulingRulesQuery, updateSchedulingRulesMutation } =
    useSchedulingRules(serviceId);
  const serviceQuery = useQuery({
    queryKey: ["edit-scheduling-rules", serviceId],
    queryFn: async () => {
      return (await GetServiceById(serviceId!)).data.Service;
    },
    enabled: Boolean(serviceId),
    refetchOnWindowFocus: false,
  });

  const service = serviceQuery.data;
  const rules = schedulingRulesQuery.data?.schedulingRules;

  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      depositType: rules?.depositType || ("none" as const),
      depositAmount: rules?.depositAmount,
      cancelCutoff: rules?.cancelCutoff || 24,
      noShowFee: rules?.noShowFee || 0,
      allowRescheduling: rules?.allowRescheduling ?? true,
      rescheduleCutoff: rules?.rescheduleCutoff || 2,
      lateCancellationFee: rules?.lateCancellationFee || 0,
      isActive: rules?.isActive ?? true,
    } as UpdateSchedulingRulesPayload,
    validationSchema: Yup.object({
      depositType: Yup.string()
        .oneOf(["none", "percentage", "fixed", "full"])
        .required(),
      depositAmount: Yup.number().when("depositType", {
        is: (val: string) => val === "percentage" || val === "fixed",
        then: (schema) => schema.required("Deposit amount is required"),
        otherwise: (schema) => schema,
      }),
      cancelCutoff: Yup.number().min(0).required("Cancel cutoff is required"),
      noShowFee: Yup.number()
        .min(0)
        .max(100)
        .required("No-show fee is required"),
      allowRescheduling: Yup.boolean().required(),
      rescheduleCutoff: Yup.number().when("allowRescheduling", {
        is: true,
        then: (schema) =>
          schema.min(0).required("Reschedule cutoff is required"),
        otherwise: (schema) => schema,
      }),
      lateCancellationFee: Yup.number()
        .min(0)
        .max(100)
        .required("Late cancellation fee is required"),
      isActive: Yup.boolean().required(),
    }),
    validateOnMount: true,
    onSubmit: noop,
  });

  const updateRulesHandler = async () => {
    if (!serviceId) return;

    formik.validateForm().then((val) => {
      const errors = Object.values(pickByType(val, "string"));
      if (errors.length === 0) {
        const { values } = formik;

        updateSchedulingRulesMutation.mutateAsync(
          {
            serviceId,
            payload: {
              ...values,
              // @ts-expect-error
              serviceId,
            },
          },
          {
            onSuccess() {
              const queryParams = teamId ? `?teamId=${teamId}` : "";
              navigate(`/teams/rules${queryParams}`);
            },
          },
        );
      } else {
        showToast("error", "Invalid Field", {
          description: errors[0],
        });
      }
    });
  };

  const goBack = () => {
    const queryParams = teamId ? `?teamId=${teamId}` : "";
    navigate(`/teams/rules${queryParams}`);
  };

  if (schedulingRulesQuery.isLoading || serviceQuery.isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <CircularLoader />
      </div>
    );
  }

  return (
    <section className="w-full max-w-2xl mx-auto space-y-6 pb-20">
      <div className="flex items-center gap-4">
        <Button variant="icon" onTap={goBack} className="p-1.5">
          <ArrowLeft02Icon className="w-5 h-5" />
        </Button>
        <div>
          <Typography variant="h2" className="font-semibold">
            Edit Scheduling Rules
          </Typography>
          {service && (
            <Typography className="text-gray-600 mt-1">
              Updating rules for {service.name}
            </Typography>
          )}
        </div>
      </div>

      <RulesForm formik={formik as any} />

      <ActionButtonGroup
        cancel={{
          title: "Cancel",
          disabled: updateSchedulingRulesMutation.isPending,
          onTap: goBack,
        }}
        next={{
          loading: updateSchedulingRulesMutation.isPending,
          hasChanges: formik.isValid,
          disabled: updateSchedulingRulesMutation.isPending || !formik.isValid,
          title: innerWidth <= 480 ? "Save" : "Save Changes",
          onTap: updateRulesHandler,
        }}
      />
      <PendingOverlay isPending={updateSchedulingRulesMutation.isPending} />
    </section>
  );
};

export default EditSchedulingRules;
