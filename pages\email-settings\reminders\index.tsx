import { Input } from "@/components/inputs";
import EmailEditor from "../email-editor";
import { Typography } from "@/components/typography";
import StaticToolTip from "@/components/ui/static-tooltip";
import EmailTemplateVariableEngine, {
  TEMPLATE_VARIABLES,
} from "../email-template-engine";
import { useFormik } from "formik";
import { noop, omit, px, removeUndefined, sleep } from "@/lib/helpers";
import Tabs from "@/components/ui/tabs";
import { useEffect, useMemo, useRef, useState } from "react";
import {
  ReminderEmailStage,
  ReminderStagePayload
} from "@/src/services/email-settings.service";
import { Card } from "@/components/ui/card";
import Chip from "@/components/ui/chip";
import ButtonTooltip from "@/pages/services/components/button-tooltip";
import { ArrowUpRightFromSquare, Plus, X } from "lucide-react";
import { <PERSON><PERSON>, SaveButton } from "@/components/buttons";
import { Service } from "@/src/interfaces/services";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useModalsBuilder } from "@/lib/modals-builder";
import Modal from "@/components/ui/modal";
import useEmailSettings from "../use-email-settings";
import { array, number, object, string } from "yup";
import PendingOverlay from "@/components/ui/pending-overlay";
import Dialog from "@/components/ui/dialog";
import { useToast } from "@/src/contexts/hooks/toast";
import { defaultEmailTemplate } from "../constants";
import VariablesModal from "../components/variables-modal";
import { Pencil } from "@gravity-ui/icons";
import CircularLoader from "@/components/ui/circular-loader";

type SideSettingProps = {
  activeId: string;
  /**
   * @dev this will will be used to make api requests for email setting name modification or adding or deletion of service variable
   * */
  id: string;
  name: string;
  /**
   * @dev this will be used for display only
   * */
  index: number;
  /**
   * @dev used to open modal
   * */
  setActiveId: VoidFunction;
  attachedServices: Service[];
  allServices: Service[];
  modifiers: {
    addService: (serviceId: string) => Promise<any>
    deleteService: (serviceId: string) => Promise<any>
    deleteTemplate: VoidFunction;
  };
};

const AnimatedCard = motion.create(Card);

const EmailTemplateSideSetting = (props: SideSettingProps) => {
  const isActiveId = useMemo(() => {
    return props.activeId === props.id;
  }, [props.activeId, props.id]);
  const { modals, modalFunctions } = useModalsBuilder({
    attachService: { open: false },
  });
  return (
    <AnimatedCard
      variants={{ closed: { height: px(48) }, open: { height: "fit-content" } }}
      initial={"closed"}
      animate={"open"}
      className="w-full rounded-3xl pb-5 flex flex-col gap-y-3 h-[48px] overflow-hidden"
    >
      <div
        className={cn(
          "rounded-t-[inherit] bg-gray-200 py-2.5 px-6 flex justify-between ",
          {
            "bg-primary-400 text-white": isActiveId,
          },
        )}
      >
        <Typography className="w-[80%] whitespace-nowrap overflow-x-hidden overflow-ellipsis ">
          {props.name || `Reminder ${props.index + 1}`}
        </Typography>
        <div className="flex items-center gap-x-3 ml-auto">
          <ButtonTooltip content="View Email Template">
            <button
              className="text-inherit"
              onClick={() => props.setActiveId()}
            >
              <ArrowUpRightFromSquare size={18} />
            </button>
          </ButtonTooltip>
        </div>
      </div>
      <div className="px-5 flex flex-wrap gap-x-3 gap-y-3">
        {props.attachedServices.map((service, index) => {
          return (
            <Chip
              onDelete={() => {
                props.modifiers.deleteService(
                  service._id
                );
              }}
              key={index}
              className="flex items-center gap-x-2"
            >
              <span className="">
                <span className="text-green-500">
                  {(service.category as any).name}{" "}
                </span>
                | {service.name}
              </span>
            </Chip>
          );
        })}
        <Button
          onTap={() => modalFunctions.openModal("attachService", {})}
          variant="icon"
          className="rounded-full !p-2 flex items-center justify-center bg-[#DFE1DB]/30 "
        >
          <Plus className="text-gray-500 w-4 h-4" />
        </Button>
      </div>
      <div className="px-5">
        <Button
          onTap={props.modifiers.deleteTemplate}
          className="w-full"
          variant="outline"
        >
          Delete Template
        </Button>
      </div>
      <Modal
        open={modals.attachService.open}
        onClose={() => modalFunctions.closeModal("attachService")}
      >
        <Modal.Body
          className={`min-h-fit min-w-80 max-w-[360px] h-fit w-fit flex flex-col gap-y-1.5 items-center justify-center rounded-[32px] bg-white `}
        >
          <div className="w-full flex justify-between items-start">
            <Typography
              variant={"h4"}
              className="font-bold font-Bricolage_Grotesque "
            >
              Attach a service
            </Typography>
            <Button
              variant="icon"
              onTap={() => modalFunctions.closeModal("attachService")}
              className="p-1 !rounded-full hover:bg-gray-100"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
          {props.allServices.length > 0 ? (
            <Typography className="text-sm text-stone-400 ">
              Clients will be sent an email using this template when the
              attached service is booked
            </Typography>
          ) : (
            <Typography className="text-base pt-3 text-stone-600">
              No services to attach
            </Typography>
          )}
          <div className="pt-2 w-full flex flex-col justify-center ">
            {props.allServices.map((service, index) => {
              return (
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.95 }}
                  onTap={() => {
                    props.modifiers
                      .addService(
                        service._id,
                      )
                      .then((_) => modalFunctions.closeModal("attachService"));
                  }}
                  className="py-1.5 flex flex-col gap-y-0.5 hover:bg-gray-100 rounded-lg px-3 cursor-pointer "
                  key={index}
                >
                  <Typography className="font-bold">{service.name}</Typography>
                  <Typography className="text-sm">
                    Category: {service.category.name}
                  </Typography>
                </motion.div>
              );
            })}
          </div>
        </Modal.Body>
      </Modal>
    </AnimatedCard>
  );
};

const inputPlaceHolder = "Hey %first%, you have an %service% booking in %time%";

const RemindersPage = () => {
  const {
    reminderStage,
    emailSettingsQuery: { data: emailSettings, isLoading },
    servicesQuery: { data: services = [] },
  } = useEmailSettings();
  const [activeId, setActiveId] = useState("");
  const selectedEmailStage =
    useMemo<ReminderEmailStage | null>(() => {
      return (
        emailSettings?.reminders?.find(
          (tmpl) => tmpl._id === activeId,
        ) || null
      );
    }, [activeId, emailSettings]);
  useEffect(() => {
    if (emailSettings)
      setActiveId(emailSettings?.reminders.at(0)?._id || "");
  }, [emailSettings]);
  const formik = useFormik({
    enableReinitialize: true,
    initialValues: {
      emailSubject: selectedEmailStage?.emailSubject || "",
      emailTemplate: selectedEmailStage?.emailTemplate || "",
      name: selectedEmailStage?.name || "",
      hoursBeforeAppointment: selectedEmailStage?.hoursBeforeAppointment || 0,
      attachedServices: selectedEmailStage?.attachedServices || [],
    },
    validationSchema: object({
      name: string().required("Name is required"),
      emailTemplate: string().required("Email template is required"),
      emailSubject: string().required("Email subject is required"),
      hoursBeforeAppointment: number().integer().min(1).max(168).required('Hours before appointment is required'),
      attachedServices: array().of(string()),
    }),
    onSubmit: noop,
  });
  const { modals, modalFunctions } = useModalsBuilder({
    confirmDelete: {
      open: false,
      id: null as string | null,
    },
    addVariable: {
      open: false,
      type: null as "subject" | "body" | null,
    },
  });
  const showToast = useToast();
  const emailSubjectRef = useRef<HTMLInputElement>(null);
  // this should have default value of saved sanitizedhtmlstring
  const [preview, setPreview] = useState("");
  // segment errors for the two inputs then we can join them later on
  const [errors, setErrors] = useState<Record<string, string[]>>({
    subject: [],
    body: [],
  });
  const [hasNameChangeIntent, setHasNameChangeIntent] = useState(false);
  const nameRef = useRef<HTMLTextAreaElement>(null);
  function getAllErrors() {
    return Object.values(errors).flat(Infinity);
  }
  function createDefaultTemplate() {
    reminderStage.create.mutateAsync(
      defaultEmailTemplate.reminder(
        `Reminder ${emailSettings?.reminders.length}`,
      ),
      {
        onSuccess() {
          showToast("info", `Template created`);
        },
        onError(error) {
          const err = error as unknown as ApiError;
          showToast(
            "error",
            err?.response?.data?.error ||
            err?.message ||
            "Something went wrong, try again",
          );
        },
      },
    );
  }
  if (isLoading)
    return (
      <div className="py-20">
        <CircularLoader />
      </div>
    );
  if (isLoading === false && !emailSettings?.reminders.length)
    return (
      <div className="flex flex-col py-3 items-center justify-center text-center">
        <PendingOverlay isPending={reminderStage.create.isPending} />
        <div className="mb-6">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Plus className="w-8 h-8 text-gray-400" />
          </div>
        </div>

        <h3 className="text-lg font-medium text-gray-900 mb-2">
          No email templates found
        </h3>

        <p className="text-gray-500 mb-6 max-w-sm">
          Get started by creating your first email template.
        </p>

        <Button
          onTap={createDefaultTemplate}
          className="font-medium py-3 px-6 flex items-center gap-2"
        >
          <Plus className="w-5 h-5" />
          Create Template
        </Button>
      </div>
    );
  return (
    <section className="mt-10 pb-32 ">
      <PendingOverlay
        isPending={
          reminderStage.create.isPending ||
          reminderStage.update.isPending ||
          reminderStage.delete.isPending ||
          reminderStage.swapAttachedService.isPending
        }
      />
      <section className="w-full flex flex-col md:grid xl:grid-cols-[30%_67%] xl:justify-between gap-y-4">
        <div className="w-full max-w-lg flex flex-col gap-y-2">
          <Button className="mb-3" onTap={createDefaultTemplate}>
            Create New Template
          </Button>
          {emailSettings?.reminders?.map((tmplSetting, index) => {
            const attachedServices = removeUndefined(
              tmplSetting.attachedServices?.map((serviceId) =>
                services.find((service) => service._id === serviceId),
              ) || [],
            )
            return (
              <EmailTemplateSideSetting
                attachedServices={attachedServices}
                index={index}
                allServices={services}
                activeId={activeId}
                id={tmplSetting._id}
                modifiers={{
                  deleteTemplate: () => {
                    modalFunctions.openModal("confirmDelete", {
                      id: tmplSetting._id,
                    });
                  },
 async addService(selectedServiceId) {
                    const thisEmailSettingId = tmplSetting._id
                    // exclude this initial confirmation from 
                    const filteredEmailTypes = emailSettings?.reminders?.filter(emailType => emailType._id !== thisEmailSettingId);
                    // find template that has serviceId attached already
                    const serviceAttachedEmailType = filteredEmailTypes.find(emailType => emailType.attachedServices.includes(selectedServiceId))
                    const options = {
                      onSuccess() {
                        showToast("info", `Template updated`);
                      },
                      onError(error: ApiError) {
                        showToast("error", error?.response?.data.error || "Something went wrong, try again");
                      },
                    } as Record<string, any>
                    if (!serviceAttachedEmailType) {
                      const isActiveSetting = tmplSetting._id === activeId
                      return reminderStage.update.mutateAsync({
                        id: tmplSetting._id,
                        payload: {
                          ...(isActiveSetting ? formik.values : omit(tmplSetting, '_id')),
                          attachedServices: attachedServices.map(ser => ser._id).concat([selectedServiceId])
                        },
                      }, options)
                    } else {
                      const serviceAttachedEmailTypeAttachedServices = serviceAttachedEmailType.attachedServices;
                      const tmplSettingAttachedServices = tmplSetting.attachedServices || []
                      return reminderStage.swapAttachedService.mutateAsync({
                        firstPayload: {
                          id: serviceAttachedEmailType._id,
                          payload: {
                            ...(serviceAttachedEmailType._id === activeId ? formik.values : omit(serviceAttachedEmailType, '_id')),
                            attachedServices: serviceAttachedEmailTypeAttachedServices.filter(serviceId => serviceId !== selectedServiceId)
                          },
                        },
                        secondPayload: {
                          id: tmplSetting._id,
                          payload: {
                            ...(tmplSetting._id === activeId ? formik.values : omit(tmplSetting, '_id')),
                            attachedServices: tmplSettingAttachedServices.concat([selectedServiceId])
                          },
                        },
                      },
                        options,
                      )
                    }
                  },
                  async deleteService(selectedServiceId) {
                    const payload = {} as ReminderStagePayload;
                    const filteredServiceIds = attachedServices
                      .map((_) => _._id)
                      .filter((serviceId) => serviceId !== selectedServiceId);
                    if (tmplSetting._id === activeId) {
                      // set formik to hold attachedServices if we are on the activeIndex
                      formik.setFieldValue("attachedServices", filteredServiceIds);
                      Object.assign(payload, {
                        ...formik.values,
                        attachedServices: filteredServiceIds,
                      });
                    } else {
                      Object.assign(payload, {
                        ...omit(tmplSetting, '_id'),
                        attachedServices: filteredServiceIds,
                      });
                    }
                    return reminderStage.update.mutateAsync(
                      {
                        id: tmplSetting._id,
                        payload: payload,
                      },
                      {
                        onSuccess() {
                          showToast("info", `Template updated`);
                        },
                        onError() {
                          showToast("error", "Something went wrong, try again");
                        },
                      },
                    );

                  },
                }}
                key={index}
                name={tmplSetting.name}
                setActiveId={() => setActiveId(tmplSetting._id)}
              />
            );
          })}
        </div>
        <div className="flex flex-col gap-y-3">
          <div className="flex items-start gap-x-2 py-5 justify-between">
            <textarea
              className="text-2xl lg:text-3xl resize-none flex-grow font-Bricolage_Grotesque font-bold bg-transparent "
              defaultValue={selectedEmailStage?.name}
              value={formik.values.name}
              ref={nameRef}
              name="name"
              onChange={({ currentTarget: { value } }) => {
                formik.setFieldValue("name", value);
              }}
              onBlur={() => setHasNameChangeIntent(false)}
              disabled={hasNameChangeIntent === false}
            />
            <Button
              className="bg-transparent text-paragraph px-0"
              onTap={async () => {
                setHasNameChangeIntent(true);
                await sleep(100);
                nameRef.current?.focus();
              }}
            >
              <Pencil width={24} height={24} />
            </Button>
          </div>
          <Input.Numeric
            placeholder={'2 representing 2 hours before appointment'}
            defaultValue={formik.values.hoursBeforeAppointment}
            label="Hours before appointment"
            className="max-w-[200px] "
            required
            name="hoursBeforeAppointment"
            onChange={({ currentTarget: { name, value } }) => {
              formik.setFieldValue(name, parseInt(value || '0'));
            }}
          />
          <div className="flex flex-col gap-y-2">
            <div className="flex items-end gap-x-1">
              <ButtonTooltip content="Add Variables">
                <button
                  onClick={() =>
                    modalFunctions.openModal("addVariable", { type: "subject" })
                  }
                  className="bg-primary-500 text-white font-bold p-2 rounded-xl"
                >
                  %
                </button>
              </ButtonTooltip>
              <Input.Text
                required
                placeholder={inputPlaceHolder}
                defaultValue={formik.values.emailSubject}
                label="EMAIL SUBJECT"
                className="flex-grow"
                inputRef={emailSubjectRef}
                name="emailSubject"
                value={formik.values.emailSubject}
                onChange={({ currentTarget: { name, value } }) => {
                  formik.setFieldValue(name, value);
                  setErrors({
                    ...errors,
                    subject: EmailTemplateVariableEngine.validateTemplate(
                      value || "",
                      "subject",
                    ),
                  });
                }}
              />
            </div>
            <StaticToolTip>
              <div className="space-y-2 text-white">
                <Typography className="font-normal">Example:</Typography>
                <Typography>
                  {EmailTemplateVariableEngine.replaceVariables(
                    formik.values.emailSubject || inputPlaceHolder,
                    EmailTemplateVariableEngine.SAMPLE_DATA,
                  )}
                </Typography>
              </div>
            </StaticToolTip>
          </div>
          <Tabs
            onValueChange={(value) => {
              if (value === "preview")
                setPreview(
                  EmailTemplateVariableEngine.replaceVariables(
                    formik.values.emailTemplate,
                    EmailTemplateVariableEngine.SAMPLE_DATA,
                  ),
                );
            }}
            defaultValue="edit"
            className="w-full"
          >
            <Tabs.List className="grid w-full max-w-[320px] mx-auto mt-2 mb-10 grid-cols-2 md:flex-grow ">
              <Tabs.Trigger
                value="edit"
                className=" px-0 flex items-center justify-center gap-x-2"
              >
                Editor
              </Tabs.Trigger>
              <Tabs.Trigger
                value="preview"
                className="px-0 flex items-center justify-center gap-x-2"
              >
                Preview
              </Tabs.Trigger>
            </Tabs.List>
            <Tabs.Content
              className="mt-2 focus:outline-none active:outline-none w-full"
              value={"edit"}
            >
              <EmailEditor
                value={formik.values.emailTemplate}
                onChange={(value) => {
                  formik.setFieldValue("emailTemplate", value);
                  setErrors({
                    ...errors,
                    body: EmailTemplateVariableEngine.validateTemplate(
                      value || "",
                      "body",
                    ),
                  });
                }}
              />
            </Tabs.Content>
            <Tabs.Content
              className="mt-2 focus:outline-none active:outline-none w-full"
              value={"preview"}
            >
              <EmailEditor.Preview value={preview} />
            </Tabs.Content>
          </Tabs>
          {/* Variable Count & Info */}
          <div className="bg-gray-100 p-4 border-t">
            {getAllErrors().length > 0 && (
              <div className="flex flex-col gap-y-2">
                <Typography className="text-red-500">
                  Errors: {getAllErrors().length}
                </Typography>
                {getAllErrors().map((err, index) => {
                  return (
                    <Typography key={index} className="text-red-500">
                      {err}
                    </Typography>
                  );
                })}
              </div>
            )}
            <div className="flex flex-col gap-y-2 items-center justify-between text-sm text-gray-600 md:flex-row">
              <span>
                Template contains{" "}
                {EmailTemplateVariableEngine.countVariables(formik.values.emailTemplate)}{" "}
                variables
              </span>
              <span>
                {Object.keys(TEMPLATE_VARIABLES).length} variables available
              </span>
            </div>
          </div>
          <div>
            <SaveButton
              title="Save"
              disabled={reminderStage.update.isPending}
              loading={reminderStage.update.isPending}
              onTap={() => {
                const allErrors = getAllErrors();
                if (allErrors.length > 0)
                  return showToast(
                    "info",
                    "Clear all the errors before update",
                    { duration: 6000 },
                  );
                reminderStage.update.mutateAsync(
                  {
                    payload: formik.values,
                    id: activeId,
                  },
                  {
                    onSuccess() {
                      showToast("info", `Template updated`);
                    },
                    onError() {
                      showToast("error", "Something went wrong, try again");
                    },
                  },
                );
              }}
            >
            </SaveButton>
          </div>
        </div>
      </section>
      {modals.confirmDelete.id && (
        <Dialog
          open={modals.confirmDelete.open}
          onClose={() => modalFunctions.closeModal("confirmDelete")}
          title={`Delete Template?`}
          description={`Are you sure you want to delete "${emailSettings?.reminders.find(
            (tmpl) => tmpl._id === modals.confirmDelete.id,
          )?.name || "this template"
            }"?`}
          action={{
            title: "Delete",
            onConfirm() {
              reminderStage.delete.mutateAsync(
                modals.confirmDelete.id!,
                {
                  onSuccess() {
                    showToast("info", "Template deleted");
                  },
                  onError() {
                    showToast("error", "Something went wrong, try again");
                  },
                },
              );
            },
          }}
        />
      )}
      {modals.addVariable.type && (
        <VariablesModal
          open={modals.addVariable.open}
          onClose={async () => modalFunctions.closeModal("addVariable")}
          onClickVariable={(variable) => {
            const close = () => modalFunctions.closeModal("addVariable");
            const { type } = modals.addVariable;
            if (!type) return close();
            const inputMap = {
              subject: emailSubjectRef.current,
              body: emailSubjectRef.current,
            };
            const inputElement = inputMap[type];
            if (!inputElement) return;
            close();
            EmailTemplateVariableEngine.insertVariable(variable, inputElement, {
              setText(template) {
                switch (type) {
                  case "subject":
                    return formik.setFieldValue("emailSubject", template);
                  case "body":
                    return formik.setFieldValue("emailTemplate", template);
                }
              },
            });
            // do things here
          }}
        />
      )}
    </section>
  );
};

export default RemindersPage;
