
import { Typography } from "@/components/typography";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  type TService,
} from "~/services/services.service";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useToast } from "~/contexts/hooks/toast";
import CircularLoader from "@/components/ui/circular-loader";
import { Link, useNavigate, useParams } from "react-router-dom";
import { getCurrencySign, inlineSwitch, px } from "@/lib/helpers";
import { GetBusinessServices } from "@/src/services/booking.service";
import { Copy01Icon } from "hugeicons-react";
import MoreInfo from "@/components/ui/more-info";
import { Button } from "@/components/buttons";
import BusinessDescSection from "./components/business-description-section";
import { GetBookingSettingsByUsername, IBookingSettings } from "@/src/services/booking-settings.service";
import { motion } from "framer-motion";

type ServiceProps = {
  service: TService;
  index: number;
  themeSettings: IBookingSettings['themeSettings'] | undefined
  user: {
    username: string;
    billingSettings: {
      preferredCurrency?: UserPreferredCurrency
    }
  }
};

const Service = ({ service, ...props }: ServiceProps) => {
  const showToast = useToast();
  const handleCopyToClipboard = (schedulingLink: string) => {
    navigator.clipboard
      .writeText(schedulingLink)
      .then(() => {
        showToast('info', 'Link copied to clipboard')
      })
      .catch((err) => {
        if (err.name === "NotAllowedError") {
          showToast(
            "error",
            "Please allow clipboard access in your browser settings.",
          );
        } else {
          showToast("error", "Failed to copy to clipboard");
        }
      })
  };
  const navigate = useNavigate()
  const pathLinkMemo = useMemo(() => {
    return `/booking/external/${props?.user?.username}/${service.urlName}/`

  }, [props.user, service.urlName])
  return (
    <div className="flex flex-col border-b pb-2 last:border-none gap-y-2 md:gap-y-3 ">
      <div className="flex items-center gap-[10px]">
        <div
          className="w-1 h-4 "
          style={{ backgroundColor: service?.color || "#e91e63" }}
        />
        <Typography className="!mt-0 text-lg ">{service?.name}</Typography>
        <span className="inline-block text-lg ml-auto font-semibold">
          {getCurrencySign(props.user?.billingSettings?.preferredCurrency || 'usd')}
          {service.price}
        </span>
      </div>
      <pre className="!mt-0 text-sm font-semibold font-Satoshi line-clamp-3 text-paragraph/60">
        {service?.description || "No description"}
      </pre>
      <div className="flex flex-col gap-y-2">
        <div className="flex items-center gap-x-3">
          <Typography className="!mt-0 md:text-lg ">Service Add-ons</Typography>
          <span className="px-2 py-0.5 text-sky-600 bg-sky-50 rounded-md text-sm">
            optional
          </span>
        </div>
        <MoreInfo.Static className="text-xs md:items-center " content="Add optional services alongside the main service" />
        <div className="flex-wrap flex items-center gap-[10px] mt-2 pb-2 ">
          {service.serviceOptions?.map((option, index) => (
            <Typography
              key={index}
              className="py-0.5 px-3 text-xs !mt-0 text-gray-600 bg-gray-100 rounded-full "
            >
              {option.name}{" "}
              <span className="text-green-500 font-bold">
                {getCurrencySign(props.user?.billingSettings?.preferredCurrency || 'usd')}
                {option.price}</span>
            </Typography>
          ))}
        </div>
      </div>
      <div className="flex items-center justify-end gap-x-3">
        <Button
          variant="dormant"
          className="text-sm whitespace-nowrap hidden items-center gap-x-2 pl-2"
          onTap={() =>
            handleCopyToClipboard(
              import.meta.env.VITE_FRONTEND_URL +
              pathLinkMemo
            )
          }
        >
          <Copy01Icon size={18} /> Copy Link
        </Button>
        <motion.button
          whileTap={{ scale: 0.95 }}
          whileHover={{ scale: 1.05 }}
          style={{
            color: props.themeSettings?.buttonTextColor || 'white',
            backgroundColor: props.themeSettings?.buttonColor || '#e91e63',
            borderRadius: px(
              inlineSwitch(props.themeSettings?.borderRadius as number | undefined | null,
                [undefined, 12 as number],
                [null, 12 as number],
                { default: props.themeSettings?.borderRadius! }
              )
            )
          }}
          onTap={() =>
            navigate(
              pathLinkMemo
            )
          }
          className="px-4 py-2 text-sm font-medium transition duration-300 ">
          Book
        </motion.button>
      </div>
    </div>
  );
};

const ViewBusinessServices: React.FC = () => {
  const { username } = useParams<{ username: string }>()
  // Group services by category
  const { data: servicesData, isLoading, isFetching } = useQuery({
    queryKey: ["services"],
    queryFn: async () => {
      const { services, user } = (await GetBusinessServices(username!)).data
      return { services, user }
    },
    enabled: Boolean(username),
    refetchOnWindowFocus: false
  });
  const { data: bookingSettings } = useQuery({
    queryKey: ['booking-settings'],
    queryFn: async () => (await GetBookingSettingsByUsername(username!)).data.bookingSettings,
    enabled: Boolean(username)
  })
  const groupedServicesMemo = useMemo(() => {
    return (
      servicesData?.services?.reduce(
        (acc: Record<string, Array<typeof service>>, service) => {
          const categoryName = service.category.name;
          if (!acc[categoryName]) acc[categoryName] = [];
          acc[categoryName].push(service);
          return acc;
        },
        {},
      ) || {}
    );
  }, [servicesData?.services]);

  if (isLoading)
    return (
      <div className="flex w-full justify-center py-10">
        <CircularLoader />
      </div>
    )
  return (
    <section style={{ background: bookingSettings?.themeSettings.backgroundColor }} className="w-full min-h-screen px-6 py-8 pb-20 mx-auto flex flex-col items-center gap-y-12 relative ">
      {bookingSettings && <BusinessDescSection settings={bookingSettings} />}
      <div className="hidden items-center justify-between">
        <Typography
          variant={"h1"}
          className="font-bold font-Bricolage_Grotesque  "
        >
          {servicesData?.user?.businessName} Services
        </Typography>
      </div>
      <div className="w-full max-w-2xl flex flex-col  gap-y-10 ">
        {Object.keys(groupedServicesMemo).length === 0 && !isLoading && !isFetching && (
          <div className="flex flex-col items-center justify-center text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No services found for this business {servicesData?.user?.businessName}
            </h3>
          </div>
        )}
        <Accordion
          type="single"
          defaultValue="item-0"
          collapsible
          className="w-full space-y-3"
        >
          {Object.keys(groupedServicesMemo)?.map((category, index) => (
            <AccordionItem key={index} value={`item-${index}`} className="" >
              <AccordionTrigger>
                <h2 style={{ color: bookingSettings?.themeSettings?.primaryColor || '#2f2f2f' }} className="text-xl capitalize font-semibold mb-2">
                  {category}
                </h2>
              </AccordionTrigger>
              <AccordionContent isAlwaysOpen className=""  >
                {groupedServicesMemo[category]?.map((service, serviceIndex) => (
                  <Service
                    user={{
                      billingSettings: servicesData?.user.billingSettings!,
                      username: username!
                    }}
                    themeSettings={bookingSettings?.themeSettings}
                    index={serviceIndex}
                    service={service}
                    key={serviceIndex}
                  />
                ))}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
      {
        !servicesData?.user?.isPremiumPlan && (
          <Link
            to={`/onboarding`}
            target="_blank"
            className="w-screen max-w-fit whitespace-nowrap bg-white/20 backdrop-blur-2xl px-3 rounded-full text-sm py-3  bottom-6 font-Aeonik_Fono hover:opacity-75 transition-opacity duration-200  font-semibold shadow-md "
          >
            Join{" "}
            <span className="relative z-20 after:w-full after:h-full after:absolute after:py-3 after:left-0 after:-z-10 after:-rotate-3 after:inline-block after:bg-green-500 ">
              {username}
            </span>{" "}
            on Puzzle Piece 🧩
          </Link>
        )}
    </section>
  );
};

export default ViewBusinessServices
