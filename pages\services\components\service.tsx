import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Dialog from "@/components/ui/dialog";
import { useModalsBuilder } from "@/lib/modals-builder";
import { useFormattedPrice } from "@/lib/use-formatted-price";
import { cn } from "@/lib/utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Copy01Icon, Delete02Icon, Edit02Icon, GoogleSheetIcon, LinkSquare01Icon } from "hugeicons-react";
import { useNavigate } from "react-router-dom";
import { useToast } from "~/contexts/hooks/toast";
import {
    DeleteService,
    type TService
} from "~/services/services.service";
import useAuthStore from "@/pages/auth/components/auth-store";
import ButtonTooltip from "./button-tooltip";


type ServiceProps = {
  service: TService;
  index: number;
  onEdit: (urlName: string) => void;
};

const Service = ({ service, ...props }: ServiceProps) => {
  const showToast = useToast();
  const handleCopyToClipboard = (schedulingLink: string) => {
    navigator.clipboard
      .writeText(schedulingLink)
      .then(() => {
        showToast(
          "success",
          "Link copied to clipboard"
        );
      })
      .catch((err) => {
        if (err.name === "NotAllowedError") {
          showToast(
            "error",
            "Please allow clipboard access in your browser settings.",
          );
        } else {
          showToast("error", "Failed to copy to clipboard");
        }
      })
  };
  const navigate = useNavigate()
  const queryClient = useQueryClient();
  const deleteServiceMutation = useMutation({
    mutationFn: DeleteService,
  });
  const { initialState: { user } } = useAuthStore()
  const { formatPrice } = useFormattedPrice()
  const { modals, modalFunctions } = useModalsBuilder({
    dialog: {
      open: false,
      props: null as {
        action: {
          title?: string;
          onConfirm: () => void;
        };
        title: string;
        description?: string;
      } | null
    }
  })
  const isPublicAccess = service.access === 'public'
  return (
    <div className="flex flex-col border-b pb-2 last:border-none gap-y-2 md:gap-y-3 ">
      <div className="flex items-center gap-[10px]">
        <div
          className="w-1 h-3 "
          style={{ backgroundColor: service?.color || "#e91e63" }}
        />
        <Typography className="!mt-0">{service?.name}</Typography>
        <span className="inline-block ml-auto font-semibold">
          {formatPrice(service.price || 0)}
        </span>
      </div>
      <Typography variant='pre' className="text-sm font-semibold font-Satoshi line-clamp-3 text-paragraph/60">
        {service?.description || "No description"}
      </Typography>
      <div className="flex-grow flex items-center gap-[10px] ">
        <Typography className={cn("py-0.5 pl-2.5 pr-3 mb-3 w-fit text-sm !mt-0 rounded-full flex gap-x-1 items-center ",
          {
            "text-lime-900 bg-lime-100": isPublicAccess,
            "text-sky-900 bg-sky-100": !isPublicAccess
          },
        )}>
          <div className={cn("size-2 mt-0.5 rounded-full",
            {
              "bg-lime-600": isPublicAccess,
              "bg-sky-600": !isPublicAccess
            },
          )} />
          {service.access}
        </Typography>
        <div className="w-fit flex items-center ml-auto gap-x-3">
          <ButtonTooltip content="Edit Service">
            <button
              className="bg-transparent text-subtext rounded-lg min-[480px]:p-1.5 min-[480px]:text-paragraph min-[480px]:bg-stone-200 active:scale-[.95]  "
              onClick={() => props.onEdit(service.urlName)}
            >
              <Edit02Icon width={18} height={18} />
            </button>
          </ButtonTooltip>
          <ButtonTooltip content="Copy booking link">
            <button
              className="bg-transparent text-subtext rounded-lg min-[480px]:p-1.5 min-[480px]:text-paragraph min-[480px]:bg-stone-200 active:scale-[.95]  "
              onClick={() =>
                handleCopyToClipboard(
                  import.meta.env.VITE_FRONTEND_URL +
                  `/booking/external/${user?.username}/${service.urlName}/`
                )
              }
            >
              <Copy01Icon width={18} height={18} />
            </button>
          </ButtonTooltip>
          <ButtonTooltip content="Book Internal Appointment">
            <button
              className="bg-transparent text-subtext rounded-lg min-[480px]:p-1.5 min-[480px]:text-paragraph min-[480px]:bg-stone-200 active:scale-[.95]  "
              onClick={() =>
                navigate(
                  `/booking/internal/${user?.username}/${service.urlName}/`
                )
              }
            >
              <LinkSquare01Icon width={18} height={18} />
            </button>
          </ButtonTooltip>
          <ButtonTooltip content="Delete Service">
            <button
              className="bg-transparent text-subtext rounded-lg min-[480px]:p-1.5 min-[480px]:text-paragraph min-[480px]:bg-stone-200 active:scale-[.95]  "
              onClick={() => {
                modalFunctions.openModal('dialog', {
                  props: {
                    title: 'Delete '.concat(service.name).concat('?'),
                    action: {
                      title: 'Delete',
                      onConfirm() {
                        deleteServiceMutation.mutate(service.urlName, {
                          onSuccess: () => {
                            queryClient.invalidateQueries({
                              queryKey: ["services"],
                            });
                            showToast("success", "Service deleted successfully");
                          },
                          onError: () => {
                            showToast("error", "Failed to delete service");
                          },
                        })

                      },
                    }
                  }
                })
              }}
            >
              <Delete02Icon width={18} height={18} />
            </button>
          </ButtonTooltip>
        </div>
      </div>
      <div className="flex-wrap flex items-center gap-[10px] pb-2 ">
        {service.serviceOptions?.map((option, index) => (
          <Typography
            key={index}
            className="py-0.5 px-3 text-xs !mt-0 text-gray-600 bg-gray-100 rounded-full "
          >
            {option.name}{" "}
            <span className="text-green-500 font-bold">{formatPrice(option.price || 0)}</span>
          </Typography>
        ))}
      </div>

      <div className="flex items-center justify-end gap-x-3">
        <Button
          variant="dormant"
          className="text-sm whitespace-nowrap flex items-center gap-x-2 pl-2"
          onTap={() => navigate(`/scheduling/intake-form/${service.urlName}`)}
        >
          <GoogleSheetIcon size={18} /> Intake Form
        </Button>
      </div>
      {modals.dialog.props &&
        <Dialog
          open={modals.dialog.open}
          onClose={() => modalFunctions.closeModal('dialog')}
          {...modals.dialog.props}
        />}
    </div>
  );
};

export default Service
