import GoogleLogo from "@/assets/images/svg-logos/google-logo.svg";
import OutlookLogo from "@/assets/images/svg-logos/outlook-logo.png";
import StripeLogo from "@/assets/images/svg-logos/stripe-logo.svg";
import { But<PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useToast } from "~/contexts/hooks/toast";
import { GlobalAxiosError } from "~/interfaces/globals";
import {
  ConnectExternalAccount,
  DisconnectExternalAccount,
  GetExternalAccount,
  ToggleActivateExternalAccount,
} from "~/services/settings.service";
import useAuthStore from "../auth/components/auth-store";
import PendingOverlay from "@/components/ui/pending-overlay";

interface ExternalAccountI {
  name: string;
  connect: boolean;
  activate: boolean;
  connectedEmail?: string;
  tokenExpiresAt?: Date;
}

const iconMap: { [key: string]: string } = {
  google: GoogleLogo,
  outlook: OutlookLogo,
  stripe: StripeLogo,
};
const AccountSettings = () => {
  const {
    initialState: { user },
  } = useAuthStore();
  const [externalAccounts, setExternalAccounts] = useState<ExternalAccountI[]>(
    []
  );
  const showToast = useToast();
  const queryClient = useQueryClient();
  const accountIcon = (name: string): string => {
    return iconMap[name] || "";
  };
  const connectAccount = async (serviceName: string) => {
    try {
      function createQueryParams(
        params: Record<string, unknown>
      ): Record<string, string> {
        return Object.fromEntries(
          Object.entries(params)
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            .filter(([_, value]) => value !== undefined)
            .map(([key, value]) => [key, String(value)]) // Convert values to strings
        );
      }
      if (serviceName === "stripe") {
        const state = serviceName;
        const queryParams = new URLSearchParams(
          createQueryParams({
            response_type: "code",
            client_id: import.meta.env.VITE_STRIPE_CLIENT_ID,
            scope: "read_write",
            state: state,
            redirect_uri: `${import.meta.env.VITE_FRONTEND_URL}/oauth/callback`,
            "stripe_user[email]": user?.email,
            // this should actually use the user's country but we cant get that from the user settings object yet
            "stripe_user[country]": '',
          })
        );
        const stripeUrl = `https://connect.stripe.com/oauth/authorize?${queryParams.toString()}`;
        window.open(stripeUrl, "_blank");
      }
      const response = await ConnectExternalAccount(serviceName);
      if (response?.status === 201 || response?.status === 200) {
        window.open(response.data.authUrl, "_blank");
      }
    } catch (error) {
      console.error("Error connecting Google:", error);
    }
  };
  const { data, error } = useQuery({
    queryFn: GetExternalAccount,
    queryKey: ["external-accounts"],
  });
  useEffect(() => {
    setExternalAccounts(data?.data?.externalAccounts);
  }, [data]);
  if (error) {
    const axiosError = error as GlobalAxiosError;
    showToast(
      "error",
      axiosError.response?.data?.error || "An unexpected error occurred"
    );
  }
  const toggleAccountMutation = useMutation({
    mutationFn: ToggleActivateExternalAccount,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["external-accounts"] });
      showToast("success", data?.data?.message);
    },
    onError: (error: GlobalAxiosError) => {
      showToast("error", error?.response?.data?.error || error?.message);
    },
  });
  const disconnectAccountMutation = useMutation({
    mutationFn: DisconnectExternalAccount,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["external-accounts"] });
      showToast("success", data?.data?.message);
    },
    onError: (error: GlobalAxiosError) => {
      showToast("error", error?.response?.data?.error || error?.message);
    },
  });
  const toggleActivation = async (serviceName: string, activate: boolean) => {
    toggleAccountMutation.mutate({
      service: serviceName,
      activate: activate,
    });
  };
  const disconnectAccount = async (serviceName: string) => {
    disconnectAccountMutation.mutate(serviceName);
  };
  return (
    <section className="mt-4" id="accounts">
      <PendingOverlay
        isPending={
          toggleAccountMutation.isPending || disconnectAccountMutation.isPending
        }
      />
      <div className="pb-3 ">
        <Typography className="text-gray-500 pb-5 rounded-xl ">
          Here you can setup and manage your integration settings.
        </Typography>
      </div>
      <div className="flex flex-col gap-y-2">
        {externalAccounts?.map((service, index) => (
          <div
            key={index}
            data-isconnected={service?.connect}
            className="flex flex-col justify-between data-[isconnected=false]:flex-row bsm:flex-row bsm:items-start"
          >
            <div className="flex items-center space-x-3 bsm:mt-3 ">
              <img
                className="w-[30px]"
                src={accountIcon(service?.name)}
                alt={`logo_${service?.name}`}
              />
              <div className="text-[20px]">
                <Typography className="capitalize">{service?.name}</Typography>
              </div>
            </div>
            <div className="bg-gray-100/80 rounded-2xl py-3 px-3 my-4 space-y-2 ">
              {service?.connect && (
                <>
                  <Typography className="text-sm font-bold my-auto">
                    Connected account:
                  </Typography>
                  <Typography className="text-sm my-auto">
                    {service?.connectedEmail}
                  </Typography>
                </>
              )}
              <div className="flex items-center gap-x-2">
                {service?.connect ? (
                  <Button
                    disabled={disconnectAccountMutation.isPending}
                    onTap={() => disconnectAccount(service?.name)}
                    className=" mb-0"
                    type="button"
                  >
                    Disconnect
                  </Button>
                ) : (
                  <Button
                    onTap={() => connectAccount(service?.name)}
                    className=" mb-0"
                    type="button"
                  >
                    Connect
                  </Button>
                )}
                {service?.connect ? (
                  service?.activate ? (
                    <Button
                      onTap={() =>
                        toggleActivation(service?.name, !service?.activate)
                      }
                      className=" mb-0"
                      type="button"
                    >
                      Deactivate
                    </Button>
                  ) : (
                    <Button
                      onTap={() =>
                        toggleActivation(service?.name, !service?.activate)
                      }
                      className=" mb-0"
                      type="button"
                    >
                      Activate
                    </Button>
                  )
                ) : null}
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default AccountSettings;
