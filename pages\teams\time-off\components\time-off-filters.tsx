import { Input } from "@/components/inputs";
import SearchableDropdown from "@/components/ui/searchable-dropdown";
import { PTOType } from "@/src/interfaces/time-off";
import { Staff } from "@/src/interfaces/staff";

type TimeOffFiltersType = {
  search: string;
  type: PTOType | '';
  status: "approved" | "pending" | "rejected" | '';
  staffId: string;
};

interface TimeOffFiltersProps {
  filters: TimeOffFiltersType,
  onFiltersChange: (filters: TimeOffFiltersType) => void;
  staff: Staff[];
}

const PTO_TYPE_OPTIONS = [
  { value: "", label: "All Types" },
  { value: "vacation", label: "Vacation" },
  { value: "sick", label: "Sick Leave" },
  { value: "personal", label: "Personal" },
  { value: "meeting", label: "Meeting" },
  { value: "training", label: "Training" },
  { value: "other", label: "Other" },
];

const STATUS_OPTIONS = [
  { value: "", label: "All Statuses" },
  { value: "pending", label: "Pending" },
  { value: "approved", label: "Approved" },
  { value: "rejected", label: "Rejected" },
];

const TimeOffFilters: React.FC<TimeOffFiltersProps> = ({
  filters,
  onFiltersChange,
  staff,
}) => {
  const staffOptions = [
    { value: "", label: "All Staff" },
    ...staff.map((s) => ({ value: s._id, label: s.name })),
  ];

  return (
    <div className="bg-white p-4 rounded-lg border space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Input.Text
          label="Search"
          name="search"
          value={filters.search}
          onChange={(e) =>
            onFiltersChange({ ...filters, search: e.target.value })
          }
          placeholder="Search descriptions..."
        />
        <div className="flex items-center gap-x-4 ">
          <SearchableDropdown
            label="Type"
            name="type"
            value={filters.type}
            onChange={(option) => {
              if (!option) return;
              onFiltersChange({ ...filters, type: option?.value as PTOType });
            }}
            options={PTO_TYPE_OPTIONS}
          />

          <SearchableDropdown
            label="Status"
            name="status"
            value={filters.status}
            onChange={(option) => {
              if (!option) return;
              onFiltersChange({ ...filters, status: option?.value as '' });
            }}
            options={STATUS_OPTIONS}
          />
        </div>
        <SearchableDropdown
          label="Staff Member"
          name="staffId"
          value={filters.staffId}
          onChange={(option) => {
            if (!option) return;
            onFiltersChange({ ...filters, staffId: option?.value });
          }}
          options={staffOptions}
        />
      </div>
    </div>
  );
};

export default TimeOffFilters;
