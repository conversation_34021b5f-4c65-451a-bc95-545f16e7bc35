import { Button } from "@/components/buttons";
import ErrorScreen from "@/components/ui/error-screen";
import Tabs from "@/components/ui/tabs";
import Toggle from "@/components/ui/toggle-2";
import { pick } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { PlusIcon } from "lucide-react";
import React, { memo, useMemo, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { type TLead } from "~/interfaces/leads";
import { LeadsCart as TLeadsCart } from "~/services/leads.service";
import { CreditIcon, LocationIcon } from "./badges";
import LeadSkeleton from "./leads-skeleton";
import Pagination from "./pagination";

type LeadProps = {
  lead: TLead;
  idx: string;
  leadsCart: TLeadsCart["leads"];
  numericId: number;
  onSelectLead: LeadListProps["onSelectLead"];
  onDeSelectLead: LeadListProps["onDeSelectLead"];
};

const bgColors = [
  "bg-green-500",
  "bg-yellow-500",
  "bg-amber-500",
  "bg-sky-500",
];

const Lead = memo(
  ({ lead, idx, leadsCart, numericId, onSelectLead, ...props }: LeadProps) => {
    const leadSelected = useMemo(() => {
      return Boolean(leadsCart.find((value) => value._id === idx));
    }, [leadsCart, idx]);
    function selectLead() {
      onSelectLead({ ...lead });
    }
    function deSelectLead() {
      props.onDeSelectLead({ ...lead });
    }
    const navigate = useNavigate();

    return (
      <div
        className="py-4 px-2 w-full flex flex-col border-b transition-colors cursor-pointer last:border-b-2"
        key={idx}
      >
        {/* Lead Header Section */}
        <div className="flex justify-between items-center mb-2">
          <div className="flex items-center w-full">
            <div
              className={`min-w-10 min-h-10 rounded-full flex items-center justify-center text-white font-bold ${
                bgColors[numericId % bgColors.length]
              }`}
            >
              {lead.name.charAt(0)}
            </div>
            <div className="ml-4 w-[70%]">
              <h3 className="text-lg w-full overflow-hidden overflow-ellipsis whitespace-nowrap font-semibold">
                {lead.name}
              </h3>
              <p className="text-sm text-gray-500 flex items-center">
                {lead.town}
              </p>
            </div>
          </div>
        </div>

        {/* Lead Description */}
        <p className="text-sm text-gray-700 w-full leading-5 line-clamp-4 ">
          {lead.description}
        </p>

        <div className="flex justify-between mt-5 items-center">
          <p className="text-gray-600 text-sm flex gap-x-2 items-center">
            <CreditIcon /> {lead.requiredCredit} Credits
          </p>
          <div className="w-fit ml-auto flex relative">
            <Button
              onTap={() => navigate(`/leads/${lead._id}`)}
              variant="ghost"
              className="font-semibold flex items-center ml-auto gap-2 origin-center"
            >
              View
            </Button>
            <Button
              onTap={() => (!leadSelected ? selectLead() : deSelectLead())}
              className={cn(
                "font-semibold flex items-center ml-auto gap-2 bg-pink-500 ",
                {
                  "bg-red-600": leadSelected,
                }
              )}
            >
              <AnimatePresence mode="wait">
                {leadSelected ? (
                  <motion.span
                    key="remove"
                    className="inline-block"
                    initial={{ opacity: 0, y: -40 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -40, transition: { delay: -0.3 } }}
                  >
                    Remove
                  </motion.span>
                ) : (
                  <motion.span
                    key="add"
                    className="inline-flex items-center gap-2"
                    initial={{ opacity: 0, y: 40 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 40, transition: { delay: -0.3 } }}
                  >
                    Add <PlusIcon className="stroke-white" />
                  </motion.span>
                )}
              </AnimatePresence>
            </Button>
          </div>
        </div>
      </div>
    );
  }
);

type LeadListProps = {
  noOfLocations: number;
  leadsCart: TLeadsCart["leads"];
  paginationProps: {
    itemsPerPage: number;
    totalBusinesses: number;
    totalPages: number;
  };
  leadsData: {
    leads: Array<TLead>;
    isLoading: boolean;
    error: Error | null;
  };
  onRetry: () => void;
  onSelectLead: (lead: TLead) => void;
  onDeSelectLead: (lead: TLead) => void;
  onSelectAllLeads: (lead: Array<TLead>) => void;
};

const LeadList: React.FC<LeadListProps> = ({
  leadsData,
  noOfLocations,
  onSelectLead,
  leadsCart,
  onSelectAllLeads,
  ...props
}) => {
  const checked = useMemo(() => {
    if (!leadsData.leads.length) return false;
    else if (leadsData.leads.length === leadsCart.length) return true;
  }, [leadsCart.length, leadsData.leads.length]);
  const listRef = useRef<HTMLElement>(null);
  const { paginationProps } = props;
  const totalLeadsForIndustryAndLoc = useMemo(
    () => paginationProps.totalBusinesses,
    [paginationProps.totalBusinesses]
  );

  return (
    <section
      ref={listRef}
      className="flex flex-col w-full gap-y-8 md:w-[70%] lg:flex-grow"
    >
      {/* Header Section */}
      <section className="flex flex-col justify-between items-start gap-y-4 mb-4 md:flex-row">
        <div className="flex-col flex gap-y-1">
          <h1 className="text-3xl font-bold">
            {totalLeadsForIndustryAndLoc} matching leads
          </h1>
          <div className="flex items-end text-gray-500 gap-x-1">
            <LocationIcon className="fill-paragraph" />
            <p className="font-medium text-paragraph ">
              {noOfLocations} locations
            </p>
          </div>
        </div>

        <div className="flex items-center pr-4 py-1 text-gray-800 gap-x-6">
          {/* Checkbox with red background */}
          <span className="font-bold">Select all leads</span>{" "}
          {/* Text is now bold */}
          <Toggle
            checked={Boolean(checked)}
            onChange={(checked) => {
              onSelectAllLeads(
                checked ? leadsData.leads.map((value) => ({ ...value })) : []
              );
            }}
          />
        </div>
      </section>

      <section className="w-full h-fit">
        <Tabs defaultValue="best-matches" className="w-full ">
          <Tabs.List className="grid w-full max-w-[320px] grid-cols-2 md:flex-grow ">
            <Tabs.Trigger
              value="best-matches"
              className=" px-0 flex items-center justify-center gap-x-2"
            >
              Best Matches
            </Tabs.Trigger>
            <Tabs.Trigger
              value="most-recent"
              className=" px-0 flex items-center justify-center gap-x-2"
            >
              Most Recent
            </Tabs.Trigger>
          </Tabs.List>
          <Tabs.Content
            value="best-matches"
            className="mt-2 focus:outline-none active:outline-none w-full"
          >
            {/* replace with error screen */}
            {leadsData.error && (
              <ErrorScreen
                message={"Try searching for another industry and location."}
                title="No businesses found"
                className="mt-10"
              />
            )}
            {leadsData.isLoading &&
              [1, 2, 3].map((lead) => <LeadSkeleton key={lead} />)}
            {!leadsData.isLoading && !leadsData.error && (
              <Pagination
                {...pick(paginationProps, "itemsPerPage", "totalPages")}
                listRef={listRef}
                data={leadsData.leads}
                renderItems={(lead, index) => {
                  return (
                    <Lead
                      key={index}
                      numericId={index}
                      idx={lead._id}
                      lead={lead}
                      onSelectLead={onSelectLead}
                      onDeSelectLead={props.onDeSelectLead}
                      leadsCart={leadsCart}
                    />
                  );
                }}
              />
            )}
          </Tabs.Content>
          <Tabs.Content
            value="most-recent"
            className="mt-2 focus:outline-none active:outline-none w-full"
          >
            {/* replace with error screen */}
            {leadsData.error && (
              <ErrorScreen
                message={"Try searching for another industry and location."}
                title="No businesses found"
                className="mt-10"
              />
            )}
            {leadsData.isLoading &&
              [1, 2, 3].map((lead) => <LeadSkeleton key={lead} />)}
            {!leadsData.isLoading && !leadsData.error && (
              <Pagination
                {...pick(paginationProps, "itemsPerPage", "totalPages")}
                listRef={listRef}
                data={leadsData.leads}
                renderItems={(lead, index) => {
                  return (
                    <Lead
                      key={index}
                      numericId={index}
                      idx={lead._id}
                      lead={lead}
                      onSelectLead={onSelectLead}
                      onDeSelectLead={props.onDeSelectLead}
                      leadsCart={leadsCart}
                    />
                  );
                }}
              />
            )}
          </Tabs.Content>
        </Tabs>
      </section>
    </section>
  );
};

export default memo(LeadList);
