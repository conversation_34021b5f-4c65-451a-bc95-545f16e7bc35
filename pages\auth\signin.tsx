import SigninLottie from "@/assets/lottie-animations/signin.json";
import { Button } from "@/components/buttons";
import { Input } from "@/components/inputs";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import { cn } from "@/lib/utils";
import { useMutation } from "@tanstack/react-query";
import { useFormik } from "formik";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { object, string } from "yup";
import { useToast } from "~/contexts/hooks/toast";
import LongArrow from "~/icons/long-arrow";
import { LoginUser } from "~/services/auth.service";
import EmailVerificationScreen from "./components/email-verification-screen";
import Lottie from "lottie-react";
import { useModalsBuilder } from "@/lib/modals-builder";
import Logo from '@/assets/logo.png'

export default function Signin() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const showToast = useToast();
  const { modals: screens, modalFunctions: screenFunctions } = useModalsBuilder({
    verifyEmail: { open: false },
  });
  const { mutateAsync, isPending } = useMutation({
    mutationFn: LoginUser,
    onSuccess: (data) => {
      localStorage.setItem("PPToken", data?.data?.token);
      navigate(searchParams.get("redirect") || "/");
      setTimeout(() => {
        showToast("success", "Logged In", {
          description: "You have been logged in successfully.",
        });
      }, 2000);
    },
    onError: (
      error: ApiError<{ clientSecret: string; status: string; planId: string }>
    ) => {
      switch (true) {
        case error.message.includes("Network"):
          showToast("error", "Network Error", {
            description: "Please check your internet connection and try again.",
            action: {
              title: "Retry",
              onClick() {
                formik.handleSubmit();
                return true;
              },
            },
          });
          break;
        case error.status === 401:
          showToast("error", "Login Error", {
            description:
              "Invalid email or password. Please check your email and password and try again.",
          });
          break;
        case error.status === 403:
          screenFunctions.openModal('verifyEmail', {});
          break;
        // this wont return a 402 error
      }
    },
  });
  const validationSchema = object({
    email: string().required("Email is required"),
    password: string().required("Password is required"),
  });
  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    validationSchema,
    onSubmit: (values) => mutateAsync(values),
  });

  if (screens.verifyEmail.open) return (
    <EmailVerificationScreen
      emailAddress={formik.values.email}
    />

  )
  return (
    <section className="w-screen h-screen flex items-center">
      <section className="w-screen h-screen bsm:max-w-[400px] bsm:mx-auto md:bsm:mx-0 overflow-y-auto flex flex-col no-scrollbar ">
        <div className="w-full flex flex-col flex-grow pt-10 items-center gap-y-3 justify-center">
          <img src={Logo} className="w-full size-28 object-contain  " />
          <Typography variant={'h3'} className="font-bold text-3xl text-center">
            Puzzle Piece <br /> Solutions
          </Typography>
        </div>

        <form
          onSubmit={formik.handleSubmit}
          className="w-full py-5 px-4 bottom-0 gap-y-10 flex-grow flex flex-col md:px-6"
        >
          <div className="w-full space-y-7 ">
            <Input.Email
              label="Email"
              name="email"
              required
              onChange={(e) => formik.setFieldValue("email", e.target.value)}
            />
            <div className="flex flex-col gap-y-2 " >
              <Input.Password
                label="Password"
                name="password"
                onChange={(e) => formik.setFieldValue("password", e.target.value)}
              />
              <Link to={'/request-password-reset'} className="text-sm font-medium hover:underline text-primary-600">Forgot password?</Link>
            </div>
          </div>
          <div className="w-full flex items-center justify-between">
            <Typography variant={"h2"} className="font-bold ">
              Sign In
            </Typography>
            <Button
              type="submit"
              variant="icon"
              className={cn("px-5 !rounded-full ", {
                "py-6": !isPending,
                "py-5": isPending,
              })}
            >
              <LongArrow
                fill="white"
                className={cn({
                  hidden: isPending,
                  block: !isPending,
                })}
              />
              <CircularLoader
                color="white"
                className={cn({
                  hidden: !isPending,
                  block: isPending,
                })}
              />
            </Button>
          </div>
          <div className="w-full flex items-center justify-center !mt-auto">
            <Typography variant={"span"} className="text-subtext text-sm ">
              Don't have an account?{" "}
              <Link className="text-primary-600 underline" to={"/onboarding"}>
                Sign up
              </Link>{" "}
            </Typography>
          </div>
        </form>
      </section>
      <section className={`hidden h-full flex-grow md:block overflow-hidden `}>
        <Lottie
          animationData={SigninLottie}
          loop={true}
          className=" aspect-square  "
        />
      </section>
    </section>
  );
}
