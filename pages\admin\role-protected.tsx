import useAuthStore from "../auth/components/auth-store";
import { ReactNode } from "react";
import NotAuthorized from "./user-not-authorized";
interface ProtectedRouteProps {
  children: ReactNode;
  roles: Array<NonNullable<User['role']>>
}

const RoleProtected: React.FC<ProtectedRouteProps> = ({ children, roles }) => {
  const {
    initialState: { user },
  } = useAuthStore();
  if (!roles.includes(user?.role as any)) {
    return <NotAuthorized />
  }

  return <>{children}</>;
};

export default RoleProtected;
