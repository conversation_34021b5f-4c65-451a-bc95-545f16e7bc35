import { SubscriptionPlan } from "@/src/interfaces/subscription-plan";
import { AxiosResponse } from "axios";
import { api } from "./api.service";

export const LoginUser = async (payload: {
  email: string;
  password: string;
}): Promise<AxiosResponse> => {
  try {
    const response = await api.post(`/login/`, payload);
    return response;
  } catch (error) {
    console.error("Login error:", error);
    throw error;
  }
};

export type SignupUserPayload = {
  business_name: string;
  first_name: string;
  last_name: string;
  username: string;
  email: string;
  country: string;
  password: string;
  planId: SubscriptionPlan["_id"];
  planInterval: "week" | "month" | "year";
  address: string;
  address_2: string;
  town: string;
  postcode: string;
};

export const SignupUser = async (
  payload: SignupUserPayload,
): Promise<
  AxiosResponse<{
    message: string;
    userId: string;
    clientSecret: string;
  }>
> => {
  try {
    const response = await api.post(`/register/`, payload);
    return response;
  } catch (error) {
    console.log("signup error:", error);
    throw error;
  }
};

export type UserDetails = {
  user: User;
  calendarSettings: CalenderSettings;
  userSettings: UserSettings;
};

export const GetUserDetails = async () => {
  try {
    const response = await api.get<{ success: boolean, message: string, data: UserDetails }>(`/user/details`);
    return response;
  } catch (error) {
    console.error("get-user-details error:", error);
    throw error;
  }
};

type SubscriptionPlansResponse = {
  plans: Array<
    Omit<SubscriptionPlan, "yearlyPrice"> & {
      yearlyPrice: number;
    }
  >;
};

export const GetSubscriptionPlans = async () => {
  try {
    const response =
      await api.get<SubscriptionPlansResponse>(`/subscription-plans`);
    return response;
  } catch (error) {
    console.error("get-subscription-plans error:", error);
    throw error;
  }
};

export type PaymentDetails = {
  error: string;
  clientSecret: string;
  plan: Prettify<SubscriptionPlan>;
  status: string;
};

export const CheckSubscriptionStatus = async (): Promise<
  AxiosResponse<PaymentDetails | null>
> => {
  try {
    const response = await api.get(`/check-subscription-status`);
    return response;
  } catch (error) {
    console.log("check-subscription-status error:", error);
    throw error;
  }
};

/** 
 * @dev this is the password reset flow
 * */
export const PasswordReset = {
  async RequestReset(payload: { email: string }) {
    try {
      const response = await api.post(`/auth/request-password-reset`, payload);
      return response;
    } catch (error) {
      console.log("check-subscription-status error:", error);
      throw error;
    }
  },
  async Reset(payload: { token: string, data: { newPassword: string } }) {
    try {
      const response = await api.post(`/auth/reset-password/${payload.token}`, payload.data);
      return response;
    } catch (error) {
      console.log("check-subscription-status error:", error);
      throw error;
    }
  }
}

/**
 * @todo Ahmed make the route for change password and remove this comment
 */
export const UpdateUserPassword = async (payload: {
  currentPassword: string;
  newPassword: string;
}): Promise<AxiosResponse> => {
  try {
    const response = await api.put(`/user/password/`, payload);
    return response;
  } catch (error) {
    console.error("update-user-password error:", error);
    throw error;
  }
};

export const UploadUserProfileImage = async (
  formData: FormData,
): Promise<AxiosResponse> => {
  try {
    const response = await api.post(`/user/profile-image`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export type UserInfoPayload = {
  business_bio?: string | undefined;
  timeFormat?: "12h" | "24h" | undefined;
  dateFormat?: string | undefined;
  first_name: string;
  last_name: string;
  username: string;
  business_name: string;
  email: string;
  address: string;
  address_2: string;
  phone: string;
  town: string;
  postcode: string;
};

export const UpdateUserInfo = async (
  payload: UserInfoPayload,
): Promise<AxiosResponse> => {
  try {
    const response = await api.put(`/user/info/`, payload);
    return response;
  } catch (error) {
    console.error("update-user-info error:", error);
    throw error;
  }
};

export const UpgradeSubscription = async (
  planId: string,
): Promise<
  AxiosResponse<{
    success: boolean;
    message: string;
    subscription: UserSubscription;
    clientSecret: string;
  }>
> => {
  try {
    const response = await api.put(`/upgrade-subscription`, {
      planId,
    });
    return response;
  } catch (error) {
    console.error("user-interaction-error", error);
    throw error;
  }
};

/**
 * @dev if this sends a 400 error, it means the token is invalid or expired
 * @dev then the user should be asked to login and get a new token
 */
export const ConfirmUserEmail = async (
  token: string,
): Promise<AxiosResponse> => {
  try {
    const response = await api.get(`/auth/confirm-email/${token}`);
    return response;
  } catch (error) {
    console.error("confirm-email error:", error);
    throw error;
  }
};
