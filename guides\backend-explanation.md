# Puzzle Piece API Documentation

## Table of Contents

1. [Overview](#overview)
2. [Getting Started](#getting-started)
   - [Base URL](#base-url)
   - [Authentication](#authentication)
   - [Rate Limiting](#rate-limiting)
3. [Core Endpoints](#core-endpoints)
   - [Authentication](#authentication-endpoints)
   - [User Management](#user-management)
   - [Services](#services)
   - [Service Option](#service-options)
   - [Scheduling Rules](#scheduling-rules)
   - [Recurring Bookings](#recurring-bookings)
   - [Intake Forms](#intake-forms)

- [Bookings](#bookings)
- [Client Self-Service](#client-self-service)
- [Digital Profile](#digital-profile)
- [Booking Settings](#booking-settings)
  - [Calendar Settings](#calendar-settings)
- [Settings](#settings)
- [Teams & Staff Management](#teams--staff-management)
- [Location Management](#location-management)
- [Staff Assignment & Routing](#staff-assignment--routing)
- [Email Service & Notifications](#email-service--notifications)
- [ICS Calendar Integration](#ics-calendar-integration)
- [PTO Management](#pto-management)
- [Staff Performance & Analytics](#staff-performance--analytics)
- [External Accounts](#external-accounts)
- [Email Settings](#email-settings)
- [Resources](#resources)

4. [Business Features](#business-features)
   - [Subscriptions](#subscriptions)
   - [Wallet & Credits](#wallet--credits)
   - [Business Leads](#business-leads)
   - [Business Clients](#business-clients)
   - [CSV Import](#csv-import)
   - [Analytics](#analytics)
5. [Admin Endpoints](#admin-endpoints)
   - [User Management](#admin-user-management)
   - [User Logs](#user-logs)
   - [FAQ Management](#faq-management)
   - [Blog Management](#blog-management)
   - [Tag Management](#tag-management)
   - [Support Tickets](#support-tickets)
   - [Promo Codes](#promo-codes)
   - [Subscription Products](#subscription-products)
   - [Subscription Prices](#subscription-prices)
   - [Credit Packages](#credit-packages)
   - [Maintenance Mode](#maintenance-mode)
6. [Error Handling](#error-handling)
7. [Webhooks](#webhooks)
8. [Contact](#contact)

---

## Overview

Welcome to the **Puzzle Piece API**! This comprehensive API provides access to the Puzzle Piece scheduling and business management platform.

### Features:

- **User Authentication & Management**: Secure registration, login, and profile management
- **Service Management**: Create, update, and manage business services
- **Booking System**: Internal and external booking capabilities with payment integration
- **Client Self-Service**: Token-based reschedule and cancel functionality for customers
- **Digital Profiles**: Customizable business profiles with website links
- **Calendar Integration**: Google Calendar and other external calendar support
- **Analytics & Reporting**: Comprehensive business analytics and insights
- **Subscription Management**: Flexible subscription plans with Stripe integration
- **Lead Management**: B2B lead generation and management
- **Client Management**: Customer relationship management tools
- **CSV Import**: Bulk import contacts, services, and bookings from CSV files
- **Admin Dashboard**: Complete administrative control panel
- **Team & Staff Management**: Multi-user team management with staff accounts and automatic verification emails
- **Location Management**: Multi-location business support with automatic geocoding, location-based services, staff assignment, and comprehensive analytics
- **Email Services**: Automated email notifications, staff verification, and marketing with business branding
- **Calendar Integration**: ICS (iCalendar) file generation for booking emails with automatic calendar event creation
- **Wallet & Credits**: Credit-based purchasing system
- **Staff Verification System**: Automatic credential emails for new staff members with security instructions

---

## Getting Started

### Base URL

The base URL for all API requests is:

```plaintext
https://api.puzzlepiecesolutions.com/api
```

### Authentication

This API uses **Bearer Token** authentication for secure access.

- **Authentication Header:**
  ```plaintext
  Authorization: Bearer <your_token_here>
  ```

### Rate Limiting

The API implements rate limiting to prevent abuse:

- **Default Limit**: 100 requests per 15 minutes per IP
- **Response**: 429 Too Many Requests when limit exceeded

---

## Core Endpoints

### Authentication Endpoints

#### 1. Register User

**Endpoint:** `POST /register`
**Description:** Creates a new user account.
**Request:**

```http
POST /register HTTP/1.1
Host: api.puzzlepiecesolutions.com
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "password": "securePassword123!",
  "username": "johndoe123"
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "message": "User registered successfully",
  "user": {
    "_id": "user_id",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "username": "johndoe123"
  }
}
```

#### 2. Login User

**Endpoint:** `POST /login`
**Description:** Authenticates a user and returns a JWT token.
**Request:**

```http
POST /login HTTP/1.1
Host: api.puzzlepiecesolutions.com
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123!"
}
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Login successful",
  "token": "jwt_token_here",
  "user": {
    "_id": "user_id",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>"
  }
}
```

#### 3. Verify Email

**Endpoint:** `GET /auth/confirm-email/:token`
**Description:** Verifies user email address using token.
**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Email verified successfully"
}
```

#### 4. Request Password Reset

**Endpoint:** `POST /auth/request-password-reset`
**Description:** Sends password reset email.
**Request:**

```http
POST /auth/request-password-reset HTTP/1.1
Host: api.puzzlepiecesolutions.com
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

#### 5. Reset Password

**Endpoint:** `POST /auth/reset-password/:token`
**Description:** Resets password using token.
**Request:**

```http
POST /auth/reset-password/:token HTTP/1.1
Host: api.puzzlepiecesolutions.com
Content-Type: application/json

{
  "password": "newPassword123!"
}
```

### User Management

#### 1. Get User Details

**Endpoint:** `GET /user/details`
**Description:** Retrieves authenticated user details with calendar settings.
**Headers:** `Authorization: Bearer <token>`
**Response:**

- **200 OK**

```json
{
  "success": true,
  "user": {
    "_id": "user_id",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>",
    "username": "johndoe123"
  },
  "calendarSettings": {}
}
```

#### 2. Update User Details

**Endpoint:** `PUT /user/info`
**Description:** Updates user profile information.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /user/info HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Smith",
  "phone": "+**********"
}
```

#### 3. Upload Profile Image

**Endpoint:** `POST /upload-profile-image`
**Description:** Uploads user profile image.
**Headers:** `Authorization: Bearer <token>`
**Request:** `multipart/form-data` with `image` field

#### 4. Check Subscription Status

**Endpoint:** `GET /check-subscription-status`
**Description:** Checks user's current subscription status.
**Headers:** `Authorization: Bearer <token>`

### Services

Services are the core offerings that businesses provide to their clients. Each service can have multiple service options attached to it, allowing for customization and add-ons.

**Service Options Integration:**

- Services can reference multiple service options via `serviceOptions` array
- When fetching services, options are populated with full details
- Options are validated during service creation/updates

#### 1. Create Service

**Endpoint:** `POST /create-service`
**Description:** Creates a new business service.
**Headers:** `Authorization: Bearer <token>`
**Request:** `multipart/form-data` with service details and `picture` field

**Form Data Fields:**

- `name`: Service name
- `description`: Service description
- `price`: Service price
- `duration`: Service duration in minutes
- `category`: Category ID
- `picture`: Service image (optional)
- `serviceOptions`: JSON string array of service option IDs (optional)

**Example:**

```http
POST /create-service HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: multipart/form-data

Form Data:
name: "Haircut Service"
description: "Professional haircut and styling"
price: 50.00
duration: 60
category: "category_id"
serviceOptions: ["option_id_1", "option_id_2"]
picture: <image_file>
```

#### 2. Get Services

**Endpoint:** `GET /service`
**Description:** Retrieves all services for authenticated user with populated service options.
**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "Services": [
    {
      "_id": "service_id",
      "userId": "user_id",
      "name": "Haircut Service",
      "description": "Professional haircut and styling",
      "price": 50.0,
      "duration": 60,
      "category": {
        "_id": "category_id",
        "name": "Hair Services"
      },
      "serviceOptions": [
        {
          "_id": "option_id_1",
          "name": "Premium Package",
          "description": "Includes additional features",
          "price": 25.0,
          "isActive": true
        }
      ],
      "picture": "image_url",
      "isActive": true,
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z"
    }
  ]
}
```

#### 3. Get Service by URL Name

**Endpoint:** `GET /service/:urlName`
**Description:** Retrieves specific service by URL name.
**Headers:** `Authorization: Bearer <token>`

#### 4. Get Service by ID

**Endpoint:** `GET /service/id/:serviceId`
**Description:** Retrieves specific service by MongoDB ObjectId.
**Headers:** `Authorization: Bearer <token>`

**Parameters:**

- `serviceId`: MongoDB ObjectId of the service

**Response:**

- **200 OK**

```json
{
  "success": true,
  "Service": {
    "_id": "service_id",
    "userId": "user_id",
    "name": "Haircut Service",
    "description": "Professional haircut and styling",
    "price": 50.0,
    "duration": 60,
    "category": {
      "_id": "category_id",
      "name": "Hair Services"
    },
    "serviceOptions": [
      {
        "_id": "option_id_1",
        "name": "Premium Package",
        "description": "Includes additional features",
        "price": 25.0,
        "isActive": true
      }
    ],
    "picture": "image_url",
    "isActive": true,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

**Error Responses:**

- **400 Bad Request**: Invalid service ID format
- **404 Not Found**: Service not found

#### 5. Update Service

**Endpoint:** `PUT /service/:urlName`
**Description:** Updates existing service.
**Headers:** `Authorization: Bearer <token>`
**Request:** `multipart/form-data` with updated service details

#### 6. Delete Service

**Endpoint:** `DELETE /delete-service/:urlName`
**Description:** Deletes a service.
**Headers:** `Authorization: Bearer <token>`

#### 7. Get Booking Services

**Endpoint:** `GET /service/bookings/:username`
**Description:** Retrieves services for external booking (public endpoint).

#### 8. Get External Booking Service

**Endpoint:** `GET /service/bookings/external/:username/:urlName`
**Description:** Retrieves specific service for external booking.

#### 9. Get Internal Booking Service

**Endpoint:** `GET /service/bookings/internal/:username/:urlName`
**Description:** Retrieves specific service for internal booking.
**Headers:** `Authorization: Bearer <token>`

### Service Options

Service options are add-ons that can be attached to any service. They allow businesses to offer additional features or upgrades to their core services. Service options are reusable across multiple services and include pricing information.

**Key Features:**

- **Reusable**: Create once, use across multiple services
- **Pricing**: Each option has its own price that gets added to the service cost
- **Active/Inactive**: Toggle availability without deleting
- **Free Plan Limit**: Free users can create up to 2 service options
- **Deletion Protection**: Cannot delete options currently in use by services

**Usage in Bookings:**

- When creating a booking, include `selectedServiceOptions` array with option IDs
- Total booking cost = service price + sum of selected option prices
- Options are validated against the service's available options

#### 1. Create Service Option

**Endpoint:** `POST /service-options`
**Description:** Creates a new service option that can be attached to any service.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /service-options HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Premium Package",
  "description": "Includes additional features",
  "price": 25.00,
  "isActive": true
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "message": "Service option created successfully",
  "serviceOption": {
    "_id": "option_id",
    "userId": "user_id",
    "name": "Premium Package",
    "description": "Includes additional features",
    "price": 25.0,
    "isActive": true,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

- **403 Forbidden** (Free plan limit)

```json
{
  "success": false,
  "error": "Free plan users can only create 2 service options. Please upgrade to create more."
}
```

- **409 Conflict** (Duplicate name)

```json
{
  "success": false,
  "error": "A service option with this name already exists"
}
```

#### 2. Get Service Options

**Endpoint:** `GET /service-options`
**Description:** Retrieves all service options for authenticated user.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `isActive`: Filter by active status (true/false)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "serviceOptions": [
    {
      "_id": "option_id",
      "userId": "user_id",
      "name": "Premium Package",
      "description": "Includes additional features",
      "price": 25.0,
      "isActive": true,
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z"
    }
  ]
}
```

#### 3. Search Service Options

**Endpoint:** `GET /service-options/search`
**Description:** Searches service options by name or description.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `q`: Search query
- `isActive`: Filter by active status

**Response:**

- **200 OK**

```json
{
  "success": true,
  "serviceOptions": [
    {
      "_id": "option_id",
      "userId": "user_id",
      "name": "Premium Package",
      "description": "Includes additional features",
      "price": 25.0,
      "isActive": true,
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z"
    }
  ]
}
```

#### 4. Get Service Option by ID

**Endpoint:** `GET /service-options/:optionId`
**Description:** Retrieves specific service option by ID.
**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "serviceOption": {
    "_id": "option_id",
    "userId": "user_id",
    "name": "Premium Package",
    "description": "Includes additional features",
    "price": 25.0,
    "isActive": true,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

- **404 Not Found**

```json
{
  "success": false,
  "error": "Service option not found"
}
```

#### 5. Update Service Option

**Endpoint:** `PUT /service-options/:optionId`
**Description:** Updates existing service option.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /service-options/:optionId HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Updated Premium Package",
  "description": "Updated description",
  "price": 30.00,
  "isActive": true
}
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Service option updated successfully",
  "serviceOption": {
    "_id": "option_id",
    "userId": "user_id",
    "name": "Updated Premium Package",
    "description": "Updated description",
    "price": 30.0,
    "isActive": true,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

- **404 Not Found**

```json
{
  "success": false,
  "error": "Service option not found"
}
```

- **409 Conflict** (Duplicate name)

```json
{
  "success": false,
  "error": "A service option with this name already exists"
}
```

#### 6. Delete Service Option

**Endpoint:** `DELETE /service-options/:optionId`
**Description:** Deletes a service option (only if not used by any services).
**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Service option deleted successfully"
}
```

- **404 Not Found**

```json
{
  "success": false,
  "error": "Service option not found"
}
```

- **400 Bad Request** (Option in use)

```json
{
  "success": false,
  "error": "Cannot delete service option. It is currently being used by one or more services.",
  "servicesCount": 2
}
```

### Scheduling Rules

Scheduling rules allow businesses to define policies for deposits, cancellation cutoffs, no-show fees, and rescheduling permissions for their services. These rules help automate business processes and ensure consistent policy enforcement.

**Key Features:**

- **Deposit Management**: Support for percentage, fixed amount, or full payment deposits
- **Cancellation Policies**: Define cutoff times for free cancellations
- **No-Show Fees**: Automatic fee calculation for missed appointments
- **Rescheduling Controls**: Set permissions and cutoff times for rescheduling
- **Flexible Policies**: Per-service customization of rules
- **Automatic Enforcement**: Fees and restrictions applied automatically

#### 1. Create Scheduling Rules

**Endpoint:** `POST /services/:serviceId/scheduling-rules`
**Description:** Creates scheduling rules for a specific service.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /services/:serviceId/scheduling-rules HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "depositType": "percentage",
  "depositAmount": 25,
  "cancelCutoff": 24,
  "noShowFee": 50,
  "allowRescheduling": true,
  "rescheduleCutoff": 2,
  "lateCancellationFee": 25,
  "isActive": true
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "message": "Scheduling rules created successfully",
  "schedulingRules": {
    "_id": "rules_id",
    "serviceId": "service_id",
    "userId": "user_id",
    "depositType": "percentage",
    "depositAmount": 25,
    "cancelCutoff": 24,
    "noShowFee": 50,
    "allowRescheduling": true,
    "rescheduleCutoff": 2,
    "lateCancellationFee": 25,
    "isActive": true,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

#### 2. Get Scheduling Rules

**Endpoint:** `GET /services/:serviceId/scheduling-rules`
**Description:** Retrieves scheduling rules for a specific service.
**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "schedulingRules": {
    "_id": "rules_id",
    "serviceId": "service_id",
    "userId": "user_id",
    "depositType": "percentage",
    "depositAmount": 25,
    "cancelCutoff": 24,
    "noShowFee": 50,
    "allowRescheduling": true,
    "rescheduleCutoff": 2,
    "lateCancellationFee": 25,
    "isActive": true
  },
  "isDefault": false
}
```

#### 3. Get All Scheduling Rules

**Endpoint:** `GET /scheduling-rules`
**Description:** Retrieves all scheduling rules for the authenticated user's services.
**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "schedulingRules": [
    {
      "_id": "rules_id",
      "serviceId": {
        "_id": "service_id",
        "name": "Haircut Service",
        "urlName": "haircut-service",
        "price": 50.0
      },
      "userId": "user_id",
      "depositType": "percentage",
      "depositAmount": 25,
      "cancelCutoff": 24,
      "noShowFee": 50,
      "allowRescheduling": true,
      "rescheduleCutoff": 2,
      "lateCancellationFee": 25,
      "isActive": true
    }
  ]
}
```

#### 4. Update Scheduling Rules

**Endpoint:** `PUT /services/:serviceId/scheduling-rules`
**Description:** Updates existing scheduling rules for a service.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /services/:serviceId/scheduling-rules HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "depositAmount": 30,
  "cancelCutoff": 48,
  "noShowFee": 75
}
```

#### 5. Delete Scheduling Rules

**Endpoint:** `DELETE /services/:serviceId/scheduling-rules`
**Description:** Deletes scheduling rules for a service (reverts to default behavior).
**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Scheduling rules deleted successfully"
}
```

#### 6. Calculate Deposit Amount

**Endpoint:** `GET /calculate-deposit`
**Description:** Calculates the required deposit amount for a booking.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `serviceId`: Service ID (required)
- `serviceOptions`: Array of service option IDs (optional)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "depositAmount": 37.5,
  "depositType": "percentage",
  "servicePrice": 50.0,
  "serviceOptionsTotal": 100.0,
  "totalPrice": 150.0
}
```

#### 7. Get Public Scheduling Policy

**Endpoint:** `GET /public/:username/:serviceName/scheduling-policy`
**Description:** Retrieves public scheduling policy information for external booking pages.

**Response:**

- **200 OK**

```json
{
  "success": true,
  "policy": {
    "hasDeposit": true,
    "depositType": "percentage",
    "depositAmount": 25,
    "cancelCutoff": 24,
    "allowRescheduling": true,
    "rescheduleCutoff": 2,
    "serviceName": "Haircut Service",
    "servicePrice": 50.0
  }
}
```

**Deposit Types:**

- `none`: No deposit required
- `percentage`: Percentage of total booking cost (0-100%)
- `fixed`: Fixed dollar amount
- `full`: Full payment required upfront

**Policy Enforcement:**

- **Deposits**: Automatically calculated and applied during booking
- **Cancellation Fees**: Applied when canceling within the cutoff period
- **No-Show Fees**: Applied when marking a booking as no-show
- **Rescheduling**: Blocked when within the rescheduling cutoff period

### Recurring Bookings

Recurring bookings allow businesses to create appointment series that repeat according to RRULE patterns (RFC 5545). This feature supports complex scheduling patterns and handles exceptions for individual occurrences.

**Key Features:**

- **RRULE Support**: Full RFC 5545 RRULE compliance for complex recurrence patterns
- **Series Management**: Create, update, and cancel entire series
- **Exception Handling**: Cancel or modify individual occurrences within a series
- **Automatic Booking Generation**: Creates individual bookings for each occurrence
- **Flexible Patterns**: Daily, weekly, monthly, and yearly recurrence
- **Preview Generation**: Preview occurrences before creating series

**RRULE Examples:**

- `RRULE:FREQ=WEEKLY;INTERVAL=1` - Every week
- `RRULE:FREQ=MONTHLY;INTERVAL=2` - Every 2 months
- `RRULE:FREQ=DAILY;COUNT=10` - Daily for 10 occurrences
- `RRULE:FREQ=WEEKLY;UNTIL=20241231T235959Z` - Weekly until end of 2024

#### 1. Create Recurring Booking Series

**Endpoint:** `POST /bookings/recurring`
**Description:** Creates a new recurring booking series with individual bookings for each occurrence.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /bookings/recurring HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "serviceId": "service_id",
  "selectedServiceOptions": ["option_id_1", "option_id_2"],
  "bookingType": "external",
  "bookedBy": "client",
  "customerInfo": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "notes": "Weekly therapy sessions",
  "rrule": "RRULE:FREQ=WEEKLY;INTERVAL=1",
  "startDate": "2024-01-15T10:00:00.000Z",
  "endDate": "2024-06-15T10:00:00.000Z",
  "timeSlot": "10:00",
  "maxOccurrences": 20
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "message": "Recurring booking series created successfully",
  "recurringSeries": {
    "_id": "series_id",
    "totalOccurrences": 20,
    "startDate": "2024-01-15T10:00:00.000Z",
    "endDate": "2024-06-15T10:00:00.000Z",
    "timeSlot": "10:00",
    "status": "active"
  },
  "bookingsCreated": 20
}
```

#### 2. Get All Recurring Series

**Endpoint:** `GET /recurring-series`
**Description:** Retrieves all recurring series for the authenticated user.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `status`: Filter by series status (active, paused, cancelled)
- `page`: Page number for pagination
- `limit`: Number of series per page

**Response:**

- **200 OK**

```json
{
  "success": true,
  "recurringSeries": [
    {
      "_id": "series_id",
      "serviceId": {
        "_id": "service_id",
        "name": "Therapy Session",
        "price": 100.0,
        "duration": 60
      },
      "rrule": "RRULE:FREQ=WEEKLY;INTERVAL=1",
      "startDate": "2024-01-15T10:00:00.000Z",
      "endDate": "2024-06-15T10:00:00.000Z",
      "timeSlot": "10:00",
      "totalOccurrences": 20,
      "status": "active",
      "isActive": true
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 5,
    "pages": 1
  }
}
```

#### 3. Get Recurring Series by ID

**Endpoint:** `GET /recurring-series/:seriesId`
**Description:** Retrieves a specific recurring series with all its bookings.
**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "recurringSeries": {
    "_id": "series_id",
    "serviceId": {
      "_id": "service_id",
      "name": "Therapy Session",
      "price": 100.0,
      "duration": 60
    },
    "rrule": "RRULE:FREQ=WEEKLY;INTERVAL=1",
    "startDate": "2024-01-15T10:00:00.000Z",
    "endDate": "2024-06-15T10:00:00.000Z",
    "timeSlot": "10:00",
    "totalOccurrences": 20,
    "status": "active",
    "exceptions": [
      {
        "date": "2024-02-15T10:00:00.000Z",
        "type": "cancelled"
      }
    ]
  },
  "bookings": [
    {
      "_id": "booking_id",
      "date": "2024-01-15T10:00:00.000Z",
      "timeSlot": "10:00",
      "status": "confirmed",
      "isRecurring": true,
      "recurringSeriesId": "series_id"
    }
  ]
}
```

#### 4. Update Recurring Series

**Endpoint:** `PUT /recurring-series/:seriesId`
**Description:** Updates a recurring series and regenerates future bookings if needed.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /recurring-series/:seriesId HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "rrule": "RRULE:FREQ=WEEKLY;INTERVAL=2",
  "timeSlot": "14:00",
  "notes": "Updated notes",
  "status": "paused"
}
```

#### 5. Cancel Recurring Series

**Endpoint:** `DELETE /recurring-series/:seriesId`
**Description:** Cancels a recurring series and all future bookings.
**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Recurring series cancelled successfully"
}
```

#### 6. Add Exception to Series

**Endpoint:** `POST /recurring-series/:seriesId/exceptions`
**Description:** Adds an exception (cancellation or modification) to a specific occurrence.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /recurring-series/:seriesId/exceptions HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "date": "2024-02-15T10:00:00.000Z",
  "type": "cancelled"
}
```

**For modifications:**

```http
POST /recurring-series/:seriesId/exceptions HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "date": "2024-02-15T10:00:00.000Z",
  "type": "modified",
  "modifiedData": {
    "timeSlot": "14:00",
    "notes": "Rescheduled to afternoon",
    "status": "confirmed"
  }
}
```

#### 7. Generate Occurrences Preview

**Endpoint:** `POST /recurring-series/preview`
**Description:** Generates a preview of occurrences based on RRULE (no authentication required).
**Request:**

```http
POST /recurring-series/preview HTTP/1.1
Host: api.puzzlepiecesolutions.com
Content-Type: application/json

{
  "rrule": "RRULE:FREQ=WEEKLY;INTERVAL=1",
  "startDate": "2024-01-15T10:00:00.000Z",
  "endDate": "2024-06-15T10:00:00.000Z",
  "maxOccurrences": 20
}
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "occurrences": [
    {
      "date": "2024-01-15",
      "dayOfWeek": "Monday"
    },
    {
      "date": "2024-01-22",
      "dayOfWeek": "Monday"
    }
  ],
  "totalOccurrences": 20
}
```

**Series Management:**

- **Active**: Series is running and generating bookings
- **Paused**: Series is temporarily stopped (no new bookings generated)
- **Cancelled**: Series is permanently stopped (all future bookings cancelled)

**Exception Types:**

- **cancelled**: Specific occurrence is cancelled
- **modified**: Specific occurrence has different time, notes, or status

### Intake Forms

Intake forms allow businesses to create custom questionnaires for their services. Each service can have its own intake form with custom questions that customers must complete when booking.

**Key Features:**

- **Per Service**: Each service can have its own custom intake form
- **Multiple Question Types**: Support for text, textarea, select, radio, checkbox, email, phone, date, number, and URL questions
- **Required/Optional**: Questions can be marked as required or optional
- **Validation**: Built-in validation for different question types
- **Ordering**: Questions can be reordered as needed
- **Active/Inactive**: Forms can be activated or deactivated

#### 1. Get Intake Form

**Endpoint:** `GET /intake-form/:serviceId`
**Description:** Retrieves the intake form for a specific service.
**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "intakeForm": {
    "isActive": true,
    "questions": [
      {
        "PP_Id": "question_1",
        "type": "text",
        "question": "What is your preferred contact method?",
        "required": true,
        "placeholder": "Enter your preference",
        "validation": {
          "minLength": 2,
          "maxLength": 100
        },
        "order": 0
      },
      {
        "PP_Id": "question_2",
        "type": "select",
        "question": "How did you hear about us?",
        "required": false,
        "options": ["Social Media", "Friend", "Advertisement", "Other"],
        "order": 1
      }
    ]
  }
}
```

#### 2. Update Intake Form

**Endpoint:** `PUT /intake-form/:serviceId`
**Description:** Creates or updates the complete intake form for a service.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /intake-form/:serviceId HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "isActive": true,
  "questions": [
    {
      "PP_Id": "question_1",
      "type": "text",
      "question": "What is your preferred contact method?",
      "required": true,
      "placeholder": "Enter your preference",
      "validation": {
        "minLength": 2,
        "maxLength": 100
      },
      "order": 0
    }
  ]
}
```

#### 3. Add Question

**Endpoint:** `POST /intake-form/:serviceId/questions`
**Description:** Adds a new question to the intake form.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /intake-form/:serviceId/questions HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "type": "select",
  "question": "How did you hear about us?",
  "required": false,
  "options": ["Social Media", "Friend", "Advertisement", "Other"],
  "order": 1
}
```

#### 4. Update Question

**Endpoint:** `PUT /intake-form/:serviceId/questions/:questionId`
**Description:** Updates an existing question in the intake form.
**Headers:** `Authorization: Bearer <token>`

#### 5. Delete Question

**Endpoint:** `DELETE /intake-form/:serviceId/questions/:questionId`
**Description:** Deletes a question from the intake form.
**Headers:** `Authorization: Bearer <token>`

#### 6. Reorder Questions

**Endpoint:** `PUT /intake-form/:serviceId/reorder`
**Description:** Reorders questions in the intake form.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /intake-form/:serviceId/reorder HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "questionIds": ["question_2", "question_1", "question_3"]
}
```

#### 7. Toggle Form Status

**Endpoint:** `PATCH /intake-form/:serviceId/toggle-status`
**Description:** Activates or deactivates the intake form.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PATCH /intake-form/:serviceId/toggle-status HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "isActive": true
}
```

#### 8. Get Public Intake Form

**Endpoint:** `GET /intake-form/public/:username/:serviceUrlName`
**Description:** Retrieves the public intake form for booking (no authentication required).
**Request:**

```http
GET /intake-form/public/johndoe/haircut-service HTTP/1.1
Host: api.puzzlepiecesolutions.com
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "intakeForm": {
    "isActive": true,
    "questions": [
      {
        "PP_Id": "question_1",
        "type": "text",
        "question": "What is your preferred contact method?",
        "required": true,
        "placeholder": "Enter your preference",
        "validation": {
          "minLength": 2,
          "maxLength": 100
        },
        "order": 0
      }
    ]
  }
}
```

**Question Types Supported:**

- `text`: Single line text input
- `textarea`: Multi-line text input
- `select`: Dropdown selection
- `radio`: Radio button selection
- `checkbox`: Multiple checkbox selection
- `email`: Email input with validation
- `phone`: Phone number input with validation
- `date`: Date picker
- `number`: Numeric input
- `url`: URL input with validation

### Bookings

#### 1. Create Internal Booking

**Endpoint:** `POST /create-bookings/internal`
**Description:** Creates a booking for authenticated users.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /create-bookings/internal HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "serviceId": "service_id",
  "date": "2024-01-15",
  "timeSlot": "10:00",
  "clientName": "John Doe",
  "clientEmail": "<EMAIL>",
  "notes": "Optional booking notes",
  "selectedServiceOptions": ["option_id_1", "option_id_2"],
  "intakeFormResponses": [
    {
      "questionId": "question_1",
      "question": "What is your preferred contact method?",
      "answer": "Email",
      "questionType": "text",
      "required": true
    }
  ]
}
```

#### 2. Create External Booking

**Endpoint:** `POST /create-bookings/external`
**Description:** Creates a booking for external users (public endpoint).
**Request:**

```http
POST /create-bookings/external HTTP/1.1
Host: api.puzzlepiecesolutions.com
Content-Type: application/json

{
  "serviceId": "service_id",
  "date": "2024-01-15",
  "timeSlot": "10:00",
  "customerInfo": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+**********"
  },
  "notes": "Optional booking notes",
  "selectedServiceOptions": ["option_id_1", "option_id_2"],
  "intakeFormResponses": [
    {
      "questionId": "question_1",
      "question": "What is your preferred contact method?",
      "answer": "Email",
      "questionType": "text",
      "required": true
    }
  ]
}
```

#### 3. Get Bookings

**Endpoint:** `GET /bookings`
**Description:** Retrieves all bookings for authenticated user.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `status`: Filter by booking status
- `date`: Filter by date
- `page`: Page number for pagination
- `limit`: Number of bookings per page

#### 4. Update Booking Status

**Endpoint:** `PUT /bookings/:bookingId/status`
**Description:** Updates the status of a booking with automatic fee calculation based on scheduling rules.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /bookings/:bookingId/status HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "no-show",
  "reason": "Client did not arrive for appointment"
}
```

**For cancellations:**

```http
PUT /bookings/:bookingId/status HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "canceled",
  "reason": "Client requested cancellation",
  "refundDeposit": true
}
```

**Request Parameters:**

- `status`: Required. One of: `pending`, `confirmed`, `canceled`, `no-show`
- `reason`: Optional. Reason for cancellation or no-show (max 500 characters)
- `refundDeposit`: Optional. Whether to refund deposit for cancellations (only if within cutoff)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "booking": {
    "_id": "booking_id",
    "status": "no-show",
    "depositRequired": 25.0,
    "depositPaid": 25.0,
    "feesCharged": 50.0,
    "paymentStatus": "partial"
  },
  "feeCharged": 50.0,
  "message": "A fee of $50.00 has been applied."
}
```

**Status-Specific Behavior:**

- **canceled**: Applies late cancellation fee if past cutoff time; handles deposit refunds
- **no-show**: Applies no-show fee; typically forfeits deposit
- **confirmed**: No fees applied
- **pending**: Resets to initial state

**Payment Status Values:**

- **pending**: Initial state, no payment received
- **partial**: Deposit paid, remaining balance outstanding
- **full**: Complete payment received (deposit covers total OR remaining balance paid)
- **refunded**: Payment refunded to customer

**Fee Calculation:**

- Fees are automatically calculated based on the service's scheduling rules
- Late cancellation fees apply when canceling within the cutoff period
- No-show fees apply when marking a booking as no-show
- Deposit refunds are only available for cancellations within the allowed timeframe

#### 5. Update Payment Status to Full

**Endpoint:** `PUT /bookings/:bookingId/payment-status/full`
**Description:** Updates payment status to full when remaining balance is paid after service completion.
**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Payment status updated to full",
  "booking": {
    "_id": "booking_id",
    "paymentStatus": "full",
    "depositPaid": 100.0
  },
  "totalAmountPaid": 100.0,
  "remainingBalance": 0
}
```

- **400 Bad Request** (Not partial payment)

```json
{
  "success": false,
  "error": "Booking payment status is not partial. Cannot update to full."
}
```

**Use Case:**

- When a customer pays only a deposit upfront
- After service completion, business collects remaining balance
- Business updates payment status to reflect full payment

#### 6. Reschedule Booking

**Endpoint:** `PUT /bookings/:bookingId/reschedule`
**Description:** Reschedules a booking to a new date/time.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /bookings/:bookingId/reschedule HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "newDate": "2024-01-16",
  "timeSlot": "14:00"
}
```

#### 6. Pay for Service

**Endpoint:** `POST /pay-service`
**Description:** Processes payment for a service booking.
**Request:**

```http
POST /pay-service HTTP/1.1
Host: api.puzzlepiecesolutions.com
Content-Type: application/json

{
  "serviceId": "service_id",
  "amount": 100.00,
  "currency": "USD",
  "paymentMethodId": "pm_**********"
}
```

#### 7. Payment Success Callback

**Endpoint:** `POST /pay-service/success`
**Description:** Handles successful payment confirmation.
**Request:**

```http
POST /pay-service/success HTTP/1.1
Host: api.puzzlepiecesolutions.com
Content-Type: application/json

{
  "paymentIntentId": "pi_**********",
  "bookingId": "booking_id"
}
```

### Client Self-Service

The Client Self-Service system allows customers to reschedule or cancel their appointments through secure, tokenized links sent via email. This system operates independently of user authentication, providing a seamless experience for clients without requiring account creation.

**Key Features:**

- **Token-Based Security**: Secure, time-limited tokens for client access
- **No Authentication Required**: Clients can manage bookings without creating accounts
- **Business Rule Integration**: Enforces scheduling rules, cutoffs, and fees automatically
- **Email Integration**: Seamless integration with booking confirmation emails
- **Mobile-Friendly**: Works on any device with email access
- **Fee Management**: Handles late cancellation fees and deposit refunds

**Security Features:**

- **Cryptographically Secure Tokens**: Generated using crypto.randomBytes()
- **Time-Limited Access**: 7-day token expiry (configurable)
- **Format Validation**: Strict token format validation
- **Single-Use Cancellation**: Cancel tokens invalidated after use
- **Token Regeneration**: New tokens created after successful reschedule

#### Token System

When a booking is created, secure tokens are automatically generated:

```json
{
  "rescheduleToken": "a1b2c3d4e5f6789...", // 64-character hex string
  "cancelToken": "f6e5d4c3b2a1987...", // 64-character hex string
  "tokenExpiry": "2024-01-22T10:00:00Z" // 7 days from creation
}
```

**Token Properties:**

- **Format**: 32-64 character hexadecimal strings
- **Expiry**: 7 days from booking creation (configurable)
- **Security**: Cryptographically secure random generation
- **Validation**: Format and expiry validation on all requests

#### Email Integration

Booking confirmation emails include self-service links:

```html
<div style="text-align: center; margin: 20px 0;">
  <a
    href="{{rescheduleLink}}"
    style="display: inline-block; background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 0 10px; font-weight: bold;"
  >
    📅 Reschedule Appointment
  </a>
  <a
    href="{{cancelLink}}"
    style="display: inline-block; background-color: #f44336; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 0 10px; font-weight: bold;"
  >
    ❌ Cancel Appointment
  </a>
</div>
```

**Email Variables:**

- `rescheduleLink`: `${FRONTEND_URL}/reschedule/${rescheduleToken}`
- `cancelLink`: `${FRONTEND_URL}/cancel/${cancelToken}`

#### Client Self-Service Flow

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Booking       │    │   Confirmation   │    │   Customer      │
│   Created       │───▶│   Email Sent     │───▶│   Receives      │
│                 │    │   with Links     │    │   Email         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   New Tokens    │    │   Booking        │    │   Customer      │
│   Generated     │◀───│   Updated        │◀───│   Clicks Link   │
│   (Reschedule)  │    │   Successfully   │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Tokens        │    │   Booking        │    │   Customer      │
│   Invalidated   │◀───│   Canceled       │◀───│   Clicks Link   │
│   (Cancel)      │    │   Successfully   │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

#### Reschedule Process

##### 1. Get Reschedule Form

**Endpoint:** `GET /api/public/reschedule/:token`
**Description:** Retrieves booking details for reschedule form.
**Authentication:** None (token-based)

**Process:**

1. Validates token format and expiry
2. Finds booking by reschedule token
3. Checks booking status (cannot reschedule canceled/no-show bookings)
4. Validates business rules and reschedule permissions
5. Returns booking details for form display

**Response:**

```json
{
  "success": true,
  "booking": {
    "id": "booking_id",
    "serviceName": "Haircut",
    "businessName": "Salon ABC",
    "currentDate": "2024-01-15T00:00:00Z",
    "currentTimeSlot": "10:00-11:00",
    "customerInfo": {
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>"
    },
    "rescheduleCutoff": 2
  }
}
```

**Error Responses:**

- **400 Bad Request**: Invalid token format
- **404 Not Found**: Booking not found or token invalid
- **400 Bad Request**: Token expired
- **400 Bad Request**: Cannot reschedule canceled/no-show booking
- **400 Bad Request**: Rescheduling not allowed within cutoff time

##### 2. Process Reschedule

**Endpoint:** `POST /api/public/reschedule/:token`
**Description:** Processes reschedule request and updates booking.
**Authentication:** None (token-based)

**Request Body:**

```json
{
  "newDate": "2024-01-16T00:00:00Z",
  "timeSlot": "14:00-15:00",
  "locationId": "location_id"
}
```

**Process:**

1. Validates token and booking status
2. Validates input data (date, time, location)
3. Re-checks business rules and reschedule permissions
4. Validates location availability if location provided
5. Updates booking with new date/time
6. Generates new tokens for future use
7. Sends reschedule confirmation email

**Response:**

```json
{
  "success": true,
  "booking": {
    "_id": "booking_id",
    "date": "2024-01-16T00:00:00Z",
    "timeSlot": "14:00-15:00",
    "status": "confirmed",
    "rescheduleToken": "new_token_here",
    "cancelToken": "new_token_here",
    "tokenExpiry": "2024-01-23T10:00:00Z"
  },
  "message": "Booking rescheduled successfully. You will receive a confirmation email shortly."
}
```

#### Cancellation Process

##### 1. Get Cancel Form

**Endpoint:** `GET /api/public/cancel/:token`
**Description:** Retrieves booking details for cancellation form.
**Authentication:** None (token-based)

**Process:**

1. Validates token format and expiry
2. Finds booking by cancel token
3. Checks booking status (cannot cancel already canceled bookings)
4. Validates business rules and cancellation permissions
5. Calculates late cancellation fees if applicable
6. Returns booking details with fee information

**Response:**

```json
{
  "success": true,
  "booking": {
    "id": "booking_id",
    "serviceName": "Haircut",
    "businessName": "Salon ABC",
    "date": "2024-01-15T00:00:00Z",
    "timeSlot": "10:00-11:00",
    "customerInfo": {
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>"
    },
    "depositPaid": 25.0,
    "canCancel": true,
    "cancelCutoff": 24,
    "lateCancellationFee": 0,
    "depositRefundable": true
  }
}
```

##### 2. Process Cancellation

**Endpoint:** `POST /api/public/cancel/:token`
**Description:** Processes cancellation request and updates booking.
**Authentication:** None (token-based)

**Request Body:**

```json
{
  "reason": "Schedule conflict",
  "refundDeposit": true
}
```

**Process:**

1. Validates token and booking status
2. Validates input data (cancellation reason)
3. Applies business rules and cancellation logic
4. Calculates late cancellation fees if outside cutoff
5. Processes deposit refund if applicable
6. Updates booking status to 'canceled'
7. Invalidates all tokens (single-use for cancellation)
8. Sends cancellation confirmation email

**Response:**

```json
{
  "success": true,
  "booking": {
    "_id": "booking_id",
    "status": "canceled",
    "feesCharged": 0,
    "depositRefunded": 25.0,
    "paymentStatus": "refunded",
    "rescheduleToken": null,
    "cancelToken": null,
    "tokenExpiry": null
  },
  "feeCharged": 0,
  "message": "Booking canceled successfully. You will receive a confirmation email shortly."
}
```

**Late Cancellation Response:**

```json
{
  "success": true,
  "booking": {
    /* updated booking data */
  },
  "feeCharged": 15.0,
  "message": "Booking canceled. A late cancellation fee of $15.00 has been applied."
}
```

#### Business Rules Integration

The system automatically enforces business policies through the `SchedulingRules` model:

**Reschedule Rules:**

- **Cutoff Time**: Configurable hours before appointment (e.g., 2 hours)
- **Permission Check**: `schedulingRules.canReschedule(appointmentDateTime)`
- **Error Message**: "Rescheduling not allowed within X hours of the appointment"

**Cancellation Rules:**

- **Cutoff Time**: Configurable hours before appointment (e.g., 24 hours)
- **Permission Check**: `schedulingRules.canCancel(appointmentDateTime)`
- **Late Fee Calculation**: `schedulingRules.calculateLateCancellationFee(servicePrice, 0)`
- **Deposit Handling**: Automatic refund processing within cutoff

#### Frontend Integration

**URL Structure:**

```
Reschedule: https://yourapp.com/reschedule/{rescheduleToken}
Cancel:     https://yourapp.com/cancel/{cancelToken}
```

**Frontend Flow:**

1. **Token Extraction**: Extract token from URL parameters
2. **Form Loading**: Call GET endpoint to load booking details
3. **User Input**: Collect new date/time or cancellation reason
4. **Validation**: Client-side validation before submission
5. **Submission**: Call POST endpoint to process changes
6. **Confirmation**: Display success message and email notification

**Example Frontend Implementation:**

```javascript
// Extract token from URL
const token = window.location.pathname.split("/").pop();

// Load reschedule form
const response = await fetch(`/api/public/reschedule/${token}`);
const data = await response.json();

if (data.success) {
  // Display form with booking details
  displayRescheduleForm(data.booking);
}

// Process reschedule
const rescheduleData = {
  newDate: selectedDate,
  timeSlot: selectedTime,
  locationId: selectedLocation,
};

const rescheduleResponse = await fetch(`/api/public/reschedule/${token}`, {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify(rescheduleData),
});
```

#### Error Handling

**Token Errors:**

- **Invalid Format**: Token doesn't match expected format
- **Expired**: Token has passed expiry date
- **Not Found**: No booking found with provided token

**Business Rule Errors:**

- **Outside Cutoff**: Attempting to reschedule/cancel outside allowed timeframe
- **Invalid Status**: Booking cannot be modified (already canceled, no-show)
- **Location Unavailable**: Selected location doesn't offer the service

**Validation Errors:**

- **Invalid Date**: Date is in the past or invalid format
- **Invalid Time**: Time slot is not available or invalid
- **Missing Fields**: Required fields not provided

**System Errors:**

- **Database Errors**: Issues with data persistence
- **Email Failures**: Problems sending confirmation emails
- **Service Unavailable**: External service dependencies

#### Security Considerations

**Token Security:**

- Tokens are cryptographically secure and unpredictable
- Time-limited access prevents indefinite access
- Format validation prevents injection attacks
- Single-use for cancellation prevents replay attacks

**Input Validation:**

- All inputs validated using Joi schemas
- Input sanitization prevents XSS attacks
- Business logic validation prevents rule bypassing
- Rate limiting prevents abuse

**Data Protection:**

- No sensitive data exposed in error messages
- Tokens invalidated after use or expiry
- Audit trail maintained for all changes
- Email notifications for all actions

### Digital Profile

#### 1. Get Digital Profile

**Endpoint:** `GET /digital-profile`
**Description:** Retrieves user's digital profile.
**Headers:** `Authorization: Bearer <token>`

#### 2. Update Digital Profile

**Endpoint:** `PUT /digital-profile`
**Description:** Updates digital profile information.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /digital-profile HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "businessName": "My Business",
  "description": "Business description",
  "contactInfo": {
    "phone": "+**********",
    "email": "<EMAIL>"
  }
}
```

#### 3. Get Digital Profile by Username

**Endpoint:** `GET /digital-profile-username/:username`
**Description:** Retrieves public digital profile by username (public endpoint).

#### 4. Upload Digital Profile Image

**Endpoint:** `POST /digital-profile/upload-image`
**Description:** Uploads digital profile image.
**Headers:** `Authorization: Bearer <token>`
**Request:** `multipart/form-data` with `image` field

#### 5. Add Website Link

**Endpoint:** `POST /digital-profile`
**Description:** Adds a website link to digital profile.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /digital-profile HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "Website",
  "url": "https://mybusiness.com",
  "isActive": true
}
```

#### 6. Update Website Link Settings

**Endpoint:** `PUT /digital-profile/website/:title`
**Description:** Updates website link settings.
**Headers:** `Authorization: Bearer <token>`

#### 7. Remove Website Link

**Endpoint:** `DELETE /business-link/website/:title`
**Description:** Removes a website link from digital profile.
**Headers:** `Authorization: Bearer <token>`

### Booking Settings

Booking settings allow users to customize their booking page with custom information (images or HTML text) and theme settings.

#### 1. Get Booking Settings

**Endpoint:** `GET /booking-settings`
**Description:** Retrieves booking settings for the authenticated user.
**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "bookingSettings": {
    "_id": "settings_id",
    "userId": "user_id",
    "bookingInfo": {
      "isImage": false,
      "data": "<p>Welcome to our booking page! We look forward to serving you.</p>"
    },
    "themeSettings": {
      "primaryColor": "#3B82F6",
      "secondaryColor": "#1E40AF",
      "backgroundColor": "#FFFFFF",
      "textColor": "#1F2937",
      "buttonColor": "#3B82F6",
      "buttonTextColor": "#FFFFFF",
      "fontFamily": "Inter, system-ui, sans-serif",
      "borderRadius": 8,
      "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
    },
    "isActive": true,
    "customCSS": "",
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  }
}
```

#### 2. Update Booking Information

**Endpoint:** `PUT /booking-settings/info`
**Description:** Updates booking information (image or HTML text).
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /booking-settings/info HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "isImage": false,
  "data": "<h2>Welcome!</h2><p>Book your appointment with us today.</p>"
}
```

#### 3. Upload Booking Image

**Endpoint:** `POST /booking-settings/upload-image`
**Description:** Uploads an image for the booking page.
**Headers:** `Authorization: Bearer <token>`
**Request:** `multipart/form-data` with `image` field

#### 4. Update Theme Settings

**Endpoint:** `PUT /booking-settings/theme`
**Description:** Updates theme settings for the booking page.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /booking-settings/theme HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "primaryColor": "#3B82F6",
  "secondaryColor": "#1E40AF",
  "backgroundColor": "#FFFFFF",
  "textColor": "#1F2937",
  "buttonColor": "#3B82F6",
  "buttonTextColor": "#FFFFFF",
  "fontFamily": "Inter, system-ui, sans-serif",
  "borderRadius": 8,
  "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
}
```

#### 5. Update Custom CSS

**Endpoint:** `PUT /booking-settings/custom-css`
**Description:** Updates custom CSS for advanced styling.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /booking-settings/custom-css HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "customCSS": ".booking-page { background: linear-gradient(45deg, #f0f0f0, #e0e0e0); }"
}
```

#### 6. Toggle Booking Page Status

**Endpoint:** `PATCH /booking-settings/toggle-status`
**Description:** Activates or deactivates the booking page.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PATCH /booking-settings/toggle-status HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "isActive": true
}
```

#### 7. Get Public Booking Settings

**Endpoint:** `GET /booking-settings/:username`
**Description:** Retrieves public booking settings for external booking page (no authentication required).
**Request:**

```http
GET /booking-settings/johndoe HTTP/1.1
Host: api.puzzlepiecesolutions.com
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "bookingSettings": {
    "bookingInfo": {
      "type": "html",
      "content": "<p>Welcome to our booking page!</p>"
    },
    "themeSettings": {
      "primaryColor": "#3B82F6",
      "secondaryColor": "#1E40AF",
      "backgroundColor": "#FFFFFF",
      "textColor": "#1F2937",
      "buttonColor": "#3B82F6",
      "buttonTextColor": "#FFFFFF",
      "fontFamily": "Inter, system-ui, sans-serif",
      "borderRadius": 8,
      "shadow": "0 4px 6px -1px rgba(0, 0, 0, 0.1)"
    },
    "customCSS": ""
  }
}
```

### Calendar Settings

#### 1. Get Calendar Settings

**Endpoint:** `GET /calendar-settings`
**Description:** Retrieves user's calendar settings.
**Headers:** `Authorization: Bearer <token>`

#### 2. Update Calendar Settings

**Endpoint:** `PUT /calendar-settings`
**Description:** Updates calendar settings.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /calendar-settings HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "workingHours": {
    "monday": {"start": "09:00", "end": "17:00"},
    "tuesday": {"start": "09:00", "end": "17:00"}
  },
  "timezone": "America/New_York",
  "bufferTime": 15
}
```

### Settings

#### 1. Get Settings

**Endpoint:** `GET /settings`
**Description:** Retrieves user settings.
**Headers:** `Authorization: Bearer <token>`

#### 2. Update Settings

**Endpoint:** `PUT /settings`
**Description:** Updates user settings.
**Headers:** `Authorization: Bearer <token>`

### Teams & Staff Management

Teams and Staff management allows business owners (MASTER accounts) to create teams, add staff members with login accounts, and manage their availability and service assignments. The system integrates with the subscription model to limit features based on plan tiers.

**Subscription-Based Limits:**

- **Free Plan**: 1 team, 2 staff members
- **Basic Plan**: 3 teams, 5 staff members per team
- **Pro Plan**: 5 teams, unlimited staff members per team

**Key Features:**

- **Team Management**: Create, update, and manage teams with soft delete support
- **Staff Accounts**: Create staff members with automatic password generation
- **Status Management**: Active/Inactive staff with soft delete functionality
- **Service Assignment**: Assign staff to specific services
- **Availability Management**: Set working hours, buffer times, and capacity
- **Calendar Integration**: Multi-staff calendar views with status filtering
- **Subscription Limits**: Automatic enforcement of plan-based restrictions
- **Password Management**: Automatic temporary password generation with change requirements

#### Team Management

##### 1. Create Team

**Endpoint:** `POST /teams`
**Description:** Creates a new team for the authenticated business user.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /teams HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Hair Styling Team",
  "description": "Our main hair styling team"
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "message": "Team created successfully",
  "team": {
    "_id": "team_id",
    "name": "Hair Styling Team",
    "description": "Our main hair styling team",
    "ownerId": "business_user_id",
    "isActive": true,
    "createdAt": "2024-01-15T10:00:00.000Z",
    "updatedAt": "2024-01-15T10:00:00.000Z"
  },
  "limits": {
    "currentCount": 1,
    "maxAllowed": 3,
    "plan": "basic"
  }
}
```

- **403 Forbidden** (Plan limit reached)

```json
{
  "success": false,
  "error": "You have reached the maximum number of teams (1) for your free plan. Upgrade to create more teams."
}
```

##### 2. Get Teams

**Endpoint:** `GET /teams`
**Description:** Retrieves all teams for the authenticated business user with optional status filtering.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `status`: Filter by team status - `active`, `inactive`, or omit for all teams (optional)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "teams": [
    {
      "_id": "team_id",
      "name": "Hair Styling Team",
      "description": "Our main hair styling team",
      "ownerId": {
        "_id": "business_user_id",
        "first_name": "John",
        "last_name": "Doe",
        "email": "<EMAIL>"
      },
      "isActive": true,
      "createdAt": "2024-01-15T10:00:00.000Z",
      "updatedAt": "2024-01-15T10:00:00.000Z"
    }
  ],
  "limits": {
    "currentCount": 1,
    "maxAllowed": 3,
    "plan": "basic"
  }
}
```

##### 3. Get Team by ID

**Endpoint:** `GET /teams/:teamId`
**Description:** Retrieves a specific team with its staff members (excluding deleted staff).
**Headers:** `Authorization: Bearer <token>`
**Response:**

- **200 OK**

```json
{
  "success": true,
  "team": {
    "_id": "team_id",
    "name": "Hair Styling Team",
    "description": "Our main hair styling team",
    "ownerId": {
      "_id": "business_user_id",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>"
    },
    "isActive": true
  },
  "staff": [
    {
      "_id": "staff_id",
      "name": "Sarah Johnson",
      "role": "Senior Stylist",
      "email": "<EMAIL>",
      "color": "#3B82F6",
      "services": [
        {
          "_id": "service_id",
          "name": "Haircut",
          "duration": 60,
          "price": 50.0
        }
      ],
      "isActive": true
    }
  ],
  "staffLimits": {
    "currentCount": 1,
    "maxAllowed": 5,
    "plan": "basic"
  }
}
```

##### 4. Update Team

**Endpoint:** `PUT /teams/:teamId`
**Description:** Updates team information.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /teams/:teamId HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Updated Hair Styling Team",
  "description": "Updated description"
}
```

##### 5. Delete Team

**Endpoint:** `DELETE /teams/:teamId`
**Description:** Deletes a team (only if no active staff members).
**Headers:** `Authorization: Bearer <token>`
**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Team deleted successfully"
}
```

- **400 Bad Request** (Has active staff)

```json
{
  "success": false,
  "error": "Cannot delete team. It has 2 active staff member(s). Please remove all staff members first."
}
```

##### 6. Get Team Statistics

**Endpoint:** `GET /teams/:teamId/stats`
**Description:** Retrieves team statistics including staff count and bookings.
**Headers:** `Authorization: Bearer <token>`
**Response:**

- **200 OK**

```json
{
  "success": true,
  "stats": {
    "staffCount": 2,
    "totalBookings": 45,
    "todayBookings": 3,
    "staffLimits": {
      "currentCount": 2,
      "maxAllowed": 5,
      "plan": "basic"
    }
  }
}
```

#### Staff Management

##### 1. Create Staff Member

**Endpoint:** `POST /staff`
**Description:** Creates a new staff member with a user account for a team. This function creates both a User document (for authentication) and a Staff document (for business logic). Staff members receive an automatic verification email with their login credentials.

**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /staff HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "teamId": "team_id",
  "name": "Sarah Johnson",
  "email": "<EMAIL>",
  "phone": "+**********",
  "role": "Senior Stylist"
}
```

**Note:** The system will automatically:

- Generate a secure temporary password (no manual password required)
- Generate a unique username from the email
- Hash the password securely
- Create a user account with role 'staff'
- Set default working hours and settings
- Set `changePassword: false` to require password change on first login
- Send a verification email with login credentials

**Response:**

- **201 Created**

```json
{
  "success": true,
  "message": "Staff user account created successfully",
  "staff": {
    "_id": "staff_id",
    "teamId": {
      "_id": "team_id",
      "name": "Hair Styling Team"
    },
    "userId": "user_id",
    "name": "Sarah Johnson",
    "email": "<EMAIL>",
    "role": "Senior Stylist",
    "color": "#3B82F6",
    "services": [],
    "workingHours": {
      "monday": { "start": "09:00", "end": "17:00", "isWorking": true },
      "tuesday": { "start": "09:00", "end": "17:00", "isWorking": true },
      "wednesday": { "start": "09:00", "end": "17:00", "isWorking": true },
      "thursday": { "start": "09:00", "end": "17:00", "isWorking": true },
      "friday": { "start": "09:00", "end": "17:00", "isWorking": true },
      "saturday": { "start": "09:00", "end": "17:00", "isWorking": false },
      "sunday": { "start": "09:00", "end": "17:00", "isWorking": false }
    },
    "buffer": 15,
    "travelTime": 30,
    "capacity": 8,
    "isActive": true
  },
  "user": {
    "_id": "user_id",
    "username": "sarah.johnson",
    "email": "<EMAIL>",
    "role": "staff"
  },
  "limits": {
    "currentCount": 2,
    "maxAllowed": 5,
    "plan": "basic"
  }
}
```

**Automatic Email Verification:**
When a staff member is created, they automatically receive a verification email containing:

- Welcome message with business branding
- Username and temporary password
- Login URL
- Security instructions to change password after first login
- Step-by-step setup instructions

**Default Settings Applied:**

- **Working Hours**: Monday-Friday 9:00 AM - 5:00 PM (Saturday-Sunday off)
- **Buffer Time**: 15 minutes between appointments
- **Travel Time**: 30 minutes
- **Daily Capacity**: 8 appointments
- **Color**: Default blue (#3B82F6)
- **Email Verification**: Automatically verified (no email verification required)

##### 2. Get Staff Members

**Endpoint:** `GET /staff`
**Description:** Retrieves all staff members for the authenticated business user with optional filtering.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `teamId`: Filter by specific team (optional)
- `status`: Filter by staff status - `active`, `inactive`, or omit for all staff (optional)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "staff": [
    {
      "_id": "staff_id",
      "teamId": {
        "_id": "team_id",
        "name": "Hair Styling Team"
      },
      "name": "Sarah Johnson",
      "email": "<EMAIL>",
      "role": "Senior Stylist",
      "color": "#3B82F6",
      "services": [
        {
          "_id": "service_id",
          "name": "Haircut",
          "duration": 60,
          "price": 50.0
        }
      ],
      "userId": {
        "_id": "user_id",
        "first_name": "Sarah",
        "last_name": "Johnson",
        "email": "<EMAIL>"
      },
      "isActive": true
    }
  ]
}
```

##### 3. Get Staff Member by ID

**Endpoint:** `GET /staff/:staffId`
**Description:** Retrieves a specific staff member's details (including inactive staff, but excluding deleted staff).
**Headers:** `Authorization: Bearer <token>`

##### 4. Update Staff Member

**Endpoint:** `PUT /staff/:staffId`
**Description:** Updates staff member information including status management.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /staff/:staffId HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "first_name": "Sarah",
  "last_name": "Johnson-Smith",
  "email": "<EMAIL>",
  "phone": "+**********",
  "role": "Lead Stylist",
  "color": "#EF4444",
  "services": ["service_id_1", "service_id_3"],
  "locations": ["location_id_1"],
  "workingHours": {
    "monday": { "start": "08:00", "end": "18:00", "isWorking": true }
  },
  "buffer": 20,
  "travelTime": 45,
  "capacity": 10,
  "isActive": true
}
```

**Note:** This endpoint updates both the Staff document and the associated User document. The `isActive` field controls whether the staff member can login and work.

##### 5. Delete Staff Member (Soft Delete)

**Endpoint:** `DELETE /staff/:staffId`
**Description:** Permanently deletes a staff member using soft delete (only if no active bookings).
**Headers:** `Authorization: Bearer <token>`
**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Staff member deleted successfully"
}
```

- **400 Bad Request** (Has active bookings)

```json
{
  "success": false,
  "error": "Cannot delete staff member. They have 3 active booking(s). Please reassign or cancel these bookings first."
}
```

**Note:** This is a soft delete operation that sets `isDeleted: true`. The staff member will no longer appear in any queries but their data is preserved for audit purposes.

##### 6. Deactivate Staff Member

**Endpoint:** `PUT /staff/:staffId/deactivate`
**Description:** Temporarily deactivates a staff member (they can be reactivated later).
**Headers:** `Authorization: Bearer <token>`
**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Staff member deactivated successfully"
}
```

**Note:** This sets `isActive: false` and `isDeactivated: true` on the user account. The staff member cannot login but can be reactivated.

##### 7. Reactivate Staff Member

**Endpoint:** `PUT /staff/:staffId/active`
**Description:** Reactivates a previously deactivated staff member.
**Headers:** `Authorization: Bearer <token>`
**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Staff member reactivated successfully"
}
```

**Note:** This sets `isActive: true` and `isDeactivated: false` on the user account.

##### 8. Reset Staff Password

**Endpoint:** `POST /staff/users/:staffId/reset-password`
**Description:** Resets a staff member's password.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /staff/users/:staffId/reset-password HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "newPassword": "newSecurePassword123"
}
```

##### 9. Assign Staff to Service

**Endpoint:** `POST /staff/assign-to-service`
**Description:** Assigns staff members to a specific service.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /staff/assign-to-service HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "serviceId": "service_id",
  "staffIds": ["staff_id_1", "staff_id_2"]
}
```

##### 7. Get Staff Availability

**Endpoint:** `GET /staff/availability`
**Description:** Retrieves staff availability for a specific date.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `date`: Date in ISO format (required)
- `serviceId`: Filter by service (optional)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "date": "2024-01-15",
  "availability": [
    {
      "staffId": "staff_id",
      "name": "Sarah Johnson",
      "role": "Senior Stylist",
      "color": "#3B82F6",
      "availability": {
        "start": "09:00",
        "end": "17:00",
        "isWorking": true
      },
      "existingBookings": [
        {
          "_id": "booking_id",
          "serviceId": {
            "_id": "service_id",
            "name": "Haircut",
            "duration": 60
          },
          "timeSlot": "10:00",
          "status": "confirmed"
        }
      ],
      "capacity": 8,
      "currentBookings": 1
    }
  ]
}
```

##### 10. Get Staff Calendar

**Endpoint:** `GET /staff/calendar`
**Description:** Retrieves staff calendar with bookings for a date range.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `startDate`: Start date in ISO format (required)
- `endDate`: End date in ISO format (required)
- `teamId`: Filter by team (optional)
- `status`: Filter by staff status - `active`, `inactive`, or omit for all staff (optional)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "staff": [
    {
      "_id": "staff_id",
      "name": "Sarah Johnson",
      "color": "#3B82F6",
      "teamId": {
        "_id": "team_id",
        "name": "Hair Styling Team"
      }
    }
  ],
  "bookings": [
    {
      "_id": "booking_id",
      "date": "2024-01-15T10:00:00.000Z",
      "timeSlot": "10:00",
      "serviceId": {
        "_id": "service_id",
        "name": "Haircut",
        "duration": 60,
        "price": 50.0
      },
      "staffId": {
        "_id": "staff_id",
        "name": "Sarah Johnson",
        "color": "#3B82F6"
      },
      "status": "confirmed"
    }
  ]
}
```

#### Staff Location Management

##### 1. Assign Staff to Locations

**Endpoint:** `PUT /staff/:staffId/locations`
**Description:** Assigns staff members to specific locations.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /staff/:staffId/locations HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "locationIds": ["location_id_1", "location_id_2"]
}
```

##### 2. Get Staff Locations

**Endpoint:** `GET /staff/locations/:staffId`
**Description:** Retrieves all locations assigned to a staff member.
**Headers:** `Authorization: Bearer <token>`

##### 3. Get Staff by Location

**Endpoint:** `GET /staff/by-location/:locationId`
**Description:** Retrieves all staff members assigned to a specific location.
**Headers:** `Authorization: Bearer <token>`

#### Staff User Management

##### 1. Get Staff User Profile

**Endpoint:** `GET /staff/profile`
**Description:** Retrieves the current staff member's profile (for staff members only).
**Headers:** `Authorization: Bearer <token>`
**Response:**

- **200 OK**

```json
{
  "success": true,
  "staff": {
    "_id": "staff_id",
    "name": "Sarah Johnson",
    "role": "Senior Stylist",
    "color": "#3B82F6",
    "services": [
      {
        "_id": "service_id",
        "name": "Haircut",
        "duration": 60,
        "price": 50.0
      }
    ],
    "workingHours": {
      "monday": { "start": "09:00", "end": "17:00", "isWorking": true }
    },
    "capacity": 8,
    "isActive": true
  },
  "user": {
    "_id": "user_id",
    "first_name": "Sarah",
    "last_name": "Johnson",
    "username": "sarah.johnson",
    "email": "<EMAIL>",
    "phone": "+**********",
    "role": "staff",
    "createdAt": "2024-01-15T10:00:00.000Z"
  }
}
```

**Note:** This endpoint is only accessible by staff members (`role: 'staff'`). It returns both the staff business information and the user account details.

#### Key Features Summary

**Status Management:**

- **Active Staff**: Can login and work (`isActive: true`)
- **Inactive Staff**: Cannot login but can be reactivated (`isActive: false`)
- **Deleted Staff**: Permanently removed from system (`isDeleted: true`)

**Password Management:**

- **Automatic Generation**: Temporary passwords generated for new staff
- **Change Requirement**: Staff must change password on first login (`changePassword: false`)
- **Password Reset**: Business owners can reset staff passwords

**Filtering & Queries:**

- **Status Filtering**: Filter staff by `active`, `inactive`, or all
- **Team Filtering**: Filter staff by specific teams
- **Soft Delete**: All queries exclude deleted staff automatically

**Subscription Integration:**

- Team and staff limits are automatically enforced based on the business owner's subscription plan
- Attempts to exceed limits return appropriate error messages with upgrade suggestions
- All limits are checked in real-time during creation operations
  **Request:**

```http
POST /staff/users HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "teamId": "team_id",
  "name": "Sarah Johnson",
  "email": "<EMAIL>",
  "phone": "+**********",
  "role": "Senior Stylist",
  "password": "securePassword123!"
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "message": "Staff user account created successfully",
  "staff": {
    "_id": "staff_id",
    "teamId": {
      "_id": "team_id",
      "name": "Hair Styling Team"
    },
    "name": "Sarah Johnson",
    "email": "<EMAIL>",
    "role": "Senior Stylist",
    "isActive": true
  },
  "user": {
    "_id": "user_id",
    "username": "sarah",
    "email": "<EMAIL>",
    "role": "staff"
  },
  "limits": {
    "currentCount": 2,
    "maxAllowed": 5,
    "plan": "basic"
  }
}
```

##### 2. Get Staff Users

**Endpoint:** `GET /staff/users`
**Description:** Retrieves all staff user accounts for the business.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `teamId`: Filter by team (optional)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "staffUsers": [
    {
      "_id": "user_id",
      "first_name": "Sarah",
      "last_name": "Johnson",
      "username": "sarah",
      "email": "<EMAIL>",
      "role": "staff",
      "isEmailVerified": true,
      "isDeactivated": false,
      "createdAt": "2024-01-15T10:00:00.000Z"
    }
  ]
}
```

##### 3. Update Staff User

**Endpoint:** `PUT /staff/users/:staffUserId`
**Description:** Updates staff user account information.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /staff/users/:staffUserId HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "first_name": "Sarah",
  "last_name": "Johnson-Smith",
  "email": "<EMAIL>",
  "phone": "+**********",
  "isDeactivated": false
}
```

##### 4. Reset Staff Password

**Endpoint:** `POST /staff/users/:staffUserId/reset-password`
**Description:** Resets a staff user's password.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /staff/users/:staffUserId/reset-password HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "newPassword": "newSecurePassword123!"
}
```

##### 5. Deactivate Staff User

**Endpoint:** `DELETE /staff/users/:staffUserId`
**Description:** Deactivates a staff user account.
**Headers:** `Authorization: Bearer <token>`
**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Staff user deactivated successfully"
}
```

##### 6. Get Staff User Profile

**Endpoint:** `GET /staff/profile`
**Description:** Retrieves the profile of the authenticated staff user.
**Headers:** `Authorization: Bearer <token>`
**Response:**

- **200 OK**

```json
{
  "success": true,
  "staff": {
    "_id": "staff_id",
    "teamId": {
      "_id": "team_id",
      "name": "Hair Styling Team"
    },
    "name": "Sarah Johnson",
    "role": "Senior Stylist",
    "services": [
      {
        "_id": "service_id",
        "name": "Haircut",
        "duration": 60,
        "price": 50.0
      }
    ],
    "workingHours": {
      "monday": { "start": "09:00", "end": "17:00", "isWorking": true }
    }
  },
  "user": {
    "_id": "user_id",
    "first_name": "Sarah",
    "last_name": "Johnson",
    "username": "sarah",
    "email": "<EMAIL>",
    "phone": "+**********",
    "role": "staff",
    "createdAt": "2024-01-15T10:00:00.000Z"
  }
}
```

**Staff Login Flow:**

1. Business owner creates staff user account via `POST /staff/users`
2. Staff member receives login credentials (email/password)
3. Staff member logs in using existing authentication endpoints
4. Staff member accesses their profile via `GET /staff/profile`
5. Staff member can view their assigned services, working hours, and bookings

**Subscription Integration:**

- Team and staff limits are automatically enforced based on the business owner's subscription plan
- Attempts to exceed limits return appropriate error messages with upgrade suggestions
- All limits are checked in real-time during creation operations

### Location Management

Location Management enables businesses to manage multiple physical locations, assign services to specific locations, and provide location-based booking capabilities. The system includes automatic geocoding, location-based search, and proximity-based location discovery.

**Key Features:**

- **Multi-Location Support**: Manage multiple business locations from one account
- **Automatic Geocoding**: Automatic coordinate lookup using OpenCage API with caching
- **Service-Location Assignment**: Assign services to specific locations with room/resource tracking
- **Staff Location Management**: Assign staff members to specific locations and track their performance
- **Location-Based Bookings**: Book services at specific locations with validation
- **Location-Based Search**: Find locations by name, address, or proximity
- **Geospatial Queries**: Find nearby locations within specified radius
- **Timezone Support**: Each location can have its own timezone
- **Advanced Analytics**: Comprehensive location performance analytics and multi-location comparisons
- **Business Intelligence**: Location usage statistics, revenue tracking, and geocoding coverage

#### 1. Create Location

**Endpoint:** `POST /locations`
**Description:** Creates a new business location with automatic geocoding.

**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /locations HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Downtown Salon",
  "address": {
    "street": "123 Main Street",
    "city": "London",
    "state": "England",
    "zipCode": "SW1A 1AA",
    "country": "United Kingdom"
  },
  "timezone": "Europe/London",
  "phone": "+44 20 7123 4567",
  "email": "<EMAIL>",
  "description": "Our flagship location in central London"
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "message": "Location created successfully",
  "location": {
    "_id": "location_id",
    "userId": "user_id",
    "name": "Downtown Salon",
    "address": {
      "street": "123 Main Street",
      "city": "London",
      "state": "England",
      "zipCode": "SW1A 1AA",
      "country": "United Kingdom"
    },
    "timezone": "Europe/London",
    "phone": "+44 20 7123 4567",
    "email": "<EMAIL>",
    "description": "Our flagship location in central London",
    "coordinates": {
      "latitude": 51.5074,
      "longitude": -0.1278
    },
    "isActive": true,
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

**Note:** The system automatically geocodes the address and stores coordinates for location-based features.

#### 2. Get All Locations

**Endpoint:** `GET /locations`
**Description:** Retrieves all locations for the authenticated user.

**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `isActive` (optional): Filter by active status (`true`/`false`)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "locations": [
    {
      "_id": "location_id_1",
      "name": "Downtown Salon",
      "address": {
        "street": "123 Main Street",
        "city": "London",
        "state": "England",
        "zipCode": "SW1A 1AA",
        "country": "United Kingdom"
      },
      "timezone": "Europe/London",
      "coordinates": {
        "latitude": 51.5074,
        "longitude": -0.1278
      },
      "isActive": true
    }
  ]
}
```

#### 3. Get Location by ID

**Endpoint:** `GET /locations/:locationId`
**Description:** Retrieves a specific location by ID.

**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "location": {
    "_id": "location_id",
    "name": "Downtown Salon",
    "address": {
      "street": "123 Main Street",
      "city": "London",
      "state": "England",
      "zipCode": "SW1A 1AA",
      "country": "United Kingdom"
    },
    "timezone": "Europe/London",
    "phone": "+44 20 7123 4567",
    "email": "<EMAIL>",
    "description": "Our flagship location in central London",
    "coordinates": {
      "latitude": 51.5074,
      "longitude": -0.1278
    },
    "isActive": true
  }
}
```

#### 4. Update Location

**Endpoint:** `PUT /locations/:locationId`
**Description:** Updates an existing location. Address changes trigger automatic re-geocoding.

**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /locations/location_id HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Downtown Salon - Updated",
  "phone": "+44 20 7123 4568",
  "description": "Updated flagship location"
}
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Location updated successfully",
  "location": {
    "_id": "location_id",
    "name": "Downtown Salon - Updated",
    "phone": "+44 20 7123 4568",
    "description": "Updated flagship location",
    "coordinates": {
      "latitude": 51.5074,
      "longitude": -0.1278
    }
  }
}
```

#### 5. Delete Location

**Endpoint:** `DELETE /locations/:locationId`
**Description:** Deletes a location. Only allowed if no active bookings or service assignments exist.

**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Location deleted successfully"
}
```

**Error Response:**

- **400 Bad Request** (if location has active bookings/services)

```json
{
  "success": false,
  "error": "Cannot delete location. It has active bookings or services assigned to it."
}
```

#### 6. Assign Service to Location

**Endpoint:** `POST /locations/assign-service`
**Description:** Assigns a service to a specific location with optional room/resource tracking.

**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /locations/assign-service HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "serviceId": "service_id",
  "locationId": "location_id",
  "room": "Room A",
  "resource": "Massage Table 1"
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "message": "Service assigned to location successfully",
  "serviceLocation": {
    "_id": "service_location_id",
    "serviceId": {
      "_id": "service_id",
      "name": "Hair Cut & Style",
      "duration": 60,
      "price": 50
    },
    "locationId": {
      "_id": "location_id",
      "name": "Downtown Salon",
      "address": {
        "street": "123 Main Street",
        "city": "London"
      }
    },
    "room": "Room A",
    "resource": "Massage Table 1",
    "isActive": true
  }
}
```

#### 7. Get Services for Location

**Endpoint:** `GET /locations/:locationId/services`
**Description:** Retrieves all services available at a specific location.

**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "location": {
    "_id": "location_id",
    "name": "Downtown Salon",
    "address": {
      "street": "123 Main Street",
      "city": "London"
    }
  },
  "services": [
    {
      "_id": "service_location_id",
      "serviceId": {
        "_id": "service_id",
        "name": "Hair Cut & Style",
        "description": "Professional haircut and styling",
        "duration": 60,
        "price": 50,
        "category": "category_id"
      },
      "room": "Room A",
      "resource": "Massage Table 1",
      "isActive": true
    }
  ]
}
```

#### 8. Find Nearby Locations

**Endpoint:** `GET /locations/nearby`
**Description:** Finds locations within a specified radius of given coordinates.

**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `latitude` (required): Center latitude
- `longitude` (required): Center longitude
- `radius` (optional): Search radius in kilometers (default: 10)

**Request:**

```http
GET /locations/nearby?latitude=51.5074&longitude=-0.1278&radius=5 HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "center": {
    "latitude": 51.5074,
    "longitude": -0.1278
  },
  "radius": 5,
  "locations": [
    {
      "_id": "location_id",
      "name": "Downtown Salon",
      "address": {
        "street": "123 Main Street",
        "city": "London"
      },
      "coordinates": {
        "latitude": 51.5074,
        "longitude": -0.1278
      },
      "distance": 0.0
    },
    {
      "_id": "location_id_2",
      "name": "West End Branch",
      "address": {
        "street": "456 Oxford Street",
        "city": "London"
      },
      "coordinates": {
        "latitude": 51.5154,
        "longitude": -0.1419
      },
      "distance": 2.3
    }
  ]
}
```

#### 9. Search Locations

**Endpoint:** `GET /locations/search`
**Description:** Searches locations by name or address components.

**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `query` (required): Search term
- `limit` (optional): Maximum results (default: 10)

**Request:**

```http
GET /locations/search?query=london&limit=5 HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "query": "london",
  "locations": [
    {
      "_id": "location_id",
      "name": "Downtown Salon",
      "address": {
        "street": "123 Main Street",
        "city": "London",
        "state": "England"
      },
      "coordinates": {
        "latitude": 51.5074,
        "longitude": -0.1278
      }
    }
  ]
}
```

#### 10. Get Location Statistics

**Endpoint:** `GET /locations/stats`
**Description:** Retrieves location usage statistics and geocoding coverage.

**Headers:** `Authorization: Bearer <token>`

**Response:**

- **200 OK**

```json
{
  "success": true,
  "stats": {
    "totalLocations": 5,
    "locationsWithCoordinates": 4,
    "locationsWithoutCoordinates": 1,
    "activeServiceLocations": 3,
    "geocodingCoverage": 80
  }
}
```

**Geolocation Features:**

- **Automatic Geocoding**: Addresses are automatically geocoded using OpenCage API
- **Smart Caching**: Coordinates are cached to avoid repeated API calls
- **Geospatial Queries**: MongoDB geospatial indexes for fast location searches
- **Distance Calculations**: Precise distance calculations using Haversine formula
- **UK Focus**: Geocoding optimized for UK addresses (configurable)

**Business Use Cases:**

- **Multi-Location Salons**: Different services at different branches
- **Medical Practices**: Multiple offices with different specialties
- **Fitness Centers**: Multiple gym locations with different equipment
- **Consulting Firms**: Multiple office locations for client meetings
- **Event Spaces**: Different rooms/resources within the same location

#### 10. Staff Location Management

**Assign Locations to Staff**

- **Endpoint:** `PUT /staff/:staffId/locations`
- **Description:** Assigns multiple locations to a staff member.
- **Headers:** `Authorization: Bearer <token>`
- **Request:**

```http
PUT /staff/staff_id/locations HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "locationIds": ["location_id_1", "location_id_2"]
}
```

- **Response:**

```json
{
  "success": true,
  "message": "Staff locations updated successfully",
  "staff": {
    "_id": "staff_id",
    "name": "Jane Smith",
    "locations": [
      {
        "_id": "location_id_1",
        "name": "Downtown Salon",
        "address": {
          "street": "123 Main Street",
          "city": "London"
        }
      }
    ]
  }
}
```

**Get Staff Locations**

- **Endpoint:** `GET /staff/locations/:staffId`
- **Description:** Retrieves all locations assigned to a staff member.
- **Headers:** `Authorization: Bearer <token>`
- **Response:**

```json
{
  "success": true,
  "staff": {
    "_id": "staff_id",
    "name": "Jane Smith",
    "role": "Stylist"
  },
  "locations": [
    {
      "_id": "location_id_1",
      "name": "Downtown Salon",
      "address": {
        "street": "123 Main Street",
        "city": "London"
      },
      "timezone": "Europe/London"
    }
  ]
}
```

**Get Staff by Location**

- **Endpoint:** `GET /staff/by-location/:locationId`
- **Description:** Retrieves all staff members assigned to a specific location.
- **Headers:** `Authorization: Bearer <token>`
- **Response:**

```json
{
  "success": true,
  "location": {
    "_id": "location_id_1",
    "name": "Downtown Salon",
    "address": {
      "street": "123 Main Street",
      "city": "London"
    }
  },
  "staff": [
    {
      "_id": "staff_id_1",
      "name": "Jane Smith",
      "role": "Stylist",
      "services": [
        {
          "_id": "service_id_1",
          "name": "Hair Cut",
          "duration": 60,
          "price": 50
        }
      ]
    }
  ]
}
```

#### 11. Location Analytics

**Get Location Analytics**

- **Endpoint:** `GET /locations/:locationId/analytics`
- **Description:** Retrieves comprehensive analytics for a specific location.
- **Headers:** `Authorization: Bearer <token>`
- **Query Parameters:**
  - `startDate` (optional): Start date in ISO format (default: 30 days ago)
  - `endDate` (optional): End date in ISO format (default: today)
- **Response:**

```json
{
  "success": true,
  "location": {
    "_id": "location_id_1",
    "name": "Downtown Salon",
    "address": {
      "street": "123 Main Street",
      "city": "London"
    }
  },
  "period": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-01-31T23:59:59.999Z"
  },
  "analytics": {
    "totalBookings": 150,
    "totalRevenue": 7500,
    "averageBookingValue": 50,
    "statusBreakdown": {
      "pending": 5,
      "confirmed": 120,
      "canceled": 15,
      "completed": 10
    },
    "serviceRevenue": {
      "Hair Cut": 3000,
      "Hair Color": 2500,
      "Styling": 2000
    },
    "dailyRevenue": {
      "2024-01-15": 250,
      "2024-01-16": 300,
      "2024-01-17": 200
    },
    "staffPerformance": [
      {
        "name": "Jane Smith",
        "role": "Stylist",
        "totalBookings": 80,
        "confirmedBookings": 75
      }
    ],
    "popularServices": [
      {
        "serviceName": "Hair Cut",
        "bookingCount": 60,
        "revenue": 3000
      }
    ]
  }
}
```

**Get Multi-Location Analytics**

- **Endpoint:** `GET /locations/analytics/multi-location`
- **Description:** Retrieves analytics comparing performance across all locations.
- **Headers:** `Authorization: Bearer <token>`
- **Query Parameters:**
  - `startDate` (optional): Start date in ISO format (default: 30 days ago)
  - `endDate` (optional): End date in ISO format (default: today)
- **Response:**

```json
{
  "success": true,
  "period": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-01-31T23:59:59.999Z"
  },
  "overall": {
    "totalLocations": 3,
    "totalBookings": 450,
    "totalRevenue": 22500,
    "averageBookingValue": 50
  },
  "locationAnalytics": [
    {
      "locationId": "location_id_1",
      "locationName": "Downtown Salon",
      "address": {
        "street": "123 Main Street",
        "city": "London"
      },
      "totalBookings": 200,
      "totalRevenue": 10000,
      "averageBookingValue": 50,
      "statusBreakdown": {
        "pending": 10,
        "confirmed": 160,
        "canceled": 20,
        "completed": 10
      }
    }
  ]
}
```

#### 12. Booking Integration with Locations

**Location-Based Booking**
All booking endpoints now support `locationId` parameter:

- **Internal Bookings:** `POST /bookings/internal`
- **External Bookings:** `POST /bookings/external`
- **Paid Service Bookings:** `POST /bookings/external/paid`

**Request Example:**

```json
{
  "serviceId": "service_id",
  "staffId": "staff_id",
  "locationId": "location_id",
  "date": "2024-01-15",
  "timeSlot": "10:00-11:00",
  "customerInfo": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+44 20 7123 4567"
  }
}
```

**Location Validation:**

- System validates that the location belongs to the user
- Ensures the service is available at the selected location
- Provides clear error messages for invalid location assignments

### Staff Assignment & Routing

Staff assignment and routing allows businesses to automatically assign staff members to bookings based on configurable strategies. This system supports multiple routing algorithms and integrates with the booking flow.

**Key Features:**

- **Multiple Routing Strategies**: Round-robin, load-balance, priority, and customer-choice
- **Auto-Assignment**: Automatic staff assignment when no staff is selected
- **Customer Choice**: Allow customers to select their preferred staff member
- **Service Integration**: Routing rules are configured per service
- **Availability Integration**: Works with staff availability and PTO systems

#### 1. Create Routing Rules

**Endpoint:** `POST /services/:serviceId/routing-rules`
**Description:** Creates routing rules for a specific service.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /services/:serviceId/routing-rules HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "strategy": "round-robin",
  "allowCustomerChoice": true,
  "autoAssign": true,
  "priorityOrder": ["staff_id_1", "staff_id_2"],
  "weights": {
    "staff_id_1": 0.7,
    "staff_id_2": 0.3
  }
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "message": "Routing rules created successfully",
  "routingRules": {
    "_id": "rules_id",
    "serviceId": {
      "_id": "service_id",
      "name": "Haircut Service",
      "urlName": "haircut-service"
    },
    "strategy": "round-robin",
    "allowCustomerChoice": true,
    "autoAssign": true,
    "isActive": true
  }
}
```

#### 2. Get Routing Rules

**Endpoint:** `GET /services/:serviceId/routing-rules`
**Description:** Retrieves routing rules for a specific service.
**Headers:** `Authorization: Bearer <token>`

#### 3. Update Routing Rules

**Endpoint:** `PUT /services/:serviceId/routing-rules`
**Description:** Updates routing rules for a service.
**Headers:** `Authorization: Bearer <token>`

#### 4. Delete Routing Rules

**Endpoint:** `DELETE /services/:serviceId/routing-rules`
**Description:** Deletes routing rules for a service.
**Headers:** `Authorization: Bearer <token>`

#### 5. Get Available Staff for Service

**Endpoint:** `GET /services/:serviceId/available-staff`
**Description:** Gets available staff for a service based on routing rules.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `date`: Date in ISO format (optional)
- `timeSlot`: Time slot in HH:MM format (optional)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "availableStaff": [
    {
      "_id": "staff_id",
      "name": "Sarah Johnson",
      "role": "Senior Stylist",
      "color": "#3B82F6"
    }
  ],
  "routingRules": {
    "strategy": "round-robin",
    "allowCustomerChoice": true,
    "autoAssign": true
  },
  "suggestedStaff": "staff_id",
  "canCustomerChoose": true
}
```

#### 6. Auto-Assign Staff

**Endpoint:** `POST /auto-assign-staff`
**Description:** Automatically assigns staff based on routing rules.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /auto-assign-staff HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "serviceId": "service_id"
}
```

#### 7. Enhanced Staff Assignment

**Endpoint:** `POST /enhanced-assignment`
**Description:** Advanced staff assignment using sophisticated algorithms with workload analysis and performance metrics.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /enhanced-assignment HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "serviceId": "service_id",
  "date": "2024-01-15T00:00:00.000Z",
  "timeSlot": "10:00",
  "preferences": {
    "preferredStaffId": "staff_id",
    "avoidStaffIds": ["staff_id_2"],
    "skillRequirements": ["haircut", "styling"],
    "experienceLevel": "senior",
    "languagePreference": "English",
    "genderPreference": "any"
  }
}
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "assignedStaffId": "staff_id",
  "assignedStaff": {
    "_id": "staff_id",
    "name": "Sarah Johnson",
    "role": "Senior Stylist",
    "color": "#3B82F6"
  },
  "reason": "Workload-based assignment",
  "alternatives": ["staff_id_2", "staff_id_3"],
  "confidence": 85,
  "workloadData": [
    {
      "staffId": "staff_id",
      "currentBookings": 3,
      "totalHours": 24,
      "efficiency": 1.2,
      "reliability": 0.95,
      "customerSatisfaction": 4.8,
      "performanceScore": 85,
      "workloadScore": 30,
      "availability": true
    }
  ]
}
```

#### 8. Get Routing Recommendations

**Endpoint:** `GET /services/:serviceId/routing-recommendations`
**Description:** Gets intelligent routing recommendations based on team size and performance analysis.
**Headers:** `Authorization: Bearer <token>`
**Response:**

- **200 OK**

```json
{
  "success": true,
  "recommendations": {
    "recommendedStrategy": "load-balance",
    "reasoning": "Medium team - load balancing optimizes workload",
    "staffAnalysis": [
      {
        "staffId": "staff_id",
        "name": "Sarah Johnson",
        "role": "Senior Stylist",
        "performance": {
          "efficiency": 1.2,
          "reliability": 0.95,
          "satisfaction": 4.8
        }
      }
    ]
  }
}
```

**Routing Strategies:**

- **round-robin**: Assigns staff in a rotating order with workload consideration
- **load-balance**: Advanced workload-based distribution with performance metrics
- **priority**: Performance-based assignment with configurable priority order
- **customer-choice**: Customer preference-based assignment with smart fallbacks
- **smart**: AI-powered assignment combining multiple factors (workload, performance, preferences)

### Email Service & Notifications

The email service provides automated email notifications for various system events, including staff verification, booking confirmations, reminders, and more. The system supports both platform defaults and custom business branding.

**Key Features:**

- **Template-Based System**: Handlebars templates with type-safe variables
- **Business Branding**: Custom logos, colors, and business names
- **Automatic Fallbacks**: Graceful degradation when custom templates fail
- **Multiple Categories**: Booking emails, onboarding emails, and notifications
- **Staff Verification**: Automatic credential emails for new staff members

#### Email Templates

**Onboarding Templates:**

- **Welcome Email**: New user welcome with login instructions
- **Email Verification**: Account verification emails
- **Password Reset**: Password reset requests
- **Staff Verification**: Staff account creation with credentials
- **Support Feedback**: Support ticket notifications

**Booking Templates:**

- **Booking Confirmation**: Customer booking confirmations
- **Booking Notification**: Business booking notifications

#### Staff Verification Emails

When a new staff member is created, the system automatically sends a verification email containing:

**Email Content:**

- Professional welcome message with business branding
- Login credentials (username and temporary password)
- Direct login link
- Security instructions to change password after first login
- Step-by-step setup instructions
- Business contact information

**Example Staff Verification Email:**

```html
Subject: Welcome to Beauty Salon - Staff Account Created Hi Sarah Johnson, Your
staff account has been created successfully! You can now access the Beauty Salon
scheduling system. Your Login Credentials: Username: sarah.johnson Temporary
Password: tempPass123 [Log In to Your Account] button Important Security Notice:
For your security, please change your password immediately after your first
login: 1. Log in using the credentials above 2. Go to your profile settings 3.
Click on "Change Password" 4. Enter a new, secure password What You Can Do Next:
• Complete your staff profile • Set your availability and working hours • View
and manage your appointments • Access your schedule and calendar • Update your
personal information Welcome aboard! The Beauty Salon Team
```

**Email Service Configuration:**

- **SMTP Settings**: Configurable via environment variables
- **Template Paths**: Organized by category (onboarding, booking)
- **Base Wrapper**: Consistent styling and branding across all emails
- **Error Handling**: Comprehensive logging and fallback mechanisms

### ICS Calendar Integration

The system automatically generates ICS (iCalendar) files for booking emails, allowing customers to add appointments directly to their calendar applications (Google Calendar, Outlook, Apple Calendar, etc.).

**Key Features:**

- **Automatic Generation**: ICS files created for all booking confirmation emails
- **Cross-Platform Compatibility**: Works with all major calendar applications
- **Rich Event Details**: Includes service information, location, and customer details
- **Reminder Alerts**: Built-in 15-minute and 1-hour reminders
- **Secure Attachments**: ICS files attached to booking emails automatically
- **Timezone Support**: Proper timezone handling for global businesses

#### ICS File Structure

**Generated ICS Content:**

```ics
BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Puzzle Piece Solutions//Booking System//EN
CALSCALE:GREGORIAN
METHOD:REQUEST
BEGIN:VEVENT
UID:<EMAIL>
DTSTAMP:20240115T100000Z
DTSTART:20240115T100000Z
DTEND:20240115T110000Z
SUMMARY:Haircut & Styling - Salon ABC
DESCRIPTION:Appointment for Haircut & Styling\n\nBooking Details:\nService: Haircut & Styling\nTime: 10:00-11:00\nBusiness: Salon ABC\n\nCustomer Information:\nName: John Doe\nEmail: <EMAIL>\nPhone: +**********\n\nThis appointment was booked through Puzzle Piece Solutions.\nYou can reschedule or cancel this appointment using the links in your confirmation email.
ORGANIZER;CN=Salon ABC:mailto:<EMAIL>
ATTENDEE;CN=John Doe:mailto:<EMAIL>
LOCATION:123 Main St, City, State 12345
STATUS:CONFIRMED
SEQUENCE:0
TRANSP:OPAQUE
BEGIN:VALARM
TRIGGER:-PT15M
ACTION:DISPLAY
DESCRIPTION:Reminder: You have an appointment in 15 minutes
END:VALARM
BEGIN:VALARM
TRIGGER:-PT1H
ACTION:DISPLAY
DESCRIPTION:Reminder: You have an appointment in 1 hour
END:VALARM
END:VEVENT
END:VCALENDAR
```

#### ICS Implementation

**Automatic Integration:**

- ICS files are automatically generated and attached to booking emails
- No additional configuration required
- Works with all existing booking flows

**Email Functions:**

```typescript
// Send booking email with ICS attachment
await sendBookingEmail(
  customerEmail,
  "Booking Confirmation",
  "bookingConfirmationContent.hbs",
  emailVariables,
  booking,
  businessUser,
  service,
  location,
  true // Include ICS attachment
);

// Send email with custom ICS data
await sendBookingEmailWithICS(
  customerEmail,
  "Booking Confirmation",
  "bookingConfirmationContent.hbs",
  emailVariables,
  icsBookingData,
  true // Include ICS attachment
);
```

**ICS Data Structure:**

```typescript
interface ICSBookingData {
  title: string; // Event title
  description: string; // Event description
  startDate: Date; // Event start time
  endDate: Date; // Event end time
  location?: string; // Event location
  organizerName: string; // Business name
  organizerEmail: string; // Business email
  attendeeName: string; // Customer name
  attendeeEmail: string; // Customer email
  bookingId: string; // Unique booking ID
  businessName: string; // Business name
  serviceName: string; // Service name
  timeSlot: string; // Time slot (e.g., "10:00-11:00")
  customerInfo?: {
    // Customer details
    firstName?: string;
    lastName?: string;
    phone?: string;
  };
}
```

#### Customer Experience

**Email Attachment:**

- ICS file automatically attached to booking confirmation emails
- Filename format: `appointment_{bookingId}_{serviceName}.ics`
- Content-Type: `text/calendar; charset=utf-8; method=REQUEST`

**Calendar Integration:**

1. Customer receives booking confirmation email with ICS attachment
2. Customer clicks on the ICS file attachment
3. Calendar application opens with pre-filled event details
4. Customer can add the appointment to their calendar with one click
5. Reminders are automatically set (15 minutes and 1 hour before)

**Supported Calendar Applications:**

- **Google Calendar**: Direct import with full event details
- **Microsoft Outlook**: Native support for ICS files
- **Apple Calendar**: Seamless integration on Mac and iOS
- **Thunderbird**: Full calendar support
- **Other Applications**: Any calendar app supporting RFC 5545 standard

#### Business Benefits

**Reduced No-Shows:**

- Customers can easily add appointments to their calendars
- Automatic reminders reduce forgotten appointments
- Professional appearance increases customer confidence

**Improved Customer Experience:**

- One-click calendar integration
- No manual data entry required
- Consistent event information across all platforms

**Operational Efficiency:**

- Automatic ICS generation requires no manual intervention
- Reduces customer service inquiries about appointment details
- Professional email appearance with calendar integration

#### Technical Details

**ICS Generation Process:**

1. Booking data is collected from database
2. ICS data structure is created using `createICSBookingData()`
3. ICS content is generated using `generateICS()`
4. ICS file is attached to email using `sendBookingEmail()`
5. Email is sent with calendar attachment

**Security Features:**

- Unique event UIDs prevent conflicts
- Proper escaping of special characters
- Timezone-aware date handling
- Input validation and sanitization

**Error Handling:**

- ICS generation failures don't prevent email sending
- Graceful fallback when ICS generation fails
- Comprehensive logging for debugging

### PTO Management

PTO (Paid Time Off) management allows businesses to handle staff time off requests, approvals, and availability tracking. This system integrates with the booking system to prevent double-booking during PTO periods.

**Key Features:**

- **PTO Requests**: Staff can submit time off requests
- **Approval Workflow**: Business owners can approve/reject requests
- **Recurring PTO**: Support for recurring time off patterns
- **Availability Integration**: PTO blocks appear in booking system
- **Email Notifications**: Automatic notifications for requests and approvals

#### 1. Create PTO Request

**Endpoint:** `POST /staff/:staffId/pto`
**Description:** Creates a PTO request for a staff member.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /staff/:staffId/pto HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "startDate": "2024-01-15T00:00:00.000Z",
  "endDate": "2024-01-17T23:59:59.000Z",
  "startTime": "09:00",
  "endTime": "17:00",
  "type": "vacation",
  "description": "Annual vacation",
  "isRecurring": false,
  "recurringPattern": {
    "frequency": "weekly",
    "interval": 1,
    "daysOfWeek": [1, 2, 3, 4, 5],
    "endDate": "2024-12-31T23:59:59.000Z"
  }
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "message": "PTO request created successfully",
  "ptoRequest": {
    "_id": "pto_id",
    "staffId": {
      "_id": "staff_id",
      "name": "Sarah Johnson",
      "email": "<EMAIL>",
      "role": "Senior Stylist"
    },
    "startDate": "2024-01-15T00:00:00.000Z",
    "endDate": "2024-01-17T23:59:59.000Z",
    "type": "vacation",
    "description": "Annual vacation",
    "isApproved": false,
    "isRecurring": false
  }
}
```

#### 2. Get PTO for Staff

**Endpoint:** `GET /staff/:staffId/pto`
**Description:** Retrieves PTO requests for a specific staff member.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `startDate`: Start date in ISO format (optional)
- `endDate`: End date in ISO format (optional)
- `isApproved`: Filter by approval status (true/false)

#### 3. Get All PTO Requests

**Endpoint:** `GET /pto`
**Description:** Retrieves all PTO requests for the business owner.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `isApproved`: Filter by approval status (true/false)
- `staffId`: Filter by staff member (optional)
- `startDate`: Start date in ISO format (optional)
- `endDate`: End date in ISO format (optional)
- `page`: Page number for pagination (default: 1)
- `limit`: Number of requests per page (default: 10)

#### 4. Update PTO Request

**Endpoint:** `PUT /pto/:ptoId`
**Description:** Updates a PTO request (only if not approved).
**Headers:** `Authorization: Bearer <token>`

#### 5. Approve/Reject PTO Request

**Endpoint:** `PATCH /pto/:ptoId/approve`
**Description:** Approves or rejects a PTO request.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PATCH /pto/:ptoId/approve HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "isApproved": true,
  "reason": "Approved for annual vacation"
}
```

#### 6. Delete PTO Request

**Endpoint:** `DELETE /pto/:ptoId`
**Description:** Deletes a PTO request (only if not approved).
**Headers:** `Authorization: Bearer <token>`

#### 7. Get Staff Availability

**Endpoint:** `GET /staff/:staffId/availability`
**Description:** Gets detailed availability for a staff member on a specific date.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `date`: Date in ISO format (required)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "date": "2024-01-15T00:00:00.000Z",
  "staff": {
    "_id": "staff_id",
    "name": "Sarah Johnson",
    "role": "Senior Stylist"
  },
  "availability": {
    "isWorking": true,
    "workingHours": {
      "start": "09:00",
      "end": "17:00"
    },
    "ptoRequests": [
      {
        "type": "vacation",
        "startTime": "09:00",
        "endTime": "17:00",
        "description": "Annual vacation"
      }
    ],
    "isAvailable": false
  }
}
```

**PTO Types:**

- `vacation`: Regular vacation time
- `sick`: Sick leave
- `personal`: Personal time off
- `meeting`: Meeting or training time
- `training`: Training or development time
- `other`: Other types of time off

**Recurring Patterns:**

- **Daily**: Every N days
- **Weekly**: Every N weeks on specific days
- **Monthly**: Every N months

**Integration with Booking System:**

- PTO periods automatically block staff availability
- Booking system prevents appointments during PTO
- Staff calendar shows PTO periods
- Email notifications for PTO requests and approvals

### Staff Performance & Analytics

Staff performance and analytics system provides comprehensive tracking of staff metrics, customer ratings, and performance insights to help business owners optimize their team's productivity and customer satisfaction.

**Key Features:**

- **Performance Metrics**: Track efficiency, reliability, customer satisfaction, and revenue metrics
- **Customer Ratings**: Collect and manage customer feedback for staff members
- **Performance Rankings**: Compare staff performance across different metrics
- **Analytics Dashboard**: Comprehensive analytics and insights
- **Period-based Analysis**: Daily, weekly, monthly, quarterly, and yearly performance tracking
- **Service Breakdown**: Performance analysis by individual services

#### 1. Get Staff Performance

**Endpoint:** `GET /staff-performance/performance`
**Description:** Retrieves staff performance metrics for a specific period.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `staffId`: Staff member ID (required)
- `startDate`: Start date in ISO format (required)
- `endDate`: End date in ISO format (required)
- `periodType`: Period type - daily, weekly, monthly, quarterly, yearly (default: monthly)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "performance": {
    "_id": "performance_id",
    "staffId": "staff_id",
    "period": {
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-01-31T23:59:59.000Z",
      "type": "monthly"
    },
    "metrics": {
      "totalBookings": 45,
      "completedBookings": 42,
      "cancelledBookings": 2,
      "noShowBookings": 1,
      "totalRevenue": 2100.0,
      "averageRating": 4.8,
      "totalReviews": 38,
      "totalHours": 42,
      "efficiency": 1.0,
      "customerSatisfaction": 4.8,
      "reliability": 0.93
    },
    "serviceBreakdown": [
      {
        "serviceId": "service_id",
        "serviceName": "Haircut",
        "bookings": 25,
        "revenue": 1250.0,
        "averageRating": 4.9
      }
    ]
  },
  "staff": {
    "_id": "staff_id",
    "name": "Sarah Johnson",
    "role": "Senior Stylist",
    "color": "#3B82F6"
  }
}
```

#### 2. Get Staff Rankings

**Endpoint:** `GET /staff-performance/rankings`
**Description:** Retrieves staff performance rankings across different metrics.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `periodType`: Period type - daily, weekly, monthly, quarterly, yearly (default: monthly)
- `metric`: Ranking metric - efficiency, reliability, customerSatisfaction, totalRevenue (default: efficiency)
- `startDate`: Start date in ISO format (required)
- `endDate`: End date in ISO format (required)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "rankings": [
    {
      "_id": "performance_id",
      "staffId": {
        "_id": "staff_id",
        "name": "Sarah Johnson",
        "role": "Senior Stylist",
        "color": "#3B82F6"
      },
      "metrics": {
        "efficiency": 1.2,
        "reliability": 0.95,
        "customerSatisfaction": 4.8,
        "totalRevenue": 2100.0
      }
    }
  ],
  "period": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-01-31T23:59:59.000Z",
    "type": "monthly"
  },
  "metric": "efficiency"
}
```

#### 3. Submit Staff Rating

**Endpoint:** `POST /staff-performance/ratings`
**Description:** Submits a customer rating and review for a staff member.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /staff-performance/ratings HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "staffId": "staff_id",
  "bookingId": "booking_id",
  "rating": 5,
  "review": "Excellent service! Very professional and friendly.",
  "isAnonymous": false,
  "customerInfo": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>"
  }
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "message": "Staff rating submitted successfully",
  "rating": {
    "_id": "rating_id",
    "staffId": "staff_id",
    "bookingId": "booking_id",
    "rating": 5,
    "review": "Excellent service! Very professional and friendly.",
    "isVerified": true,
    "createdAt": "2024-01-15T10:00:00.000Z"
  }
}
```

#### 4. Get Staff Ratings

**Endpoint:** `GET /staff-performance/staff/:staffId/ratings`
**Description:** Retrieves all ratings and reviews for a specific staff member.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `page`: Page number for pagination (default: 1)
- `limit`: Number of ratings per page (default: 10)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "ratings": [
    {
      "_id": "rating_id",
      "rating": 5,
      "review": "Excellent service! Very professional and friendly.",
      "customerInfo": {
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>"
      },
      "isAnonymous": false,
      "isVerified": true,
      "createdAt": "2024-01-15T10:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 38,
    "pages": 4
  },
  "summary": {
    "averageRating": 4.8,
    "totalRatings": 38,
    "ratingDistribution": [
      { "_id": 5, "count": 25 },
      { "_id": 4, "count": 10 },
      { "_id": 3, "count": 2 },
      { "_id": 2, "count": 1 },
      { "_id": 1, "count": 0 }
    ]
  }
}
```

#### 5. Get Staff Analytics

**Endpoint:** `GET /staff-performance/analytics`
**Description:** Retrieves comprehensive staff performance analytics.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `startDate`: Start date in ISO format (required)
- `endDate`: End date in ISO format (required)
- `periodType`: Period type - daily, weekly, monthly, quarterly, yearly (default: monthly)

**Response:**

- **200 OK**

```json
{
  "success": true,
  "analytics": {
    "totalStaff": 5,
    "averageEfficiency": 1.1,
    "averageReliability": 0.92,
    "averageSatisfaction": 4.6,
    "topPerformers": [
      {
        "staffId": "staff_id",
        "name": "Sarah Johnson",
        "efficiency": 1.2,
        "reliability": 0.95,
        "satisfaction": 4.8
      }
    ]
  },
  "period": {
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-01-31T23:59:59.000Z",
    "type": "monthly"
  }
}
```

**Performance Metrics:**

- **Efficiency**: Bookings completed per hour worked
- **Reliability**: Percentage of completed vs total bookings
- **Customer Satisfaction**: Average customer rating
- **Total Revenue**: Revenue generated by the staff member

**Rating System:**

- **1-5 Star Rating**: Customer rating scale
- **Review Text**: Detailed customer feedback
- **Verification**: Automatic verification against booking records
- **Anonymous Reviews**: Option for anonymous feedback
- **Email Notifications**: Staff notifications for new ratings

**Analytics Features:**

- **Period-based Analysis**: Compare performance across different time periods
- **Service Breakdown**: Performance analysis by individual services
- **Trend Analysis**: Track performance trends over time
- **Benchmarking**: Compare staff performance against team averages

### External Accounts

#### 1. Connect External Account

**Endpoint:** `POST /external-accounts/connect`
**Description:** Connects external service account (Google Calendar, etc.).
**Headers:** `Authorization: Bearer <token>`

#### 2. Disconnect External Account

**Endpoint:** `DELETE /external-accounts/disconnect/:provider`
**Description:** Disconnects external service account.
**Headers:** `Authorization: Bearer <token>`

#### 3. Get Connected Accounts

**Endpoint:** `GET /external-accounts`
**Description:** Retrieves connected external accounts.
**Headers:** `Authorization: Bearer <token>`

### Email Settings

#### 1. Get Email Settings

**Endpoint:** `GET /email-settings`
**Description:** Retrieves email notification settings.
**Headers:** `Authorization: Bearer <token>`

#### 2. Update Email Settings

**Endpoint:** `PUT /email-settings`
**Description:** Updates email notification settings.
**Headers:** `Authorization: Bearer <token>`

### Resources

#### 1. Get Resources

**Endpoint:** `GET /resources`
**Description:** Retrieves available resources.
**Headers:** `Authorization: Bearer <token>`

---

## Business Features

### Subscriptions

#### 1. Get Subscription Plans

**Endpoint:** `GET /subscription-plans`
**Description:** Retrieves available subscription plans.
**Response:**

- **200 OK**

```json
{
  "success": true,
  "plans": [
    {
      "_id": "plan_id",
      "name": "Basic Plan",
      "price": 29.99,
      "features": ["feature1", "feature2"]
    }
  ]
}
```

#### 2. Get Current Subscription Plan

**Endpoint:** `GET /user-subscription-plan`
**Description:** Retrieves user's current subscription plan.
**Headers:** `Authorization: Bearer <token>`

#### 3. Upgrade Subscription

**Endpoint:** `PUT /upgrade-subscription`
**Description:** Upgrades user subscription.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
PUT /upgrade-subscription HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "planId": "plan_id"
}
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Subscription upgraded successfully",
  "subscription": {},
  "clientSecret": "pi_**********_secret_xyz"
}
```

#### 4. Setup Payment Intent

**Endpoint:** `GET /setup-intent`
**Description:** Creates setup intent for payment method.
**Headers:** `Authorization: Bearer <token>`

#### 5. Add Payment Method

**Endpoint:** `POST /add-payment-method`
**Description:** Adds payment method to user account.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /add-payment-method HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "paymentMethodId": "pm_**********"
}
```

#### 6. Add Payment Method on Signup

**Endpoint:** `POST /add-payment/on-signup`
**Description:** Adds payment method during user registration.
**Request:**

```http
POST /add-payment/on-signup HTTP/1.1
Host: api.puzzlepiecesolutions.com
Content-Type: application/json

{
  "paymentMethodId": "pm_**********",
  "userId": "user_id"
}
```

#### 7. Delete Payment Method

**Endpoint:** `DELETE /delete-payment-method/:paymentMethodId`
**Description:** Removes payment method from user account.
**Headers:** `Authorization: Bearer <token>`

### Wallet & Credits

#### 1. Purchase Credits

**Endpoint:** `POST /purchase-credit`
**Description:** Purchases credits using wallet.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /purchase-credit HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "creditPackageId": "package_id",
  "paymentMethodId": "pm_**********"
}
```

#### 2. Get Credit Packages

**Endpoint:** `GET /credit-packages`
**Description:** Retrieves available credit packages.
**Headers:** `Authorization: Bearer <token>`

#### 3. Get Payment Methods

**Endpoint:** `GET /payment-method`
**Description:** Retrieves user's payment methods.
**Headers:** `Authorization: Bearer <token>`

#### 4. Purchase Credit Success

**Endpoint:** `POST /purchase-credit/success`
**Description:** Handles successful credit purchase.
**Headers:** `Authorization: Bearer <token>`

### Business Leads

#### 1. Search Business Leads

**Endpoint:** `GET /leads`
**Description:** Searches for available business leads.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `industry`: Filter by industry
- `location`: Filter by location
- `page`: Page number
- `limit`: Results per page

#### 2. Get Lead by ID

**Endpoint:** `GET /leads/:Id`
**Description:** Retrieves specific lead details.
**Headers:** `Authorization: Bearer <token>`

#### 3. Add Lead to Cart

**Endpoint:** `POST /leads/buy`
**Description:** Adds lead to purchase cart.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /leads/buy HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "leadId": "lead_id",
  "quantity": 1
}
```

#### 4. Get Leads Cart

**Endpoint:** `GET /leads/carts`
**Description:** Retrieves user's leads cart.
**Headers:** `Authorization: Bearer <token>`

#### 5. Get Purchased Leads Cart

**Endpoint:** `GET /leads/carts/purchased`
**Description:** Retrieves purchased leads.
**Headers:** `Authorization: Bearer <token>`

#### 6. Get Pending Cart

**Endpoint:** `GET /leads/cart/pending`
**Description:** Retrieves pending cart items.
**Headers:** `Authorization: Bearer <token>`

#### 7. Checkout Lead Cart

**Endpoint:** `GET /leads/checkout/:cartId`
**Description:** Processes lead cart checkout.
**Headers:** `Authorization: Bearer <token>`

#### 8. Get Purchased Leads

**Endpoint:** `GET /leads-purchased`
**Description:** Retrieves all purchased leads.
**Headers:** `Authorization: Bearer <token>`

### Business Clients

#### 1. Get Business Clients

**Endpoint:** `GET /business-client`
**Description:** Retrieves all business clients.
**Headers:** `Authorization: Bearer <token>`

#### 2. Get Business Client by ID

**Endpoint:** `GET /business-client/:Id`
**Description:** Retrieves specific client details.
**Headers:** `Authorization: Bearer <token>`

#### 3. Create Business Client

**Endpoint:** `POST /business-client`
**Description:** Creates a new business client.
**Headers:** `Authorization: Bearer <token>`
**Request:**

```http
POST /business-client HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "Client Name",
  "email": "<EMAIL>",
  "phone": "+**********",
  "notes": "Client notes"
}
```

#### 4. Update Business Client

**Endpoint:** `PUT /business-client/:Id`
**Description:** Updates client information.
**Headers:** `Authorization: Bearer <token>`

#### 5. Delete Business Client

**Endpoint:** `DELETE /business-client/:Id`
**Description:** Deletes a business client.
**Headers:** `Authorization: Bearer <token>`

### CSV Import

The CSV Import feature allows business owners to bulk import contacts, services, and bookings from CSV files with automatic column mapping, data validation, and duplicate detection.

**Key Features:**

- **Multiple Import Types**: Support for contacts, services, and bookings
- **Automatic Column Mapping**: Smart suggestions for mapping CSV columns to database fields
- **Data Validation**: Comprehensive validation with detailed error reporting
- **Duplicate Detection**: Multiple strategies for handling duplicate records
- **Progress Tracking**: Real-time import status and progress monitoring
- **Template Downloads**: Pre-formatted CSV templates for each import type

#### 1. Preview CSV File

**Endpoint:** `POST /csv-import/preview`
**Description:** Preview uploaded CSV file and get column mapping suggestions.
**Headers:** `Authorization: Bearer <token>`
**Request:** Form data with `importType` and `file`
**Response:**

```json
{
  "success": true,
  "preview": {
    "headers": ["name", "email", "phone"],
    "sampleRows": [{ "name": "John Doe", "email": "<EMAIL>" }],
    "totalRows": 100,
    "suggestedMapping": {
      "name": "clientName",
      "email": "clientEmail"
    }
  }
}
```

#### 2. Import CSV Data

**Endpoint:** `POST /csv-import/import`
**Description:** Import CSV data with column mapping and options.
**Headers:** `Authorization: Bearer <token>`
**Request:** Form data with `importType`, `mapping`, `options`, and `file`
**Response:**

```json
{
  "success": true,
  "message": "CSV import completed",
  "results": {
    "totalRows": 100,
    "successfulRows": 95,
    "failedRows": 5,
    "importId": "64f8a1b2c3d4e5f6a7b8c9d0"
  },
  "errors": [
    {
      "rowNumber": 3,
      "error": "Invalid email format",
      "data": { "name": "Jane Doe", "email": "invalid-email" }
    }
  ]
}
```

#### 3. Get Import History

**Endpoint:** `GET /csv-import/history`
**Description:** Retrieve import history for the authenticated user.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `importType` (optional): Filter by import type
- `limit` (optional): Number of records to return (default: 20, max: 100)
- `offset` (optional): Number of records to skip (default: 0)

#### 4. Get Import Details

**Endpoint:** `GET /csv-import/:id`
**Description:** Get detailed information about a specific import.
**Headers:** `Authorization: Bearer <token>`

#### 5. Cancel Import

**Endpoint:** `DELETE /csv-import/:id`
**Description:** Cancel a pending or processing import.
**Headers:** `Authorization: Bearer <token>`

#### 6. Download Template

**Endpoint:** `GET /csv-import/templates/:type`
**Description:** Download a CSV template for the specified import type.
**Headers:** `Authorization: Bearer <token>`
**Parameters:**

- `type`: 'contacts' | 'services' | 'bookings'

**Import Types:**

**Contacts Template:**

```csv
clientName,clientEmail,phoneNumber,lastAppointmentDate
"John Doe","<EMAIL>","+**********","2024-01-15"
```

**Services Template:**

```csv
name,description,price,duration,color,isActive
"Consultation","Initial consultation",100,60,"#3B82F6",true
```

**Bookings Template:**

```csv
serviceId,date,timeSlot,customerInfo.firstName,customerInfo.lastName,customerInfo.email,notes
"service_id","2024-02-01","09:00-10:00","John","Doe","<EMAIL>","Initial consultation"
```

**Duplicate Handling Strategies:**

- `skip`: Skip duplicate records (default)
- `update`: Update existing records with new data
- `create_new`: Create new records with modified identifiers

### Analytics

#### 1. Track Page View

**Endpoint:** `POST /page-view/track`
**Description:** Tracks page view analytics.
**Headers:** `Authorization: Bearer <token>`

#### 2. Track Page Session

**Endpoint:** `POST /page-session/track`
**Description:** Tracks page session analytics.
**Headers:** `Authorization: Bearer <token>`

#### 3. Track Conversion

**Endpoint:** `POST /conversion/track`
**Description:** Tracks conversion analytics.
**Headers:** `Authorization: Bearer <token>`

#### 4. Track Element Interaction

**Endpoint:** `POST /interaction/track`
**Description:** Tracks user element interactions.
**Headers:** `Authorization: Bearer <token>`

#### 5. Get Today's Appointment Revenue

**Endpoint:** `GET /appointment-renvenue/today`
**Description:** Retrieves today's appointment revenue.
**Headers:** `Authorization: Bearer <token>`

#### 6. Get Current Month Appointment Revenue

**Endpoint:** `GET /appointment-renvenue/current-month`
**Description:** Retrieves current month's appointment revenue.
**Headers:** `Authorization: Bearer <token>`

#### 7. Get Appointment Return Rate

**Endpoint:** `GET /appointment/return-rate/:serviceId`
**Description:** Fetches return rate for a specific service.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `timeInterval`: 'today' | 'weekly' | 'monthly' | 'yearly'

#### 8. Get Most Used Services

**Endpoint:** `GET /appointment/most-used-service`
**Description:** Retrieves most used services analytics.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `timeInterval`: 'today' | 'weekly' | 'monthly' | 'yearly'

#### 9. Get Client Count and Percentage

**Endpoint:** `GET /appointment/client-percentage/:serviceId`
**Description:** Fetches client count and percentage for a service.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `timeInterval`: 'today' | 'weekly' | 'monthly' | 'yearly'

#### 10. Get Average Revenue for Service

**Endpoint:** `GET /appointment/average-revenue-service/:serviceId`
**Description:** Fetches average revenue for a specific service.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `timeInterval`: 'today' | 'weekly' | 'monthly' | 'yearly'

#### 11. Get Highest Average Revenue Service

**Endpoint:** `GET /appointment/highest-average-revenue-service/`
**Description:** Fetches service with highest average revenue.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `timeInterval`: 'today' | 'weekly' | 'monthly' | 'yearly'

#### 12. Get Highest Total Revenue Service

**Endpoint:** `GET /appointment/highest-total-revenue-service/`
**Description:** Fetches service with highest total revenue.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `timeInterval`: 'today' | 'weekly' | 'monthly' | 'yearly'

#### 13. Get Monthly Service Return Rate

**Endpoint:** `GET /monthly-return-rate/service/:serviceId`
**Description:** Fetches monthly service return rate.
**Headers:** `Authorization: Bearer <token>`

#### 14. Get Quarterly Service Return Rate

**Endpoint:** `GET /quarterly-return-rate/service/:serviceId`
**Description:** Fetches quarterly service return rate.
**Headers:** `Authorization: Bearer <token>`

#### 15. Get Clients for Re-engagement

**Endpoint:** `GET /clients-reengagement/service/:serviceId`
**Description:** Retrieves clients for re-engagement campaigns.
**Headers:** `Authorization: Bearer <token>`

#### 16. Get Inactive Clients

**Endpoint:** `GET /inactive-client/service/`
**Description:** Retrieves inactive clients list.
**Headers:** `Authorization: Bearer <token>`

#### 17. Get Re-engagement Strategies

**Endpoint:** `GET /analytics/client/:clientId/re-engagement`
**Description:** Retrieves re-engagement strategies for a client.
**Headers:** `Authorization: Bearer <token>`

#### 18. Get No-Show & Late-Cancel Rates (Advanced Analytics)

**Endpoint:** `GET /no-show-late-cancel-rates`
**Description:** Fetches no-show and late cancellation rates by service, staff, and timeframe.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `timeInterval`: 'today' | 'weekly' | 'monthly' | 'yearly' | 'lastMonth' (optional, default: 'lastMonth')
- `serviceId`: Filter by specific service ID (optional)
- `staffId`: Filter by specific staff ID (optional)

**Response:**

```json
{
  "message": "No-show and late-cancel rates retrieved successfully",
  "timeFrameData": [
    {
      "timeFrame": "Week 1",
      "totalBookings": 50,
      "noShows": 5,
      "lateCancels": 3,
      "completed": 42,
      "noShowRate": "10.00%",
      "lateCancelRate": "6.00%",
      "completionRate": "84.00%",
      "revenueImpact": 340
    }
  ],
  "serviceBreakdown": [
    {
      "serviceId": "service_id",
      "serviceName": "Haircut",
      "totalBookings": 20,
      "noShows": 2,
      "lateCancels": 1,
      "noShowRate": "10.00%",
      "lateCancelRate": "5.00%"
    }
  ],
  "staffBreakdown": [
    {
      "staffId": "staff_id",
      "staffName": "John Doe",
      "totalBookings": 30,
      "noShows": 3,
      "lateCancels": 2,
      "noShowRate": "10.00%",
      "lateCancelRate": "6.67%"
    }
  ]
}
```

#### 19. Get Client Cohort Analysis (Advanced Analytics)

**Endpoint:** `GET /client-cohort-analysis`
**Description:** Analyzes client cohorts, lifetime value (LTV), and rebooking patterns.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `cohortType`: 'monthly' | 'quarterly' (optional, default: 'monthly')
- `startDate`: Start date for analysis (optional)
- `endDate`: End date for analysis (optional)

**Response:**

```json
{
  "message": "Client cohort analysis retrieved successfully",
  "cohortType": "monthly",
  "overallMetrics": {
    "totalClients": 500,
    "totalRevenue": "125000.00",
    "averageLTV": "250.00"
  },
  "cohorts": [
    {
      "cohort": "2024-01",
      "totalClients": 50,
      "activeClients": 40,
      "churnedClients": 10,
      "retentionRate": "80.00%",
      "totalRevenue": "12500.00",
      "averageLTV": "250.00",
      "totalBookings": 150,
      "averageBookingsPerClient": "3.00",
      "rebookingClients": 35,
      "rebookingRate": "70.00%",
      "averageRebookingWindow": "21 days"
    }
  ]
}
```

#### 20. Get Staff Utilization & Revenue by Source (Advanced Analytics)

**Endpoint:** `GET /staff-utilization-revenue-source`
**Description:** Tracks staff utilization rates and analyzes revenue by booking source/channel.
**Headers:** `Authorization: Bearer <token>`
**Query Parameters:**

- `timeInterval`: 'today' | 'weekly' | 'monthly' | 'yearly' | 'lastMonth' (optional, default: 'lastMonth')
- `staffId`: Filter by specific staff ID (optional)

**Response:**

```json
{
  "message": "Staff utilization and revenue by source retrieved successfully",
  "staffUtilization": [
    {
      "staffId": "staff_id",
      "staffName": "John Doe",
      "totalBookings": 45,
      "totalAvailableHours": "160.00",
      "totalBookedHours": "120.00",
      "utilizationRate": "75.00%",
      "totalRevenue": "6000.00",
      "revenuePerHour": "50.00"
    }
  ],
  "revenueBySource": [
    {
      "source": "Business (Internal)",
      "bookings": 30,
      "revenue": "3000.00",
      "percentage": "50.00%"
    },
    {
      "source": "Client (External)",
      "bookings": 25,
      "revenue": "2500.00",
      "percentage": "41.67%"
    },
    {
      "source": "Direct Booking",
      "bookings": 5,
      "revenue": "500.00",
      "percentage": "8.33%"
    }
  ],
  "totalRevenue": "6000.00"
}
```

#### 21. Export Cohort Analysis Data (CSV)

**Endpoint:** `GET /cohort-analysis/export-csv`
**Description:** Exports cohort analysis data as a CSV file for further analysis.
**Headers:** `Authorization: Bearer <token>`

**Response:** Returns a CSV file with cohort data

```csv
Cohort,Total Clients,Total Revenue,Average LTV,Active Clients,Retention Rate
2024-01,50,12500.00,250.00,40,80.00%
2024-02,55,13750.00,250.00,45,81.82%
```

---

-

```
{
  "message": "Client count and percentage for the service retrieved successfully.",
  "serviceName": "Haircut",
  "serviceId": "service1_id",
  "data": [
    {
      "timeFrame": "Today",
      "serviceName": "Haircut",
      "clientCount": 5,
      "percentage": "10.00%"
    }
  ]
}

```

#### 7. Get Average Revenue For A Service

**Endpoint:** `GET /appointment/average-revenue-service:serviceId?timeInterval=''`
**Description:** Fetches the Average Revenue and Overall Average of a specific Service for a Business
**Request:**

```http
GET /appointment/average-revenue-service:serviceId?timeInterval=''
Params: timeInterval: 'today'|'Monthly' | 'weekly' | 'yearly'
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**
-

```
{
  "message": "Average revenue data retrieved successfully.",
  "serviceId": "service1_id",
  "serviceName": "Haircut",
  "overallAverageRevenue": 45.75,
  "data": [
    {
      "timeFrame": "Today",
      "serviceName": "Haircut",
      "totalRevenue": 250,
      "appointmentCount": 5,
      "averageRevenue": 50
    }
  ]
  ....
}


```

#### 8. Get Service with Highest Avergae Revenue

**Endpoint:** `GET /appointment/highest-average-revenue-service:serviceId?timeInterval=''`
**Description:** Fetches the Service with the Highest Average Revenue for a Business
**Request:**

```http
GET /appointment/highest-average-revenue-service:serviceId?timeInterval=''
Params: timeInterval: 'today'|'Monthly' | 'weekly' | 'yearly'
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**
-

```
{
  "message": "Service with highest average revenue retrieved successfully.",
  "service": {
    "id": "service3_id",
    "name": "Premium Facial",
    "totalRevenue": 1500,
    "appointmentCount": 15,
    "averageRevenue": 100
  },
  "timeFrameData": [
    {
      "timeFrame": "Monday",
      "totalRevenue": 200,
      "appointmentCount": 2,
      "averageRevenue": 100
    },
    {
      "timeFrame": "Tuesday",
      "totalRevenue": 300,
      "appointmentCount": 3,
      "averageRevenue": 100
    },
    // Other days of the week...
  ]
}


```

#### 9. Get Service with Highest Total Revenue

**Endpoint:** `GET /appointment/highest-total-revenue-service:serviceId?timeInterval=''`
**Description:** Fetches the Service with the Highest Revenue for a Business
**Request:**

```http
GET /appointment/highest-total-revenue-service:serviceId?timeInterval=''
Params: timeInterval: 'today'|'Monthly' | 'weekly' | 'yearly'
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**
-

```
{
  "message": "Service with highest total revenue retrieved successfully.",
  "service": {
    "id": "service1_id",
    "name": "Haircut",
    "totalRevenue": 2500,
    "appointmentCount": 50,
    "averageRevenue": 50
  },
  "timeFrameData": [
    {
      "timeFrame": "Monday",
      "totalRevenue": 350,
      "appointmentCount": 7,
      "averageRevenue": 50
    },
    {
      "timeFrame": "Tuesday",
      "totalRevenue": 400,
      "appointmentCount": 8,
      "averageRevenue": 50
    },
    // Other days of the week...
  ]
}



```

## Admin Endpoints

**Note:** All admin endpoints require admin role authorization. Blog management endpoints also support blogger role authorization.

### Admin User Management

#### 1. Create Admin

**Endpoint:** `POST /admin/create`
**Description:** Creates a new admin user.
**Request:**

```http
POST /admin/create HTTP/1.1
Host: api.puzzlepiecesolutions.com
Content-Type: application/json

{
  "first_name": "Admin",
  "last_name": "User",
  "email": "<EMAIL>",
  "password": "securePassword123!",
  "username": "adminuser"
}
```

### User Management

**Request:**

```http
POST /admin/create HTTP/1.1
Host: api.puzzlepiecesolutions.com
Content-Type: application/json

{
  "first_name": "Admin",
  "last_name": "User",
  "email": "<EMAIL>",
  "password": "securePassword123!",
  "username": "adminuser"
}
```

### User Management

#### 1. Create Blogger

**Endpoint:** `POST /blogger/create`
**Description:** Creates a new blogger user with permissions for blog management.
**Request:**

```http
POST /blogger/create HTTP/1.1
Host: api.puzzlepiecesolutions.com
Content-Type: application/json

{
  "first_name": "Blogger",
  "last_name": "User",
  "email": "<EMAIL>",
  "password": "securePassword123!",
  "username": "bloggeruser"
}
```

**Response:**

- **201 Created**

```json
{
  "message": "Blogger User registered successfully",
  "userId": "user_id"
}
```

#### 2. Get All Users

**Endpoint:** `GET /admin/user`
**Description:** Fetches a list of all users.
**Headers:** `Authorization: Bearer <admin_token>`
**Response:**

- **200 OK**

```json
{
  "data": [
    {
      "_id": "user_id",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "username": "johndoe123"
    }
  ]
}
```

#### 2. Create User

**Endpoint:** `POST /admin/user`  
**Description:** Creates a new user.
**Headers:** `Authorization: Bearer <admin_token>`
**Request:**

```http
POST /admin/user HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "first_name": "John",
  "last_name": "Doe",
  "username": "johndoe123",
  "email": "<EMAIL>",
  "password": "securePassword123!"
}
```

#### 3. Edit User

**Endpoint:** `POST /admin/user/edit`
**Description:** Edits existing user information.
**Headers:** `Authorization: Bearer <admin_token>`

#### 4. Delete User

**Endpoint:** `DELETE /admin/user/:userId`
**Description:** Deletes a user.
**Headers:** `Authorization: Bearer <admin_token>`
**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "User deleted successfully"
}
```

#### 5. Toggle User Status

**Endpoint:** `PATCH /admin/toggle-status/:userId`
**Description:** Activates/deactivates user account.
**Headers:** `Authorization: Bearer <admin_token>`
**Request:**

```http
PATCH /admin/toggle-status/:userId HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "isActive": true
}
```

### User Logs

#### 1. Get All Users with Logs

**Endpoint:** `GET /admin/logs/users`
**Description:** Fetches a list of all users with logs.
**Headers:** `Authorization: Bearer <admin_token>`

#### 2. Get User Logs

**Endpoint:** `GET /admin/logs/:userId`
**Description:** Fetches logs for a specific user.
**Headers:** `Authorization: Bearer <admin_token>`

### FAQ Management

#### 1. Get All FAQs

**Endpoint:** `GET /admin/faq`
**Description:** Fetches all FAQs (public endpoint).

#### 2. Get FAQ by ID

**Endpoint:** `GET /admin/faq/:Id`
**Description:** Fetches specific FAQ by ID.
**Headers:** `Authorization: Bearer <admin_token>`

#### 3. Create FAQ

**Endpoint:** `POST /admin/faq`
**Description:** Creates a new FAQ.
**Headers:** `Authorization: Bearer <admin_token>`
**Request:**

```http
POST /admin/faq HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "question": "How do I reset my password?",
  "answer": "Click on 'Forgot Password' on the login page.",
  "category": "Authentication"
}
```

#### 4. Update FAQ

**Endpoint:** `PUT /admin/faq/:Id`
**Description:** Updates existing FAQ.
**Headers:** `Authorization: Bearer <admin_token>`

#### 5. Delete FAQ

**Endpoint:** `DELETE /admin/faq/:Id`
**Description:** Deletes FAQ.
**Headers:** `Authorization: Bearer <admin_token>`

### Blog Management

**Note:** Blog management endpoints support both admin and blogger role authorization.

#### 1. Create Blog

**Endpoint:** `POST /admin/blogs`
**Description:** Creates a new blog post.
**Headers:** `Authorization: Bearer <admin_token>` or `Authorization: Bearer <blogger_token>`
**Request:** `multipart/form-data` with blog details and `image` field

#### 2. Upload Blog Image

**Endpoint:** `POST /admin/blogs/image`
**Description:** Uploads blog image.
**Headers:** `Authorization: Bearer <admin_token>` or `Authorization: Bearer <blogger_token>`
**Request:** `multipart/form-data` with `image` field

#### 3. Update Blog

**Endpoint:** `PUT /admin/blogs/:Id`
**Description:** Updates existing blog post.
**Headers:** `Authorization: Bearer <admin_token>` or `Authorization: Bearer <blogger_token>`
**Request:** `multipart/form-data` with updated blog details

#### 4. Get Blog by ID

**Endpoint:** `GET /admin/blogs/:Id`
**Description:** Retrieves blog post by ID (public endpoint).

#### 5. Get All Blogs

**Endpoint:** `GET /admin/blogs`
**Description:** Fetches all blogs (public endpoint).

#### 6. Delete Blog

**Endpoint:** `DELETE /admin/blogs/:Id`
**Description:** Deletes blog post.
**Headers:** `Authorization: Bearer <admin_token>` or `Authorization: Bearer <blogger_token>`

### Tag Management

**Note:** Tag management endpoints support both admin and blogger role authorization.

#### 1. Create Tag

**Endpoint:** `POST /admin/tags`
**Description:** Creates a new tag.
**Headers:** `Authorization: Bearer <admin_token>` or `Authorization: Bearer <blogger_token>`
**Request:**

```http
POST /admin/tags HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "name": "Technology"
}
```

#### 2. Get All Tags

**Endpoint:** `GET /admin/tags`
**Description:** Fetches all tags (public endpoint).

#### 3. Delete Tag

**Endpoint:** `DELETE /admin/tag/:Id`
**Description:** Deletes tag.
**Headers:** `Authorization: Bearer <admin_token>` or `Authorization: Bearer <blogger_token>`

### Support Tickets

#### 1. Create Support Ticket

**Endpoint:** `POST /support-tickets`
**Description:** Creates a new support ticket.
**Headers:** `Authorization: Bearer <admin_token>`
**Request:**

```http
POST /support-tickets HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "userId": "user_id",
  "title": "Issue with Payment",
  "description": "I am unable to process my payment.",
  "priority": "high"
}
```

#### 2. Get All Tickets

**Endpoint:** `GET /admin/tickets`
**Description:** Fetches all support tickets.
**Headers:** `Authorization: Bearer <admin_token>`

#### 3. Get Ticket by ID

**Endpoint:** `GET /admin/tickets/:ticketId`
**Description:** Retrieves specific ticket details.
**Headers:** `Authorization: Bearer <admin_token>`

#### 4. Update Ticket Status

**Endpoint:** `PATCH /admin/tickets/update`
**Description:** Updates ticket status.
**Headers:** `Authorization: Bearer <admin_token>`

#### 5. Delete Ticket

**Endpoint:** `DELETE /admin/tickets/:ticketId`
**Description:** Deletes support ticket.
**Headers:** `Authorization: Bearer <admin_token>`

### Promo Codes

#### 1. Create Promo Code

**Endpoint:** `POST /admin/promo-code`
**Description:** Creates a new promo code.
**Headers:** `Authorization: Bearer <admin_token>`
**Request:**

```http
POST /admin/promo-code HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "code": "SAVE10",
  "type": "subscription",
  "discount": {
    "type": "percentage",
    "amount": 10
  },
  "description": "Get 10% off on your subscription",
  "expiringDate": "2024-12-31T23:59:59Z",
  "isActive": true,
  "maxUsage": 100
}
```

#### 2. Get All Promo Codes

**Endpoint:** `GET /admin/promo-code`
**Description:** Fetches all promo codes.
**Headers:** `Authorization: Bearer <admin_token>`

#### 3. Get Promo Code by ID

**Endpoint:** `GET /admin/promo-code/:Id`
**Description:** Retrieves specific promo code.
**Headers:** `Authorization: Bearer <admin_token>`

#### 4. Update Promo Code

**Endpoint:** `PUT /admin/promo-code/:Id`
**Description:** Updates promo code.
**Headers:** `Authorization: Bearer <admin_token>`

#### 5. Delete Promo Code

**Endpoint:** `DELETE /admin/promo-code/:Id`
**Description:** Deletes promo code.
**Headers:** `Authorization: Bearer <admin_token>`

### Subscription Products

#### 1. Create Product

**Endpoint:** `POST /admin/products`
**Description:** Creates a new subscription product.
**Headers:** `Authorization: Bearer <admin_token>`

#### 2. Get All Products

**Endpoint:** `GET /admin/products`
**Description:** Fetches all subscription products.
**Headers:** `Authorization: Bearer <admin_token>`

#### 3. Get Product by ID

**Endpoint:** `GET /admin/products/:Id`
**Description:** Retrieves specific product.
**Headers:** `Authorization: Bearer <admin_token>`

#### 4. Update Product

**Endpoint:** `PUT /admin/products/:Id`
**Description:** Updates product.
**Headers:** `Authorization: Bearer <admin_token>`

#### 5. Delete Product

**Endpoint:** `DELETE /admin/products/:Id`
**Description:** Deletes product.
**Headers:** `Authorization: Bearer <admin_token>`

### Subscription Prices

#### 1. Create Price

**Endpoint:** `POST /admin/price`
**Description:** Creates a new subscription price.
**Headers:** `Authorization: Bearer <admin_token>`

#### 2. Get All Prices

**Endpoint:** `GET /admin/price`
**Description:** Fetches all subscription prices.
**Headers:** `Authorization: Bearer <admin_token>`

#### 3. Get Price by ID

**Endpoint:** `GET /admin/price/:Id`
**Description:** Retrieves specific price.
**Headers:** `Authorization: Bearer <admin_token>`

#### 4. Update Price

**Endpoint:** `PUT /admin/price/:Id`
**Description:** Updates price.
**Headers:** `Authorization: Bearer <admin_token>`

#### 5. Delete Price

**Endpoint:** `DELETE /admin/price/:Id`
**Description:** Deletes price.
**Headers:** `Authorization: Bearer <admin_token>`

### Credit Packages

#### 1. Create Credit Package

**Endpoint:** `POST /admin/credit`
**Description:** Creates a new credit package.
**Headers:** `Authorization: Bearer <admin_token>`

#### 2. Get All Credit Packages

**Endpoint:** `GET /admin/credit`
**Description:** Fetches all credit packages.

#### 3. Get Credit Package by ID

**Endpoint:** `GET /admin/credit/:Id`
**Description:** Retrieves specific credit package.
**Headers:** `Authorization: Bearer <admin_token>`

#### 4. Update Credit Package

**Endpoint:** `PUT /admin/credit/:Id`
**Description:** Updates credit package.
**Headers:** `Authorization: Bearer <admin_token>`

#### 5. Delete Credit Package

**Endpoint:** `DELETE /admin/credit/:Id`
**Description:** Deletes credit package.
**Headers:** `Authorization: Bearer <admin_token>`

### Maintenance Mode

#### 1. Get Maintenance Mode

**Endpoint:** `GET /maintenance`
**Description:** Checks if system is in maintenance mode (public endpoint).

#### 2. Set Maintenance Mode

**Endpoint:** `PUT /maintenance`
**Description:** Sets system maintenance mode.
**Headers:** `Authorization: Bearer <admin_token>`
**Request:**

```http
PUT /maintenance HTTP/1.1
Host: api.puzzlepiecesolutions.com
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "isMaintenanceMode": true,
  "message": "System is under maintenance"
}
```

---

**Endpoint:** `GET /admin/logs/:userId`  
**Description:** Fetches a particular user logs.

**Request:**

```http
GET /admin/logs/:userId HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**

```json
{
  "data": []
}
```

### FAQ Management

#### 1. Get All FAQs

**Endpoint:** `GET /admin/faq`  
**Description:** Fetches a list of all FAQs.

**Request:**

```http
GET /admin/faq HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**

```json
{
  "data": [
    {
      "_id": "1",
      "question": "What is the API?",
      "answer": "The API provides functionality to manage data and access features.",
      "category": "General"
    },
    {
      "_id": "2",
      "question": "How do I authenticate?",
      "answer": "Use the Bearer token provided after logging in.",
      "category": "Authentication"
    }
  ]
}
```

---

#### 2. Get FAQ by ID

**Endpoint:** `GET /admin/faq/:Id`  
**Description:** Fetches a specific FAQ by its ID.

**Request:**

```http
GET /admin/faq/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**

```json
{
  "_id": "1",
  "question": "What is the API?",
  "answer": "The API provides functionality to manage data and access features.",
  "category": "General"
}
```

- **404 Not Found**

```json
{
  "error": "FAQ not found"
}
```

---

#### 3. Create a New FAQ

**Endpoint:** `POST /admin/faq`  
**Description:** Creates a new FAQ.

**Request:**

```http
POST /admin/faq HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: application/json

{
  "question": "How do I reset my password?",
  "answer": "Click on 'Forgot Password' on the login page.",
  "category": "Authentication"
}
```

**Response:**

- **201 Created**

```json
{
  "_id": "3",
  "question": "How do I reset my password?",
  "answer": "Click on 'Forgot Password' on the login page.",
  "caregory": "Authentication"
}
```

- **400 Bad Request**

```json
{
  "error": "Invalid input"
}
```

---

#### 4. Update an FAQ

**Endpoint:** `PUT /admin/faq/:Id`  
**Description:** Updates an existing FAQ by ID.

**Request:**

```http
PUT /admin/faq/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: application/json

{
  "question": "What is the updated API?",
  "answer": "The API now supports additional features.",
  "category": "General"
}
```

**Response:**

- **200 OK**

```json
{
  "_id": "1",
  "question": "What is the updated API?",
  "answer": "The API now supports additional features.",
  "category": "General"
}
```

- **404 Not Found**

```json
{
  "error": "FAQ not found"
}
```

---

#### 5. Delete an FAQ

**Endpoint:** `DELETE /admin/faq/:Id`  
**Description:** Deletes an FAQ by ID.

**Request:**

```http
DELETE /admin/faq/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "FAQ deleted successfully"
}
```

- **404 Not Found**

```json
{
  "error": "FAQ not found"
}
```

### Blog Management

#### 1. Create a New Blog

**Endpoint:** `POST /admin/blogs`  
**Description:** Creates a new blog post.

**Request:**

```http
POST /admin/blogs HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: multipart/form-data

{
  "title": "The Future of AI",
  "shortDescription": "An overview of AI advancements.",
  "longDescription": "Artificial Intelligence (AI) has been transforming industries...",
  "authorName": "Jane Doe",
  "tags": ["AI", "Technology"],
  "timePosted": "2024-10-21T12:30:00Z",
  "image": <image_file>
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "data": {
    "_id": "1",
    "title": "The Future of AI",
    "shortDescription": "An overview of AI advancements.",
    "longDescription": "Artificial Intelligence (AI) has been transforming industries...",
    "authorName": "Jane Doe",
    "tags": ["AI", "Technology"],
    "timePosted": "2024-10-21T12:30:00Z",
    "image": "https://example.com/uploads/blog_image.jpg"
  }
}
```

---

#### 2. Update a Blog

**Endpoint:** `PUT /admin/blogs/:Id`  
**Description:** Updates an existing blog post by ID.

**Request:**

```http
PUT /admin/blogs/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: multipart/form-data

{
  "title": "The Future of AI - Updated",
  "shortDescription": "A comprehensive overview of AI advancements.",
  "longDescription": "Artificial Intelligence (AI) continues to revolutionize...",
  "authorName": "Jane Doe",
  "tags": ["AI", "Technology", "Innovation"],
  "timePosted": "2024-10-21T15:00:00Z",
  "image": <updated_image_file>
}
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Blog updated successfully"
}
```

---

#### 3. Get a Blog by ID

**Endpoint:** `GET /admin/blogs/:Id`  
**Description:** Retrieves details of a blog post by its ID.

**Request:**

```http
GET /admin/blogs/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**

```json
{
  "_id": "1",
  "title": "The Future of AI",
  "shortDescription": "An overview of AI advancements.",
  "longDescription": "Artificial Intelligence (AI) has been transforming industries...",
  "authorName": "Jane Doe",
  "tags": ["AI", "Technology"],
  "timePosted": "2024-10-21T12:30:00Z",
  "image": "https://example.com/uploads/blog_image.jpg"
}
```

---

#### 4. Get All Blogs

**Endpoint:** `GET /admin/blogs`  
**Description:** Fetches a list of all blogs.

**Request:**

```http
GET /admin/blogs HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**

```json
{
  "data": [
    {
      "_id": "1",
      "title": "The Future of AI",
      "shortDescription": "An overview of AI advancements.",
      "authorName": "Jane Doe",
      "tags": ["AI", "Technology"],
      "timePosted": "2024-10-21T12:30:00Z",
      "image": "https://example.com/uploads/blog_image.jpg"
    },
    {
      "_id": "2",
      "title": "Understanding Blockchain",
      "shortDescription": "Basics of blockchain technology.",
      "authorName": "John Smith",
      "tags": ["Blockchain", "Technology"],
      "timePosted": "2024-10-22T10:00:00Z",
      "image": "https://example.com/uploads/blog_image2.jpg"
    }
  ]
}
```

---

#### 5. Delete a Blog

**Endpoint:** `DELETE /admin/blogs/:Id`  
**Description:** Deletes a blog post by its ID.

**Request:**

```http
DELETE /admin/blogs/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Blog deleted successfully"
}
```

---

### Tag Management

#### 1. Create a New Tag

**Endpoint:** `POST /admin/tags`  
**Description:** Creates a new tag for blogs.

**Request:**

```http
POST /admin/tags HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: application/json

{
  "name": "Technology"
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "data": {
    "_id": "1",
    "name": "Technology"
  }
}
```

---

#### 2. Get All Tags

**Endpoint:** `GET /admin/tags`  
**Description:** Fetches all available tags.

**Request:**

```http
GET /admin/tags HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**

```json
{
  "data": [
    {
      "_id": "1",
      "name": "Technology"
    },
    {
      "_id": "2",
      "name": "AI"
    }
  ]
}
```

---

#### 3. Delete a Tag

**Endpoint:** `DELETE /admin/tag/:Id`  
**Description:** Deletes a tag by its ID.

**Request:**

```http
DELETE /admin/tag/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Tag deleted successfully"
}
```

---

### Support Tickets

#### 1. Create a New Support Ticket

**Endpoint:** `POST /support-tickets`  
**Description:** Allows a user to raise a new support ticket.

**Request:**

```http
POST /support-tickets HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: application/json

{
  "userId": "63e8c99f6c441c1ecf0aeb2d",
  "title": "Issue with Payment",
  "description": "I am unable to process my payment for the order.",
  "priority": "high"
}
```

**Response:**

- **201 Created**

```json
{
  "message": "Support ticket created successfully",
  "ticket": {
    "_id": "64b6c8f2c5e5c1a1e9bcd019",
    "userId": "63e8c99f6c441c1ecf0aeb2d",
    "title": "Issue with Payment",
    "description": "I am unable to process my payment for the order.",
    "status": "open",
    "priority": "high",
    "ip": "***********",
    "createdAt": "2024-11-03T12:00:00.000Z",
    "updatedAt": "2024-11-03T12:00:00.000Z"
  }
}
```

---

#### 2. View All Support Tickets

**Endpoint:** `GET /admin/tickets`  
**Description:** Fetches a list of all support tickets for an admin to manage.

**Request:**

```http
GET /admin/tickets/list HTTP/1.1
Host: api.example.com
Authorization: Bearer <admin_token_here>
```

**Response:**

- **200 OK**

```json
[
  {
    "_id": "64b6c8f2c5e5c1a1e9bcd019",
    "userId": {
      "_id": "63e8c99f6c441c1ecf0aeb2d",
      "username": "johndoe",
      "email": "<EMAIL>"
    },
    "title": "Issue with Payment",
    "description": "I am unable to process my payment for the order.",
    "status": "open",
    "priority": "high",
    "ip": "***********",
    "createdAt": "2024-11-03T12:00:00.000Z",
    "updatedAt": "2024-11-03T12:00:00.000Z"
  },
  {
    "_id": "64b6c8f2c5e5c1a1e9bcd020",
    "userId": {
      "_id": "63e8c99f6c441c1ecf0aeb2e",
      "username": "janedoe",
      "email": "<EMAIL>"
    },
    "title": "Unable to Reset Password",
    "description": "I did not receive the password reset link.",
    "status": "resolved",
    "priority": "medium",
    "ip": "***********",
    "createdAt": "2024-11-01T10:00:00.000Z",
    "updatedAt": "2024-11-02T08:00:00.000Z"
  }
]
```

---

#### 3. Update Ticket Status

**Endpoint:** `PATCH /admin/tickets/update`  
**Description:** Allows an admin to update the status of a support ticket.

**Request:**

```http
PATCH /tickets/update HTTP/1.1
Host: api.example.com
Authorization: Bearer <admin_token_here>
Content-Type: application/json

{
  "ticketId": "64b6c8f2c5e5c1a1e9bcd019",
  "status": "in-progress"
}
```

**Response:**

- **200 OK**

```json
{
  "message": "Ticket status updated successfully",
  "ticket": {
    "_id": "64b6c8f2c5e5c1a1e9bcd019",
    "userId": "63e8c99f6c441c1ecf0aeb2d",
    "title": "Issue with Payment",
    "description": "I am unable to process my payment for the order.",
    "status": "in-progress",
    "priority": "high",
    "ip": "***********",
    "createdAt": "2024-11-03T12:00:00.000Z",
    "updatedAt": "2024-11-03T13:00:00.000Z"
  }
}
```

---

#### 4. Delete a Support Ticket

**Endpoint:** `DELETE /admin/tickets/:ticketId`  
**Description:** Allows an admin to delete a support ticket.

**Request:**

```http
DELETE /tickets/64b6c8f2c5e5c1a1e9bcd019 HTTP/1.1
Host: api.example.com
Authorization: Bearer <admin_token_here>
```

**Response:**

- **200 OK**

```json
{
  "message": "Ticket deleted successfully"
}
```

---

### Promo Code Management

#### 1. Create a Promo Code

**Endpoint:** `POST /admin/promo-code/`  
**Description:** Creates a new promo code.

**Request:**

```http
POST /admin/promo-code/ HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: application/json

{
  "code": "SAVE10",
  "type": "subscription",
  "discount": {
    "type": "percentage",
    "amount": 10
  },
  "description": "Get 10% off on your subscription",
  "expiringDate": "2024-12-31T23:59:59Z",
  "isActive": true,
  "maxUsage": 100
}
```

**Response:**

- **201 Created**

```json
{
  "success": true,
  "data": {
    "_id": "1",
    "code": "SAVE10",
    "type": "subscription",
    "discount": {
      "type": "percentage",
      "amount": 10
    },
    "description": "Get 10% off on your subscription",
    "expiringDate": "2024-12-31T23:59:59Z",
    "isActive": true,
    "maxUsage": 100
  }
}
```

---

#### 2. Get All Promo Codes

**Endpoint:** `GET /admin/promo-code/`  
**Description:** Fetches a list of all promo codes.

**Request:**

```http
GET /admin/promo-code/ HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**

```json
{
  "data": [
    {
      "_id": "1",
      "code": "SAVE10",
      "type": "subscription",
      "discount": {
        "type": "percentage",
        "amount": 10
      },
      "description": "Get 10% off on your subscription",
      "expiringDate": "2024-12-31T23:59:59Z",
      "isActive": true,
      "maxUsage": 100
    },
    {
      "_id": "2",
      "code": "BUY50",
      "type": "credit_purchase",
      "discount": {
        "type": "fixed",
        "amount": 50
      },
      "description": "Get $50 off your next credit purchase",
      "expiringDate": "2024-11-30T23:59:59Z",
      "isActive": false,
      "maxUsage": 50
    }
  ]
}
```

---

#### 3. Get Promo Code by ID

**Endpoint:** `GET /admin/promo-code/:Id`  
**Description:** Retrieves details of a promo code by its ID.

**Request:**

```http
GET /admin/promo-code/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**

```json
{
  "_id": "1",
  "code": "SAVE10",
  "type": "subscription",
  "discount": {
    "type": "percentage",
    "amount": 10
  },
  "description": "Get 10% off on your subscription",
  "expiringDate": "2024-12-31T23:59:59Z",
  "isActive": true,
  "maxUsage": 100
}
```

---

#### 4. Update a Promo Code

**Endpoint:** `PUT /admin/promo-code/:Id`  
**Description:** Updates an existing promo code by ID.

**Request:**

```http
PUT /admin/promo-code/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
Content-Type: application/json

{
  "code": "SAVE15",
  "type": "subscription",
  "discount": {
    "type": "percentage",
    "amount": 15
  },
  "description": "Get 15% off on your subscription",
  "expiringDate": "2024-12-31T23:59:59Z",
  "isActive": true,
  "maxUsage": 150
}
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Promo code updated successfully"
}
```

---

#### 5. Delete a Promo Code

**Endpoint:** `DELETE /admin/promo-code/:Id`  
**Description:** Deletes a promo code by its ID.

**Request:**

```http
DELETE /admin/promo-code/:Id HTTP/1.1
Host: api.example.com
Authorization: Bearer <your_token_here>
```

**Response:**

- **200 OK**

```json
{
  "success": true,
  "message": "Promo code deleted successfully"
}
```

## Error Handling

All error responses follow a consistent format:

```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

### Common HTTP Status Codes:

- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **400 Bad Request**: Invalid input or request format
- **401 Unauthorized**: Authentication required or failed
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource conflict (e.g., duplicate email)
- **422 Unprocessable Entity**: Validation errors
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server-side error

### Error Response Examples:

#### Validation Error (422)

```json
{
  "success": false,
  "error": "Validation failed",
  "details": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ]
}
```

#### Authentication Error (401)

```json
{
  "success": false,
  "error": "Invalid token or token expired"
}
```

#### Permission Error (403)

```json
{
  "success": false,
  "error": "Insufficient permissions to access this resource"
}
```

## Webhooks

### Stripe Webhooks

The API supports Stripe webhooks for payment processing:

**Endpoint:** `POST /stripe/webhooks`
**Description:** Handles Stripe webhook events for payment confirmations, subscription updates, etc.

**Supported Events:**

- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`

**Security:** Webhooks are verified using Stripe signatures to ensure authenticity.

## Contact

For technical support, API questions, or general inquiries:

- **Email:** <EMAIL>
- **Website:** [https://puzzlepiecesolutions.com](https://puzzlepiecesolutions.com)
- **Documentation:** [https://docs.puzzlepiecesolutions.com](https://docs.puzzlepiecesolutions.com)
- **Developer Portal:** [https://developers.puzzlepiecesolutions.com](https://developers.puzzlepiecesolutions.com)

### Support Hours:

- **Monday - Friday**: 9:00 AM - 6:00 PM EST
- **Weekend**: Emergency support only

### Response Times:

- **General Inquiries**: Within 24 hours
- **Technical Issues**: Within 4-8 hours
- **Critical Issues**: Within 2 hours

---

## License

This API is licensed under the **MIT License**. See the [LICENSE](LICENSE) file for more details.

### Usage Terms:

- Commercial use permitted
- Modification allowed
- Distribution allowed
- Private use allowed
- No warranty provided

---

## Changelog

### Version 1.0.0 (Current)

- Initial API release
- Complete user management system
- Booking and scheduling functionality
- Analytics and reporting features
- Admin dashboard capabilities
- Payment integration with Stripe
- Email notification system
- Digital profile management
- Business leads and client management

### Upcoming Features:

- Advanced analytics dashboard
- Multi-language support
- Mobile app APIs
- Advanced reporting tools
- Integration marketplace
