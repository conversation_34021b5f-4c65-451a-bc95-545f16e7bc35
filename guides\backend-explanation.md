Based on the code and documentation, here's how the Scheduling Rules feature works:

## Overview
Scheduling Rules allow businesses to define automated policies for deposits, cancellations, no-show fees, and rescheduling permissions for their services. Each service can have one unique set of scheduling rules.

## Core Features

### 1. **Deposit Management**
- **Types**: `none`, `percentage`, `fixed`, `full`
- **Validation**: Percentage (0-100%), fixed amounts (≥0), full payment (100% of service)
- **Calculation**: Automatic deposit calculation including service options

### 2. **Cancellation Policies**
- **Cancel Cutoff**: Hours before appointment when free cancellation is allowed (default: 24h)
- **Late Cancellation Fee**: Percentage of total price charged for late cancellations
- **Permission Check**: `canCancel(appointmentDateTime)` method validates timing

### 3. **No-Show Management**
- **No-Show Fee**: Percentage of total price (0-100%) charged for missed appointments
- **Automatic Calculation**: `calculateNoShowFee(servicePrice, serviceOptionsTotal)`

### 4. **Rescheduling Controls**
- **Allow Rescheduling**: Boolean flag to enable/disable rescheduling
- **Reschedule Cutoff**: Hours before appointment when rescheduling is allowed (default: 2h)
- **Permission Check**: `canReschedule(appointmentDateTime)` validates timing and permissions

## Data Structure

````typescript path=src/models/schedulingRules.ts mode=EXCERPT
export interface ISchedulingRules extends Document {
  serviceId: mongoose.Types.ObjectId; // One rule set per service
  userId: mongoose.Types.ObjectId;
  depositType: 'none' | 'percentage' | 'fixed' | 'full';
  depositAmount?: number;
  cancelCutoff: number; // Hours before appointment
  noShowFee: number; // Percentage of total price
  allowRescheduling: boolean;
  rescheduleCutoff: number; // Hours before appointment
  lateCancellationFee: number; // Percentage of total price
  isActive: boolean;
}
````

## API Endpoints

### **Create Rules**: `POST /services/:serviceId/scheduling-rules`
- Creates new scheduling rules for a service
- Validates business logic and service ownership
- Prevents duplicate rules (409 error if exists)

### **Get Rules**: `GET /services/:serviceId/scheduling-rules`
- Returns rules for a specific service
- Falls back to default rules if none exist
- Includes `isDefault: true/false` flag

### **Get All Rules**: `GET /scheduling-rules`
- Returns all rules for user's services
- Populates service information

### **Update Rules**: `PUT /services/:serviceId/scheduling-rules`
- Updates existing rules or creates new ones
- Partial updates supported

### **Public Policy**: `GET /public/:username/:serviceName/scheduling-policy`
- Public endpoint for customers to view policies
- No authentication required

## Business Logic Validation

````typescript path=src/validators/schedulingRules.ts mode=EXCERPT
// Validates deposit amount based on type
depositAmount: Joi.number().min(0).when('depositType', {
  is: 'percentage',
  then: Joi.number().max(100),
  otherwise: Joi.number().min(0)
})
````

## Instance Methods

The model provides calculation and validation methods:

````typescript path=src/models/schedulingRules.ts mode=EXCERPT
// Calculate deposit based on type and service price
calculateDepositAmount(servicePrice: number, serviceOptionsTotal?: number): number

// Calculate fees as percentage of total price
calculateNoShowFee(servicePrice: number, serviceOptionsTotal?: number): number
calculateLateCancellationFee(servicePrice: number, serviceOptionsTotal?: number): number

// Time-based permission checks
canCancel(appointmentDateTime: Date): boolean
canReschedule(appointmentDateTime: Date): boolean
````

## Default Rules
When no custom rules exist, the system provides defaults:
- No deposit required
- 24-hour cancellation cutoff
- Rescheduling allowed with 2-hour cutoff
- No fees for no-shows or late cancellations

## Integration Points
- **Booking System**: Enforces policies during booking creation/modification
- **Payment Processing**: Calculates deposits and fees automatically
- **Email Notifications**: Includes policy information in confirmations
- **Frontend**: Displays policies to customers during booking flow

The system ensures consistent policy enforcement across all booking operations while providing flexibility for different business models.
