import { BookingPayload, ExternalBookingPayload } from "@/src/services/booking.service";
import { createContext, useContext } from "react";

interface InternalBookingContextType {
  values: BookingPayload,
  setValue<T extends BookingPayload, K extends keyof T>(key: K, newValue: T[K]): void
}
export interface ExternalBookingContextType {
  values: ExternalBookingPayload,
  setValue<T extends ExternalBookingPayload, K extends keyof T>(key: K, newValue: T[K]): void
}

export const InternalBookingContext = createContext<InternalBookingContextType | null>(null);
export const ExternalBookingContext = createContext<ExternalBookingContextType | null>(null);

export const useBookingContext = <T extends 'internal' | 'external'>(type: T) => {
  const context = useContext(
    type === 'internal' ? InternalBookingContext : ExternalBookingContext as any
  );
  if (!context) {
    throw new Error("useBookingContext must be used within a BookingProvider");
  }
  return context as (T extends 'internal' ? InternalBookingContextType : ExternalBookingContextType)
};
