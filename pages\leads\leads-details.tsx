import { But<PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import { useQuery } from "@tanstack/react-query";
import { useParams } from "react-router-dom";
import { GetLeadById } from "~/services/leads.service";
import { MailIcon, PhoneIcon } from "./components/badges";
import LeadDetailsSkeleton from "./components/lead-details-skeleton";

export default function LeadDetails() {
  const params = useParams<{ id: string }>();
  const { data: { data: { Lead: lead } = {} } = {}, isFetching } = useQuery({
    queryKey: ["lead-details"],
    queryFn: () => GetLeadById(params?.id || ""),
    enabled: Boolean(params?.id),
  });
  if (isFetching) return <LeadDetailsSkeleton />;
  else
    return (
      <section className="w-full max-w-4xl mx-auto space-y-8 md:px-8 ">
        <div className="w-full bg-gray-50 rounded-3xl px-4 pt-8 pb-4  space-y-8 ">
          {/* Header Section */}
          <div className="flex flex-col gap-y-5">
            <Typography className="px-2 bg-gray-100 w-fit rounded-md">
              Lead Full name
            </Typography>
            <Typography
              variant="h4"
              className="font-bold overflow-hidden overflow-ellipsis whitespace-nowrap"
            >
              {lead?.name || ""}
            </Typography>
          </div>

          {/* Service and Location */}
          <div className="w-full flex flex-col gap-y-4">
            <div className="flex items-center justify-between">
              <Typography className="">Industry</Typography>
              <Typography variant="p" className="font-extrabold">
                {lead?.industry || ""}
              </Typography>
            </div>
            <div className="flex items-center justify-between">
              <Typography className="">Location</Typography>
              <Typography variant="p" className="font-extrabold">
                {lead?.postcode || ""}, {lead?.town}
              </Typography>
            </div>
            <div className="flex items-center justify-between gap-2">
              <div className="flex items-center gap-x-3">
                <Typography className="">Phone</Typography>
                <PhoneIcon />
              </div>
              <Typography variant="p" className="font-extrabold">
                {lead?.telephone_numbers[0] || ""}
              </Typography>
            </div>
            <div className="flex items-center justify-between gap-2">
              <div className="flex items-center gap-x-3">
                <Typography className="">Email Address</Typography>
                <MailIcon />
              </div>
              <Typography variant="p" className="font-extrabold">
                {lead?.email_addresses[0] || ""}
              </Typography>
            </div>
          </div>
        </div>
        {/* Action Buttons */}
        <div className="flex">
          <Button className="px-4 py-2 w-full">Contact</Button>
          <Button variant="ghost" className="px-4 py-2 w-full text-gray-600">
            Not interested
          </Button>
        </div>
      </section>
    );
}
