import { But<PERSON> } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Menu from "@/components/ui/menu";
import Toggle from "@/components/ui/toggle-2";
import { and, noop } from "@/lib/helpers";
import { cn } from "@/lib/utils";
import { Staff } from "@/src/interfaces/staff";
import { useFormik } from "formik";
import {
  Clock01Icon,
  Delete02Icon,
  Edit02Icon,
  MoreVerticalIcon,
} from "hugeicons-react";
import WorkingHoursModal from "../staff/components/working-hours-modal";
import { useModalsBuilder } from "@/lib/modals-builder";
import { useMemo } from "react";

interface StaffCardProps {
  staff: Staff;
  onEdit: (staffId: string) => void;
  onDelete: (staffId: string) => void;
  onToggleActivation: (staffId: string, isActive: boolean) => void;
}

const DAYS = [
  { key: "monday", label: "Mon" },
  { key: "tuesday", label: "Tu<PERSON>" },
  { key: "wednesday", label: "Wed" },
  { key: "thursday", label: "Thur" },
  { key: "friday", label: "Fri" },
  { key: "saturday", label: "Sat" },
  { key: "sunday", label: "Sun" },
] as const;

const StaffCard: React.FC<StaffCardProps> = ({
  staff,
  onEdit,
  onDelete,
  ...props
}) => {
  const formik = useFormik({
    initialValues: {
      isActive: staff.isActive,
    },
    onSubmit: noop,
    enableReinitialize: true,
  });
  const { modals, modalFunctions } = useModalsBuilder({
    workingHours: {
      open: false,
      data: null as Nullable<{
        staff: Staff;
      }>,
    },
  });
  const staffWorkingHours = useMemo(() => {
    const workingHoursMap = new Map<string, Array<string>>();
    DAYS.forEach(({ key, label }) => {
      const workingHour = staff.workingHours[key];
      if (workingHour && workingHour.isWorking) {
        const timeSlot = `${workingHour.start} - ${workingHour.end}`;
        workingHoursMap.set(timeSlot, [
          ...(workingHoursMap.get(timeSlot) || []),
          label,
        ]);
      }
    });
    return Array.from(workingHoursMap.entries());
  }, [staff.workingHours]);

  return (
    <>
      <div className="bg-stone-200 w-full rounded-3xl flex flex-col ">
        <div className="flex flex-1 items-center justify-between px-4 py-4 font-medium transition-all">
          <div className="flex w-full items-start justify-between">
            <div className="flex items-center gap-4">
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center text-white font-semibold"
                style={{ backgroundColor: staff.color }}
              >
                {staff.name.charAt(0).toUpperCase()}
              </div>{" "}
              <div>
                <Typography
                  variant="h3"
                  className="font-semibold text-lg overflow-ellipsis overflow-hidden whitespace-nowrap"
                >
                  {staff.name}
                </Typography>{" "}
                <Typography className="text-gray-600 text-sm">
                  {staff.role}{" "}
                </Typography>
              </div>
            </div>
            <Menu transformOrigin="top-right">
              <Menu.Trigger className="cursor-pointer p-1">
                <Button className="bg-transparent w-fit text-gray-500 rounded-lg p-0">
                  <MoreVerticalIcon size={20} strokeWidth={3} />{" "}
                </Button>
              </Menu.Trigger>
              <Menu.Content className="h-fit focus:outline-none min-w-fit !min-h-0 right-0 whitespace-nowrap">
                <Menu.Item
                  className="flex items-center gap-x-4 pr-5"
                  onClick={() => onEdit(staff._id)}
                >
                  Edit <Edit02Icon width={16} height={16} />
                </Menu.Item>{" "}
                <Menu.Item
                  className="flex items-center gap-x-4 pr-5"
                  onClick={() =>
                    modalFunctions.openModal("workingHours", {
                      data: { staff },
                    })
                  }
                >
                  Working Hours <Clock01Icon width={16} height={16} />
                </Menu.Item>{" "}
                <Menu.Item
                  className="flex items-center gap-x-4 pr-5 text-red-600"
                  onClick={() => onDelete(staff._id)}
                >
                  Delete <Delete02Icon width={16} height={16} />
                </Menu.Item>{" "}
              </Menu.Content>
            </Menu>{" "}
          </div>
        </div>
        <div className="overflow-hidden flex-grow flex flex-col text-sm transition-all">
          <div
            className={cn(
              "pb-4 pt-0 flex-grow bg-white rounded-3xl p-4 mx-[2px] mb-[2px]",
            )}
          >
            {staff.email && (
              <Typography className="text-gray-500 text-sm">
                {staff.email}{" "}
              </Typography>
            )}{" "}
            <div className="flex items-center justify-between mt-3">
              <Typography
                className={cn(
                  "py-0.5 pl-2.5 pr-3 w-fit text-sm rounded-full flex gap-x-1 items-center ",
                  {
                    "text-green-900 bg-green-100": formik.values.isActive,
                    "text-red-900 bg-red-100": !formik.values.isActive,
                  },
                )}
              >
                <div
                  className={cn("size-2 mt-0.5 rounded-full", {
                    "bg-green-600": formik.values.isActive,
                    "bg-red-600": !formik.values.isActive,
                  })}
                />
                {formik.values.isActive ? "active" : "inactive"}
              </Typography>
              <Toggle
                checked={formik.values.isActive}
                onChange={(e) => {
                  formik.setFieldValue("isActive", e);
                  props.onToggleActivation(staff._id, e);
                }}
              />
            </div>
            <div className="mt-3 flex font-medium items-center gap-4 text-sm text-gray-600">
              <span>Capacity: {staff.capacity}</span>
              {staff.services.length > 0 && (
                <span>
                  {staff.services.length} service
                  {staff.services.length !== 1 ? "s" : ""}
                </span>
              )}{" "}
            </div>
            <div className="mt-3 flex flex-col gap-y-2 py-2 px-3 bg-gray-50 rounded-xl">
              {staffWorkingHours.map(([timeSlot, days]) => (
                <Typography
                  key={timeSlot}
                  className="w-full flex items-center justify-between">
                  {days.join(", ")}{" "}
                  <span className="inline-block ml-auto">{timeSlot}</span>
                </Typography>
              ))}
            </div>
          </div>
        </div>
      </div>

      {and([modals.workingHours.open, modals.workingHours.data?.staff]) && (
        <WorkingHoursModal
          isOpen={modals.workingHours.open}
          onClose={modalFunctions.returnClose("workingHours")}
          staff={modals.workingHours.data!.staff!}
        />
      )}
    </>
  );
};

export default StaffCard;
