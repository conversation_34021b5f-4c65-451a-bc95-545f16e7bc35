import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import Modal, { BaseModalProps } from "@/components/ui/modal";
import { X } from "lucide-react";
import { omit, pick, truncateText } from "@/lib/helpers";
import { useDigitalProfileContext } from "../hooks/use-digital-profile";
import { useToast } from "@/src/contexts/hooks/toast";

type Props = BaseModalProps & {
  onSave: (style: string) => void;
};

export default function ButtonsChangeModal(props: Props) {
  const { profile:
    { theme: { buttonStyles } = {} }
  } = useDigitalProfileContext()
  const showToast = useToast()

  return (
    <Modal {...pick(props, 'onClose', 'open')} >
      <Modal.Body
        className={`min-h-fit min-w-80 h-screen w-screen flex flex-col gap-y-6 items-center rounded-none bg-white max-[768px]:border-none md:h-fit md:w-fit md:justify-center md:items-center md:rounded-[32px] md:max-w-[480px] md:min-h-fit md:px-6 md:py-6`}
      >
        <div className="w-full flex justify-between items-center">
          <Typography variant={"h3"} className="font-bold ">
            Select Button Theme
          </Typography>
          <Button
            variant="icon"
            onTap={props.onClose}
            className="p-1 !rounded-full"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        <div className="w-full space-y-2">
          <div className="space-y-2 pb-2 hidden ">
            <p className="text-sm text-gray-600">Selected theme</p>
          </div>

          <div className="space-y-2 w-full ">
            <label className="text-sm text-gray-600 hidden">All themes</label>
            <div className="w-full grid grid-cols-2 gap-2 max-h-80 overflow-y-auto no-scrollbar md:max-h-60 ">
              {buttonStyles?.map((style, index) => {
                const KEYS_TO_OMIT = ['hoverEffect', 'gradient', 'background'] as const
                const themeButtonStyleObj = omit(style.style, ...KEYS_TO_OMIT)
                const { gradient, background } = style.style
                return (
                  <div
                    key={index}
                    className="flex flex-col gap-y-3 items-center">

                    <button
                      style={{
                        ...themeButtonStyleObj,
                        background: gradient ?? background
                      }}
                      onClick={() => props.onSave(style.name)}
                      className="w-full h-14 "
                    />

                    <Typography>{truncateText(style.name, innerWidth <= 768 ? 20 : 30)}</Typography>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
        <Button
          onTap={() => {
            showToast('info', 'Coming Soon')
          }}
          className="w-full mt-auto md:mt-3 py-3 !rounded-full font-medium "
        >
          Create Theme
        </Button>
      </Modal.Body>
    </Modal>
  );
}
