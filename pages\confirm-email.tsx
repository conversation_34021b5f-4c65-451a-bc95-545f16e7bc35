import { Button } from "@/components/buttons";
import { Typography } from "@/components/typography";
import CircularLoader from "@/components/ui/circular-loader";
import ErrorScreen from "@/components/ui/error-screen";
import { ConfirmUserEmail } from "@/src/services/auth.service";
import { useQuery } from "@tanstack/react-query";
import { Tick02Icon } from "hugeicons-react";
import { useNavigate, useParams } from "react-router-dom";

/**
 * @dev after the confirmed email on the backend, they can login.
 */
const ConfirmEmail = () => {
  const { token } = useParams<{ token: string }>();
  const navigate = useNavigate();
  const { isFetching, data } = useQuery({
    queryKey: ["confirm-mail"],
    queryFn: async () => {
      try {
        const response = await ConfirmUserEmail(token as string);
        return { status: response.status };
      } catch (error) {
        return { status: 400 };
      }
    },
    enabled: typeof token === "string",
    refetchOnWindowFocus: false,
  });
  // use query here to confirm email
  if (isFetching)
    return (
      <div className="w-full h-screen py-10 flex justify-center">
        <CircularLoader />
      </div>
    );
  if (![200, 201].includes(data?.status as number))
    return (
      <ErrorScreen
        className="mt-20"
        title="Email token expired"
        message="Your email confirmation token has expired. Please click try again to login and request a new confirmation token."
        onRetry={() => navigate("/signin")}
      />
    );
  return (
    <section
      className={`h-fit w-fit mx-auto mt-20 flex px-8 py-10 flex-col gap-y-7 items-center max-w-[400px] justify-center rounded-[28px] bg-white `}
    >
      <Typography variant={"h4"} className=" ">
        Email confirmed!
      </Typography>
      <div className=" flex flex-col gap-y-4 ">
        <div className="flex flex-col gap-y-4 pt-2 px-3">
          <div className="p-8 w-fit h-fit bg-green-500 rounded-full mx-auto">
            <Tick02Icon className="w-8 h-8 text-white " />
          </div>
          <Typography variant="p" className="!mt-0 text-center">
            Your email has been confirmed successfully! You may login in now.
          </Typography>
          <Button
            variant="full"
            className="mt-5"
            onTap={() => navigate("/signin")}
          >
            Login
          </Button>
        </div>
      </div>
    </section>
  );
};

export default ConfirmEmail;
